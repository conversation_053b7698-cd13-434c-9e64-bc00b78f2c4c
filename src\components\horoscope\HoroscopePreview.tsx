import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { motion } from 'framer-motion';
import { horoscopeSigns } from '../../data/horoscopes';
import useHoroscopeCache from '../../hooks/useHoroscopeCache';

interface HoroscopePreviewProps {
  signId: string;
  onReadMore: (signId: string) => void;
  arrowPosition?: number; // 箭头的水平位置百分比
  type?: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'love'; // 运势类型
  pageType?: string; // 页面类型，用于处理特殊日期类型
  date?: Date; // 日期参数，用于特定日期的运势
  containerWidth?: number; // 添加容器宽度属性
}

const HoroscopePreview: React.FC<HoroscopePreviewProps> = ({ 
  signId, 
  onReadMore,
  arrowPosition = 50, // 默认箭头位置在中间
  type = 'daily', // 默认为每日运势
  pageType,
  date = new Date(), // 默认为当前日期
  containerWidth // 容器宽度
}) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [content, setContent] = useState<string>('');
  const [localLoading, setLocalLoading] = useState<boolean>(true);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [previewWidth, setPreviewWidth] = useState<number | undefined>(containerWidth);
  
  // 检查当前语言
  const currentLanguage = i18n.language;
  const isEnglishOrJapanese = currentLanguage === 'en' || currentLanguage === 'ja';
  
  // 检查是否是特殊日期类型
  const isSpecialDateType = pageType && ['yesterday', 'tomorrow', 'lastweek', 'nextweek', 'lastmonth', 'nextmonth', 'lastyear', 'nextyear', 'lastlove', 'nextlove'].includes(pageType);
  
  // 使用与HoroscopeContent组件相同的hook
  const { loading, error, getHoroscopeForSign, getHoroscopeForSignWithDate } = useHoroscopeCache(type);

  // 检测设备类型并获取容器宽度
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768); // 768px是常见的移动端断点
    };
    
    // 初始检查
    checkIfMobile();
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkIfMobile);
    
    // 尝试获取星座区域的宽度
    const zodiacContainer = document.querySelector('.zodiac-signs-container');
    if (zodiacContainer) {
      setPreviewWidth(zodiacContainer.clientWidth);
    } else if (!previewWidth) {
      // 如果找不到星座容器且没有设置宽度，使用视口宽度减去边距
      setPreviewWidth(window.innerWidth - 32); // 减去左右边距
    }
    
    // 清理监听器
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // 获取星座名称
  const getSignName = () => {
    const sign = horoscopeSigns.find(s => s.id === signId);
    return sign ? t(sign.nameKey, sign.defaultName) : '';
  };

  // 获取运势类型的标题
  const getTypeTitle = () => {
    // 根据pageType获取更具体的标题
    if (pageType) {
      switch (pageType) {
        case 'yesterday':
          return t('horoscope.yesterday_title', '昨日运势');
        case 'tomorrow':
          return t('horoscope.tomorrow_title', '明日运势');
        case 'lastweek':
          return t('horoscope.lastweek_title', '上周运势');
        case 'nextweek':
          return t('horoscope.nextweek_title', '下周运势');
        case 'lastmonth':
          return t('horoscope.lastmonth_title', '上月运势');
        case 'nextmonth':
          return t('horoscope.nextmonth_title', '下月运势');
        case 'lastyear':
          return t('horoscope.lastyear_title', '去年运势');
        case 'nextyear':
          return t('horoscope.nextyear_title', '明年运势');
        case 'lastlove':
          return t('horoscope.lastlove_title', '上月爱情运势');
        case 'nextlove':
          return t('horoscope.nextlove_title', '下月爱情运势');
      }
    }
    
    // 默认标题
    switch (type) {
      case 'daily':
        return t('horoscope.daily_title', '每日运势');
      case 'weekly':
        return t('horoscope.weekly_title', '每周运势');
      case 'monthly':
        return t('horoscope.monthly_title', '每月运势');
      case 'yearly':
        return t('horoscope.yearly_title', '年度运势');
      case 'love':
        return t('horoscope.love_title', '爱情运势');
      default:
        return t('horoscope.daily_title', '每日运势');
    }
  };

  // 监控数据加载状态和signId变化
  useEffect(() => {
    if (loading) {
      setLocalLoading(true);
    } else if (signId) {
      // 数据加载完成后，尝试获取当前星座数据
      updateContent();
    }
  }, [loading, signId, i18n.language, type, pageType, date]);

  // 更新内容的函数
  const updateContent = async () => {
    if (!signId || loading) return;
    
    setLocalLoading(true);
    
    try {
      let horoscopeData;
      
      // 根据是否是特殊日期类型，调用不同的方法获取数据
      if (isSpecialDateType) {
        // 对于特殊日期类型，使用传入的日期参数
        horoscopeData = await getHoroscopeForSignWithDate(signId, date);
      } else {
        // 对于当前日期的运势，使用默认方法
        horoscopeData = await getHoroscopeForSign(signId);
      }
      
      if (horoscopeData) {
        setContent(horoscopeData.content);
      } else {
        // 如果没有找到数据，设置为空字符串
        setContent('');
      }
    } catch (err) {
      setContent('');
    } finally {
      setLocalLoading(false);
    }
  };

  const handleReadMore = () => {
    onReadMore(signId);
  };

  // 定义样式对象
  const twoLineTextStyle = {
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical' as 'vertical',
    overflow: 'hidden',
    lineHeight: '1.5em',
    maxHeight: '3em',
    position: 'relative' as 'relative'
  };
  
  const readMoreButtonStyle = {
    position: 'absolute' as 'absolute',
    bottom: 0,
    right: 0,
    background: isDark ? 'rgb(0, 0, 0)' : 'rgb(255, 255, 255)',
    paddingLeft: '20px',
    height: '1.5em',
    lineHeight: '1.5em',
    whiteSpace: 'nowrap' as 'nowrap',
    width: '120px',
    borderTopLeftRadius: '3px',
    borderBottomLeftRadius: '3px',
    boxShadow: isDark ? 'none' : '0 0 5px rgba(0, 0, 0, 0.1)'
  };

  // 优化箭头样式，仅在非移动端显示
  const arrowStyle = {
    position: 'absolute' as 'absolute',
    top: '-14px', // 增加高度使箭头更加明显
    left: `${arrowPosition}%`,
    transform: 'translateX(-50%)',
    width: '28px', // 增加宽度
    height: '14px', // 增加高度
    overflow: 'hidden',
    zIndex: 10,
    pointerEvents: 'none' as 'none', // 确保箭头不会干扰点击事件
    display: isMobile ? 'none' : 'block' // 移动端隐藏箭头
  };

  const arrowInnerStyle = {
    position: 'absolute' as 'absolute',
    top: '7px', // 调整位置
    left: '0',
    width: '28px', // 增加宽度
    height: '28px', // 增加高度
    transform: 'rotate(45deg)',
    background: isDark ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.95)',
    borderLeft: isDark ? '1px solid rgba(168, 85, 247, 0.4)' : '1px solid rgba(216, 180, 254, 1)',
    borderTop: isDark ? '1px solid rgba(168, 85, 247, 0.4)' : '1px solid rgba(216, 180, 254, 1)',
    boxShadow: isDark ? 'none' : '-1px -1px 5px rgba(0, 0, 0, 0.07)'
  };

  // 获取示例内容，用于在加载时保持气泡宽度
  const getExampleContent = () => {
    // 返回一个足够长的示例内容，确保宽度足够
    return t('horoscope.preview.example_content', "这是一个示例内容，用于保持气泡宽度。这是一个示例内容，用于保持气泡宽度。这是一个示例内容，用于保持气泡宽度。这是一个示例内容，用于保持气泡宽度。这是一个示例内容，用于保持气泡宽度。这是一个示例内容，用于保持气泡宽度。这是一个示例内容，用于保持气泡宽度。这是一个示例内容，用于保持气泡宽度。这是一个示例内容，用于保持气泡宽度。这是一个示例内容，用于保持气泡宽度。");
  };

  // 移动端和桌面端样式分离，避免属性冲突
  const basePreviewStyle = {
    width: previewWidth ? `${previewWidth}px` : '100%', // 使用计算出的宽度或100%
    maxWidth: '100%', // 最大宽度100%
    boxSizing: 'border-box' as 'border-box',
    padding: '16px', // 确保内边距一致
  };
  
  // 移动端特有样式
  const mobileSpecificStyle = isMobile ? {
    borderRadius: '12px',
    margin: '0 8px', // 只在移动端设置水平边距
  } : {
    margin: '0 auto', // 桌面端水平居中
    marginTop: '16px', // 桌面端顶部边距
  };

  // 合并样式，避免冲突
  const previewStyle = {
    ...basePreviewStyle,
    ...mobileSpecificStyle,
  };

  // 加载状态下的内容样式，确保内容足够长
  const loadingContentStyle = {
    ...twoLineTextStyle,
    minHeight: '3em', // 确保最小高度
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`relative backdrop-blur-xl rounded-xl border shadow-lg ${
        isDark 
          ? 'bg-black/70 border-purple-500/30 text-white' 
          : 'bg-white/90 border-purple-200 text-gray-800'
      }`}
      style={previewStyle}
    >
      {/* 箭头 - 仅在非移动端显示 */}
      <div style={arrowStyle}>
        <div style={arrowInnerStyle}></div>
      </div>

      <div className="relative w-full">
        {/* 标题 - 始终显示，不管是否加载完成 */}
        <h3 className={`font-bold italic mb-2 text-lg ${isDark ? 'text-purple-300' : 'text-purple-700'}`}>
          {getSignName()}{getTypeTitle()}
        </h3>
        
        {/* 内容区域 - 根据加载状态显示不同内容 */}
        <div className="relative w-full">
          {loading || localLoading ? (
            <div className="animate-pulse w-full">
              <p style={loadingContentStyle} className={`text-base ${isDark ? 'text-gray-500' : 'text-gray-400'} w-full`}>
                {getExampleContent()}
              </p>
              <div 
                style={readMoreButtonStyle}
                className={isDark ? 'bg-black' : 'bg-white'}
              >
                <span 
                  className={`absolute font-medium text-lg ${isDark ? 'text-gray-400' : 'text-gray-500'}`} 
                  style={{ 
                    letterSpacing: '0.15em',
                    left: isEnglishOrJapanese ? '2px' : '4px' // 英语和日语环境下省略号位置调整
                  }}
                >
                  ...
                </span>
                <button className={`font-medium absolute right-0 pr-2 ${
                  isDark ? 'text-purple-400' : 'text-purple-600'
                }`}>
                  {t('common.read_more', '阅读更多')}
                </button>
              </div>
            </div>
          ) : error ? (
            <p className={`${isDark ? 'text-red-400' : 'text-red-500'} text-base`}>{t('horoscope.preview.error', '获取数据出错')}</p>
          ) : content ? (
            <>
              <p 
                className={`text-base ${isDark ? 'text-gray-300' : 'text-gray-700'} w-full`}
                style={twoLineTextStyle}
              >
                {content}
              </p>
              <div 
                style={readMoreButtonStyle}
                className={isDark ? 'bg-black' : 'bg-white'}
              >
                <span 
                  className={`absolute font-medium text-lg ${isDark ? 'text-gray-400' : 'text-gray-500'}`} 
                  style={{ 
                    letterSpacing: '0.15em',
                    left: isEnglishOrJapanese ? '2px' : '4px' // 英语和日语环境下省略号位置调整
                  }}
                >
                  ...
                </span>
                <button
                  onClick={handleReadMore}
                  className={`font-medium absolute right-0 pr-2 ${
                    isDark 
                      ? 'text-purple-400 hover:text-purple-300' 
                      : 'text-purple-600 hover:text-purple-700'
                  }`}
                >
                  {t('common.read_more', '阅读更多')}
                </button>
              </div>
            </>
          ) : (
            <p className={`text-base ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              {t('horoscope.no_data', '暂无星座运势数据')}
            </p>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default HoroscopePreview; 