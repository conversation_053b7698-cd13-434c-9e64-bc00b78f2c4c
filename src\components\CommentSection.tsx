import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { FaStar, FaComment, FaHandsClapping } from 'react-icons/fa6';
import axiosInstance from '../utils/axios';

interface Comment {
  id: string;
  userId: string;
  username: string;
  rating: number;
  content: string;
  createdAt: string;
  parentId?: string;
  replyToUserId?: string;
  replyToUsername?: string;
  replyCount?: number;
  likeCount?: number;
}

interface CommentSectionProps {
  sessionId?: string;
  pageType?: 'yes-no-tarot' | 'tarot-result' | 'daily-fortune';
  className?: string;
  showWriteComment?: boolean;
  onShowAllComments?: () => void;
}

const CommentSection: React.FC<CommentSectionProps> = ({
  sessionId,
  pageType = 'tarot-result',
  className = '',
  showWriteComment = true,
  onShowAllComments
}) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const { user } = useUser();
  const isDark = theme === 'dark';
  
  const [rating, setRating] = useState<number | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [editorExpanded, setEditorExpanded] = useState(false);

  // 文本编辑相关状态
  const editorRef = useRef<HTMLDivElement>(null);
  const [isBoldActive, setIsBoldActive] = useState(false);
  const [isItalicActive, setIsItalicActive] = useState(false);

  // 回复相关状态
  const [replyingTo, setReplyingTo] = useState<any>(null);
  const [replyContent, setReplyContent] = useState('');
  const [expandedReplies, setExpandedReplies] = useState<Set<string>>(new Set());
  const [commentReplies, setCommentReplies] = useState<{[key: string]: any[]}>({});
  const replyEditorRef = useRef<HTMLDivElement>(null);
  const [replyBoldActive, setReplyBoldActive] = useState(false);
  const [replyItalicActive, setReplyItalicActive] = useState(false);

  // 点赞相关状态
  const [userLikeCounts, setUserLikeCounts] = useState<{[key: string]: number}>({});
  const [commentLikeCounts, setCommentLikeCounts] = useState<{[key: string]: number}>({});

  // 拍手动画状态
  const [clapAnimations, setClapAnimations] = useState<{[key: string]: {count: number, visible: boolean, fadeOut: boolean, bounce: boolean}}>({});
  const [clickAnimations, setClickAnimations] = useState<{[key: string]: boolean}>({});

  // 使用useRef来存储定时器，避免状态更新导致的问题
  const clapTimeouts = useRef<{[key: string]: NodeJS.Timeout}>({});

  // 按相关性排序评论，将当前用户的评论放在最前面
  const sortCommentsByRelevance = (comments: Comment[]) => {
    const sorted = [...comments];

    // 分离当前用户的评论和其他评论
    const userComments = sorted.filter(comment => user && comment.userId === user.id);
    const otherComments = sorted.filter(comment => !user || comment.userId !== user.id);

    // 对其他评论按相关性排序（点赞数 + 评分权重）
    const sortedOtherComments = otherComments.sort((a, b) => {
      const scoreA = (a.likeCount || 0) + (a.rating || 0) * 2;
      const scoreB = (b.likeCount || 0) + (b.rating || 0) * 2;
      return scoreB - scoreA;
    });

    // 将当前用户的评论放在最前面，然后是其他排序后的评论
    return [...userComments, ...sortedOtherComments];
  };

  // 粗体功能
  const handleBold = () => {
    const editor = editorRef.current;
    if (!editor) return;

    editor.focus();
    document.execCommand('bold', false);
    setIsBoldActive(!isBoldActive);
  };

  // 斜体功能
  const handleItalic = () => {
    const editor = editorRef.current;
    if (!editor) return;

    editor.focus();
    document.execCommand('italic', false);
    setIsItalicActive(!isItalicActive);
  };

  // 键盘快捷键处理
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key.toLowerCase()) {
        case 'b':
          e.preventDefault();
          handleBold();
          break;
        case 'i':
          e.preventDefault();
          handleItalic();
          break;
      }
    }
  };

  // 处理富文本编辑器内容变化
  const handleEditorInput = () => {
    const editor = editorRef.current;
    if (!editor) return;

    const content = editor.innerText || '';

    if (content.trim()) {
      setSubmitError(null);
    }
  };

  // 将HTML内容转换为纯文本用于提交
  const getPlainTextContent = () => {
    const editor = editorRef.current;
    if (!editor) return '';

    return editor.innerText || '';
  };

  // 将HTML内容转换为Markdown格式用于存储
  const getMarkdownContent = () => {
    const editor = editorRef.current;
    if (!editor) return '';

    let html = editor.innerHTML;

    // 将HTML标签转换为Markdown
    html = html.replace(/<strong>(.*?)<\/strong>/g, '**$1**');
    html = html.replace(/<b>(.*?)<\/b>/g, '**$1**');
    html = html.replace(/<em>(.*?)<\/em>/g, '*$1*');
    html = html.replace(/<i>(.*?)<\/i>/g, '*$1*');

    // 移除其他HTML标签
    html = html.replace(/<[^>]*>/g, '');

    // 处理换行
    html = html.replace(/<br\s*\/?>/gi, '\n');
    html = html.replace(/<div>/gi, '\n');
    html = html.replace(/<\/div>/gi, '');

    return html;
  };

  // 简单的Markdown渲染函数（用于显示已有评论）
  const renderMarkdown = (text: string) => {
    // 处理粗体 **text**
    let rendered = text.replace(/\*\*(.*?)\*\*/g, `<strong style="font-weight: 800; color: ${isDark ? '#F3F4F6' : '#111827'}; text-shadow: 0 0 1px ${isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.3)'};">$1</strong>`);
    // 处理斜体 *text*
    rendered = rendered.replace(/\*(.*?)\*/g, `<em style="font-style: italic; color: ${isDark ? '#E5E7EB' : '#374151'}; font-weight: 500;">$1</em>`);

    return { __html: rendered };
  };

  // 组件卸载时提交待处理的点赞和清理定时器
  useEffect(() => {
    return () => {
      // 清理所有定时器
      Object.values(clapTimeouts.current).forEach(timeout => {
        clearTimeout(timeout);
      });
      clapTimeouts.current = {};

      // 组件卸载时异步提交待处理的点赞
      (async () => {
        try {
          const { submitPendingLikes, getPendingLikesCount } = await import('../services/commentLikeService');
          const pendingCount = getPendingLikesCount();
          if (pendingCount > 0 && user) {
            await submitPendingLikes();
          }
        } catch (error) {
          console.log('组件卸载时提交点赞失败:', error);
        }
      })();
    };
  }, [user]);

  // 加载评论数据
  useEffect(() => {
    loadComments();
  }, []);

  const loadComments = async () => {
    setIsLoading(true);
    try {
      // 加载所有评论，不区分会话
      const response = await axiosInstance.get('/api/comments/all');
      if (response.data.success) {
        const allComments = response.data.data.comments || [];
        // 只显示顶级评论（没有parentId的评论）
        const topLevelComments = allComments.filter((comment: any) => !comment.parentId);

        // 按相关性排序，并将当前用户的评论放在最前面
        const sortedComments = sortCommentsByRelevance(topLevelComments);
        setComments(sortedComments);

        // 初始化点赞数量
        const likeCounts: {[key: string]: number} = {};
        topLevelComments.forEach((comment: any) => {
          likeCounts[comment.id] = comment.likeCount || 0;
        });
        setCommentLikeCounts(likeCounts);

        // 加载用户点赞次数
        if (user && topLevelComments.length > 0) {
          const commentIds = topLevelComments.map((comment: any) => comment.id);
          await loadUserLikeCounts(commentIds);
        }

        // 注释掉自动填充用户之前评论的逻辑，避免提交后重新显示
        // 检查当前用户是否已经对当前会话评论过
        // if (user && sessionId) {
        //   const userComment = response.data.data.comments.find(
        //     (comment: any) => comment.userId === user.id && comment.sessionId === sessionId
        //   );
        //   if (userComment) {
        //     setRating(userComment.rating || null);
        //     if (editorRef.current) {
        //       editorRef.current.innerHTML = userComment.content;
        //     }
        //   }
        // }
      }
    } catch (error) {
      console.log('加载评论失败:', error);
      setComments([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 加载评论的回复
  const loadReplies = async (commentId: string) => {
    try {
      const response = await axiosInstance.get(`/api/comments/${commentId}/replies`);
      if (response.data.success) {
        setCommentReplies(prev => ({
          ...prev,
          [commentId]: response.data.data.replies || []
        }));
      }
    } catch (error) {
      console.log('加载回复失败:', error);
    }
  };

  // 切换回复展开状态
  const toggleReplies = async (commentId: string) => {
    const newExpanded = new Set(expandedReplies);
    if (newExpanded.has(commentId)) {
      newExpanded.delete(commentId);
    } else {
      newExpanded.add(commentId);
      // 如果还没有加载过回复，则加载
      if (!commentReplies[commentId]) {
        await loadReplies(commentId);
      }
    }
    setExpandedReplies(newExpanded);
  };

  // 开始回复
  const startReply = (comment: any) => {
    setReplyingTo(comment);
    setReplyContent('');
    setReplyBoldActive(false);
    setReplyItalicActive(false);
  };

  // 取消回复
  const cancelReply = () => {
    setReplyingTo(null);
    setReplyContent('');
    setReplyBoldActive(false);
    setReplyItalicActive(false);
  };

  // 提交回复
  const submitReply = async () => {
    if (!replyingTo || !replyContent.trim()) return;

    try {
      await axiosInstance.post('/api/comments', {
        content: replyContent.trim(),
        parentId: replyingTo.id,
        replyToUserId: replyingTo.userId,
        pageType: pageType,
        sessionId: sessionId || null,
        language: i18n.language
      });

      // 重新加载回复
      await loadReplies(replyingTo.id);

      // 清空回复状态
      cancelReply();

      // 确保回复区域是展开的
      setExpandedReplies(prev => new Set([...prev, replyingTo.id]));
    } catch (error: any) {
      console.log('回复失败:', error);
      const errorMessage = error.response?.data?.message || '回复提交失败，请重试';
      console.log(errorMessage);
    }
  };

  // 加载用户点赞次数
  const loadUserLikeCounts = async (commentIds: string[]) => {
    if (!user || !commentIds.length) return;

    try {
      const { getCommentsLikeCounts } = await import('../services/commentLikeService');
      const likeCounts = await getCommentsLikeCounts(commentIds);

      setUserLikeCounts(likeCounts);
    } catch (error) {
      console.log('加载用户点赞次数失败:', error);
    }
  };

  // 添加点赞（支持连击）
  const addLike = async (commentId: string) => {
    if (!user) return;

    try {
      const { addCommentLikeLocal } = await import('../services/commentLikeService');

      // 本地添加点赞
      const result = addCommentLikeLocal(commentId, 1);

      // 更新用户点赞次数显示
      setUserLikeCounts(prev => ({
        ...prev,
        [commentId]: result.totalCount
      }));

      // 更新总点赞数量显示
      setCommentLikeCounts(prev => ({
        ...prev,
        [commentId]: (prev[commentId] || 0) + 1
      }));

      // 触发点击动画
      setClickAnimations(prev => ({
        ...prev,
        [commentId]: true
      }));

      // 0.3秒后停止点击动画
      setTimeout(() => {
        setClickAnimations(prev => ({
          ...prev,
          [commentId]: false
        }));
      }, 300);

      // 添加或更新飞出动画（使用useRef防抖机制）
      // 清除之前的定时器
      if (clapTimeouts.current[commentId]) {
        clearTimeout(clapTimeouts.current[commentId]);
      }

      // 更新动画状态
      setClapAnimations(prev => {
        const existing = prev[commentId];
        return {
          ...prev,
          [commentId]: {
            count: existing ? existing.count + 1 : 1,
            visible: true,
            fadeOut: false,
            bounce: true
          }
        };
      });

      // 0.3秒后停止bounce动画
      setTimeout(() => {
        setClapAnimations(prev => ({
          ...prev,
          [commentId]: prev[commentId] ? {
            ...prev[commentId],
            bounce: false
          } : prev[commentId]
        }));
      }, 300);

      // 设置新的定时器
      clapTimeouts.current[commentId] = setTimeout(() => {
        // 先触发fadeOut动画
        setClapAnimations(prev => ({
          ...prev,
          [commentId]: prev[commentId] ? {
            ...prev[commentId],
            fadeOut: true
          } : prev[commentId]
        }));

        // 500ms后完全移除元素
        setTimeout(() => {
          setClapAnimations(prev => {
            const { [commentId]: removed, ...rest } = prev;
            return rest;
          });
        }, 500);

        delete clapTimeouts.current[commentId];
      }, 1500);

    } catch (error) {
      console.log('点赞操作失败:', error);
    }
  };

  // 提交评论
  const handleSubmit = async () => {
    const plainTextContent = getPlainTextContent();
    const markdownContent = getMarkdownContent();

    if (!plainTextContent.trim()) {
      setSubmitError(t('comment.please_enter_comment', '请输入评论内容'));
      return;
    }

    if (!user) {
      setSubmitError(t('comment.login_required', '请先登录后再评论'));
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      await axiosInstance.post('/api/comments', {
        sessionId,
        rating: rating, // 可以为 null
        content: markdownContent.trim(),
        pageType,
        language: i18n.language
      });

      // 重新加载评论列表
      await loadComments();

      // 提交成功后清空输入框和评分
      if (editorRef.current) {
        editorRef.current.innerHTML = '';
      }
      setRating(null);
      setEditorExpanded(false);
      setSubmitError(null); // 清空错误信息
      setIsBoldActive(false); // 重置格式化按钮状态
      setIsItalicActive(false);

    } catch (error: any) {
      // console.log('提交评论失败:', error);
      // console.log('错误详情:', error.response?.data);
      setSubmitError(error.response?.data?.message || t('comment.submit_error', '提交失败，请重试'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    // 处理数据库时间格式，确保正确解析
    let date: Date;
    if (dateString.includes('T')) {
      // ISO格式
      date = new Date(dateString);
    } else {
      // MySQL DATETIME格式 (YYYY-MM-DD HH:mm:ss)，假设为UTC时间
      date = new Date(dateString + 'Z'); // 添加Z表示UTC时间
    }

    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const weeks = Math.floor(days / 7);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    if (minutes < 1) {
      return t('comment.time.just_now', '刚刚');
    } else if (hours < 1) {
      return t('comment.time.minutes_ago', '{{count}}分钟前', { count: minutes });
    } else if (days < 1) {
      return t('comment.time.hours_ago', '{{count}}小时前', { count: hours });
    } else if (days < 7) {
      return t('comment.time.days_ago', '{{count}}天前', { count: days });
    } else if (weeks < 4) {
      return t('comment.time.weeks_ago', '{{count}}周前', { count: weeks });
    } else if (months < 12) {
      return t('comment.time.months_ago', '{{count}}个月前', { count: months });
    } else {
      return t('comment.time.years_ago', '{{count}}年前', { count: years });
    }
  };

  // 渲染星级评分
  const renderStars = (rating: number, interactive: boolean = false, onRate?: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => interactive && onRate && onRate(star)}
            disabled={!interactive}
            className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`}
          >
            <FaStar
              className={`w-4 h-4 ${
                star <= rating 
                  ? 'text-yellow-400' 
                  : isDark ? 'text-gray-600' : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  return (
    <div className={`${
      isDark
        ? 'bg-gradient-to-b from-gray-800/40 to-gray-900/40 border border-purple-500/10'
        : 'bg-[#F4F4F5] border border-purple-300/30'
    } backdrop-blur-md rounded-xl sm:rounded-2xl overflow-hidden shadow-2xl ${className}`}>
      {/* 添加拍手动画的CSS样式 */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes clapFloat {
            0% {
              opacity: 1;
              transform: translateX(-50%) translateY(0) scale(1);
            }
            100% {
              opacity: 1;
              transform: translateX(-50%) translateY(0) scale(1);
            }
          }

          @keyframes clapBounce {
            0% {
              transform: translateX(-50%) translateY(0) scale(1);
            }
            50% {
              transform: translateX(-50%) translateY(0) scale(1.1);
            }
            100% {
              transform: translateX(-50%) translateY(0) scale(1);
            }
          }

          @keyframes clapFadeOut {
            0% {
              opacity: 1;
              transform: translateX(-50%) translateY(0) scale(1);
            }
            100% {
              opacity: 0;
              transform: translateX(-50%) translateY(-30px) scale(1);
            }
          }

          @keyframes clapPulse {
            0% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.2);
            }
            100% {
              transform: scale(1);
            }
          }

          .clap-icon-animate {
            animation: clapPulse 0.3s ease-in-out;
          }

          /* 回复输入框placeholder样式 */
          [contenteditable][data-placeholder]:empty::before {
            content: attr(data-placeholder);
            color: #9CA3AF;
            pointer-events: none;
          }

          .dark [contenteditable][data-placeholder]:empty::before {
            color: #6B7280;
          }
        `
      }} />

      {/* Header with comment title */}
      <div className={`px-4 sm:px-8 py-4 sm:py-6 ${
        isDark ? 'border-b border-gray-700/30' : 'border-b border-gray-300/50'
      } flex justify-start items-center`}>
        <h3 className={`text-xl font-bold ${isDark ? 'text-white' : 'text-gray-800'}`}>
          Responses ({comments.length})
        </h3>
      </div>

      {/* 写评价区域 */}
      {showWriteComment && user && (
        <div className="px-4 sm:px-8 py-6">
          {/* 用户信息 */}
          <div className="flex items-center mb-4">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              isDark ? 'bg-purple-600' : 'bg-purple-500'
            } text-white text-lg font-medium mr-3`}>
              {user.username?.charAt(0)?.toUpperCase() || 'U'}
            </div>
            <div className="flex-1">
              <div className={`font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                {user.username || t('comment.user', '用户')}
              </div>
            </div>
          </div>

          {/* 评论输入框 */}
          <div className="mb-4">
            <div className="mb-2"></div>
            <div 
              className={`rounded-lg ${isDark ? 'bg-gray-800' : 'bg-gray-100'} p-4 transition-all duration-200 ${
                editorExpanded ? 'min-h-[150px]' : 'min-h-[50px] cursor-text'
              }`}
              onClick={() => {
                if (!editorExpanded) {
                  setEditorExpanded(true);
                  setTimeout(() => {
                    if (editorRef.current) {
                      editorRef.current.focus();
                    }
                  }, 100);
                }
              }}
            >
              {/* 富文本编辑器 */}
              <div
                ref={editorRef}
                contentEditable={editorExpanded}
                onInput={handleEditorInput}
                onKeyDown={handleKeyDown}
                className={`w-full p-0 border-0 bg-transparent text-base rich-text-editor ${
                  isDark
                    ? 'text-gray-200'
                    : 'text-gray-800'
                } focus:outline-none transition-all ${
                  editorExpanded ? 'min-h-[80px]' : 'min-h-[24px] max-h-[24px] overflow-hidden'
                }`}
                style={{
                  wordWrap: 'break-word',
                  overflowWrap: 'break-word'
                }}
                data-placeholder={t('comment.share_thoughts', '分享您的想法...')}
                suppressContentEditableWarning={true}
              />

              {/* CSS样式通过className处理 */}
              <style dangerouslySetInnerHTML={{
                __html: `
                  .rich-text-editor:empty:before {
                    content: attr(data-placeholder);
                    color: ${isDark ? '#9CA3AF' : '#6B7280'};
                    pointer-events: none;
                  }
                  .rich-text-editor:focus:empty:before {
                    content: attr(data-placeholder);
                    color: ${isDark ? '#9CA3AF' : '#6B7280'};
                    pointer-events: none;
                  }
                  .rich-text-editor strong,
                  .rich-text-editor b {
                    font-weight: 800 !important;
                    color: ${isDark ? '#F3F4F6' : '#111827'} !important;
                    text-shadow: 0 0 1px ${isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.3)'};
                  }
                  .rich-text-editor em,
                  .rich-text-editor i {
                    font-style: italic !important;
                    color: ${isDark ? '#E5E7EB' : '#374151'} !important;
                    font-weight: 500;
                  }
                  /* 移除默认的聚焦边框 */
                  .rich-text-editor:focus {
                    outline: none !important;
                    box-shadow: none !important;
                    border: none !important;
                  }
                  /* 移除contentEditable元素的默认边框 */
                  [contenteditable] {
                    outline: none !important;
                    box-shadow: none !important;
                    border: none !important;
                  }
                  /* 防止在点击时出现闪烁边框 */
                  [contenteditable]:focus-visible {
                    outline: none !important;
                    box-shadow: none !important;
                    border: none !important;
                  }
                `
              }} />

              {/* 工具栏 */}
              {editorExpanded && (
                <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
                  {/* 左侧区域：格式按钮和评分 */}
                  <div className="flex items-center">
                    {/* 格式按钮 */}
                    <div className="flex items-center space-x-1 mr-4">
                      <button
                        type="button"
                        onClick={handleBold}
                        className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                          isBoldActive
                            ? isDark
                              ? 'bg-gray-600 text-gray-200 hover:bg-gray-500'
                              : 'bg-gray-300 text-gray-800 hover:bg-gray-400'
                            : isDark
                              ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                              : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                        }`}
                        title={t('editor.bold', '粗体 (Ctrl+B)')}
                      >
                        <span className="font-bold text-base">B</span>
                      </button>
                      <button
                        type="button"
                        onClick={handleItalic}
                        className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                          isItalicActive
                            ? isDark
                              ? 'bg-gray-600 text-gray-200 hover:bg-gray-500'
                              : 'bg-gray-300 text-gray-800 hover:bg-gray-400'
                            : isDark
                              ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                              : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                        }`}
                        title={t('editor.italic', '斜体 (Ctrl+I)')}
                      >
                        <span className="italic text-base">i</span>
                      </button>
                    </div>

                    {/* 评分组件 */}
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        {renderStars(rating || 0, true, (newRating) => {
                          setRating(newRating);
                        })}
                      </div>
                    </div>
                  </div>

                  {/* 右侧操作按钮 */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        setRating(null);
                        if (editorRef.current) {
                          editorRef.current.innerHTML = '';
                        }
                        setSubmitError(null);
                        setEditorExpanded(false);
                      }}
                      className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                        isDark
                          ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                          : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {t('comment.cancel', '取消')}
                    </button>
                    <button
                      onClick={handleSubmit}
                      disabled={isSubmitting || !getPlainTextContent().trim()}
                      className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                        isSubmitting || !getPlainTextContent().trim()
                          ? 'bg-gray-300 cursor-not-allowed text-gray-500'
                          : 'bg-blue-500 hover:bg-blue-600 text-white'
                      }`}
                    >
                      {isSubmitting ? t('comment.submitting', '提交中...') : t('comment.submit_comment', '提交评价')}
                    </button>
                  </div>
                </div>
              )}

              {/* 错误提示 */}
              {submitError && (
                <div className="text-red-500 text-sm mt-2">
                  {submitError}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 评论列表 */}
      <div className="px-4 sm:px-8 py-6">
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
          </div>
        ) : comments.length > 0 ? (
          <div className="space-y-8">
            {comments.slice(0, 3).map((comment) => (
              <div
                key={comment.id}
                className="pb-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
              >
                <div className="flex items-start space-x-4">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    isDark ? 'bg-purple-600' : 'bg-purple-500'
                  } text-white text-lg font-medium flex-shrink-0`}>
                    {comment.username?.charAt(0)?.toUpperCase() || 'U'}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-2">
                      <p className={`text-base font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                        {comment.username || t('comment.anonymous_user', '匿名用户')}
                      </p>
                      {user && user.id === comment.userId && (
                        <span className={`text-xs px-1.5 py-0.5 rounded ${
                          isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-600'
                        }`}>
                          {t('comment.you', '您')}
                        </span>
                      )}
                      {comment.rating && (
                        <div className="flex items-center space-x-0.5">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <FaStar
                              key={star}
                              className={`w-3.5 h-3.5 ${
                                star <= comment.rating
                                  ? 'text-yellow-400'
                                  : isDark ? 'text-gray-600' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                      )}
                      <p
                        className="text-sm"
                        style={{ color: isDark ? '#6B7280' : '#9CA3AF' }}
                      >
                        {formatTime(comment.createdAt)}
                      </p>
                    </div>
                    <div
                      className={`text-base ${isDark ? 'text-gray-200' : 'text-gray-800'} leading-relaxed mb-4`}
                      dangerouslySetInnerHTML={renderMarkdown(comment.content)}
                    />

                    {/* 回复操作按钮 */}
                    <div className="flex items-center space-x-4">
                      {/* 拍手点赞按钮 */}
                      <div className="relative">
                        <button
                          onClick={() => addLike(comment.id)}
                          className={`clap-button flex items-center space-x-1 text-sm transition-colors duration-200 ${
                            (userLikeCounts[comment.id] || 0) > 0
                              ? 'text-yellow-400'
                              : isDark
                                ? 'text-gray-600 hover:text-yellow-400'
                                : 'text-gray-400 hover:text-yellow-400'
                          }`}
                        >
                          <FaHandsClapping
                            className={`w-4 h-4 transition-all duration-200 ${
                              clickAnimations[comment.id]
                                ? 'clap-icon-animate'
                                : ''
                            }`}
                          />
                          <span className="font-medium">{commentLikeCounts[comment.id] || comment.likeCount || 0}</span>
                        </button>

                        {/* 拍手动画特效 */}
                        {clapAnimations[comment.id]?.visible && (
                          <div
                            key={comment.id}
                            className="absolute -top-12 left-1/2 transform -translate-x-1/2 pointer-events-none"
                            style={{
                              animation: clapAnimations[comment.id]?.fadeOut
                                ? 'clapFadeOut 0.5s ease-out forwards'
                                : clapAnimations[comment.id]?.bounce
                                  ? 'clapBounce 0.3s ease-out forwards'
                                  : 'clapFloat 0.3s ease-out forwards'
                            }}
                          >
                            <div
                              className="w-10 h-10 rounded-full flex items-center justify-center text-base font-bold shadow-lg bg-yellow-400"
                              style={{ color: '#000000' }}
                            >
                              +{clapAnimations[comment.id].count}
                            </div>
                          </div>
                        )}
                      </div>

                      <button
                        onClick={() => startReply(comment)}
                        className={`text-sm ${isDark ? 'text-gray-600 hover:text-gray-500' : 'text-gray-400 hover:text-gray-600'} transition-colors`}
                      >
                        {t('comment.reply', '回复')}
                      </button>
                      {(comment.replyCount || 0) > 0 && (
                        <button
                          onClick={() => toggleReplies(comment.id)}
                          className={`text-sm ${isDark ? 'text-gray-600 hover:text-gray-500' : 'text-gray-400 hover:text-gray-600'} transition-colors`}
                        >
                          {expandedReplies.has(comment.id) ? t('comment.hide_replies', '收起') : `${t('comment.show_replies', '查看')}${comment.replyCount || 0}${t('comment.replies', '条回复')}`}
                        </button>
                      )}
                    </div>

                    {/* 回复输入框 */}
                    {replyingTo?.id === comment.id && (
                      <div className="mt-4">
                        <div className={`rounded-lg ${isDark ? 'bg-gray-800' : 'bg-gray-100'} p-4`}>
                          {/* 回复输入区域 */}
                          <div
                            ref={replyEditorRef}
                            contentEditable
                            suppressContentEditableWarning={true}
                            onInput={(e) => {
                              const target = e.target as HTMLDivElement;
                              setReplyContent(target.innerHTML);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                                e.preventDefault();
                                submitReply();
                              }
                              if (e.ctrlKey || e.metaKey) {
                                switch (e.key.toLowerCase()) {
                                  case 'b':
                                    e.preventDefault();
                                    document.execCommand('bold', false);
                                    setReplyBoldActive(!replyBoldActive);
                                    break;
                                  case 'i':
                                    e.preventDefault();
                                    document.execCommand('italic', false);
                                    setReplyItalicActive(!replyItalicActive);
                                    break;
                                }
                              }
                            }}
                            className={`w-full p-0 border-0 bg-transparent text-base min-h-[60px] ${
                              isDark
                                ? 'text-gray-200'
                                : 'text-gray-800'
                            } focus:outline-none rich-text-editor`}
                            style={{
                              minHeight: '60px',
                              wordWrap: 'break-word',
                              overflowWrap: 'break-word'
                            }}
                            data-placeholder={`${t('comment.reply_to', '回复')} @${comment.username}...`}
                          />

                          {/* 工具栏 */}
                          <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                            {/* 左侧格式按钮 */}
                            <div className="flex items-center space-x-1">
                              <button
                                type="button"
                                onClick={() => {
                                  if (replyEditorRef.current) {
                                    replyEditorRef.current.focus();
                                    document.execCommand('bold', false);
                                    setReplyBoldActive(!replyBoldActive);
                                  }
                                }}
                                className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                                  replyBoldActive
                                    ? isDark
                                      ? 'bg-gray-600 text-gray-200 hover:bg-gray-500'
                                      : 'bg-gray-300 text-gray-800 hover:bg-gray-400'
                                    : isDark
                                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                      : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                                }`}
                                title={t('editor.bold', '粗体 (Ctrl+B)')}
                              >
                                <span className="font-bold text-base">B</span>
                              </button>
                              <button
                                type="button"
                                onClick={() => {
                                  if (replyEditorRef.current) {
                                    replyEditorRef.current.focus();
                                    document.execCommand('italic', false);
                                    setReplyItalicActive(!replyItalicActive);
                                  }
                                }}
                                className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                                  replyItalicActive
                                    ? isDark
                                      ? 'bg-gray-600 text-gray-200 hover:bg-gray-500'
                                      : 'bg-gray-300 text-gray-800 hover:bg-gray-400'
                                    : isDark
                                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                      : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                                }`}
                                title={t('editor.italic', '斜体 (Ctrl+I)')}
                              >
                                <span className="italic text-base">i</span>
                              </button>
                            </div>

                            {/* 右侧操作按钮 */}
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={cancelReply}
                                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                                  isDark
                                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                                }`}
                              >
                                {t('comment.cancel_reply', '取消')}
                              </button>
                              <button
                                onClick={submitReply}
                                disabled={!replyContent.trim()}
                                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                                  replyContent.trim()
                                    ? 'bg-blue-500 hover:bg-blue-600 text-white'
                                    : 'bg-gray-300 cursor-not-allowed text-gray-500'
                                }`}
                              >
                                {t('comment.submit_reply', '回复')}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* 回复列表 */}
                    {expandedReplies.has(comment.id) && commentReplies[comment.id] && (
                      <div className="mt-4 relative">
                        {/* 左侧垂直段落线 */}
                        <div className="absolute left-5 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>
                        <div className="space-y-4 pl-10">
                          {commentReplies[comment.id].map((reply: any) => (
                            <div key={reply.id} className="py-2">
                              <div className="flex items-start space-x-3">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                  isDark ? 'bg-purple-600' : 'bg-purple-500'
                                } text-white text-sm font-medium flex-shrink-0`}>
                                  {reply.username?.charAt(0)?.toUpperCase() || 'U'}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <p className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                                      {reply.username || t('comment.anonymous_user', '匿名用户')}
                                    </p>
                                    {user && user.id === reply.userId && (
                                      <span className={`text-xs px-1.5 py-0.5 rounded ${
                                        isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-600'
                                      }`}>
                                        {t('comment.you', '您')}
                                      </span>
                                    )}
                                    {reply.replyToUsername && (
                                      <span
                                        className="text-sm"
                                        style={{ color: isDark ? '#6B7280' : '#9CA3AF' }}
                                      >
                                        {t('comment.reply_to', '回复')} @{reply.replyToUsername}
                                      </span>
                                    )}
                                    <span
                                      className="text-xs"
                                      style={{ color: isDark ? '#6B7280' : '#9CA3AF' }}
                                    >
                                      {formatTime(reply.createdAt)}
                                    </span>
                                  </div>
                                  <div
                                    className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-700'} leading-relaxed`}
                                    dangerouslySetInnerHTML={renderMarkdown(reply.content)}
                                  />
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <FaComment className={`w-16 h-16 mx-auto mb-4 ${isDark ? 'text-gray-600' : 'text-gray-400'}`} />
            <p className={`text-lg ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              {t('comment.no_comments', '暂无评价，快来抢沙发吧！')}
            </p>
          </div>
        )}
      </div>

      {/* See all responses 按钮 */}
      {comments.length > 3 && onShowAllComments && (
        <div className="px-4 sm:px-8 pb-6">
          <button
            onClick={onShowAllComments}
            className={`px-6 py-2 rounded-full transition-colors ${
              isDark
                ? 'bg-blue-600 hover:bg-blue-700 text-white border-none'
                : 'bg-blue-500 hover:bg-blue-600 text-white border-none'
            }`}
          >
            {t('comment.see_all_responses', 'See all responses')}
          </button>
        </div>
      )}
    </div>
  );
};

export default CommentSection;
