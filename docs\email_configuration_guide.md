# 多邮箱轮转发送配置指南

## 概述

本指南说明如何配置多邮箱轮转发送功能，以提高邮件发送的可靠性和减少单一邮箱的发送压力。系统会在每次发送邮件时，从配置的多个邮箱中随机选择一个进行发送。

## 配置步骤

### 1. 修改 .env 文件

在服务器的 `.env` 文件中，按照以下格式添加多个邮箱配置：

```
# 主要邮箱配置
EMAIL_HOST=smtp.163.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password
EMAIL_FROM=<EMAIL>

# 备用邮箱配置
EMAIL_HOST_2=smtp.163.com
EMAIL_PORT_2=465
EMAIL_SECURE_2=true
EMAIL_USER_2=<EMAIL>
EMAIL_PASS_2=your_email_password_2
EMAIL_FROM_2=<EMAIL>

# 可以继续添加更多邮箱配置，只需递增数字后缀
# EMAIL_HOST_3=smtp.example.com
# EMAIL_PORT_3=465
# EMAIL_SECURE_3=true
# EMAIL_USER_3=<EMAIL>
# EMAIL_PASS_3=your_email_password_3
# EMAIL_FROM_3=<EMAIL>
```

您可以按照这个模式添加任意数量的邮箱配置，只需递增数字后缀即可。

### 2. 重启服务器

配置完成后，重启服务器以应用新的配置。服务器启动时会自动加载所有配置的邮箱，并在日志中显示已加载的邮箱数量。

## 工作原理

1. 系统启动时，会自动扫描 `.env` 文件中的所有邮箱配置。
2. 每次需要发送邮件时，系统会从已配置的邮箱列表中随机选择一个。
3. 如果发送失败，系统会自动重试（最多3次），每次重试都会使用新随机选择的邮箱。
4. 所有邮件发送操作都会记录在日志中，包括使用的邮箱地址和发送结果。

## 故障排除

如果邮件发送失败，请检查以下几点：

1. 确保所有邮箱的密码或授权码正确。
2. 检查邮箱服务提供商的SMTP设置是否正确。
3. 确认邮箱服务提供商是否允许SMTP发送。
4. 检查是否达到了邮箱服务提供商的发送限制。

## 日志监控

系统会在日志中记录邮件发送的详细信息，包括：

- 使用的邮箱地址
- 发送尝试次数
- 发送成功或失败的状态
- 错误信息（如果发送失败）

您可以通过检查服务器日志来监控邮件发送状态。 