import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

interface ActionButtonProps {
  flipped: boolean;
  canPredictToday: boolean;
  handleAction: () => void;
  getFontClass: () => string;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  flipped,
  canPredictToday,
  handleAction,
  getFontClass
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <motion.div 
      className={`flex justify-center ${flipped ? 'mt-4 sm:mt-8' : 'mt-8'}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.6 }}
    >
      <motion.button
        className={`relative px-10 py-4 rounded-full font-medium text-lg ${getFontClass()}
          ${(flipped || !canPredictToday)
            ? 'bg-purple-600 hover:bg-purple-500 transition-colors duration-200' 
            : theme === 'light' 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-gray-700 cursor-not-allowed'
          }`}
        whileHover={(flipped || !canPredictToday) ? { scale: 1.02 } : {}}
        whileTap={(flipped || !canPredictToday) ? { scale: 0.98 } : {}}
        onClick={handleAction}
        disabled={!flipped && canPredictToday}
        style={{
          boxShadow: (flipped || !canPredictToday) ? "0 0 20px rgba(168, 85, 247, 0.3)" : "none",
        }}
      >
        {!canPredictToday
          ? <span style={{color: 'white'}}>{t('daily.view_today_fortune', '查看今日运势')}</span>
          : flipped 
            ? <span style={{color: 'white'}}>{t('daily.view_fortune', '查看今日运势')}</span> 
            : <span className={theme === 'light' ? 'text-gray-600' : 'text-white'}>{t('daily.tap_to_draw', '点击卡牌揭示今日指引')}</span>}
      </motion.button>
    </motion.div>
  );
};

export default ActionButton; 