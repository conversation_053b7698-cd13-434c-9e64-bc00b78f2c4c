import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import ConfirmDialog from '../ConfirmDialog';
import { useDropdown } from '../../contexts/DropdownContext';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';

const NavbarWrapper: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { t } = useTranslation();
  const { setOpenDropdown } = useDropdown();
  const [showConfirmNav, setShowConfirmNav] = useState(false);
  const [pendingPath, setPendingPath] = useState<string | null>(null);

  // 当确认对话框显示时，确保关闭所有下拉菜单
  useEffect(() => {
    if (showConfirmNav) {
      setOpenDropdown(null);
    }
  }, [showConfirmNav, setOpenDropdown]);

  // 监听Navbar中的链接点击事件
  useEffect(() => {
    // 阻止所有导航栏链接的默认行为，强制显示确认对话框
    const handleNavbarLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a');
      
      // 如果不是链接，或者是带target的外部链接，或者是锚点链接，则不处理
      if (!link || link.getAttribute('target') === '_blank' || link.getAttribute('href') === '#') {
        return;
      }
      
      // 如果是邮件链接或外部链接，不拦截
      const href = link.getAttribute('href') || '';
      if (href.startsWith('mailto:') || href.startsWith('http://') || href.startsWith('https://')) {
        return;
      }
      
      // 阻止默认导航行为
      e.preventDefault();
      e.stopPropagation();
      
      // 保存要导航的路径
      setPendingPath(href);
      setShowConfirmNav(true);
      
      // 关闭所有下拉菜单
      setOpenDropdown(null);
    };

    // 由于我们希望拦截导航栏的所有点击事件，使用捕获阶段进行监听
    const navbar = document.querySelector('nav');
    if (navbar) {
      navbar.addEventListener('click', handleNavbarLinkClick, true);
    }

    return () => {
      if (navbar) {
        navbar.removeEventListener('click', handleNavbarLinkClick, true);
      }
    };
  }, [setOpenDropdown]);

  // 确认导航
  const handleConfirmNavigation = () => {
    setShowConfirmNav(false);
    if (pendingPath) {
      // 清除所有相关的缓存
      localStorage.removeItem('selectedReader');
      localStorage.removeItem('selectedSpread');
      localStorage.removeItem('userQuestion');
      localStorage.removeItem('tarotReadingState');
      localStorage.removeItem('tarotDialogState');
      localStorage.removeItem('selectedCards');
      localStorage.removeItem('sessionId');
      localStorage.removeItem('readingResult');
      localStorage.removeItem('lastSessionId');
      // 清除推荐相关的缓存
      localStorage.removeItem('spreadRecommendation');
      localStorage.removeItem('recommendationQuestion');
      
      // 使用自定义navigate，它会自动处理语言参数
      navigate(pendingPath, { replace: true });
    }
    setPendingPath(null);
  };

  // 取消导航
  const handleCancelNavigation = () => {
    setShowConfirmNav(false);
    setPendingPath(null);
  };

  // 检查是否有完成的阅读结果
  const readingResultExists = localStorage.getItem('readingResult') !== null;

  // 获取确认消息
  const getConfirmMessage = () => {
    if (readingResultExists) {
      return t('reading.confirm_exit'); // "占卜结果已保存，是否退出占卜？"
    }
    return t('reading.confirm_exit_progress'); // "退出将丢失进度，是否确认退出"
  };

  return (
    <ConfirmDialog
      isOpen={showConfirmNav}
      message={getConfirmMessage()}
      onConfirm={handleConfirmNavigation}
      onCancel={handleCancelNavigation}
    />
  );
};

export default NavbarWrapper; 