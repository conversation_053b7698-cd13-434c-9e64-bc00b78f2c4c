import React, { useState, useRef, useEffect } from 'react';
import {useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import ConfirmDialog from './ConfirmDialog';
import UserAuth from './UserAuth';
import ThemeToggle from './ThemeToggle';
import { availableLanguages } from '../i18n';
import { useDropdown } from '../contexts/DropdownContext';
import { getFontClass } from '../utils/fontUtils';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import LanguageLink from './LanguageLink';
import MobileMenu from './MobileMenu';

// 定义DropdownType类型，与DropdownContext中保持一致
type DropdownType = 'language' | 'user' | 'mobile' | 'catalog' | 'fortune' | 'blog' | 'blog-mobile' | 'mobile-blog' | null;

interface NavbarProps {
  className?: string;
}

const Navbar: React.FC<NavbarProps> = ({ className = '' }) => {
  const location = useLocation();
  const { navigate, changeLanguage } = useLanguageNavigate();
  const { t, i18n } = useTranslation();
  const [showConfirm, setShowConfirm] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);
  const { openDropdown, setOpenDropdown, registerDropdownRef } = useDropdown();
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const mobileButtonRef = useRef<HTMLButtonElement>(null);
  const blogButtonRef = useRef<HTMLDivElement>(null);
  const blogDropdownRef = useRef<HTMLDivElement>(null);
  const [_, setForceUpdate] = useState(0);
  
  // 移动端菜单状态
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  // Register refs with context
  useEffect(() => {
    registerDropdownRef('language', buttonRef, dropdownRef);
    registerDropdownRef('blog', blogButtonRef, blogDropdownRef);
  }, [registerDropdownRef]);

  useEffect(() => {
    setForceUpdate(prev => prev + 1);
  }, [i18n.language]);

  // 博客分类
  const blogCategories = [
    { path: '/horoscope', label: t('blog.categories.horoscope', '星座运势') },
    { path: '/zodiac-traits', label: t('blog.categories.zodiac_traits', '星座特质') },
    { path: '/tarot-guide', label: t('blog.tarot_guide.heading', '塔罗指南') },
    { path: '/general-divination', label: t('blog.general_divination.heading', '大众占卜') }
  ];

  const links = [
    { path: '/home', label: t('nav.home') },
    { path: '/daily-fortune', label: t('nav.daily_fortune', '每日运势') },
    { path: '/yes-no-tarot', label: t('nav.yes_no_tarot', 'Yes/No 塔罗') },
    { path: '/blog', label: t('nav.blog', '博客'), hasDropdown: true },
    { path: '/membership', label: t('nav.membership', '会员订阅') }
  ];

  const isInReadingFlow = location.pathname.startsWith('/reading/') || // 在阅读流程中
                         location.pathname === '/tarot-result'; // 或在结果页面
  const isReadingComplete = location.pathname === '/tarot-result' && // 必须在结果页面
    (localStorage.getItem('readingResult') !== null || // 已经有解读结果
    localStorage.getItem('tarotDialogState') !== null); // 或者有对话状态

  const getConfirmMessage = () => {
    if (isReadingComplete) {
      return t('reading.confirm_exit');
    }
    return t('reading.confirm_exit_progress');
  };

  const handleLogoClick = (e: React.MouseEvent) => {
    if (isInReadingFlow) {
      e.preventDefault();
      setPendingNavigation('/');
      setShowConfirm(true);
    } else {
      e.preventDefault();
      
      // 使用自定义导航钩子
      navigate('/');
    }
  };

  const handleConfirm = () => {
    setShowConfirm(false);
    if (pendingNavigation) {
      // 清除所有相关的缓存
      localStorage.removeItem('selectedReader');
      localStorage.removeItem('selectedSpread');
      localStorage.removeItem('userQuestion');
      localStorage.removeItem('tarotReadingState');
      localStorage.removeItem('tarotDialogState');
      localStorage.removeItem('selectedCards');
      localStorage.removeItem('sessionId');
      localStorage.removeItem('readingResult');
      // 清除推荐相关的缓存
      localStorage.removeItem('spreadRecommendation');
      localStorage.removeItem('recommendationQuestion');
      
      // 使用自定义导航钩子
      navigate(pendingNavigation, { replace: true });
    }
    setPendingNavigation(null);
  };

  const handleCancel = () => {
    setShowConfirm(false);
    setPendingNavigation(null);
  };

  const handleNavClick = (e: React.MouseEvent, path: string, hasDropdown?: boolean) => {
    e.preventDefault();
    
    if (path === '#' || hasDropdown) {
      return;
    }
    
    if (isInReadingFlow) {
      setPendingNavigation(path);
      setShowConfirm(true);
    } else {
      // 使用自定义导航钩子
      navigate(path);
      // 关闭所有下拉菜单
      setOpenDropdown(null);
    }
  };

  // 处理移动端导航确认
  const handleMobileNavConfirm = (path: string) => {
    setPendingNavigation(path);
    setShowConfirm(true);
    setIsMobileMenuOpen(false);
  };

  const getIsActive = (path: string) => {
    const pathWithLang = `/${i18n.language}${path === '/home' ? '/home' : path}`;
    const currentPath = location.pathname;
    
    if (path === '/home') {
      if (currentPath === '/') {
        return false;
      }
      return currentPath === '/home' || currentPath.match(/^\/[^\/]+\/home$/);
    }
    
    if (path === '/') {
      return location.pathname === '/' || location.pathname.startsWith('/reading');
    }
    
    if (path === '/membership') {
      return (
        currentPath === '/membership' || 
        currentPath === pathWithLang || 
        currentPath === '/membership-inter' || 
        currentPath === `/${i18n.language}/membership-inter`
      );
    }
    
    return currentPath === path || currentPath === pathWithLang;
  };

  const handleLanguageChange = (lang: string) => {
    // 所有语言都使用changeLanguage方法，包括繁体中文
    // 移除繁体中文的特殊处理，使用统一的方法
    changeLanguage(lang);
    setOpenDropdown(null);
    
    const landingFrame = document.querySelector('iframe[src^="/landing"]');
    if (landingFrame) {
      (landingFrame as HTMLIFrameElement).contentWindow?.postMessage({
        type: 'LANGUAGE_CHANGE',
        language: lang
      }, '*');
    }
  };

  // 保留所有语言选项，包括繁体中文
  const displayLanguages = availableLanguages;

  // 语言原生名称映射
  const languageNativeNames = {
    'zh-CN': '简体中文',
    'zh-TW': '正體中文',
    'en': 'English',
    'ja': '日本語'
  };

  return (
    <>
      <nav 
        className={`fixed top-0 left-0 right-0 z-50 shadow-md dark:bg-black/80 bg-white/90 backdrop-blur-md ${className}`}
        onMouseLeave={() => setOpenDropdown(null)}
      >
        <div className="w-full px-4 sm:px-8">
          <div className="flex items-center justify-between h-14 relative">
            <div className="flex items-center gap-1">
              <button
                ref={mobileButtonRef}
                className="lg:hidden p-2 -ml-2 dark:text-gray-100 text-gray-800 dark:hover:text-white hover:text-black focus:outline-none transition-all duration-300"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                aria-label="Toggle mobile menu"
              >
                <div className="relative w-6 h-6 flex items-center justify-center">
                  {/* 汉堡菜单第一条线 */}
                  <span 
                    className={`absolute w-6 h-[2px] bg-current transform transition-all duration-300 ease-in-out ${
                      isMobileMenuOpen 
                        ? 'rotate-45' 
                        : '-translate-y-1'
                    }`}
                  ></span>
                  
                  {/* 汉堡菜单第二条线 */}
                  <span 
                    className={`absolute w-6 h-[2px] bg-current transform transition-all duration-300 ease-in-out ${
                      isMobileMenuOpen 
                        ? '-rotate-45' 
                        : 'translate-y-1'
                    }`}
                  ></span>
                </div>
              </button>

              <LanguageLink 
                to="/"
                onClick={handleLogoClick}
                className="flex items-center gap-1.5 sm:gap-2"
              >
                <img 
                  src="https://cdn.tarotqa.com/images-optimized/favicon_small.webp" 
                  alt="TarotQA Logo" 
                  className="h-6 sm:h-7 w-auto object-contain"
                />
                <div className="flex flex-col items-start leading-none">
                  <span 
                    className="text-lg sm:text-2xl font-black tracking-wide"
                    style={{ fontFamily: "var(--font-zh-CN)", color: "#C36DFC" }}
                  >
                    TarotQA
                  </span>
                </div>
              </LanguageLink>
            </div>

            <div className="hidden lg:flex absolute left-1/2 -translate-x-1/2 items-center space-x-2 lg:space-x-4 xl:space-x-8">
              {links.map(({ path, label, hasDropdown }) => (
                <div key={path} className="relative">
                  {hasDropdown ? (
                    <div 
                      ref={blogButtonRef}
                      className="relative px-2 lg:px-3 py-2 cursor-pointer"
                      onClick={() => setOpenDropdown(openDropdown === 'blog' as DropdownType ? null : 'blog' as DropdownType)}
                      onMouseEnter={() => setOpenDropdown('blog' as DropdownType)}
                      onMouseLeave={() => setOpenDropdown(null)}
                    >
                      <span className={`relative text-sm lg:text-sm xl:text-base 2xl:text-lg transition-colors whitespace-nowrap
                                ${i18n.language === 'en' ? 'font-bold tracking-wide' : 'font-extrabold'}
                                ${getFontClass(i18n.language)}
                                ${getIsActive(path)
                                  ? 'dark:text-white text-black font-black'
                                  : 'dark:text-gray-100 text-gray-800 dark:hover:text-white hover:text-black'
                                }`}
                      >
                        {label}
                        <span className="ml-1 inline-block">▾</span>
                      </span>
                      {getIsActive(path) && (
                        <motion.div
                          layoutId="navbar-indicator"
                          className="absolute -bottom-[1px] left-0 right-0 h-[2px] bg-gradient-to-r from-purple-500 to-pink-500"
                          initial={false}
                          transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        />
                      )}
                    </div>
                  ) : (
                    <LanguageLink
                      to={path}
                      onClick={(e) => handleNavClick(e, path)}
                      className={`relative px-2 lg:px-3 py-2 text-sm lg:text-sm xl:text-base 2xl:text-lg transition-colors whitespace-nowrap
                                ${i18n.language === 'en' ? 'font-bold tracking-wide' : 'font-extrabold'}
                                ${getFontClass(i18n.language)}
                                ${getIsActive(path)
                                  ? 'dark:text-white text-black font-black'
                                  : 'dark:text-gray-100 text-gray-800 dark:hover:text-white hover:text-black'
                                }`}
                    >
                      {getIsActive(path) && (
                        <motion.div
                          layoutId="navbar-indicator"
                          className="absolute -bottom-[1px] left-0 right-0 h-[2px] bg-gradient-to-r from-purple-500 to-pink-500"
                          initial={false}
                          transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        />
                      )}
                      <span className={`${i18n.language === 'en' ? 'tracking-wide' : ''} ${getFontClass(i18n.language)} whitespace-nowrap`}>
                        {label}
                      </span>
                    </LanguageLink>
                  )}
                  
                  {/* 博客下拉菜单 */}
                  {hasDropdown && (
                    <AnimatePresence>
                      {openDropdown === 'blog' as DropdownType && (
                        <motion.div
                          ref={blogDropdownRef}
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{ duration: 0.2 }}
                          className="absolute top-full left-0 mt-1 w-56 rounded-xl shadow-xl dark:shadow-purple-500/20 shadow-purple-500/10 dark:bg-black bg-white z-50 border border-purple-500/30 overflow-hidden"
                          onMouseEnter={() => setOpenDropdown('blog' as DropdownType)}
                        >
                          <div className="relative py-2">
                            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 pointer-events-none rounded-xl" />
                            <div className="absolute -top-10 -right-10 w-24 h-24 bg-purple-500/10 rounded-full blur-xl pointer-events-none"></div>
                            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-pink-500/10 rounded-full blur-xl pointer-events-none"></div>
                            
                            <div className="relative space-y-0 px-2">
                              {blogCategories.map((category) => (
                                <React.Fragment key={category.path}>
                                  <LanguageLink
                                    to={category.path}
                                    onClick={(e) => handleNavClick(e, category.path)}
                                    className={`block px-4 py-3 rounded-lg text-base xl:text-lg transition-all duration-300 text-center blog-dropdown-item
                                             font-medium
                                             ${i18n.language === 'en' ? 'tracking-wide' : ''}
                                             ${getFontClass(i18n.language)}
                                             dark:text-gray-100 text-gray-800 
                                             dark:hover:text-white hover:text-black hover:font-bold`}
                                  >
                                    {category.label}
                                  </LanguageLink>
                                </React.Fragment>
                              ))}
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
              ))}
            </div>

            <div className="flex items-center space-x-4 py-[3px]">
              {/* 语言切换按钮和下拉菜单 */}
              <div className="relative flex flex-col items-center">
                <button
                  ref={buttonRef}
                  onClick={() => setOpenDropdown(openDropdown === 'language' ? null : 'language')}
                  onMouseEnter={() => setOpenDropdown('language')}
                  className="flex items-center justify-center w-8 h-8 rounded-full
                           dark:text-gray-100 text-gray-800 dark:hover:text-white hover:text-black 
                           hover:bg-purple-500/10
                           transition-all duration-200"
                >
                  <div className="relative w-5 h-5 flex items-center justify-center">
                    <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                    </svg>
                  </div>
                </button>
                
                {/* 下拉菜单使用固定宽度并右对齐 */}
                <AnimatePresence>
                  {openDropdown === 'language' && (
                    <>
                      {/* PC端下拉菜单 */}
                      <motion.div
                        ref={dropdownRef}
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        transition={{ duration: 0.2 }}
                        className="hidden lg:block absolute top-full mt-1 w-40 rounded-xl shadow-xl dark:shadow-purple-500/20 shadow-purple-500/10 
                                 dark:bg-black bg-white z-50 border border-purple-500/30 overflow-hidden"
                        onMouseEnter={() => setOpenDropdown('language')}
                      >
                        <div className="relative py-2">
                          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 pointer-events-none rounded-xl" />
                          <div className="absolute -top-10 -right-10 w-24 h-24 bg-purple-500/10 rounded-full blur-xl pointer-events-none"></div>
                          <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-pink-500/10 rounded-full blur-xl pointer-events-none"></div>
                          
                          <div className="relative space-y-0 px-1">
                            {displayLanguages.map((lang) => (
                              <button
                                key={lang}
                                onClick={() => handleLanguageChange(lang)}
                                className="block w-full px-2 py-3 rounded-lg text-base xl:text-lg transition-all duration-300 text-center blog-dropdown-item
                                         font-medium
                                         dark:text-gray-100 text-gray-800
                                         dark:hover:text-white hover:text-black hover:font-bold"
                              >
                                {languageNativeNames[lang as keyof typeof languageNativeNames]}
                              </button>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                      
                      {/* 移动端下拉菜单 - 保持全宽 */}
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        transition={{ duration: 0.2 }}
                        className="lg:hidden fixed left-0 right-0 top-14 
                                 dark:bg-black bg-white backdrop-blur-xl shadow-lg z-[999]
                                 border-t border-purple-500/20"
                      >
                        <div className="relative">
                          <div className="absolute inset-0 bg-gradient-to-b from-purple-500/5 to-pink-500/5 pointer-events-none" />
                          
                          <div className="relative px-2 py-3 space-y-1.5">
                            {displayLanguages.map((lang) => (
                              <button
                                key={lang}
                                onClick={() => handleLanguageChange(lang)}
                                className="flex items-center justify-center w-full px-3 py-2.5 rounded-xl text-base font-medium
                                         transition-all duration-200 border border-purple-500/20
                                         dark:text-gray-100 text-gray-800 dark:hover:text-white hover:text-black hover:bg-purple-500/10 hover:border-purple-500/30
                                         whitespace-nowrap"
                              >
                                <span className={`${i18n.language === 'en' ? 'tracking-wide' : ''} ${getFontClass(i18n.language)}`}>
                                  {languageNativeNames[lang as keyof typeof languageNativeNames]}
                                </span>
                              </button>
                            ))}
                          </div>

                          <div className="absolute -bottom-6 left-0 right-0 h-6 bg-gradient-to-b dark:from-black/20 from-white/20 to-transparent pointer-events-none" />
                        </div>
                      </motion.div>
                    </>
                  )}
                </AnimatePresence>
              </div>
              <ThemeToggle />
              <UserAuth />
            </div>
          </div>
        </div>
      </nav>

      {/* 使用新的MobileMenu组件 */}
      <MobileMenu 
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        isInReadingFlow={isInReadingFlow}
        onConfirmNavigation={handleMobileNavConfirm}
      />

      <ConfirmDialog
        isOpen={showConfirm}
        message={getConfirmMessage()}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />
    </>
  );
};

export default Navbar;