import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Footer from '../Footer';
import ConfirmDialog from '../ConfirmDialog';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';

const FooterWithNavigation: React.FC = () => {
  const { t } = useTranslation();
  const [showConfirmNav, setShowConfirmNav] = useState(false);
  const [pendingPath, setPendingPath] = useState<string>('');
  const { navigate } = useLanguageNavigate();

  const handleNavigation = (e: React.MouseEvent<HTMLAnchorElement>, path: string) => {
    // 始终阻止默认导航行为，并显示确认对话框
    e.preventDefault();
    // 使用原始路径，useLanguageNavigate会自动处理语言参数
    setPendingPath(path);
    setShowConfirmNav(true);
  };

  const handleConfirmNavigation = () => {
    setShowConfirmNav(false);
    navigate(pendingPath);
  };

  const handleCancelNavigation = () => {
    setShowConfirmNav(false);
    setPendingPath('');
  };

  return (
    <>
      <div className="w-full mt-8">
        <Footer onNavigate={handleNavigation} />
      </div>
      
      <ConfirmDialog
        isOpen={showConfirmNav}
        message={localStorage.getItem('apiCallInProgress') === 'true' ? t('reading.confirm_exit_progress') : t('reading.confirm_exit')}
        onConfirm={handleConfirmNavigation}
        onCancel={handleCancelNavigation}
      />
    </>
  );
};

export default FooterWithNavigation; 