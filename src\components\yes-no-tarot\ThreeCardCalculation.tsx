import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

const ThreeCardCalculation: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  
  // 定义不同的计算结果类型
  const resultTypes = [
    {
      title: t('yes_no_tarot.three_card_how_it_works.definite_yes_title', 'Definite YES (All Three Cards Positive)'),
      description: t('yes_no_tarot.three_card_how_it_works.definite_yes_description', 'When all three cards carry positive energy, the universe strongly supports your path forward. This is your green light to proceed with confidence.'),
      color: 'bg-green-500',
      textColor: theme === 'light' ? 'text-green-700' : 'text-green-400'
    },
    {
      title: t('yes_no_tarot.three_card_how_it_works.likely_yes_title', 'Likely YES (Two Positive, One Negative)'),
      description: t('yes_no_tarot.three_card_how_it_works.likely_yes_description', 'Two supportive cards outweigh one challenging card. Success is probable, but requires effort and patience to overcome obstacles.'),
      color: 'bg-emerald-400',
      textColor: theme === 'light' ? 'text-emerald-700' : 'text-emerald-400'
    },
    {
      title: t('yes_no_tarot.three_card_how_it_works.conditional_yes_title', 'Conditional YES (Mixed Energies)'),
      description: t('yes_no_tarot.three_card_how_it_works.conditional_yes_description', 'Equal positive and negative forces suggest your outcome depends entirely on your actions and choices moving forward.'),
      color: 'bg-yellow-400',
      textColor: theme === 'light' ? 'text-yellow-700' : 'text-yellow-400'
    },
    {
      title: t('yes_no_tarot.three_card_how_it_works.likely_no_title', 'Likely NO (Two Negative, One Positive)'),
      description: t('yes_no_tarot.three_card_how_it_works.likely_no_description', 'Significant obstacles outweigh positive influences. Consider alternative approaches or timing.'),
      color: 'bg-orange-400',
      textColor: theme === 'light' ? 'text-orange-700' : 'text-orange-400'
    },
    {
      title: t('yes_no_tarot.three_card_how_it_works.definite_no_title', 'Definite NO (All Three Cards Negative)'),
      description: t('yes_no_tarot.three_card_how_it_works.definite_no_description', 'Strong universal resistance to your current path. The cards advise patience and reconsideration.'),
      color: 'bg-red-500',
      textColor: theme === 'light' ? 'text-red-700' : 'text-red-400'
    }
  ];

  return (
    <div className="mt-24 sm:mt-32">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('yes_no_tarot.three_card_how_it_works.title', 'How We Calculate Your Three Card Answer')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
      </div>
      
      {/* 计算方法内容 */}
      <div className="max-w-4xl mx-auto">
        <div className="space-y-6">
          {resultTypes.map((result, index) => (
            <div 
              key={index} 
              className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 rounded-xl overflow-hidden shadow-md transition-all duration-300 hover:border-purple-500/40"
            >
              <div className="flex flex-col sm:flex-row">
                {/* 左侧彩色指示条 */}
                <div className={`w-full sm:w-2 h-2 sm:h-auto ${result.color}`}></div>
                
                {/* 内容区域 */}
                <div className="p-5 flex-1">
                  <h3 className={`text-lg sm:text-xl font-semibold mb-2 ${result.textColor}`}>
                    {result.title}
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    {result.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ThreeCardCalculation; 