import YesNoTarotZhCN from './yes-no-tarot-zh-CN';
import YesNoTarotZhTW from './yes-no-tarot-zh-TW';
import DoesHeLikeMeZhTW from './does-he-like-me-zh-TW';
import DoesHeLikeMeZhCN from './does-he-like-me-zh-CN';
import DoesHeLikeMeEn from './does-he-like-me-en';
import DoesHeLikeMeJa from './does-he-like-me-ja';
import YesNoTarotEn from './yes-no-tarot-en';
import YesNoTarotJa from './yes-no-tarot-ja';
import PersonalityTestZhCN from './personality-test-zh-CN';
import PersonalityTestZhTW from './personality-test-zh-TW';
import PersonalityTestEn from './personality-test-en';
import PersonalityTestJa from './personality-test-ja';
import FoldingWaistTestZhTW from './folding-waist-test-zh-TW';
import FoldingWaistTestZhCN from './folding-waist-test-zh-CN';
import FoldingWaistTestEn from './folding-waist-test-en';
import FoldingWaistTestJa from './folding-waist-test-ja';
import CompleteTarotGuideEn from './complete-tarot-guide-en';
import CompleteTarotGuideZhCN from './complete-tarot-guide-zh-CN';
import CompleteTarotGuideZhTW from './complete-tarot-guide-zh-TW';
import CompleteTarotGuideJa from './complete-tarot-guide-ja';
import CompleteLoveTarotGuideZhTW from './complete-love-tarot-guide-zh-TW';
import CompleteLoveTarotGuideEn from './complete-love-tarot-guide-en';
import CompleteLoveTarotGuideJa from './complete-love-tarot-guide-ja';
import CompleteLoveTarotGuideZhCN from './complete-love-tarot-guide-zh-CN';
import DailyFortuneTheStarEn from './daily-fortune-the-star-en';
import DailyFortuneTheStarJa from './daily-fortune-the-star-ja';
import DailyFortuneTheStarZhCN from './daily-fortune-the-star-zh-CN';
import DailyFortuneTheStarZhTW from './daily-fortune-the-star-zh-TW';
import YesOrNoTarotGuideEn from './yes-or-no-tarot-guide-en';
import YesOrNoTarotGuideJa from './yes-or-no-tarot-guide-ja';
import YesOrNoTarotGuideZhCN from './yes-or-no-tarot-guide-zh-CN';
import YesOrNoTarotGuideZhTW from './yes-or-no-tarot-guide-zh-TW';
import PowerfulTarotSpreadsEn from './5-powerful-tarot-spreads-en';
import PowerfulTarotSpreadsJa from './5-powerful-tarot-spreads-ja';
import PowerfulTarotSpreadsZhCN from './5-powerful-tarot-spreads-zh-CN';
import PowerfulTarotSpreadsZhTW from './5-powerful-tarot-spreads-zh-TW';
import DreamSchoolEn from './can-you-get-into-your-dream-school-en';
import DreamSchoolJa from './can-you-get-into-your-dream-school-ja';
import DreamSchoolZhCN from './can-you-get-into-your-dream-school-zh-CN';
import DreamSchoolZhTW from './can-you-get-into-your-dream-school-zh-TW';
import WhatIsARisingSignZhTW from './what-is-a-rising-sign-zh-TW';
import WhatIsARisingSignEn from './what-is-a-rising-sign-en';
import WhatIsARisingSignJa from './what-is-a-rising-sign-ja';
import WhatIsARisingSignZhCN from './what-is-a-rising-sign-zh-CN';
import LeoManLikesYouZhTW from './the-ultimate-signs-a-leo-man-likes-you-zh-TW';
import LeoManLikesYouEn from './the-ultimate-signs-a-leo-man-likes-you-en';
import LeoManLikesYouJa from './the-ultimate-signs-a-leo-man-likes-you-ja';
import LeoManLikesYouZhCN from './the-ultimate-signs-a-leo-man-likes-you-zh-CN';
import AriesManSignsOfLoveEn from './aries-mans-signs-of-love-en';
import AriesManSignsOfLoveJa from './aries-mans-signs-of-love-ja';
import AriesManSignsOfLoveZhCN from './aries-mans-signs-of-love-zh-CN';
import AriesManSignsOfLoveZhTW from './aries-mans-signs-of-love-zh-TW';
import AquariusManLoveArticleEn from './aquarius-man-love-article-en';
import AquariusManLoveArticleJa from './aquarius-man-love-article-ja';
import AquariusManLoveArticleZhCN from './aquarius-man-love-article-zh-CN';
import AquariusManLoveArticleZhTW from './aquarius-man-love-article-zh-TW';
import GeminiMenLoveSecretsEn from './gemini-men-love-secrets-en';
import GeminiMenLoveSecretsZhCN from './gemini-men-love-secrets-zh-CN';
import GeminiMenLoveSecretsJa from './gemini-men-love-secrets-ja';
import GeminiMenLoveSecretsZhTW from './gemini-men-love-secrets-zh-TW';
import CapricornManLoveEn from './capricorn-man-love-en';
import CapricornManLoveZhCN from './capricorn-man-love-zh-CN';
import CapricornManLoveZhTW from './capricorn-man-love-zh-TW';
import CapricornManLoveJa from './capricorn-man-love-ja';
import ScorpioManLoveArticleZhCN from './scorpio-man-love-article-zh-CN';
import ScorpioManLoveArticleEn from './scorpio-man-love-article-en';
import ScorpioManLoveArticleJa from './scorpio-man-love-article-ja';
import ScorpioManLoveArticleZhTW from './scorpio-man-love-article-zh-TW';
// // 导入示例文章（新URL格式）
// import ExampleZodiacTraitsZhCN from './example-zodiac-traits-zh-CN';
// import ExampleTarotGuideZhCN from './example-tarot-guide-zh-CN';
// import ExampleGeneralDivinationZhCN from './example-general-divination-zh-CN';
import { BlogPost } from '../types';

// 按日期排序的辅助函数（倒序：最新的在前面）
const sortBlogsByDateDesc = (blogs: BlogPost[]): BlogPost[] => {
  return [...blogs].sort((a, b) => {
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return dateB.getTime() - dateA.getTime(); // 倒序排列
  });
};

// 博客文章集合，按语言分组，并按日期倒序排列
export const blogPostsByLang: { [key: string]: BlogPost[] } = {
  'zh-CN': sortBlogsByDateDesc([YesNoTarotZhCN, DoesHeLikeMeZhCN, PersonalityTestZhCN, FoldingWaistTestZhCN, CompleteTarotGuideZhCN, CompleteLoveTarotGuideZhCN, DailyFortuneTheStarZhCN, YesOrNoTarotGuideZhCN, PowerfulTarotSpreadsZhCN, DreamSchoolZhCN, WhatIsARisingSignZhCN, LeoManLikesYouZhCN, AriesManSignsOfLoveZhCN, AquariusManLoveArticleZhCN, GeminiMenLoveSecretsZhCN, CapricornManLoveZhCN, ScorpioManLoveArticleZhCN]),
  'zh-TW': sortBlogsByDateDesc([YesNoTarotZhTW, DoesHeLikeMeZhTW, PersonalityTestZhTW, FoldingWaistTestZhTW, CompleteTarotGuideZhTW, CompleteLoveTarotGuideZhTW, DailyFortuneTheStarZhTW, YesOrNoTarotGuideZhTW, PowerfulTarotSpreadsZhTW, DreamSchoolZhTW, WhatIsARisingSignZhTW, LeoManLikesYouZhTW, AriesManSignsOfLoveZhTW, AquariusManLoveArticleZhTW, GeminiMenLoveSecretsZhTW, CapricornManLoveZhTW, ScorpioManLoveArticleZhTW]),
  'en': sortBlogsByDateDesc([YesNoTarotEn, DoesHeLikeMeEn, PersonalityTestEn, FoldingWaistTestEn, CompleteTarotGuideEn, CompleteLoveTarotGuideEn, DailyFortuneTheStarEn, YesOrNoTarotGuideEn, PowerfulTarotSpreadsEn, DreamSchoolEn, WhatIsARisingSignEn, LeoManLikesYouEn, AriesManSignsOfLoveEn, AquariusManLoveArticleEn, GeminiMenLoveSecretsEn, CapricornManLoveEn, ScorpioManLoveArticleEn]),
  'ja': sortBlogsByDateDesc([YesNoTarotJa, DoesHeLikeMeJa, PersonalityTestJa, FoldingWaistTestJa, CompleteTarotGuideJa, CompleteLoveTarotGuideJa, DailyFortuneTheStarJa, YesOrNoTarotGuideJa, PowerfulTarotSpreadsJa, DreamSchoolJa, WhatIsARisingSignJa, LeoManLikesYouJa, AriesManSignsOfLoveJa, AquariusManLoveArticleJa, GeminiMenLoveSecretsJa, CapricornManLoveJa, ScorpioManLoveArticleJa]),
  'default': sortBlogsByDateDesc([YesNoTarotEn, DoesHeLikeMeEn, PersonalityTestEn, CompleteTarotGuideEn, CompleteLoveTarotGuideEn, DailyFortuneTheStarEn, YesOrNoTarotGuideEn, PowerfulTarotSpreadsEn, DreamSchoolEn, WhatIsARisingSignEn, LeoManLikesYouEn, AriesManSignsOfLoveEn, AquariusManLoveArticleEn, GeminiMenLoveSecretsEn, CapricornManLoveEn, ScorpioManLoveArticleEn]), // 使用英文版本作为默认
};

// 获取所有博客文章的扁平列表（也按日期倒序排列）
export const allBlogPosts: BlogPost[] = sortBlogsByDateDesc([...Object.values(blogPostsByLang).flat()]);

// 导出默认博客列表
export default allBlogPosts;

// 如果需要单独导出某些博客组件，可以在这里添加，但目前不需要重复导出
// 因为它们已经包含在blogPostsByLang和allBlogPosts中了