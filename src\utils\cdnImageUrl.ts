/**
 * CDN资源URL处理工具
 * 在开发环境下直接使用相对路径
 * 在生产环境下添加CDN前缀
 */

// CDN基础URL
const CDN_BASE_URL = 'https://cdn.tarotqa.com';

/**
 * 处理资源URL，根据环境返回适当的URL
 * 开发环境：直接使用相对路径
 * 生产环境：添加CDN前缀
 * 
 * @param url 原始URL，相对路径或完整URL
 * @returns 处理后的URL
 */
export const getResourceUrl = (url: string): string => {
  // 如果URL为空或不是字符串，直接返回
  if (!url || typeof url !== 'string') {
    return url;
  }
  
  // 检查是否为开发环境
  const isDevelopment = import.meta.env.MODE === 'development';
  
  // 如果URL已经是完整的URL（以http开头），则不做处理
  if (url.startsWith('http')) {
    return url;
  }
  
  // 确保路径以/开头
  const normalizedPath = url.startsWith('/') ? url : `/${url}`;
  
  // 开发环境直接返回相对路径
  if (isDevelopment) {
    return normalizedPath;
  }
  
  // 生产环境添加CDN前缀
  // 移除开头的/
  const cleanPath = normalizedPath.substring(1);
  return `${CDN_BASE_URL}/${cleanPath}`;
};

/**
 * 将任何URL转换为CDN URL格式
 * 
 * @param url 原始URL
 * @returns CDN格式的URL
 */
export const toCdnUrl = (url: string): string => {
  // 如果URL为空或不是字符串，直接返回
  if (!url || typeof url !== 'string') {
    return url;
  }
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http')) {
    return url;
  }
  
  // 确保路径没有开头的/
  const cleanPath = url.startsWith('/') ? url.substring(1) : url;
  return `${CDN_BASE_URL}/${cleanPath}`;
};

// 为了保持向后兼容，保留getImageUrl函数
export const getImageUrl = getResourceUrl; 