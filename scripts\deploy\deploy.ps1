# 参数定义

param(
    [Parameter(Mandatory=$true)][string]$version,
    [Parameter(Mandatory=$true)][string]$server_ip,
    [Parameter(Mandatory=$true)][string]$ssh_key_path,
    [Parameter(Mandatory=$false)][switch]$use_local_version,
    [Parameter(Mandatory=$false)][string]$local_version,
    [Parameter(Mandatory=$false)][switch]$skip_cdn_refresh,
    [Parameter(Mandatory=$false)][switch]$force_cdn_refresh_all
)

# 检查SSH密钥文件

if (-not (Test-Path $ssh_key_path)) {
    Write-Host "Error: SSH key file not found: $ssh_key_path"
    Write-Host "Usage: ./deploy.ps1 -version v1.0.0 -server_ip ************** -ssh_key_path C:\Users\<USER>\.ssh\tarot.pem [-use_local_version] [-local_version v0.9.0] [-skip_cdn_refresh] [-force_cdn_refresh_all]"
    exit 1
}

# 检查版本号格式

if ($version -notmatch '^v\d+\.\d+\.\d+$') {
    Write-Host "Error: Version must be in format v1.0.0"
    exit 1
}

# 设置部署文件存储目录

$deploysStorageDir = "deploys"
if (-not (Test-Path $deploysStorageDir)) {
    New-Item -ItemType Directory -Path $deploysStorageDir | Out-Null
}

# 创建临时部署目录

$deployDir = "deploy_${version}"
$deployTar = Join-Path $deploysStorageDir "deploy_${version}.tar"

if ($use_local_version) {

    # 如果指定了local_version，使用指定的版本

    $sourceVersion = if ($local_version) { $local_version } else { $version }
    $sourceDeployDir = "deploy_${sourceVersion}"
    $sourceDeployTar = "${sourceDeployDir}.tar"
    $localDeployTar = Join-Path $deploysStorageDir $sourceDeployTar

    if (-not (Test-Path $localDeployTar)) {
        Write-Host "Error: Local version $sourceVersion not found at: $localDeployTar"
        Write-Host "Available versions:"
        Get-ChildItem -Path $deploysStorageDir -Filter "deploy_v*.tar" | ForEach-Object {
            $versionMatch = $_.Name -match 'deploy_(v\d+\.\d+\.\d+)\.tar'
            if ($versionMatch) {
                Write-Host "  $($matches[1])"
            }
        }
        exit 1
    }
    Write-Host "Using local version: $sourceVersion"
    

    # 避免相同文件的复制

    if ($localDeployTar -ne $deployTar) {
        Copy-Item $localDeployTar $deployTar
    }
} else {
    if (Test-Path $deployDir) {
        Remove-Item -Recurse -Force $deployDir
    }
    New-Item -ItemType Directory -Path $deployDir | Out-Null

    # 构建前端

    Write-Host "Building frontend..."
    npm run build

    # 创建目标目录结构

    Write-Host "Creating directory structure..."
    $distDir = Join-Path $deployDir "dist"
    $serverDir = Join-Path $deployDir "server"
    New-Item -ItemType Directory -Path $distDir | Out-Null
    New-Item -ItemType Directory -Path $serverDir | Out-Null

    # 复制文件

    Write-Host "Copying files..."
    Get-ChildItem -Path "dist" | ForEach-Object {
        if ($_.PSIsContainer) {
            Copy-Item -Path $_.FullName -Destination (Join-Path $distDir $_.Name) -Recurse
        } else {
            Copy-Item -Path $_.FullName -Destination $distDir
        }
    }

    Get-ChildItem -Path "server" -Exclude "node_modules" | ForEach-Object {
        if ($_.PSIsContainer) {
            Copy-Item -Path $_.FullName -Destination (Join-Path $serverDir $_.Name) -Recurse
        } else {
            Copy-Item -Path $_.FullName -Destination $serverDir
        }
    }

    # 创建版本信息文件

    $versionInfo = @{
        version = $version
        deployTime = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        gitCommit = (git rev-parse HEAD)
    } | ConvertTo-Json
    $versionInfo | Out-File -FilePath (Join-Path $deployDir "version.json") -Encoding UTF8

    # 压缩部署目录

    Write-Host "Compressing files..."
    tar -czf $deployTar $deployDir
}

# 在服务器上创建必要的目录并设置权限

Write-Host "Setting up server directories and permissions..."
$setupCommand = "ssh -i `"$ssh_key_path`" root@${server_ip} 'mkdir -p /var/www/tarot/versions && touch /var/www/tarot/versions/deploy.log && chmod 666 /var/www/tarot/versions/deploy.log && chmod 755 /var/www/tarot/versions'"
Invoke-Expression $setupCommand

# 使用SCP上传到服务器

Write-Host "Uploading to server..."
$scpCommand = "scp -i `"$ssh_key_path`" ${deployTar} root@${server_ip}:/var/www/tarot/versions/"
Invoke-Expression $scpCommand

# 在服务器上设置脚本权限

Write-Host "Setting up permissions..."
$setupCommand = "ssh -i `"$ssh_key_path`" root@${server_ip} 'cd /var/www/tarot && mkdir -p scripts/deploy && chmod +x scripts/deploy/*.sh'"
Invoke-Expression $setupCommand

# 执行部署脚本

Write-Host "Executing deployment script..."
$sshCommand = "ssh -i `"$ssh_key_path`" root@${server_ip} 'cd /var/www/tarot && bash ./scripts/deploy/deploy.sh $version'"
Invoke-Expression $sshCommand

# 清理临时文件

Write-Host "Cleaning up..."
if (Test-Path $deployDir) {
    Remove-Item -Recurse -Force $deployDir
}

Write-Host "Deployment completed successfully!"

# 执行CDN预热

if (-not $skip_cdn_refresh) {
    Write-Host "Starting CDN refresh process..."
    $scriptDir = Split-Path -Parent $PSCommandPath
    $rootDir = Split-Path -Parent (Split-Path -Parent $scriptDir)
    $scriptsDir = Join-Path $rootDir "scripts"
    $cdnRefreshScript = Join-Path $scriptsDir "cdn_refresh.ps1"
    if ($force_cdn_refresh_all) {
        & $cdnRefreshScript -force_refresh_all -auto_execute
    } else {
        & $cdnRefreshScript -auto_execute
    }
    Write-Host "CDN refresh process completed."
} else {
    Write-Host "CDN refresh skipped as requested."
}