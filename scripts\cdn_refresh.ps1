# CDN预热脚本
# 在部署完成后执行CDN预热，只预热新增的URL

param(
    [Parameter(Mandatory=$false)][switch]$force_refresh_all, # 是否强制刷新所有URL
    [Parameter(Mandatory=$false)][switch]$auto_execute # 是否自动执行CDN预热，不询问
)

# 脚本所在目录
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
# 项目根目录
$rootDir = Split-Path -Parent $scriptDir

# 定义文件路径
$urlListPath = Join-Path $scriptDir "urllist.txt"
$tempUrlListPath = Join-Path $scriptDir "urllist.temp.txt"
$newUrlsListPath = Join-Path $scriptDir "urllist.new.txt" # 新增URL的临时文件
$cdnUrlGeneratorPath = Join-Path $scriptDir "generate_cdn_urls.py"
$refreshScriptPath = Join-Path $scriptDir "Refresh.py"

# 阿里云访问凭证
$accessKey = "LTAI5tKfGxYfN1bkAxBo1arR"
$accessKeySecret = "******************************"

# 检查Python是否安装
try {
    python --version | Out-Null
} catch {
    Write-Host "Error: Python is not installed or not in PATH"
    exit 1
}

# 检查是否存在旧的URL列表
$hasOldUrlList = Test-Path $urlListPath
if ($hasOldUrlList) {
    # 如果存在旧的URL列表，读取它
    $oldUrls = Get-Content $urlListPath
    Write-Host "Found existing URL list with $(($oldUrls | Measure-Object).Count) URLs."
}

# 生成新的URL列表到临时文件
Write-Host "Generating new URL list..."
# 切换到项目根目录执行Python脚本
$currentDir = Get-Location
Set-Location $rootDir
try {
    # 生成URL列表但不执行CDN预热，先保存到临时文件
    python $cdnUrlGeneratorPath --output $tempUrlListPath
    if (-not (Test-Path $tempUrlListPath)) {
        Write-Host "URL generation failed."
        Set-Location $currentDir
        exit 1
    }
    $newUrls = Get-Content $tempUrlListPath
    Write-Host "Generated new URL list with $(($newUrls | Measure-Object).Count) URLs."
} finally {
    # 恢复原来的目录
    Set-Location $currentDir
}

# 确定需要刷新的URL
$urlsToRefresh = @()
if ($hasOldUrlList -and -not $force_refresh_all) {
    # 找出新增的URL
    $urlsToRefresh = $newUrls | Where-Object { $oldUrls -notcontains $_ }
    $newUrlCount = $urlsToRefresh.Count
    
    if ($newUrlCount -gt 0) {
        Write-Host "Found $newUrlCount new URLs to refresh."
        
        # 输出新增的URL
        Write-Host "New URLs to refresh:"
        $urlsToRefresh | ForEach-Object { Write-Host "  $_" }
        
        # 将需要刷新的URL写入新的临时文件，使用UTF-8编码不带BOM
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllLines($newUrlsListPath, $urlsToRefresh, $utf8NoBom)
        Write-Host "New URLs saved to $newUrlsListPath"
    } else {
        Write-Host "No new URLs found, nothing to refresh."
        
        # 更新URL列表
        Copy-Item -Path $tempUrlListPath -Destination $urlListPath -Force
        
        # 清理临时文件
        Remove-Item -Path $tempUrlListPath -Force
        exit 0
    }
} else {
    # 第一次运行或强制刷新所有URL
    if ($force_refresh_all) {
        Write-Host "Force refresh all URLs requested."
    } else {
        Write-Host "No existing URL list found, this is the first run."
    }
    
    # 使用所有URL
    $urlsToRefresh = $newUrls
    $newUrlCount = $urlsToRefresh.Count
    Write-Host "Will refresh all $newUrlCount URLs."
    
    # 将所有URL写入新的临时文件，使用UTF-8编码不带BOM
    $utf8NoBom = New-Object System.Text.UTF8Encoding $false
    [System.IO.File]::WriteAllLines($newUrlsListPath, $urlsToRefresh, $utf8NoBom)
    Write-Host "All URLs saved to $newUrlsListPath"
}

# 无论是否执行CDN预热，都更新URL列表
Copy-Item -Path $tempUrlListPath -Destination $urlListPath -Force
Write-Host "URL list updated for future comparison."

# 如果不是自动执行模式，询问用户是否继续
$shouldExecute = $auto_execute
if (-not $auto_execute) {
    $choice = Read-Host "Do you want to execute CDN push operation now? (y/n)"
    $shouldExecute = $choice.ToLower() -eq "y"
    
    if (-not $shouldExecute) {
        Write-Host "CDN push operation cancelled."
    }
}

# 执行CDN预热
if ($shouldExecute -and $newUrlCount -gt 0) {
    Write-Host "Executing CDN refresh for $newUrlCount URLs..."
    # 等待2秒后执行CDN预热
    Write-Host "Waiting for 2 seconds before executing CDN push..."
    Start-Sleep -Seconds 2
    
    # 切换到项目根目录执行Python脚本
    $currentDir = Get-Location
    Set-Location $rootDir
    try {
        # 使用包含新URL的文件进行CDN预热
        python $refreshScriptPath -i $accessKey -k $accessKeySecret -r $newUrlsListPath -t push
        Write-Host "CDN refresh completed."
    } finally {
        # 恢复原来的目录
        Set-Location $currentDir
    }
}

# 清理临时文件
if (Test-Path $tempUrlListPath) {
    Remove-Item -Path $tempUrlListPath -Force
}
if (Test-Path $newUrlsListPath) {
    Remove-Item -Path $newUrlsListPath -Force
} 