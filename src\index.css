/* @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&family=Raleway:wght@300;400;500;600;700&family=Inter:wght@400;500;600&display=swap'); */

@import './styles/theme-override.css';
@import './styles/title-override.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply text-moonlight-500 font-['Raleway'] m-0;
    overflow-y: auto;
    /* 基础滚动优化 */
    -webkit-overflow-scrolling: touch;
  }

  html {
    @apply h-full;
    overflow-y: auto;
    height: 100%;
    position: relative;
  }

  #root {
    @apply h-full;
    overflow-y: auto;
    position: relative;
  }

  /* 全局数字字体设置 */
  input,
  select,
  textarea,
  option,
  button,
  input[type="number"],
  input[type="text"],
  input[type="tel"],
  input[type="email"],
  input[type="password"],
  input[type="date"],
  input[type="time"],
  .numeric,
  time,
  [data-numeric],
  .number,
  .price,
  .count,
  .percentage,
  .date,
  .time,
  .duration,
  .score,
  .rating,
  .statistics,
  [class*="number"],
  [class*="count"],
  [class*="price"],
  [class*="score"],
  [class*="rating"],
  [class*="amount"],
  [class*="quantity"],
  [class*="total"],
  [class*="balance"],
  [class*="duration"],
  [class*="percentage"],
  [class*="statistics"],
  [class*="metric"],
  [class*="value"],
  [class*="digit"],
  [class*="decimal"],
  [class*="integer"],
  [class*="float"] {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
    font-feature-settings: "tnum" on, "lnum" on;
    -webkit-font-feature-settings: "tnum" on, "lnum" on;
    font-variant-numeric: tabular-nums;
  }

  /* 全局滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-black/30;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-purple-300/50 dark:bg-purple-500/50 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-purple-400/70 dark:bg-purple-500/70;
  }

  /* 确保页面始终占满视口高度 */
  #root {
    min-height: 100vh;
  }

  .app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-mystic-900;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gradient-to-b from-cosmic-400/70 to-starlight-400/70 dark:from-cosmic-400 dark:to-starlight-400 rounded-full;
  }

  /* 当页面需要禁用滚动时使用这个类 */
  .no-scroll {
    @apply overflow-hidden;
  }
  
  /* 基础滚动优化 */
  .main-content {
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
    overflow-y: auto;
  }
  
  /* PC端滚动条设置 */
  @media (min-width: 769px) {
    html, body, #root, .app, .main-content {
      overflow-y: auto !important;
      scrollbar-width: auto !important;
    }
    
    ::-webkit-scrollbar {
      display: block !important;
      width: 6px !important;
    }
  }
  
  /* 针对iOS设备的专用修复 */
  @supports (-webkit-touch-callout: none) {
    @media (max-width: 768px) {
      html, body, #root, .app {
        height: 100%;
        overflow: auto;
        overscroll-behavior-y: none;
      }
      
      .main-content {
        -webkit-overflow-scrolling: touch;
        touch-action: pan-y;
        overscroll-behavior-y: none;
      }
      
      .disable-bounce {
        overscroll-behavior: none;
        -webkit-overflow-scrolling: touch;
      }
    }
  }
  
  @media screen and (-webkit-min-device-pixel-ratio: 0) {
    @media (max-width: 768px) {
      html, body, #root, .app {
        height: 100%;
        overflow: auto;
      }
      
      .main-content {
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
      }
      

      
      .android-scroll-fix {
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        overscroll-behavior-y: auto !important;
        width: 100% !important;
      }
    }
  }
  
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 98%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --text-color: black;
    --heading-color: black;
  }
  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --text-color: var(--foreground);
    --heading-color: var(--foreground);
  }
}

@layer components {
  .card {
    @apply relative overflow-hidden rounded-2xl bg-gradient-to-b from-mystic-800/50 to-mystic-900/50 p-8 
           shadow-xl transition-all duration-300 hover:shadow-2xl hover:shadow-cosmic-500/10
           border border-cosmic-500/10 hover:border-cosmic-500/30 backdrop-blur-sm;
  }
  
  .btn-primary {
    @apply px-8 py-3 bg-gradient-to-r from-cosmic-500 to-starlight-500 rounded-full
           text-moonlight-500 font-semibold shadow-lg hover:shadow-cosmic-500/20
           transform hover:-translate-y-1 transition-all duration-300
           focus:outline-none focus:ring-2 focus:ring-cosmic-500/50;
  }

  .mystic-container {
    @apply container mx-auto px-4 py-6;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-cosmic-400 to-starlight-400 bg-clip-text text-transparent;
  }

  .bg-gradient {
    @apply bg-gradient-to-r from-cosmic-400 to-starlight-400;
  }

  /* 3D 翻转相关样式 */
  .perspective-1000 {
    perspective: 1000px;
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
    transform-style: preserve-3d;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.5);
  }
}

@layer utilities {
  .text-shadow-glow {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.7),
                 0 0 20px rgba(255, 255, 255, 0.5),
                 0 0 30px rgba(255, 255, 255, 0.3);
  }
  
  /* 塔罗对话框滚动条样式 */
  .scrollbar-tarot::-webkit-scrollbar {
    width: 6px;
    display: block;
  }
  
  .scrollbar-tarot::-webkit-scrollbar-track {
    background: rgba(17, 24, 39, 0.1);
    border-radius: 3px;
  }
  
  .scrollbar-tarot::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.3);
    border-radius: 3px;
  }
  
  .scrollbar-tarot::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 92, 246, 0.5);
  }
  
  /* 确保在移动端也显示滚动条 */
  @media (max-width: 768px) {
    .scrollbar-tarot {
      -webkit-overflow-scrolling: touch;
      scrollbar-width: thin;
    }
    
    .scrollbar-tarot::-webkit-scrollbar {
      width: 4px;
    }
  }
}

/* 数字样式 */
.font-din {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.tabular-nums {
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum" on, "lnum" on;
  -webkit-font-feature-settings: "tnum" on, "lnum" on;
}

/* 禁用滚动条样式 */
.no-scroll {
  overflow: hidden !important;
  height: 100vh !important;
  position: fixed !important;
  width: 100% !important;
}

/* 隐藏滚动条但允许滚动 */
.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground transition-colors duration-300;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 多语言字体支持 */
.japanese {
  font-family: "Noto Sans JP", "Hiragino Sans", "Hiragino Kaku Gothic Pro", "游ゴシック", "Yu Gothic", "YuGothic", "メイリオ", "Meiryo", sans-serif;
}

.chinese {
  font-family: "Noto Sans SC", "Noto Sans TC", "PingFang SC", "PingFang TC", "Microsoft YaHei", "微软雅黑", "STHeiti", "华文黑体", sans-serif;
}

/* 确保移动端的繁体中文字体与简体中文一致 */
@media (max-width: 768px) {
  :lang(zh-TW) {
    font-family: var(--font-zh-CN);
  }
  
  .chinese-trad {
    font-family: var(--font-zh-CN);
  }
}

/* Video Section Styles */
.video-section {
  padding: 0;
  margin: 2rem 0;
  width: 100%;
}

.video-text-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 100%;
  margin: 0 auto;
}

.video-wrapper {
  width: 100%;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.2);
  aspect-ratio: 16 / 9;
}

.video-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  z-index: 1;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;
  cursor: pointer;
}

.play-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translate(-50%, -50%) scale(1.1);
}

.play-button svg {
  width: 40px;
  height: 40px;
  margin-left: 5px;
}

.video-text {
  text-align: center;
  padding: 0 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.video-text h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #fff;
  font-weight: 600;
}

.video-text p {
  font-size: 1.2rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
}

/* 添加视频控件的自定义样式 */
.video-wrapper video::-webkit-media-controls-panel {
  background: rgba(0, 0, 0, 0.7);
}

.video-wrapper video::-webkit-media-controls-play-button {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}

.video-wrapper video::-webkit-media-controls-timeline {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  height: 3px;
}

.video-wrapper video::-webkit-media-controls-current-time-display,
.video-wrapper video::-webkit-media-controls-time-remaining-display {
  color: #fff;
}

/* 浅色主题特定样式 */
:root:not(.dark) {
  background-color: #ffffff;
  --text-color: #000000;
  --heading-color: #000000;
}

body:not(.dark) {
  background-color: #ffffff !important;
  color: var(--text-color);
}

/* 确保浅色主题有正确的文本颜色 */
h1:not(.dark), h2:not(.dark), h3:not(.dark), h4:not(.dark), h5:not(.dark), h6:not(.dark) {
  color: var(--heading-color);
}

p:not(.dark), span:not(.dark), li:not(.dark), a:not(.dark):not(.text-gradient) {
  color: var(--text-color);
}

/* 非深色主题下的正文文本颜色 */
:not(.dark) .body-text {
  color: var(--text-color);
}

/* 非深色主题下的文本容器 */
:not(.dark) div:not(.text-gradient):not([class*="bg-"]) {
  color: var(--text-color);
}

/* 保留特殊元素的原有颜色 */
:not(.dark) .text-gradient,
:not(.dark) [class*="text-gradient"],
:not(.dark) [class*="bg-clip-text"],
:not(.dark) [class*="bg-gradient"] {
  /* 保留原有渐变色 */
}

/* 深色主题特定样式 */
.dark {
  background-color: #000000;
  --text-color: var(--foreground);
  --heading-color: var(--foreground);
}

.dark body {
  background-color: #000000 !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

/* 平滑过渡效果 */
.transition-smooth {
  transition: all 0.3s ease-in-out;
}

/* 占位符淡入淡出效果 */
.placeholder-fade {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.placeholder-fade-in {
  opacity: 1;
}

