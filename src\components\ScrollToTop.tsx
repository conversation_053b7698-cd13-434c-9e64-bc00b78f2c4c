import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { handlePageChangeScroll, forceResetScroll } from '../utils/scrollHelper';
import { applyScrollBehaviorFix, isAndroid, isMobileDevice } from '../utils/preventOverscroll';

export default function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    // 应用设备特定的滚动优化
    applyScrollBehaviorFix();
    
    // 根据设备类型设置不同的overflow行为
    if (isAndroid()) {
      // Android设备需要保持overflow: auto以确保可以滚动
      document.body.style.overflow = 'auto';
      const mainContent = document.querySelector('.main-content');
      if (mainContent instanceof HTMLElement) {
        // 确保main-content可以滚动，但不设置position和z-index等影响布局的属性
        mainContent.style.overflow = 'auto';
        
        // 确保卡片和容器布局正确
        document.querySelectorAll('.card, .container, [class*="card"], [class*="container"]')
          .forEach(el => {
            if (el instanceof HTMLElement) {
              // 确保不会有奇怪的边距影响布局
              el.style.boxSizing = 'border-box';
              // 避免设置固定的宽度，而是让布局自然流动
              el.style.maxWidth = '100%';
            }
          });
      }
    } else if (isMobileDevice()) {
      // iOS和其他移动设备
      document.body.style.overflow = 'auto';
    } else {
      // PC端 - 确保滚动条显示
      document.body.style.overflow = 'auto';
      document.documentElement.style.overflow = 'auto';
      const mainContent = document.querySelector('.main-content');
      if (mainContent instanceof HTMLElement) {
        mainContent.style.overflow = 'auto';
      }
    }
    
    // 使用我们的辅助函数处理页面变化时的滚动
    handlePageChangeScroll();
    
    // 针对移动设备的额外处理
    if (isMobileDevice()) {
      // 移动设备上的额外保障措施
      const cleanupTimers: NodeJS.Timeout[] = [];
      
      // 在组件挂载后和更新后立即执行
      forceResetScroll();
      
      // 对Android设备设置较少的延时，避免干扰正常滚动
      const delayTimes = isAndroid() ? [50, 150] : [50, 150, 300, 500];
      
      // 设置多个延时器来确保在页面完全加载后也能执行滚动重置
      delayTimes.forEach(delay => {
        const timer = setTimeout(() => {
          forceResetScroll();
          // 确保延时执行后依然保持body可滚动
          document.body.style.overflow = 'auto';
          if (isAndroid()) {
            const mainContent = document.querySelector('.main-content');
            if (mainContent instanceof HTMLElement) {
              mainContent.style.overflow = 'auto';
              // 避免设置影响布局的属性
              if (mainContent.style.position === 'relative') {
                mainContent.style.position = '';
              }
            }
          }
        }, delay);
        cleanupTimers.push(timer);
      });
      
      // 添加一个事件监听器，在页面完成加载后再次尝试重置
      const handleLoad = () => {
        forceResetScroll();
        document.body.style.overflow = 'auto';
        if (isAndroid()) {
          const mainContent = document.querySelector('.main-content');
          if (mainContent instanceof HTMLElement) {
            mainContent.style.overflow = 'auto';
            // 避免设置影响布局的属性
            if (mainContent.style.position === 'relative') {
              mainContent.style.position = '';
            }
          }
        }
      };
      window.addEventListener('load', handleLoad);
      
      // 清理函数
      return () => {
        cleanupTimers.forEach(timer => clearTimeout(timer));
        window.removeEventListener('load', handleLoad);
        // 确保在组件卸载时保持body可滚动
        document.body.style.overflow = 'auto';
      };
    }
    
    // PC端的处理 - 确保滚动条显示
    return () => {
      document.body.style.overflow = 'auto';
      document.documentElement.style.overflow = 'auto';
    };
  }, [pathname]);

  return null;
} 