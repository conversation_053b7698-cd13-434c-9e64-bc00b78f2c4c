import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { TAROT_CARDS } from '../data/tarot-cards';
import { getCardDetails } from '../data/tarot-card-details';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import SEO from '../components/SEO';
// import { IoChevronBackOutline } from 'react-icons/io5';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { generateCardPath } from '../utils/tarotUtils';
import { getTarotCardSEOConfig } from '../lib/SEOConfig';

// 根据当前语言获取卡牌的相应描述字段
const getLocalizedField = (card: any, field: string, language: string, fallback: string) => {
  if (!card) return fallback;
  
  // 根据当前语言选择对应的字段
  switch (language) {
    case 'en':
      return card[`${field}En`] || card[field] || fallback;
    case 'ja':
      return card[`${field}Ja`] || card[field] || fallback;
    case 'zh-TW':
      return card[`${field}ZhTW`] || card[field] || fallback;
    default:
      return card[field] || fallback;
  }
};

// 根据当前语言获取卡牌的相应对象字段（如keywords, meanings）
const getLocalizedObjectField = (card: any, field: string, subfield: string, language: string, fallback: string) => {
  if (!card) return fallback;
  
  // 根据当前语言选择对应的字段
  let fieldObject;
  switch (language) {
    case 'en':
      fieldObject = card[`${field}En`] || card[field];
      break;
    case 'ja':
      fieldObject = card[`${field}Ja`] || card[field];
      break;
    case 'zh-TW':
      fieldObject = card[`${field}ZhTW`] || card[field];
      break;
    default:
      fieldObject = card[field];
  }
  
  return fieldObject && fieldObject[subfield] ? fieldObject[subfield] : fallback;
};

const TarotCardDetail: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { navigate } = useLanguageNavigate();
  const { cardName } = useParams<{ cardName: string }>();
  const [card, setCard] = useState<typeof TAROT_CARDS[0] | null>(null);
  const [cardImage, setCardImage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [orientation, setOrientation] = useState<'upright' | 'reversed'>('upright');
  const [cardDetails, setCardDetails] = useState<any>(null);
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  useEffect(() => {
    if (cardName) {
      // 将URL中的格式（如'the-fool'）转换回卡牌英文名称格式（如'The Fool'）
      const formattedCardName = decodeURIComponent(cardName)
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      
      // 根据英文名称查找卡牌
      const foundCard = TAROT_CARDS.find(c => 
        generateCardPath(c.nameEn) === cardName.toLowerCase() ||
        c.nameEn === formattedCardName
      );
      
      if (foundCard) {
        setCard(foundCard);
        const imageName = `${foundCard.nameEn}.webp`;
        setCardImage(`/images-optimized/tarot/${imageName}`);
        
        // 获取卡牌详细信息
        const details = getCardDetails(foundCard.id);
        setCardDetails(details);
      }
    }
    
    setLoading(false);
  }, [cardName, i18n.language]);

  // 获取卡牌翻译名称
  const getCardTranslatedName = (card: typeof TAROT_CARDS[0]): string => {
    if (card.id <= 21) {
      // 大阿卡纳
      return t(`reading.cards.major.${card.id}`);
    } else {
      // 小阿卡纳
      const nameEn = card.nameEn.toLowerCase();
      let suit = '';
      if (nameEn.includes('wands')) suit = 'wands';
      else if (nameEn.includes('cups')) suit = 'cups';
      else if (nameEn.includes('swords')) suit = 'swords';
      else if (nameEn.includes('pentacles')) suit = 'pentacles';
      
      const rankMap: { [key: string]: string } = {
        'ace': 'ace',
        'two': '2',
        'three': '3',
        'four': '4',
        'five': '5',
        'six': '6',
        'seven': '7',
        'eight': '8',
        'nine': '9',
        'ten': '10',
        'page': 'page',
        'knight': 'knight',
        'queen': 'queen',
        'king': 'king'
      };
      
      const rank = Object.keys(rankMap).find(r => nameEn.startsWith(r.toLowerCase()));
      return rank ? t(`reading.cards.${suit}.${rankMap[rank]}`) : card.name;
    }
  };

  if (loading) {
    return (
      <div className={`min-h-screen flex flex-col items-center justify-center relative ${isDark ? 'text-white' : 'text-gray-800'}`}>
        <LandingBackground />
        <div className="relative z-10">
          <div className="animate-spin w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full"></div>
          <p className="mt-4 text-lg font-medium">{t('gallery.card_detail.loading', '加载中...')}</p>
        </div>
      </div>
    );
  }

  if (!card) {
    return (
      <div className={`min-h-screen flex flex-col items-center justify-center relative ${isDark ? 'text-white' : 'text-gray-800'}`}>
        <LandingBackground />
        <div className="relative z-10 text-center">
          <h1 className="text-3xl font-bold mb-2">{t('gallery.card_detail.card_not_found', '未找到卡牌')}</h1>
          <p className={`text-lg ${isDark ? 'text-gray-300' : 'text-gray-600'} mb-6`}>{t('gallery.card_detail.card_not_found_description', '找不到您请求的塔罗牌')}</p>
          <button 
            onClick={() => navigate(`/gallery`)}
            className="px-6 py-2 bg-purple-600 rounded-full text-white font-medium hover:bg-purple-700 transition-colors"
          >
            {t('gallery.card_detail.back_to_gallery', '返回图鉴')}
          </button>
        </div>
      </div>
    );
  }

  // 获取卡牌对应的翻译键
  const getCardTranslationKey = () => {
    if (card.id <= 21) {
      // 大阿卡纳
      return `reading.cards.major.${card.id}`;
    } else {
      // 小阿卡纳
      const nameEn = card.nameEn.toLowerCase();
      let suit = '';
      if (nameEn.includes('wands')) suit = 'wands';
      else if (nameEn.includes('cups')) suit = 'cups';
      else if (nameEn.includes('swords')) suit = 'swords';
      else if (nameEn.includes('pentacles')) suit = 'pentacles';
      
      const rankMap: { [key: string]: string } = {
        'ace': 'ace',
        'two': '2',
        'three': '3',
        'four': '4',
        'five': '5',
        'six': '6',
        'seven': '7',
        'eight': '8',
        'nine': '9',
        'ten': '10',
        'page': 'page',
        'knight': 'knight',
        'queen': 'queen',
        'king': 'king'
      };
      
      const rank = Object.keys(rankMap).find(r => nameEn.toLowerCase().startsWith(r));
      return rank ? `reading.cards.${suit}.${rankMap[rank]}` : '';
    }
  };

  const cardTranslationKey = getCardTranslationKey();
  const cardTranslatedName = getCardTranslatedName(card);

  return (
    <div className={`min-h-screen flex flex-col relative ${isDark ? 'text-white' : 'text-gray-800'}`}>
      <SEO 
        {...getTarotCardSEOConfig(
          card.nameEn, 
          cardTranslatedName, 
          i18n.language
        )} 
      />
      <LandingBackground />
      
      {/* 主要内容 */}
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8 -mt-12">
          {/* 删除返回按钮 */}

          <div className="flex flex-col md:flex-row gap-8 items-stretch">
            {/* 卡牌图片 - 移动端下显示在前面 */}
            <div className="w-full md:w-1/3 flex flex-col order-1 md:order-1">
              <div className="p-4 sm:p-6 rounded-2xl h-full flex flex-col items-center justify-start">
                <motion.div 
                  className={`relative w-full max-w-[200px] sm:max-w-[250px] mx-auto ${orientation === 'reversed' ? 'rotate-180' : ''}`}
                  animate={{ rotate: orientation === 'reversed' ? 180 : 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <img 
                    src={cardImage} 
                    alt={cardTranslatedName}
                    className="w-full rounded-xl"
                  />
                </motion.div>
                
                {/* 正逆位切换 */}
                <div className="flex gap-3 mt-4">
                  <button
                    onClick={() => setOrientation('upright')}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors
                      ${orientation === 'upright'
                        ? 'bg-purple-600 text-white'
                        : isDark 
                          ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                  >
                    {t('gallery.card_detail.card_upright', '正位')}
                  </button>
                  <button
                    onClick={() => setOrientation('reversed')}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors
                      ${orientation === 'reversed'
                        ? 'bg-purple-600 text-white'
                        : isDark 
                          ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                  >
                    {t('gallery.card_detail.card_reversed', '逆位')}
                  </button>
                </div>
              </div>
            </div>

            {/* 卡牌信息 - 移动端下显示在后面 */}
            <div className="w-full md:w-2/3 order-2 md:order-2">
              <div className={`${isDark ? 'bg-gray-900/60' : 'bg-[#F4F4F5]/90'} backdrop-blur-sm p-6 rounded-2xl h-full shadow-lg`}>
                <h1 className="text-2xl md:text-3xl font-bold mb-4">{cardTranslatedName}</h1>
                
                {/* 描述 */}
                <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'} leading-relaxed mb-6`}>
                  {cardDetails 
                    ? getLocalizedField(cardDetails, 'description', i18n.language, '')
                    : t(`${cardTranslationKey}.description`, '')}
                </p>
                
                <div className="mt-6 space-y-6">
                  {/* 关键词 */}
                  <div>
                    <h3 className={`text-lg font-medium mb-2 ${isDark ? 'text-purple-400' : 'text-purple-600'}`}>
                      {t('gallery.card_detail.card_keywords', '关键词')}
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {cardDetails ? (
                        <>
                          {getLocalizedObjectField(cardDetails, 'keywords', orientation, i18n.language, '').split(',').map((keyword: string, index: number) => (
                            <span key={index} className={`px-3 py-1 ${isDark ? 'bg-gray-800 text-gray-300' : 'bg-white text-gray-700'} rounded-full text-sm`}>
                              {keyword.trim()}
                            </span>
                          ))}
                        </>
                      ) : (
                        // 备选：使用原有的翻译字符串
                        <>
                          {t(`${cardTranslationKey}.keywords.${orientation}`, '').split(',').map((keyword, index) => (
                            <span key={index} className={`px-3 py-1 ${isDark ? 'bg-gray-800 text-gray-300' : 'bg-white text-gray-700'} rounded-full text-sm`}>
                              {keyword.trim()}
                            </span>
                          ))}
                        </>
                      )}
                    </div>
                  </div>
                  
                  {/* 含义 */}
                  <div>
                    <h3 className={`text-lg font-medium mb-2 ${isDark ? 'text-purple-400' : 'text-purple-600'}`}>
                      {orientation === 'upright' 
                        ? t('gallery.card_detail.card_upright_meaning', '正位含义') 
                        : t('gallery.card_detail.card_reversed_meaning', '逆位含义')}
                    </h3>
                    <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'} leading-relaxed`}>
                      {cardDetails 
                        ? getLocalizedObjectField(cardDetails, 'meanings', orientation, i18n.language, '')
                        : t(`${cardTranslationKey}.meanings.${orientation}`, '')}
                    </p>
                  </div>
                  
                  {/* 象征意义 */}
                  <div>
                    <h3 className={`text-lg font-medium mb-2 ${isDark ? 'text-purple-400' : 'text-purple-600'}`}>
                      {t('gallery.card_detail.card_symbolism', '象征意义')}
                    </h3>
                    <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'} leading-relaxed`}>
                      {cardDetails 
                        ? getLocalizedField(cardDetails, 'symbolism', i18n.language, '')
                        : t(`${cardTranslationKey}.symbolism`, '')}
                    </p>
                  </div>
                  
                  {/* 原型 - 仅适用于大阿卡纳 */}
                  {card.id <= 21 && (
                    <div>
                      <h3 className={`text-lg font-medium mb-2 ${isDark ? 'text-purple-400' : 'text-purple-600'}`}>
                        {t('gallery.card_detail.card_archetype', '原型')}
                      </h3>
                      <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'} leading-relaxed`}>
                        {cardDetails
                          ? getLocalizedField(cardDetails, 'archetype', i18n.language, '')
                          : t(`${cardTranslationKey}.archetype`, '')}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="relative z-10">
        <Footer />
      </div>
    </div>
  );
};

export default TarotCardDetail; 