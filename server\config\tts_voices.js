/**
 * 占卜师TTS音色配置
 * 包含两种TTS服务的音色映射:
 * 1. 基础TTS (Edge TTS) - 用于茉伊(Molly)占卜师
 * 2. 高级TTS (火山引擎) - 用于其他占卜师
 * 
 * 支持多语言：中文(zh)、英语(en)、日语(ja)
 */

// 基础TTS音色配置 (Edge TTS)
const basicTtsVoices = {
  // 中文音色
  default: 'zh-CN-XiaoxiaoNeural', // 默认音色
  basic: 'zh-CN-XiaoxiaoNeural',    // 茉伊(女声)
  
  // 英语音色
  default_en: 'en-US-AriaNeural',   // 默认英语女声
  basic_en: 'en-US-AriaNeural',     // 茉伊英语(女声)
  
  // 日语音色
  default_ja: 'ja-JP-NanamiNeural', // 默认日语女声
  basic_ja: 'ja-JP-NanamiNeural',   // 茉伊日语(女声)
};

// 高级TTS音色配置 (火山引擎)
const proTtsVoices = {
  // 中文音色
  default: 'zh_female_wanwanxiaohe_moon_bigtts', // 默认女声
  elias: 'zh_male_shaonianzixin_moon_bigtts',      // 林曜(男声)
  claire: 'ICL_zh_female_wumeiyujie_tob',      // 苏谨(女声)
  raven: 'zh_female_wanwanxiaohe_moon_bigtts',     // 渡鸦(女声)
  aurora: 'ICL_zh_female_jiaoruoluoli_tob',        // 月熙(女声)
  vincent: 'zh_male_aojiaobazong_moon_bigtts',      // Vincent(男声)
  
  // 英语音色
  default_en: 'en_female_emily_moon_bigtts',    // 默认英语女声
  elias_en: 'zh_male_shaonianzixin_moon_bigtts',         // 林曜英语(男声)
  claire_en: 'en_female_emotional_moon_bigtts',     // 苏谨英语(女声)
  raven_en: 'en_female_product_darcie_moon_bigtts',      // 渡鸦英语(女声)
  aurora_en: 'zh_female_mengyatou_mars_bigtts',     // 月熙英语(女声)
  vincent_en: 'zh_male_wennuanahu_moon_bigtts',      // Vincent英语(男声)
  
  // 日语音色
  default_ja: 'ja_female_yuki_moon_bigtts',     // 默认日语女声
  elias_ja: 'multi_zh_male_youyoujunzi_moon_bigtts',         // 林曜日语(男声)
  claire_ja: 'multi_female_maomao_conversation_wvae_bigtts',      // 苏谨日语(女声)
  raven_ja: 'multi_female_gaolengyujie_moon_bigtts',        // 渡鸦日语(女声)
  aurora_ja: 'multi_female_shuangkuaisisi_moon_bigtts',    // 月熙日语(女声)
  vincent_ja: 'multi_male_wanqudashu_moon_bigtts',       // Vincent日语(男声)
};

/**
 * 获取占卜师对应的TTS音色
 * @param {string} readerId - 占卜师ID
 * @param {string} serviceType - 服务类型，'basic'或其他
 * @param {string} language - 语言代码，如'zh'、'en'、'ja'
 * @returns {string} 对应的音色ID
 */
function getVoiceForReader(readerId, serviceType = 'basic', language = 'zh') {
  // 如果是非中文，使用对应语言的音色
  const langSuffix = language !== 'zh' ? `_${language}` : '';
  
  if (serviceType === 'basic') {
    // 尝试获取特定语言的音色，如果不存在则使用该语言的默认音色
    return basicTtsVoices[`${readerId}${langSuffix}`] || basicTtsVoices[`default${langSuffix}`];
  } else {
    // 尝试获取特定语言的音色，如果不存在则使用该语言的默认音色
    return proTtsVoices[`${readerId}${langSuffix}`] || proTtsVoices[`default${langSuffix}`];
  }
}

module.exports = {
  getVoiceForReader,
  basicTtsVoices,
  proTtsVoices
}; 