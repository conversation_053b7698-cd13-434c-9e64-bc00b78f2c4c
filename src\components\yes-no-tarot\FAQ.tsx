import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

const FAQ: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <div className="max-w-[95%] lg:max-w-5xl mx-auto mt-24 sm:mt-32">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('yes_no_tarot.faq.title')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
        <p className={`${
          theme === 'light' ? 'text-gray-700' : 'text-gray-300'
        } text-lg max-w-3xl mx-auto`}>
          {t('yes_no_tarot.faq.description')}
        </p>
      </div>

      {/* FAQ内容 */}
      <div className="space-y-6">
        {/* FAQ Item 1 */}
        <div 
          className={`p-6 rounded-lg ${
            theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
          }`}
        >
          <h3 
            className={`text-lg font-semibold mb-2 ${
              theme === 'light' ? 'text-gray-800' : 'text-white'
            }`}
          >
            {t('yes_no_tarot.faq.q1')}
          </h3>
          <div>
            <p 
              className={`${
                theme === 'light' ? 'text-gray-600' : 'text-gray-300'
              }`}
            >
              {t('yes_no_tarot.faq.a1')}
            </p>
          </div>
        </div>

        {/* FAQ Item 2 */}
        <div 
          className={`p-6 rounded-lg ${
            theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
          }`}
        >
          <h3 
            className={`text-lg font-semibold mb-2 ${
              theme === 'light' ? 'text-gray-800' : 'text-white'
            }`}
          >
            {t('yes_no_tarot.faq.q2')}
          </h3>
          <div>
            <p 
              className={`${
                theme === 'light' ? 'text-gray-600' : 'text-gray-300'
              }`}
            >
              {t('yes_no_tarot.faq.a2')}
            </p>
          </div>
        </div>

        {/* FAQ Item 3 */}
        <div 
          className={`p-6 rounded-lg ${
            theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
          }`}
        >
          <h3 
            className={`text-lg font-semibold mb-2 ${
              theme === 'light' ? 'text-gray-800' : 'text-white'
            }`}
          >
            {t('yes_no_tarot.faq.q3')}
          </h3>
          <div>
            <p 
              className={`${
                theme === 'light' ? 'text-gray-600' : 'text-gray-300'
              }`}
            >
              {t('yes_no_tarot.faq.a3')}
            </p>
          </div>
        </div>

        {/* FAQ Item 4 */}
        <div 
          className={`p-6 rounded-lg ${
            theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
          }`}
        >
          <h3 
            className={`text-lg font-semibold mb-2 ${
              theme === 'light' ? 'text-gray-800' : 'text-white'
            }`}
          >
            {t('yes_no_tarot.faq.q4')}
          </h3>
          <div>
            <p 
              className={`${
                theme === 'light' ? 'text-gray-600' : 'text-gray-300'
              }`}
            >
              {t('yes_no_tarot.faq.a4')}
            </p>
          </div>
        </div>

        {/* FAQ Item 5 */}
        <div 
          className={`p-6 rounded-lg ${
            theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
          }`}
        >
          <h3 
            className={`text-lg font-semibold mb-2 ${
              theme === 'light' ? 'text-gray-800' : 'text-white'
            }`}
          >
            {t('yes_no_tarot.faq.q5')}
          </h3>
          <div>
            <p 
              className={`${
                theme === 'light' ? 'text-gray-600' : 'text-gray-300'
              }`}
            >
              {t('yes_no_tarot.faq.a5')}
            </p>
          </div>
        </div>
      </div>

      {/* FAQ Schema 标记 */}
      <script 
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": t('yes_no_tarot.faq.q1'),
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": t('yes_no_tarot.faq.a1')
                }
              },
              {
                "@type": "Question",
                "name": t('yes_no_tarot.faq.q2'),
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": t('yes_no_tarot.faq.a2')
                }
              },
              {
                "@type": "Question",
                "name": t('yes_no_tarot.faq.q3'),
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": t('yes_no_tarot.faq.a3')
                }
              },
              {
                "@type": "Question",
                "name": t('yes_no_tarot.faq.q4'),
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": t('yes_no_tarot.faq.a4')
                }
              },
              {
                "@type": "Question",
                "name": t('yes_no_tarot.faq.q5'),
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": t('yes_no_tarot.faq.a5')
                }
              }
            ]
          })
        }}
      />
      
      {/* Yes/No Tarot Schema 标记 */}
      <script 
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CreativeWork",
            "name": "TarotQA Yes/No 塔羅占卜",
            "datePublished": new Date().toISOString().split('T')[0],
            "description": "專業的是非問題塔羅占卜，通過單卡或三卡牌陣為您提供清晰、直接的答案指引。",
            "about": {
              "@type": "Thing",
              "name": "是非塔羅占卜"
            },
            "audience": {
              "@type": "Audience",
              "audienceType": "尋求明確答案的塔羅愛好者"
            }
          })
        }}
      />
    </div>
  );
};

export default FAQ; 