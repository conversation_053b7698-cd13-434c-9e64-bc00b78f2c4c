import axiosInstance from '../utils/axios';

// 本地缓存点赞状态的接口
interface ReaderVoteCache {
  readerId: string;
  action: 'add' | 'remove';  // add表示点赞，remove表示取消点赞
  timestamp: number;
}

// 用于存储点赞/取消点赞的本地缓存
const pendingVotes: ReaderVoteCache[] = [];

// 添加日志前缀，方便跟踪和调试
// const logPrefix = '[ReaderVoteCache]';

// 获取指定占卜师的投票数
export const getReaderVotes = async (readerId: string): Promise<number> => {
  // 硬编码点赞数
  const hardcodedVotes: {[key: string]: number} = {
    'basic': 1500,    // 茉伊排第一，点赞数最高
    'raven': 743,    // 渡鸦排第二
    'vincent': 582,  // 文森特排第三
    'claire': 251,   // 苏谨排第四
    'elias': 132,    // 林曜排第五
    'aurora': 86     // 月熙排第六，点赞数最低
  };
  
  // 直接返回硬编码的点赞数
  return hardcodedVotes[readerId] || 0;
  
  /* 暂时注释掉原有的API调用
  try {
    const response = await axiosInstance.get(`/api/reader/votes/${readerId}`);
    return response.data.voteCount;
  } catch (error) {
    console.error('Error getting reader votes:', error);
    return 0;
  }
  */
};

// 获取所有占卜师的投票数
export const getAllReadersVotes = async (): Promise<Array<{reader_id: string, voteCount: number}>> => {
  // 硬编码点赞数
  const hardcodedVotes = [
    { reader_id: 'basic', voteCount: 1500 },    // 茉伊排第一，点赞数最高
    { reader_id: 'raven', voteCount: 743 },    // 渡鸦排第二
    { reader_id: 'vincent', voteCount: 582 },  // 文森特排第三
    { reader_id: 'claire', voteCount: 251 },   // 苏谨排第四
    { reader_id: 'elias', voteCount: 132 },    // 林曜排第五
    { reader_id: 'aurora', voteCount: 86 }     // 月熙排第六，点赞数最低
  ];
  
  return hardcodedVotes;
  
  /* 暂时注释掉原有的API调用
  try {
    const response = await axiosInstance.get('/api/reader/votes');
    return response.data;
  } catch (error) {
    console.error('Error getting all reader votes:', error);
    return [];
  }
  */
};

// 在本地切换对占卜师的投票状态
export const toggleReaderVoteLocal = (readerId: string, serverVotedState?: boolean): {voted: boolean, voteCount: number} => {
  try {
    // 如果提供了服务器状态，使用它作为基础
    const hasServerState = serverVotedState !== undefined;
    
    // 查找此占卜师在本地缓存中的最新状态
    const existingVoteIndex = pendingVotes.findIndex(vote => vote.readerId === readerId);
    
    // 当前的投票状态 - 如果提供了服务器状态且本地无缓存，优先使用服务器状态
    const currentVoted = (existingVoteIndex !== -1) 
      ? getUserVotedLocal(readerId) 
      : (hasServerState ? serverVotedState : false);
    
    // 当前操作是点赞还是取消点赞 - 反转当前状态
    const action = currentVoted ? 'remove' : 'add';
    
    
    // 如果已有相同占卜师的待处理操作，移除它
    if (existingVoteIndex !== -1) {
      pendingVotes.splice(existingVoteIndex, 1);
    }
    
    // 添加新的操作
    pendingVotes.push({
      readerId,
      action,
      timestamp: Date.now()
    });
    
    
    // 切换后的状态始终是当前状态的反向
    const newVotedState = !currentVoted;
        
    // 返回切换后的状态
    return {
      voted: newVotedState,
      voteCount: 0 // 这个值在前端会被忽略，因为前端会根据之前的值和当前操作计算显示的值
    };
  } catch (error) {
    // console.error(`${logPrefix} 本地切换点赞状态出错:`, error);
    throw error;
  }
};

// 检查用户在本地缓存中是否投票给指定占卜师
export const getUserVotedLocal = (readerId: string): boolean => {
  // 遍历缓存，找到最后一次对该占卜师的操作
  const lastAction = [...pendingVotes]
    .reverse()
    .find(vote => vote.readerId === readerId);
  

  // 如果找到操作，根据操作类型返回状态
  if (lastAction) {
    const isVoted = lastAction.action === 'add';
    return isVoted;
  }
  
  // 如果本地缓存中没有，返回false
  return false;
};

// 提交所有待处理的投票到服务器
export const submitPendingVotes = async (): Promise<{success: boolean, message: string}> => {
  if (pendingVotes.length === 0) {
    return { success: true, message: '没有待处理的点赞操作' };
  }
  
  try {    
    // 克隆当前队列，以便在提交后清空
    const votesToSubmit = [...pendingVotes];
    
    // 调用API提交所有待处理的投票
    const response = await axiosInstance.post('/api/reader/batch-votes', { votes: votesToSubmit });
    
    // 提交成功后清空本地缓存
    if (response.data.success) {
      pendingVotes.length = 0;
    } else {
      // console.error(`${logPrefix} 提交点赞操作失败:`, response.data.message);
    }
    
    return {
      success: response.data.success,
      message: response.data.message || '点赞操作已提交'
    };
  } catch (error) {
    // console.error(`${logPrefix} 提交点赞操作时发生错误:`, error);
    return {
      success: false,
      message: '提交点赞操作时出错'
    };
  }
};

// 获取待处理的点赞操作数量
export const getPendingVotesCount = (): number => {
  return pendingVotes.length;
};

// 原有的服务器API调用，继续保留以备需要
// 切换对占卜师的投票状态（投票或取消投票）- 服务器API
export const toggleReaderVote = async (readerId: string): Promise<{voted: boolean, voteCount: number}> => {
  try {
    const timestamp = Date.now();
    const response = await axiosInstance.post(`/api/reader/vote/${readerId}`, { timestamp });
    return response.data;
  } catch (error) {
    // console.error('Error toggling reader vote:', error);
    throw error;
  }
};

// 检查用户是否投票给指定占卜师
export const checkUserVoted = async (readerId: string): Promise<boolean> => {
  try {
    // 如果用户未登录，直接返回false
    if (!localStorage.getItem('token')) {
      return false;
    }
    
    const response = await axiosInstance.get(`/api/reader/user-vote/${readerId}`);
    return response.data.hasVoted;
  } catch (error) {
    // console.error('Error checking user vote:', error);
    return false;
  }
};

// 获取用户投票过的所有占卜师
export const getUserVotedReaders = async (): Promise<string[]> => {
  try {
    // 如果用户未登录，直接返回空数组
    if (!localStorage.getItem('token')) {
      return [];
    }
    
    const response = await axiosInstance.get('/api/reader/user-votes');
    return response.data.votedReaders;
  } catch (error) {
    // console.error('Error getting user voted readers:', error);
    return [];
  }
}; 