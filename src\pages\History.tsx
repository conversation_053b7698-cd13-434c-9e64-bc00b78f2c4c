import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import "./History.css";
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../components/ui/pagination";
import { useTranslation } from 'react-i18next';
import SEO from '../components/SEO';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

interface Card {
  name: string;
  position: string;
  isReversed: boolean;
}

interface Session {
  id: string;
  timestamp: string;
  question: string;
  status: string;
  ethical_status?: string; // 新增安全检测状态字段
  selectedCards: Card[];
  selectedReader: {
    id: string;
    name: string;
    type: string;
  } | null;
  selectedSpread: {
    id: string;
    name: string;
    cardCount: number;
  } | null;
  readingResult: any;
}

const ITEMS_PER_PAGE = 5;

const History: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const [historyItems, setHistoryItems] = useState<Session[]>([]);
  const [filteredItems, setFilteredItems] = useState<Session[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  const getTranslatedCardName = (cardName: string) => {
    const majorArcanaMap: { [key: string]: number } = {
      '愚者': 0, '魔术师': 1, '女祭司': 2, '女皇': 3, '皇帝': 4,
      '教皇': 5, '恋人': 6, '战车': 7, '力量': 8, '隐士': 9,
      '命运之轮': 10, '正义': 11, '倒吊人': 12, '死神': 13,
      '节制': 14, '恶魔': 15, '塔': 16, '星星': 17, '月亮': 18,
      '太阳': 19, '审判': 20, '世界': 21
    };

    if (majorArcanaMap[cardName] !== undefined) {
      return t(`reading.cards.major.${majorArcanaMap[cardName]}`);
    }

    const suits = {
      '权杖': 'wands',
      '圣杯': 'cups',
      '宝剑': 'swords',
      '钱币': 'pentacles'
    };

    const ranks = {
      '王牌': 'ace',
      '二': '2',
      '三': '3',
      '四': '4',
      '五': '5',
      '六': '6',
      '七': '7',
      '八': '8',
      '九': '9',
      '十': '10',
      '侍者': 'page',
      '骑士': 'knight',
      '皇后': 'queen',
      '国王': 'king'
    };

    for (const [suitCh, suitEn] of Object.entries(suits)) {
      if (cardName.includes(suitCh)) {
        for (const [rankCh, rankEn] of Object.entries(ranks)) {
          if (cardName.includes(rankCh)) {
            return t(`reading.cards.${suitEn}.${rankEn}`);
          }
        }
      }
    }

    return cardName;
  };

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          navigate('/login');
          return;
        }

        const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/session/history`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data.success) {
          const formattedSessions = response.data.sessions
            .filter((session: Session) => session.readingResult !== null)
            .map((session: Session) => ({
              ...session,
              timestamp: session.timestamp ? new Date(session.timestamp).toLocaleString() : 'Unknown date'
            }));
          setHistoryItems(formattedSessions);
          setError(null);
        } else {
          setError(response.data.message || '获取历史记录失败');
        }
      } catch (error) {
        setError('获取历史记录时发生错误');
      } finally {
        setLoading(false);
      }
    };

    fetchHistory();
  }, []);

  useEffect(() => {
    if (!loading && historyItems.length === 0) {
      document.body.classList.add('no-scroll');
    } else {
      document.body.classList.remove('no-scroll');
    }

    return () => {
      document.body.classList.remove('no-scroll');
    };
  }, [loading, historyItems.length]);

  useEffect(() => {
    if (historyItems.length > 0) {
      let filtered = [...historyItems].filter(item => {
        // 排除 daily-fortune 和 yes-no-single-card 的会话
        if (item.selectedSpread?.id === 'daily-fortune' || item.selectedSpread?.id === 'yes-no-single-card') {
          return false;
        }
        
        return item.readingResult !== null || 
          item.ethical_status === 'ethical_intervention' || 
          item.ethical_status === 'ethical_intervention_follow' ||
          item.status === 'ethical_intervention' || 
          item.status === 'ethical_intervention_follow';
      });
      
      if (searchText) {
        filtered = filtered.filter(item => 
          item.question.toLowerCase().includes(searchText.toLowerCase())
        );
      }
      
      if (selectedDate) {
        filtered = filtered.filter(item => {
          const itemDate = new Date(item.timestamp);
          return itemDate.toDateString() === selectedDate.toDateString();
        });
      }
      
      setFilteredItems(filtered);
      setCurrentPage(1);
    } else {
      setFilteredItems([]);
    }
  }, [historyItems, searchText, selectedDate]);

  const totalPages = Math.ceil(filteredItems.length / ITEMS_PER_PAGE);
  const paginatedItems = filteredItems.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleViewDetail = (id: string) => {
    navigate(`/history/${id}`);
  };

  const renderPaginationItems = () => {
    const items = [];
    const maxVisiblePages = 5;
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    if (startPage > 1) {
      items.push(
        <PaginationItem key="1">
          <PaginationLink 
            onClick={() => handlePageChange(1)}
            className="font-['Inter']"
          >
            1
          </PaginationLink>
        </PaginationItem>
      );
      if (startPage > 2) {
        items.push(
          <PaginationItem key="ellipsis-1">
            <PaginationEllipsis className="text-purple-300" />
          </PaginationItem>
        );
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            onClick={() => handlePageChange(i)}
            isActive={currentPage === i}
            className={`font-['Inter'] ${currentPage === i ? 'bg-purple-500/20 border-purple-500/50' : ''}`}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        items.push(
          <PaginationItem key="ellipsis-2">
            <PaginationEllipsis className="text-purple-300" />
          </PaginationItem>
        );
      }
      items.push(
        <PaginationItem key={totalPages}>
          <PaginationLink 
            onClick={() => handlePageChange(totalPages)}
            className="font-['Inter']"
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  if (loading) {
    return (
      <div className={`fixed inset-0 ${isDark ? 'bg-dark-900' : 'bg-white'} flex items-center justify-center`}>
        <LandingBackground />
        <div className={`${isDark ? 'text-white' : 'text-gray-800'} relative z-10`}>{t('common.loading')}</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`fixed inset-0 ${isDark ? 'bg-dark-900' : 'bg-white'} flex items-center justify-center`}>
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col relative">
      <SEO />
      <LandingBackground />
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8">
          <div className="text-center mt-8 sm:mt-10">
            <h1 className={`main-title mb-1 ${getFontClass()}`}>{t('history.title')}</h1>
            <p className={`sub-title mb-4 sm:mb-6 ${getFontClass()}`}>{t('history.subtitle')}</p>
          </div>

          {historyItems.length > 0 && (
            <div className="max-w-4xl mx-auto mb-8 flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative group">
                <div className={`absolute -inset-0.5 bg-gradient-to-r ${isDark ? 'from-purple-500/20 to-pink-500/20' : 'from-purple-300/30 to-pink-300/30'} rounded-xl blur opacity-50 
                             group-hover:opacity-75 transition duration-300`}></div>
                <input
                  type="text"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  placeholder={t('history.search.placeholder')}
                  className={`relative w-full px-4 py-3 sm:py-2 ${isDark ? 'bg-black/40 text-white' : 'bg-white/70 text-gray-800'} border ${isDark ? 'border-purple-500/30' : 'border-purple-300/50'} rounded-xl placeholder-gray-400
                           focus:outline-none focus:border-purple-500/60 focus-visible:outline-none focus-visible:border-purple-500/60
                           focus-within:outline-none focus-within:border-purple-500/60
                           hover:border-purple-500/50 hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]
                           group-hover:border-purple-500/50 group-hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]
                           transition-all duration-300 backdrop-blur-sm font-['Noto_Sans_SC']
                           text-base sm:text-sm`}
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
              <div className="flex gap-2 items-center">
                <div className="relative group flex-1 sm:flex-none">
                  <div className={`absolute -inset-0.5 bg-gradient-to-r ${isDark ? 'from-purple-500/20 to-pink-500/20' : 'from-purple-300/30 to-pink-300/30'} rounded-xl blur opacity-50 
                               group-hover:opacity-75 transition duration-300`}></div>
                  <div className="block sm:hidden w-full">
                    <div className="relative">
                      <input
                        type="date"
                        value={selectedDate ? selectedDate.toISOString().split('T')[0] : ''}
                        onChange={(e) => {
                          const date = e.target.value ? new Date(e.target.value) : null;
                          if (date) {
                            date.setHours(12, 0, 0, 0);
                          }
                          setSelectedDate(date);
                        }}
                        className={`relative w-full h-[52px] px-4 ${isDark ? 'bg-black/40 text-white' : 'bg-white/70 text-gray-800'} border ${isDark ? 'border-purple-500/30' : 'border-purple-300/50'} rounded-xl
                               focus:outline-none focus:border-purple-500/60 focus-visible:outline-none focus-visible:border-purple-500/60
                               focus-within:outline-none focus-within:border-purple-500/60
                               hover:border-purple-500/50 hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]
                               group-hover:border-purple-500/50 group-hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]
                               transition-all duration-300 backdrop-blur-sm font-['Noto_Sans_SC']
                               text-base appearance-none`}
                      />
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none text-gray-400">
                        {!selectedDate && t('history.date.placeholder')}
                      </div>
                    </div>
                  </div>
                  <div className="hidden sm:block">
                    <DatePicker
                      selected={selectedDate}
                      onChange={(date: Date | null) => setSelectedDate(date)}
                      placeholderText={t('history.date.placeholder')}
                      dateFormat="yyyy-MM-dd"
                      className={`relative w-[200px] px-4 py-2 ${isDark ? 'bg-black/40 text-white' : 'bg-white/70 text-gray-800'} border ${isDark ? 'border-purple-500/30' : 'border-purple-300/50'} rounded-xl placeholder-gray-400
                              focus:outline-none focus:border-purple-500/60 focus-visible:outline-none focus-visible:border-purple-500/60
                              focus-within:outline-none focus-within:border-purple-500/60
                              hover:border-purple-500/50 hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]
                              group-hover:border-purple-500/50 group-hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]
                              transition-all duration-300 backdrop-blur-sm font-['Noto_Sans_SC']
                              text-sm`}
                      wrapperClassName="w-[200px]"
                    />
                  </div>
                </div>
                {selectedDate && (
                  <motion.button
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    onClick={() => setSelectedDate(null)}
                    className={`relative px-4 sm:px-3 py-3 sm:py-2 rounded-xl ${isDark ? 'bg-black/40' : 'bg-white/70'} border ${isDark ? 'border-purple-500/30' : 'border-purple-300/50'} ${isDark ? 'text-white' : 'text-gray-800'}
                             hover:border-purple-500/50 hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]
                             focus:outline-none focus:border-purple-500/60 active:border-purple-500/60
                             focus-visible:outline-none focus-visible:border-purple-500/60
                             focus-within:outline-none focus-within:border-purple-500/60
                             active:outline-none active:shadow-[0_0_15px_rgba(168,85,247,0.15)]
                             transition-all duration-300 backdrop-blur-sm select-none
                             text-base sm:text-sm`}
                  >
                    <div className={`absolute -inset-0.5 bg-gradient-to-r ${isDark ? 'from-purple-500/20 to-pink-500/20' : 'from-purple-300/30 to-pink-300/30'} rounded-xl blur opacity-50 
                                 group-hover:opacity-75 transition duration-300`}></div>
                    <span className="relative">✕</span>
                  </motion.button>
                )}
              </div>
            </div>
          )}

          <div className="max-w-4xl mx-auto space-y-8">
            {historyItems.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                className={`text-center py-16 px-6 ${isDark ? 'bg-dark-800/50' : 'bg-gray-100/60'} rounded-2xl border ${isDark ? 'border-purple-500/30' : 'border-purple-300/50'} backdrop-blur-sm`}
              >
                <motion.div 
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.1 }}
                  className="mb-6"
                >
                  <div className="w-24 h-24 mx-auto mb-6 relative">
                    <div className={`absolute inset-0 bg-gradient-to-r ${isDark ? 'from-purple-500/20 to-pink-500/20' : 'from-purple-300/20 to-pink-300/20'} rounded-full animate-pulse`}></div>
                    <div className={`absolute inset-2 bg-gradient-to-r ${isDark ? 'from-purple-500/30 to-pink-500/30' : 'from-purple-300/30 to-pink-300/30'} rounded-full animate-pulse`} style={{ animationDelay: '200ms' }}></div>
                    <div className={`absolute inset-4 bg-gradient-to-r ${isDark ? 'from-purple-500/40 to-pink-500/40' : 'from-purple-300/40 to-pink-300/40'} rounded-full animate-pulse`} style={{ animationDelay: '400ms' }}></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-4xl">🔮</span>
                    </div>
                  </div>
                  <h2 className={`text-2xl font-bold ${isDark ? 'bg-gradient-to-r from-purple-400 to-pink-400 text-transparent bg-clip-text' : 'text-purple-700'} mb-3 font-['Noto_Sans_SC']`}>
                    {t('history.empty.title')}
                  </h2>
                  <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'} text-lg mb-8 font-['Noto_Sans_SC']`}>
                    {t('history.empty.description')}
                  </p>
                </motion.div>
                <motion.button
                  onClick={() => navigate('/')}
                  className="px-8 py-3 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium
                           hover:from-purple-600 hover:to-pink-600 transform hover:scale-105 transition-all duration-300
                           shadow-lg shadow-purple-500/20 hover:shadow-purple-500/30"
                  whileHover={{ y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {t('history.empty.button')}
                </motion.button>
              </motion.div>
            ) : (
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.15, ease: [0.4, 0, 0.2, 1] }}
                className="space-y-8"
              >
                {paginatedItems.map((item) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    onClick={() => handleViewDetail(item.id)}
                    className="relative group cursor-pointer"
                  >
                    <div className={`absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-pink-600 
                                  rounded-xl blur opacity-60 group-hover:opacity-100 transition duration-200`}>
                    </div>
                    <div className={`relative ${isDark ? 'bg-black' : 'bg-white'} rounded-xl p-4 sm:p-6`}>
                      <div className="flex flex-col gap-4 sm:gap-6">
                        <div className="flex-1 min-w-0">
                          <div className="relative flex items-center pr-2 sm:pr-8 mb-2 sm:mb-4" ref={(el) => {
                            if (el) {
                              const isOverflowing = el.scrollWidth > el.clientWidth;
                              const questionEl = el.querySelector('.question-text');
                              const ellipsisEl = el.querySelector('.question-ellipsis');
                              if (questionEl && ellipsisEl) {
                                if (isOverflowing) {
                                  questionEl.classList.add('w-[calc(100%-20px)]', 'sm:w-[calc(100%-32px)]');
                                  ellipsisEl.classList.remove('hidden');
                                } else {
                                  questionEl.classList.remove('w-[calc(100%-20px)]', 'sm:w-[calc(100%-32px)]');
                                  ellipsisEl.classList.add('hidden');
                                }
                              }
                            }
                          }}>
                            <h3 className="question-text text-lg sm:text-xl font-bold text-purple-400 
                                       truncate flex-1 font-['Noto_Sans_SC']
                                       leading-tight sm:leading-relaxed">
                              {item.question}
                            </h3>
                            <span className="question-ellipsis text-lg sm:text-xl font-bold text-purple-400 hidden flex-shrink-0 ml-1 sm:ml-2">
                              ...
                            </span>
                          </div>
                          
                          <div className="flex flex-col gap-2">
                            <div className="font-['Inter'] text-purple-300 tracking-wide whitespace-nowrap">{item.timestamp}</div>
                            
                            <div className="flex items-center gap-2 overflow-hidden sm:hidden">
                              <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} whitespace-nowrap font-['Noto_Sans_SC'] truncate`}>
                                {item.selectedReader?.name ? t(`reader.${item.selectedReader.id}.name`) : t('history.reader.none')}
                              </span>
                              <span className="text-gray-600 flex-shrink-0">·</span>
                              <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} whitespace-nowrap font-['Noto_Sans_SC'] truncate`}>
                                {item.selectedSpread?.name ? t(`spreads.${item.selectedSpread.id.replace(/-/g, '_')}.name`) : t('history.spread.single')}
                              </span>
                              <span className="text-gray-600 flex-shrink-0">·</span>
                              <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} whitespace-nowrap font-['Noto_Sans_SC'] flex-shrink-0`}>
                                {item.selectedSpread?.cardCount || 1}{t('history.card_count')}
                              </span>
                            </div>

                            <div className="hidden sm:flex items-center gap-4">
                              <div className="flex items-center gap-2">
                                <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} whitespace-nowrap font-['Noto_Sans_SC']`}>
                                  {item.selectedReader?.name ? t(`reader.${item.selectedReader.id}.name`) : t('history.reader.none')}
                                </span>
                                <span className="text-gray-600">·</span>
                                <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} whitespace-nowrap font-['Noto_Sans_SC']`}>
                                  {item.selectedSpread?.name ? t(`spreads.${item.selectedSpread.id.replace(/-/g, '_')}.name`) : t('history.spread.single')}
                                </span>
                                <span className="text-gray-600">·</span>
                                <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} whitespace-nowrap font-['Noto_Sans_SC']`}>
                                  {item.selectedSpread?.cardCount || 1}{t('history.card_count')}
                                </span>
                              </div>

                              <div className="flex items-center gap-2 flex-1 min-w-0">
                                <div className="flex gap-2 overflow-hidden whitespace-nowrap" ref={(el) => {
                                  if (el) {
                                    const container = el.parentElement;
                                    if (!container) return;

                                    const moreLabel = container.querySelector('.more-label');
                                    if (!moreLabel) return;

                                    moreLabel.classList.add('hidden');

                                    const cards = Array.from(el.children) as HTMLElement[];
                                    let visibleCards = 0;
                                    let totalWidth = 0;
                                    const containerWidth = el.clientWidth;
                                    const moreLabelWidth = moreLabel.getBoundingClientRect().width + 8;
                                    const availableWidth = containerWidth - moreLabelWidth;
                                    const gap = 8;

                                    cards.forEach(card => {
                                      card.style.display = '';
                                    });

                                    const cardWidths = cards.map(card => card.getBoundingClientRect().width + gap);

                                    for (let i = 0; i < cards.length; i++) {
                                      const newTotalWidth = totalWidth + cardWidths[i];
                                      if (newTotalWidth <= availableWidth) {
                                        totalWidth = newTotalWidth;
                                        visibleCards++;
                                        cards[i].style.display = '';
                                      } else {
                                        cards[i].style.display = 'none';
                                      }
                                    }

                                    if (visibleCards < cards.length) {
                                      const remainingCards = cards.length - visibleCards;
                                      moreLabel.classList.remove('hidden');
                                      moreLabel.textContent = `+${remainingCards}`;
                                    }
                                  }
                                }}>
                                  {item.selectedCards.map((card, index) => {
                                    const translatedName = getTranslatedCardName(card.name);
                                    return (
                                      <span 
                                        key={index}
                                        className={`inline-flex flex-shrink-0 px-2.5 py-1 rounded-full text-sm ${isDark ? 'bg-dark-700/90 text-white' : 'bg-gray-100 text-gray-800'} font-medium
                                                 border ${isDark ? 'border-purple-500/30' : 'border-purple-300/50'} shadow-[0_0_10px_rgba(168,85,247,0.15)]
                                                 whitespace-nowrap font-['Noto_Sans_SC']`}
                                      >
                                        {translatedName}
                                        {card.isReversed && `（${t('reading.result.reversed')}）`}
                                      </span>
                                    );
                                  })}
                                </div>
                                <span className={`more-label flex-shrink-0 px-2.5 py-1 rounded-full text-sm ${isDark ? 'bg-dark-700/90 text-purple-300' : 'bg-gray-100 text-purple-600'} 
                                               font-semibold border ${isDark ? 'border-purple-500/50' : 'border-purple-400/50'} 
                                               shadow-[0_0_15px_rgba(168,85,247,0.3)] backdrop-blur-sm
                                               whitespace-nowrap hidden font-['Inter']`}>
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </div>

          {filteredItems.length > ITEMS_PER_PAGE && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="mt-8 px-4 sm:px-0"
            >
              <Pagination>
                <PaginationContent className="flex flex-nowrap justify-center gap-2">
                  <PaginationItem className="flex-shrink-0">
                    <PaginationPrevious
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`${isDark ? 'bg-black/40' : 'bg-white/70'} border ${isDark ? 'border-purple-500/30' : 'border-purple-300/50'} ${isDark ? 'text-white' : 'text-gray-800'} hover:bg-black/60
                               hover:border-purple-500/50 transition-all duration-300 backdrop-blur-sm
                               disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap`}
                    >
                      {t('history.pagination.previous')}
                    </PaginationPrevious>
                  </PaginationItem>

                  <div className="hidden sm:flex items-center gap-2">
                    {renderPaginationItems()}
                  </div>

                  <div className="flex sm:hidden items-center gap-2 flex-shrink-0">
                    <PaginationItem>
                      <span className={`px-4 py-2 rounded-lg ${isDark ? 'bg-black/40' : 'bg-white/70'} border ${isDark ? 'border-purple-500/30' : 'border-purple-300/50'} ${isDark ? 'text-white' : 'text-gray-800'} backdrop-blur-sm whitespace-nowrap`}>
                        <span className="font-['Inter'] text-purple-300">{currentPage}</span>
                        <span className="text-gray-500 mx-2">/</span>
                        <span className="font-['Inter'] text-gray-400">{totalPages}</span>
                      </span>
                    </PaginationItem>
                  </div>

                  <PaginationItem className="flex-shrink-0">
                    <PaginationNext
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className={`${isDark ? 'bg-black/40' : 'bg-white/70'} border ${isDark ? 'border-purple-500/30' : 'border-purple-300/50'} ${isDark ? 'text-white' : 'text-gray-800'} hover:bg-black/60
                               hover:border-purple-500/50 transition-all duration-300 backdrop-blur-sm
                               disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap`}
                    >
                      {t('history.pagination.next')}
                    </PaginationNext>
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </motion.div>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default History;