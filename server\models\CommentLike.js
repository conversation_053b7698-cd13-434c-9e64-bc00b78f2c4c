const { getConnection } = require('../services/database');

class CommentLike {
  /**
   * 点赞评论（支持连击）
   * @param {string} commentId - 评论ID
   * @param {string} userId - 用户ID
   * @param {number} count - 点赞次数，默认1
   * @returns {Promise<{success: boolean, newCount: number, userLikeCount: number}>} 操作结果
   */
  static async like(commentId, userId, count = 1) {
    const pool = await getConnection();

    try {
      // 获取当前评论的点赞信息
      const [rows] = await pool.query(
        'SELECT like_count, user_like_counts FROM comments WHERE id = ?',
        [commentId]
      );

      if (rows.length === 0) {
        throw new Error('评论不存在');
      }

      const comment = rows[0];
      let userLikeCounts = {};

      // 解析用户点赞次数记录
      if (comment.user_like_counts) {
        try {
          userLikeCounts = JSON.parse(comment.user_like_counts);
        } catch (e) {
          userLikeCounts = {};
        }
      }

      // 增加用户的点赞次数
      const currentUserLikes = userLikeCounts[userId] || 0;
      const newUserLikes = currentUserLikes + count;
      userLikeCounts[userId] = newUserLikes;

      // 计算总点赞数
      const totalLikes = Object.values(userLikeCounts).reduce((sum, likes) => sum + likes, 0);

      // 更新数据库
      await pool.query(
        'UPDATE comments SET like_count = ?, user_like_counts = ? WHERE id = ?',
        [totalLikes, JSON.stringify(userLikeCounts), commentId]
      );

      return {
        success: true,
        newCount: totalLikes,
        userLikeCount: newUserLikes
      };
    } catch (error) {
      throw error;
    }
  }



  /**
   * 获取用户对评论的点赞次数
   * @param {string} commentId - 评论ID
   * @param {string} userId - 用户ID
   * @returns {Promise<number>} 用户点赞次数
   */
  static async getUserLikeCount(commentId, userId) {
    const pool = await getConnection();

    const [rows] = await pool.query(
      'SELECT user_like_counts FROM comments WHERE id = ?',
      [commentId]
    );

    if (rows.length === 0) {
      return 0;
    }

    const comment = rows[0];
    if (!comment.user_like_counts) {
      return 0;
    }

    try {
      const userLikeCounts = JSON.parse(comment.user_like_counts);
      return userLikeCounts[userId] || 0;
    } catch (e) {
      return 0;
    }
  }

  /**
   * 获取多个评论的用户点赞次数
   * @param {string[]} commentIds - 评论ID数组
   * @param {string} userId - 用户ID
   * @returns {Promise<{[commentId: string]: number}>} 用户点赞次数映射
   */
  static async getUserLikeCounts(commentIds, userId) {
    if (!commentIds.length || !userId) {
      return {};
    }

    const pool = await getConnection();
    const placeholders = commentIds.map(() => '?').join(',');

    const [rows] = await pool.query(
      `SELECT id, user_like_counts FROM comments WHERE id IN (${placeholders})`,
      commentIds
    );

    const result = {};

    rows.forEach(comment => {
      let userLikeCount = 0;

      if (comment.user_like_counts) {
        try {
          const userLikeCounts = JSON.parse(comment.user_like_counts);
          userLikeCount = userLikeCounts[userId] || 0;
        } catch (e) {
          userLikeCount = 0;
        }
      }

      result[comment.id] = userLikeCount;
    });

    // 确保所有请求的评论ID都有结果
    commentIds.forEach(commentId => {
      if (!(commentId in result)) {
        result[commentId] = 0;
      }
    });

    return result;
  }

  /**
   * 添加点赞（支持连击）
   * @param {string} commentId - 评论ID
   * @param {string} userId - 用户ID
   * @param {number} count - 点赞次数，默认1
   * @returns {Promise<{success: boolean, totalLikes: number, userLikes: number}>} 操作结果
   */
  static async addLike(commentId, userId, count = 1) {
    const result = await this.like(commentId, userId, count);

    return {
      success: result.success,
      totalLikes: result.newCount,
      userLikes: result.userLikeCount
    };
  }
}

module.exports = CommentLike;
