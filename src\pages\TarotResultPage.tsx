import { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';  
import { useTranslation } from 'react-i18next';
import { useLocation, useSearchParams } from 'react-router-dom';
import axiosInstance from '../utils/axios';

import LandingBackground from '../components/LandingBackground';
import ConfirmDialog from '../components/ConfirmDialog';
import VipPromptDialog from '../components/VipPromptDialog';

import ShareDialog from '../components/ShareDialog';
import CommentDialog from '../components/CommentDialog';
import { useUser } from '../contexts/UserContext';
import { useDropdown } from '../contexts/DropdownContext';
import { checkUserDeepAnalysisUsage, checkUserFollowupUsage } from '../services/userService';
import { useTarotProgress } from '../hooks/useTarotProgress';
import { TAROT_CARDS, TarotCard } from '../data/tarot-cards';
import SEO from '../components/SEO';
import { useTheme } from '../contexts/ThemeContext';
// 暂时注释掉深度解析相关导入
// import { useDeepAnalysis } from '../hooks/useDeepAnalysis';
import { useFollowUp } from '../hooks/useFollowUp';
import { Message, Reader } from '../types/tarot';
import NavbarWrapper from '../components/tarot/NavbarWrapper';
import FooterWithNavigation from '../components/tarot/FooterWithNavigation';
import TarotReadingContent from '../components/tarot/TarotReadingContent';
import TarotCardDisplay from '../components/tarot/TarotCardDisplay';
import ContinueExploreButton from '../components/tarot/ContinueExploreButton';
import CommentSection from '../components/CommentSection';
import { markdownStyles, lightModeMarkdownStyles } from '../styles/MarkdownStyles';
import { getFontClass } from '../utils/tarotUtils';
import { useTarotReading } from '../hooks/useTarotReading';
import { audioManager } from '../components/speech/audioManager';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { useMessageProcessor } from '../hooks/useMessageProcessor';
import { HistorySession } from '../types/history';

// 扩展Window接口，添加全局锁
declare global {
  interface Window {
    tarotGlobalLocks?: Record<string, boolean>;
  }
}

const TarotResultPage = () => {
  const { t, i18n } = useTranslation();
  const { navigate } = useLanguageNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { user } = useUser();
  const { theme } = useTheme();
  const { setOpenDropdown } = useDropdown();
  const isDark = theme === 'dark';
  const [showConfirmExit, setShowConfirmExit] = useState(false);
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  const [selectedCards, setSelectedCards] = useState<any[]>([]);
  const [spread, setSpread] = useState(JSON.parse(localStorage.getItem('selectedSpread') || 'null'));
  const [reader, setReader] = useState<Reader | null>(null);
  const [showContent, setShowContent] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [followUpQuestion, setFollowUpQuestion] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDeepAnalysis, setShowDeepAnalysis] = useState(false);
  const [initialReadingCompleted, setInitialReadingCompleted] = useState(false);
  const [deepAnalysisLoading, setDeepAnalysisLoading] = useState(false);
  // 暂时保留变量声明但不使用
  // const [isGeneratingDeepAnalysis, setIsGeneratingDeepAnalysis] = useState(false);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(false);
  const [hasUsedDeepAnalysis, setHasUsedDeepAnalysis] = useState(false);
  const [hasUsedFollowup, setHasUsedFollowup] = useState(false);
  // 暂时保留变量声明但不使用
  // const [receivingStreamContent, setReceivingStreamContent] = useState(false);
  // const [progressBarCompleted, setProgressBarCompleted] = useState(false);
  const [receivingStreamContent] = useState(false); // 保留变量但移除setter
  const [progressBarCompleted] = useState(false); // 保留变量但移除setter

  const [hasEthicalIssue, setHasEthicalIssue] = useState(false);
  const [hasLanguageParam, setHasLanguageParam] = useState(false);
  const [isSharedView, setIsSharedView] = useState(false);
  const [sharedSessionId, setSharedSessionId] = useState<string | null>(null);
  const [isLoadingSharedReading, setIsLoadingSharedReading] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [sharedHistorySession, setSharedHistorySession] = useState<HistorySession | null>(null);
  
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // 使用消息处理器处理分享的解读结果
  const processedSharedMessages = useMessageProcessor(sharedHistorySession);
  
  // 添加一个ref来保存navigate函数，避免useEffect依赖问题
  const navigateRef = useRef(navigate);
  
  // 更新navigateRef当navigate变化时
  useEffect(() => {
    navigateRef.current = navigate;
  }, [navigate]);
  
  // 创建稳定的导航函数
  const navigateToHome = useCallback(() => {
    navigateRef.current('/home', { replace: true });
  }, []);

  const {
    isGenerating: useTarotProgressIsGenerating,
    startProgress,
    markApiReceived,
    completeProgress
  } = useTarotProgress({
    duration: 35000,
    onComplete: () => {
      // 确保进度状态重置
      // console.log('Basic reading progress completed');
    }
  });

  // 保留深度解析进度值，但注释掉进度控制函数
  const {
    progress: deepAnalysisProgressValue,
    isGenerating: _,
    // 以下函数暂时不使用
    // startProgress: startDeepAnalysisProgress,
    // markApiReceived: markDeepAnalysisReceived,
    // completeProgress: completeDeepAnalysisProgress
  } = useTarotProgress({
    duration: 45000,
    onComplete: () => {
    }
  });

  // 使用自定义hook处理深度解析 - 暂时注释掉以解决TS警告
  // const handleDeepAnalysisClick = useDeepAnalysis({
  //   user, 
  //   navigate: navigateRef.current,
  //   hasUsedDeepAnalysis,
  //   isGeneratingDeepAnalysis,
  //   t,
  //   setShowVipPrompt,
  //   setIsGeneratingDeepAnalysis,
  //   setProgressBarCompleted,
  //   setReceivingStreamContent,
  //   setMessages, 
  //   setShouldAutoScroll,
  //   messagesContainerRef,
  //   setDeepAnalysisLoading,
  //   startDeepAnalysisProgress,
  //   markDeepAnalysisReceived,
  //   completeDeepAnalysisProgress,
  //   progressBarCompleted,
  //   receivingStreamContent,
  //   deepAnalysisLoading,
  //   getFontClass: () => getFontClass(i18n.language),
  //   setShowDeepAnalysis,
  //   setHasUsedDeepAnalysis
  // });

  // 使用新的useTarotReading hook来处理塔罗牌解读的生成
  const { executeTarotReading } = useTarotReading({
    setShowContent,
    setMessages,
    setShouldAutoScroll,
    setInitialReadingCompleted,
    setDeepAnalysisLoading,
    setIsGeneratingDeepAnalysis: () => {}, // 提供空函数代替
    markApiReceived,
    completeProgress
  });

  // 检测是否是分享链接
  useEffect(() => {
    const isShared = searchParams.get('shared') === 'true';
    const sessionId = searchParams.get('sessionId');
    
    setIsSharedView(isShared);
    if (isShared && sessionId) {
      setSharedSessionId(sessionId);
    }
  }, [searchParams]);

  // 处理分享链接的加载逻辑
  useEffect(() => {
    const loadSharedReading = async () => {
      if (isSharedView && sharedSessionId) {
        try {
          setIsLoadingSharedReading(true);
          
          // 调用后端API获取分享的解读结果
          const response = await axiosInstance.get(`/api/reading/shared/${sharedSessionId}`);
          
          if (response.data && response.data.session) {
            const sharedSession = response.data.session;

            // 更新状态
            setSelectedCards(sharedSession.selectedCards || []);
            setSpread(sharedSession.selectedSpread);
            setReader(sharedSession.selectedReader);
            setShowContent(true);
            setInitialReadingCompleted(true);

            // 将分享数据转换为历史会话格式，以便使用useMessageProcessor
            // 使用稳定的时间戳，避免无限循环
            const historySession: HistorySession = {
              question: sharedSession.question || '',
              readingResult: sharedSession.readingResult,
              selectedCards: sharedSession.selectedCards || [],
              selectedReader: sharedSession.selectedReader,
              selectedSpread: sharedSession.selectedSpread,
              deepAnalysis: undefined,
              dialogHistory: sharedSession.dialogHistory || [], // 包含追问对话历史
              timestamp: '2024-01-01T00:00:00.000Z', // 使用固定时间戳
              status: 'completed'
            };

            setSharedHistorySession(historySession);

            // 保存到localStorage以便后续操作
            localStorage.setItem('userQuestion', sharedSession.question || '');
            localStorage.setItem('selectedSpread', JSON.stringify(sharedSession.selectedSpread || {}));
            localStorage.setItem('selectedReader', JSON.stringify(sharedSession.selectedReader || {}));
            localStorage.setItem('selectedCards', JSON.stringify(sharedSession.selectedCards || []));
            localStorage.setItem('readingResult', typeof sharedSession.readingResult === 'string' ? sharedSession.readingResult : JSON.stringify(sharedSession.readingResult));
            localStorage.setItem('sessionId', sharedSession.id);
          }
        } catch (error) {
          console.error('加载分享解读结果失败:', error);
          // 显示错误消息
          setShowContent(true);
          setMessages([
            { 
              type: 'reader', 
              content: t('reading.shared_reading_error', '无法加载分享的解读结果，请稍后重试。'),
              className: 'font-sans error-message'
            }
          ]);
        } finally {
          setIsLoadingSharedReading(false);
        }
      }
    };
    
    loadSharedReading();
  }, [isSharedView, sharedSessionId, t]);

  // 从 localStorage 读取数据，但仅在非分享视图下执行
  useEffect(() => {
    // 如果是分享视图且正在加载，则跳过从localStorage读取
    if (isSharedView && (isLoadingSharedReading || sharedSessionId)) {
      return;
    }
    
    const loadSelectedCards = () => {
      const cardsData = JSON.parse(localStorage.getItem('selectedCards') || '[]');
      // 确保每个卡牌都有完整的信息
      const cardsWithInfo = cardsData.map((card: any) => {
        const cardInfo = TAROT_CARDS.find((c: TarotCard) => c.id === card.id);
        return {
          ...card,
          nameEn: cardInfo?.nameEn || card.nameEn || card.name
        };
      });
      setSelectedCards(cardsWithInfo);
    };

    const savedReader = localStorage.getItem('selectedReader');
    if (savedReader) {
      try {
        const readerObj = JSON.parse(savedReader);
        setReader(readerObj);
      } catch (error) {
        // console.error('Error parsing reader data:', error);
      }
    }

    // 检查高维建议和深度解析的状态
    const sessionId = localStorage.getItem('sessionId');
    if (sessionId) {
      const analysisShown = localStorage.getItem(`${sessionId}_analysisShown`) === 'true';
      setShowDeepAnalysis(analysisShown);
    }

    loadSelectedCards();
  }, [isSharedView, isLoadingSharedReading, sharedSessionId]);

  // Auto scroll to bottom when messages update
  useEffect(() => {
    if (!shouldAutoScroll) return;

    const messagesContainer = messagesContainerRef.current;
    if (!messagesContainer) return;

    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    setShouldAutoScroll(false);
  }, [messages, shouldAutoScroll]);

  // 组件卸载时停止音频播放
  useEffect(() => {
    return () => {
      // 停止所有正在播放的音频
      audioManager.stopAllAudio();
    };
  }, []);

  // 使用useTarotReading hook执行塔罗牌解读
  useEffect(() => {
    // 如果是分享视图且已有共享会话ID，则跳过执行解读
    if (isSharedView && sharedSessionId) {
      return;
    }

    const cleanup = executeTarotReading();
    return cleanup;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSharedView, sharedSessionId]);

  // 在分享视图下，使用处理过的消息
  useEffect(() => {
    if (isSharedView && processedSharedMessages.length > 0) {
      // 转换消息类型以匹配tarot.ts中的Message接口
      const convertedMessages: Message[] = processedSharedMessages.map(msg => ({
        type: msg.type as 'user' | 'reader',
        content: msg.content,
        className: undefined,
        isParagraph: undefined,
        id: undefined
      }));
      setMessages(convertedMessages);
    }
  }, [isSharedView, processedSharedMessages]);

  // 在消息更新时保存到localStorage并检查伦理问题
  useEffect(() => {
    const sessionId = localStorage.getItem('sessionId');
    if (sessionId && messages.length > 0) {
      localStorage.setItem(`${sessionId}_messages`, JSON.stringify(messages));
      
      // 检查是否存在伦理问题干预信息
      const lastMessage = messages[messages.length - 1];
      if (lastMessage && lastMessage.type === 'reader') {
        // 从EventSource响应中获取的hasEthicalIssue标记
        const hasEthicalIssueFlag = localStorage.getItem(`${sessionId}_hasEthicalIssue`) === 'true';
        if (hasEthicalIssueFlag) {
          setHasEthicalIssue(true);
        }
        
        // 检查消息内容是否包含伦理干预信息的特征（以防hasEthicalIssue标记丢失）
        const ethicalPatterns = [
          '⚠️ 重要提示',
          '⚠️ 提示',
          '⚠️ 内容提示',
          '⚠️ Important Notice',
          '⚠️ Notice',
          '⚠️ Content Notice',
          '⚠️ 重要なお知らせ',
          '⚠️ お知らせ'
        ];
        
        if (lastMessage.content && ethicalPatterns.some(pattern => lastMessage.content.includes(pattern))) {
          setHasEthicalIssue(true);
        }
      }
    }
  }, [messages]);



  // 获取翻译后的牌阵信息
  const getTranslatedSpreadInfo = () => {
    if (!spread) return null;

    const spreadId = spread.id.replace(/-/g, '_'); // 将 id 中的连字符替换为下划线
    if (Object.keys(t('spreads', { returnObjects: true })).includes(spreadId)) {
      // 如果在翻译文件中存在该牌阵的翻译
      return {
        name: t(`spreads.${spreadId}.name`),
        description: t(`spreads.${spreadId}.description`),
        positions: spread.positions ? spread.positions.map((_: string, index: number) => {
          const positionKeys = Object.keys(t(`spreads.${spreadId}.positions`, { returnObjects: true }));
          return t(`spreads.${spreadId}.positions.${positionKeys[index]}`);
        }) : []
      };
    }
    // 如果没有翻译，返回原始值
    return {
      name: spread.name,
      description: spread.description,
      positions: spread.positions || []
    };
  };

  const translatedSpreadInfo = getTranslatedSpreadInfo();

  // 在页面加载时检查用户是否已使用过深度解析和追问
  useEffect(() => {
    const checkUserUsageStatus = async () => {
      if (!user) return;
      
      // 只对非VIP用户进行检查
      if (user.vipStatus !== 'active') {
        try {
          const [hasUsedDeep, hasUsedFollowupResult] = await Promise.all([
            checkUserDeepAnalysisUsage(),
            checkUserFollowupUsage()
          ]);
          setHasUsedDeepAnalysis(hasUsedDeep);
          setHasUsedFollowup(hasUsedFollowupResult);
        } catch (error) {
          // console.error('检查用户功能使用情况失败:', error);
          // 默认设置为已使用过，确保安全
          setHasUsedDeepAnalysis(true);
          setHasUsedFollowup(true);
        }
      }
    };

    checkUserUsageStatus();
  }, [user]);

  // 使用useFollowUp hook处理追问功能
  const handleFollowUpSubmit = useFollowUp({
    user,
    hasUsedFollowup,
    isSubmitting,
    followUpQuestion,
    setFollowUpQuestion,
    setIsSubmitting,
    setShowVipPrompt,
    setMessages,
    messagesContainerRef,
    t,
    startProgress,
    markApiReceived,
    completeProgress,
    setHasUsedFollowup
  });

  // 当确认对话框显示时，确保关闭所有下拉菜单
  useEffect(() => {
    if (showConfirmExit) {
      setOpenDropdown(null);
    }
  }, [showConfirmExit, setOpenDropdown]);

  // 修改退出确认处理函数
  const handleExitConfirm = useCallback(() => {
    setShowConfirmExit(false);
    // 停止所有音频播放
    audioManager.stopAllAudio();
    
    // 清除所有相关的缓存
    localStorage.removeItem('selectedReader');
    localStorage.removeItem('selectedSpread');
    localStorage.removeItem('userQuestion');
    localStorage.removeItem('tarotReadingState');
    localStorage.removeItem('tarotDialogState');
    localStorage.removeItem('selectedCards');
    localStorage.removeItem('sessionId');
    localStorage.removeItem('readingResult');
    // 清除推荐相关的缓存
    localStorage.removeItem('spreadRecommendation');
    localStorage.removeItem('recommendationQuestion');
    
    // 使用带语言参数的路径导航
    navigateToHome();
  }, [navigateToHome]);

  const handleExitCancel = () => {
    setShowConfirmExit(false);
  };

  // 获取确认消息
  const getConfirmMessage = () => {
    const hasResult = localStorage.getItem('readingResult') !== null;
    if (hasResult) {
      return t('reading.confirm_exit'); // "占卜结果已保存，是否退出占卜？"
    }
    return t('reading.confirm_exit_progress'); // "退出将丢失进度，是否确认退出"
  };

  // 检测当前路径是否带有语言参数
  useEffect(() => {
    // 检查路径是否匹配 /:lang/tarot-result 模式
    const hasLangParam = /^\/[a-z]{2}(-[A-Z]{2})?\/tarot-result/.test(location.pathname);
    setHasLanguageParam(hasLangParam);
  }, [location.pathname]);

  // 组件挂载时检查localStorage中的hasEthicalIssue标记
  useEffect(() => {
    const sessionId = localStorage.getItem('sessionId');
    if (sessionId) {
      const hasEthicalIssueFlag = localStorage.getItem(`${sessionId}_hasEthicalIssue`) === 'true';
      if (hasEthicalIssueFlag) {
        setHasEthicalIssue(true);
      }
    }
  }, []);



  return (
    <div className="min-h-screen flex flex-col relative">
      <SEO />
      <NavbarWrapper />
      <LandingBackground />
      
      {/* 添加Markdown样式 */}
      <style dangerouslySetInnerHTML={{ __html: isDark ? markdownStyles : markdownStyles + lightModeMarkdownStyles }} />
      
      <div className="min-h-screen flex flex-col">
        {/* 添加页面顶部标题，根据是否有语言参数调整间距 */}
        <div className="flex-grow relative z-10">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-2">
            <div className={`w-full text-center ${hasLanguageParam ? 'mt-8 sm:mt-10' : 'mt-24 sm:mt-28'}`}>
              <h1 className={`main-title mb-1 hidden sm:block ${getFontClass(i18n.language)}`}>{t('reading.page_title', '塔罗解读结果')}</h1>
              <p className={`sub-title mb-2 ${getFontClass(i18n.language)}`}></p>
            </div>
          </div>
        </div>
        
        <div className="flex-1 max-w-6xl w-full mx-auto px-2 lg:px-8 py-0">
          {/* 加载指示器 */}
          {isLoadingSharedReading && (
            <div className="flex flex-col items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
              <p className={`text-lg ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                {t('reading.loading_shared_reading', '正在加载分享的解读结果...')}
              </p>
            </div>
          )}
          
          <AnimatePresence>
            {showContent && !isLoadingSharedReading && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="h-full flex flex-col gap-2 sm:gap-3 mt-0"
              >
                {/* Reading content */}
                <TarotReadingContent 
                  messages={messages}
                  reader={reader}
                  initialReadingCompleted={initialReadingCompleted}
                  deepAnalysisLoading={deepAnalysisLoading}
                  isSubmitting={isSubmitting}
                  receivingStreamContent={receivingStreamContent}
                  progressBarCompleted={progressBarCompleted}
                  showDeepAnalysis={showDeepAnalysis}
                  deepAnalysisProgressValue={deepAnalysisProgressValue}
                  messagesContainerRef={messagesContainerRef}
                  // 隐藏深度解析按钮，注释掉相关属性但提供空函数以满足类型要求
                  // onDeepAnalysisClick={handleDeepAnalysisClick}
                  onDeepAnalysisClick={() => {}}
                  onSubmitFollowUp={handleFollowUpSubmit}
                  followUpQuestion={followUpQuestion}
                  setFollowUpQuestion={setFollowUpQuestion}
                  onShowFeedback={() => setShowVipPrompt(true)}
                  hasUsedDeepAnalysis={hasUsedDeepAnalysis}
                  hasUsedFollowup={hasUsedFollowup}
                  useTarotProgressIsGenerating={useTarotProgressIsGenerating}
                  user={user}
                  sessionId={localStorage.getItem('sessionId') || undefined}
                  hasEthicalIssue={hasEthicalIssue}
                  isSharedView={isSharedView}
                  onShowShare={() => setShowShareDialog(true)}
                  onShowComment={() => setShowCommentDialog(true)}
                />

                {/* Selected cards display */}
                <TarotCardDisplay 
                  selectedCards={selectedCards}
                  translatedSpreadInfo={translatedSpreadInfo}
                />

                {/* Comment section - 只在非分享视图或已加载完成时显示 */}
                {(!isSharedView || !isLoadingSharedReading) && (
                  <div className="mt-12 mb-8">
                    <CommentSection
                      sessionId={sharedSessionId || localStorage.getItem('sessionId') || undefined}
                      pageType="tarot-result"
                      className=""
                      showWriteComment={!isSharedView && !!user}
                      onShowAllComments={() => setShowCommentDialog(true)}
                    />
                  </div>
                )}

                {/* Continue exploring button */}
                <div className="flex justify-center gap-4 mb-8">
                  <ContinueExploreButton
                    onButtonClick={() => setShowConfirmExit(true)}
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        <FooterWithNavigation />
      </div>

      {/* VIP提示弹窗 */}
      <VipPromptDialog 
        isOpen={showVipPrompt} 
        onCancel={() => setShowVipPrompt(false)}
      />

      {/* 退出确认弹窗 */}
      <ConfirmDialog
        isOpen={showConfirmExit}
        message={getConfirmMessage()}
        onConfirm={handleExitConfirm}
        onCancel={handleExitCancel}
      />



      {/* 分享对话框 */}
      <ShareDialog
        isOpen={showShareDialog}
        onClose={() => setShowShareDialog(false)}
        pageType="tarot-result"
        sessionId={sharedSessionId || localStorage.getItem('sessionId') || undefined}
      />

      {/* 评论对话框 */}
      <CommentDialog
        isOpen={showCommentDialog}
        onClose={() => setShowCommentDialog(false)}
        pageType="tarot-result"
        sessionId={sharedSessionId || localStorage.getItem('sessionId') || undefined}
      />
    </div>
  );
};

export default TarotResultPage; 