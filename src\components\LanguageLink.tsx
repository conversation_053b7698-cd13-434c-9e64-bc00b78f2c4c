import React from 'react';
import { Link as RouterLink, LinkProps } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import { availableLanguages } from '../i18n';

/**
 * 自定义Link组件，用于处理带有语言参数的链接
 * 如果当前URL已经包含语言参数，则保持该语言参数
 * 如果当前URL不包含语言参数，则不添加语言参数
 */
export const LanguageLink: React.FC<LinkProps> = ({ to, children, ...rest }) => {
  const location = useLocation();
  
  // 检查当前URL是否已经包含语言参数
  const currentPath = location.pathname;
  const pathMatch = currentPath.match(/^\/([^\/]+)(\/.*)?$/);
  const isInLanguageRoute = pathMatch && availableLanguages.includes(pathMatch[1]);
  
  let targetPath = typeof to === 'string' ? to : to.pathname || '';
  
  // 只有当当前URL带有语言参数时，才在链接中保持该语言
  if (isInLanguageRoute && typeof to === 'string') {
    const currentLang = pathMatch[1];
    // 如果目标路径不是以当前语言开头，则添加当前语言
    if (!targetPath.startsWith(`/${currentLang}`)) {
      // 处理根路径的特殊情况
      if (targetPath === '/') {
        targetPath = `/${currentLang}`;
      } else if (targetPath.startsWith('/')) {
        // 移除开头的斜杠，避免出现双斜杠
        const cleanPath = targetPath.substring(1);
        targetPath = `/${currentLang}/${cleanPath}`;
      } else {
        targetPath = `/${currentLang}/${targetPath}`;
      }
    }
  } else if (isInLanguageRoute && typeof to === 'object' && to.pathname) {
    const currentLang = pathMatch[1];
    // 如果目标路径不是以当前语言开头，则添加当前语言
    if (!to.pathname.startsWith(`/${currentLang}`)) {
      // 处理根路径的特殊情况
      if (to.pathname === '/') {
        to = { ...to, pathname: `/${currentLang}` };
      } else if (to.pathname.startsWith('/')) {
        // 移除开头的斜杠，避免出现双斜杠
        const cleanPath = to.pathname.substring(1);
        to = { ...to, pathname: `/${currentLang}/${cleanPath}` };
      } else {
        to = { ...to, pathname: `/${currentLang}/${to.pathname}` };
      }
    }
  }
  
  // 将处理后的路径传递给RouterLink
  return <RouterLink to={typeof to === 'string' ? targetPath : to} {...rest}>{children}</RouterLink>;
};

export default LanguageLink; 