import React, { useState, useEffect } from 'react';
// import { useTranslation } from 'react-i18next';
import axiosInstance from '../utils/axios';

interface ReaderVoice {
  readerId: string;
  readerName: string;
  voiceId: string;
}

interface TtsRecord {
  id: string;
  text: string;
  voice: string;
  reader_id?: string;
  created_at: string;
  message_id?: string;
  session_id: string;
}

interface TtsVoicesConfig {
  basicTtsVoices: Record<string, string>;
  proTtsVoices: Record<string, string>;
}

// 根据从服务器获取的音色配置生成ReaderVoice数组
const getVoicesByLanguage = (config: TtsVoicesConfig | null, language: string): ReaderVoice[] => {
  if (!config) return [];
  
  const { basicTtsVoices, proTtsVoices } = config;
  const langSuffix = language !== 'zh' ? `_${language}` : '';
  
  // 获取对应语言的音色
  const getVoice = (readerId: string, isBasic: boolean) => {
    if (isBasic) {
      return basicTtsVoices[`${readerId}${langSuffix}`] || basicTtsVoices[`default${langSuffix}`] || basicTtsVoices.default;
    } else {
      return proTtsVoices[`${readerId}${langSuffix}`] || proTtsVoices[`default${langSuffix}`] || proTtsVoices.default;
    }
  };
  
  // 构建占卜师音色配置
  return [
    { 
      readerId: 'basic', 
      readerName: language === 'zh' ? '茉伊' : language === 'en' ? 'Molly' : 'モリー', 
      voiceId: getVoice('basic', true)
    },
    { 
      readerId: 'elias', 
      readerName: language === 'zh' ? '林曜' : language === 'en' ? 'Elias' : 'エリアス', 
      voiceId: getVoice('elias', false)
    },
    { 
      readerId: 'claire', 
      readerName: language === 'zh' ? '苏谨' : language === 'en' ? 'Claire' : 'クレア', 
      voiceId: getVoice('claire', false)
    },
    { 
      readerId: 'raven', 
      readerName: language === 'zh' ? '渡鸦' : language === 'en' ? 'Raven' : 'レイヴン', 
      voiceId: getVoice('raven', false)
    },
    { 
      readerId: 'aurora', 
      readerName: language === 'zh' ? '月熙' : language === 'en' ? 'Aurora' : 'オーロラ', 
      voiceId: getVoice('aurora', false)
    },
    { 
      readerId: 'vincent', 
      readerName: language === 'zh' ? '文森特' : language === 'en' ? 'Vincent' : 'ヴィンセント', 
      voiceId: getVoice('vincent', false)
    },
  ];
};

const TtsGeneratorPage: React.FC = () => {
  // const { i18n } = useTranslation();
  const [readerVoices, setReaderVoices] = useState<ReaderVoice[]>([]);
  const [scripts, setScripts] = useState<Record<string, string>>({});
  const [generationStatus, setGenerationStatus] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId] = useState('reader_intro');
  const [audioElements, setAudioElements] = useState<Record<string, HTMLAudioElement | null>>({});
  const [ttsRecords, setTtsRecords] = useState<TtsRecord[]>([]);
  const [querySessionId, setQuerySessionId] = useState('');
  const [queryStatus, setQueryStatus] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('zh'); // 默认中文
  const [voicesConfig, setVoicesConfig] = useState<TtsVoicesConfig | null>(null);
  
  // 添加日志记录
  const [logs, setLogs] = useState<string[]>([]);
  
  // 重写console.log以捕获日志
  useEffect(() => {
    const originalConsoleLog = console.log;
    console.log = (...args) => {
      originalConsoleLog(...args);
      const logMessage = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ');
      
      setLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${logMessage}`]);
    };
    
    return () => {
      console.log = originalConsoleLog;
    };
  }, []);
  
  // 从服务器获取音色配置
  useEffect(() => {
    const fetchVoicesConfig = async () => {
      try {
        const response = await axiosInstance.get('/tts/voices');
        setVoicesConfig(response.data);
        console.log('[配置] 从服务器获取音色配置成功');
      } catch (error) {
        console.error('[配置] 获取音色配置失败:', error);
      }
    };
    
    fetchVoicesConfig();
  }, []);
  
  // 初始化占卜师数据
  useEffect(() => {
    // 初始化脚本
    updateScriptsByLanguage('zh');
    
    // 设置默认查询sessionId为统一会话ID
    setQuerySessionId(sessionId);
    
    // 如果已经获取到音色配置，则设置初始音色
    if (voicesConfig) {
      setReaderVoices(getVoicesByLanguage(voicesConfig, 'zh'));
      console.log('[配置] 使用服务器音色配置初始化占卜师数据');
    }
  }, [sessionId, voicesConfig]);
  
  // 根据选择的语言更新脚本
  const updateScriptsByLanguage = (lang: string) => {
    const initialScripts: Record<string, string> = {};
    
    if (lang === 'zh') {
      initialScripts['basic'] = `你好，我是茉伊，我将用塔罗牌为你进行解读。`;
      initialScripts['elias'] = `你好，我是林曜，我将用塔罗牌为你进行解读。`;
      initialScripts['claire'] = `你好，我是苏谨，我将用塔罗牌为你进行解读。`;
      initialScripts['raven'] = `你好，我是渡鸦，我将用塔罗牌为你进行解读。`;
      initialScripts['aurora'] = `你好，我是月熙，我将用塔罗牌为你进行解读。`;
      initialScripts['vincent'] = `你好，我是文森特，我将用塔罗牌为你进行解读。`;
    } else if (lang === 'en') {
      initialScripts['basic'] = `Hello, I'm Molly. I will read the tarot cards for you.`;
      initialScripts['elias'] = `Hello, I'm Elias. I will read the tarot cards for you.`;
      initialScripts['claire'] = `Hello, I'm Claire. I will read the tarot cards for you.`;
      initialScripts['raven'] = `Hello, I'm Raven. I will read the tarot cards for you.`;
      initialScripts['aurora'] = `Hello, I'm Aurora. I will read the tarot cards for you.`;
      initialScripts['vincent'] = `Hello, I'm Vincent. I will read the tarot cards for you.`;
    } else if (lang === 'ja') {
      initialScripts['basic'] = `こんにちは、モリーです。タロットカードを読んであげましょう。`;
      initialScripts['elias'] = `こんにちは、エリアスです。タロットカードを読んであげましょう。`;
      initialScripts['claire'] = `こんにちは、クレアです。タロットカードを読んであげましょう。`;
      initialScripts['raven'] = `こんにちは、レイヴンです。タロットカードを読んであげましょう。`;
      initialScripts['aurora'] = `こんにちは、オーロラです。タロットカードを読んであげましょう。`;
      initialScripts['vincent'] = `こんにちは、ヴィンセントです。タロットカードを読んであげましょう。`;
    }
    
    setScripts(initialScripts);
  };
  
  // 处理语言切换
  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLang = e.target.value;
    setSelectedLanguage(newLang);
    updateScriptsByLanguage(newLang);
    
    // 如果已经获取到音色配置，则更新音色
    if (voicesConfig) {
      setReaderVoices(getVoicesByLanguage(voicesConfig, newLang));
      console.log(`[语言切换] 切换到${newLang}语言，更新音色配置`);
    }
  };
  
  // 为单个占卜师生成TTS
  const generateSingleTts = async (readerId: string, text: string) => {
    try {
      setGenerationStatus(prev => ({ ...prev, [readerId]: '生成中...' }));
      
      // 使用readerId_para_0格式作为messageId，如果是basic占卜师，则使用base_para_0
      // 添加语言前缀以区分不同语言的缓存
      const messageId = readerId === 'basic' 
        ? `base_${selectedLanguage}_para_0` 
        : `${readerId}_${selectedLanguage}_para_0`;
      // 构建缓存键格式
      const cacheKey = `${sessionId}_${messageId}`;
      
      // 添加日志输出
      console.log(`[TTS] 正在为占卜师 ${readerId} 生成语音，参数:`, {
        text,
        readerId,
        sessionId,
        messageId,
        cacheKey,
        language: selectedLanguage
      });
      
      // 查找对应的音色
      const reader = readerVoices.find(r => r.readerId === readerId);
      const voiceId = reader ? reader.voiceId : '';
      
      console.log(`[TTS] 使用音色: ${voiceId} for ${readerId}, 语言: ${selectedLanguage}`);
      
      // 调用TTS API
      const response = await axiosInstance.post('/tts', {
        text,
        readerId,
        sessionId,
        messageId,
        cacheKey,
        voice: voiceId, // 直接指定音色
        language: selectedLanguage // 添加语言参数
      }, {
        responseType: 'arraybuffer'
      });
      
      // 将二进制音频数据转换为Blob
      const blob = new Blob([response.data], { type: 'audio/mpeg' });
      const url = URL.createObjectURL(blob);
      
      // 创建音频元素
      const audio = new Audio(url);
      
      // 添加音频加载事件，打印一些元数据
      audio.onloadedmetadata = () => {
        console.log(`[TTS] 音频加载完成: ${readerId}, 时长=${audio.duration}秒`);
      };
      
      // 保存音频元素以便测试播放
      setAudioElements(prev => ({
        ...prev,
        [readerId]: audio
      }));
      
      setGenerationStatus(prev => ({ ...prev, [readerId]: '生成成功' }));
      
      // 查询最新TTS记录
      fetchTtsRecords(sessionId);
    } catch (error) {
      console.error(`生成${readerId}的TTS失败:`, error);
      setGenerationStatus(prev => ({ ...prev, [readerId]: '生成失败' }));
    }
  };
  
  // 测试播放音频
  const playAudio = (readerId: string) => {
    // 使用sessionId + messageId(readerId_para_0)查询并播放
    // 添加语言前缀以确保播放正确语言的音频
    const messageId = readerId === 'basic' 
      ? `base_${selectedLanguage}_para_0` 
      : `${readerId}_${selectedLanguage}_para_0`;
    playAudioBySessionAndMessage(sessionId, messageId);
  };
  
  // 下载音频文件
  const downloadAudio = (readerId: string) => {
    const audio = audioElements[readerId];
    if (!audio) {
      alert(`${readerId}没有可用的音频，请先生成TTS`);
      return;
    }
    
    // 获取音频源URL
    const audioSrc = audio.src;
    
    // 创建下载链接
    const a = document.createElement('a');
    a.href = audioSrc;
    a.download = `${readerId}_intro.mp3`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    console.log(`[TTS] 下载占卜师 ${readerId} 的音频`);
  };
  
  // 生成所有占卜师的TTS
  const generateAllTts = async () => {
    setIsLoading(true);
    
    for (const reader of readerVoices) {
      const script = scripts[reader.readerId];
      if (script) {
        await generateSingleTts(reader.readerId, script);
      }
    }
    
    setIsLoading(false);
  };
  
  // 测试不同的TTS API请求方式
  const testSpecificTtsApi = async (readerId: string) => {
    try {
      const text = scripts[readerId] || `你好，我是${readerId}，这是一条测试语音。`;
      
      // 从readerVoices中获取当前语言下的音色
      const readerVoice = readerVoices.find(r => r.readerId === readerId);
      const voiceId = readerVoice?.voiceId || '';
      
      console.log(`[TEST] 测试占卜师 ${readerId} 的TTS API，音色: ${voiceId}`);
      
      // 尝试明确指定使用pro-tts
      if (readerId !== 'basic') {
        // 添加语言前缀以区分不同语言的缓存
        const messageId = readerId === 'basic' 
          ? `base_${selectedLanguage}_para_0` 
          : `${readerId}_${selectedLanguage}_para_0`;
        const response = await axiosInstance.post('/pro-tts', {
          text,
          readerId,
          sessionId,
          voice: voiceId,
          messageId,
          cacheKey: `${sessionId}_${messageId}`,
          language: selectedLanguage // 添加语言参数
        }, {
          responseType: 'arraybuffer'
        });
        
        // 处理响应
        const blob = new Blob([response.data], { type: 'audio/mpeg' });
        const url = URL.createObjectURL(blob);
        const audio = new Audio(url);
        
        audio.onloadedmetadata = () => {
          console.log(`[TEST] 直接调用pro-tts成功: ${readerId}, 时长=${audio.duration}秒`);
        };
        
        setAudioElements(prev => ({
          ...prev,
          [`${readerId}_proTts`]: audio
        }));
        
        setGenerationStatus(prev => ({ ...prev, [`${readerId}_proTts`]: '直接调用成功' }));
      }
    } catch (error) {
      console.error(`[TEST] 直接调用TTS API失败:`, error);
      setGenerationStatus(prev => ({ ...prev, [`${readerId}_proTts`]: '直接调用失败' }));
    }
  };
  
  // 修改脚本
  const handleScriptChange = (readerId: string, value: string) => {
    setScripts(prev => ({
      ...prev,
      [readerId]: value
    }));
  };
  
  // 查询特定sessionId的TTS记录
  const fetchTtsRecords = async (sid: string) => {
    try {
      setQueryStatus('查询中...');
      const response = await axiosInstance.get(`/tts/session/${sid}`);
      if (response.data && response.data.records) {
        setTtsRecords(response.data.records);
        setQueryStatus(`成功获取 ${response.data.records.length} 条记录`);
      } else {
        setTtsRecords([]);
        setQueryStatus('未找到记录');
      }
    } catch (error) {
      console.error('获取TTS记录失败:', error);
      setQueryStatus('查询失败');
      setTtsRecords([]);
    }
  };
  
  // 根据会话ID和消息ID播放音频
  const playAudioBySessionAndMessage = async (sid: string, mid: string) => {
    try {
      console.log(`[TTS] 尝试播放 session=${sid}, message=${mid} 的音频`);
      
      // 使用现有的API接口查询该会话的所有TTS记录
      const response = await axiosInstance.get(`/tts/session/${sid}`);
      
      if (response.data && response.data.records && response.data.records.length > 0) {
        // 在前端筛选出对应messageId的记录
        const targetRecords = response.data.records.filter(
          (record: TtsRecord) => record.message_id === mid
        );
        
        if (targetRecords.length > 0) {
          // 获取最新的一条记录（如果有多条相同messageId的记录）
          const record = targetRecords[0];
          console.log(`[TTS] 找到音频记录:`, record);
          
          // 播放这条记录的音频
          await playAudioById(record.id);
        } else {
          console.error(`未找到 message=${mid} 的音频记录`);
          alert(`未找到占卜师 ${mid} 的音频记录，请先生成TTS`);
        }
      } else {
        console.error(`未找到会话 ${sid} 的任何音频记录`);
        alert(`未找到会话 ${sid} 的任何音频记录`);
      }
    } catch (error) {
      console.error('播放音频失败:', error);
      alert('播放音频失败');
    }
  };
  
  // 播放指定ID的音频
  const playAudioById = async (audioId: string) => {
    try {
      // 获取音频文件
      const response = await axiosInstance.get(`/tts/audio/${audioId}`, {
        responseType: 'arraybuffer'
      });
      
      // 创建音频对象
      const blob = new Blob([response.data], { type: 'audio/mpeg' });
      const url = URL.createObjectURL(blob);
      const audio = new Audio(url);
      
      // 停止所有其他音频
      Object.values(audioElements).forEach(element => {
        if (element) {
          element.pause();
          element.currentTime = 0;
        }
      });
      
      // 播放
      audio.play();
      
      // 播放完成后释放资源
      audio.onended = () => {
        URL.revokeObjectURL(url);
      };
    } catch (error) {
      console.error('播放音频失败:', error);
      alert('播放音频失败');
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">占卜师介绍TTS生成器</h1>
      <p className="mb-4">此页面用于生成占卜师介绍的语音文件并存储到数据库。会话ID: {sessionId}</p>
      
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <label className="font-medium">选择语言:</label>
          <select 
            value={selectedLanguage}
            onChange={handleLanguageChange}
            className="border rounded px-3 py-2"
          >
            <option value="zh">中文</option>
            <option value="en">英语</option>
            <option value="ja">日语</option>
          </select>
          <span className="text-sm text-blue-600">
            当前语言: {selectedLanguage === 'zh' ? '中文' : selectedLanguage === 'en' ? '英语' : '日语'}
          </span>
        </div>
        
        <div className="flex items-center mb-4">
          <div className={`text-sm ${voicesConfig ? 'text-green-600' : 'text-orange-600'}`}>
            音色配置来源: {voicesConfig ? '服务器配置 (server/config/tts_voices.js)' : '加载中...'}
          </div>
        </div>
        
        <button 
          className="bg-purple-600 text-white px-4 py-2 rounded shadow hover:bg-purple-700 disabled:opacity-50"
          onClick={generateAllTts}
          disabled={isLoading}
        >
          {isLoading ? '生成中...' : '为所有占卜师生成介绍音频'}
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {readerVoices.map(reader => (
          <div key={reader.readerId} className="border rounded-lg p-4 shadow">
            <h2 className="text-xl font-semibold mb-2">{reader.readerName} ({reader.readerId})</h2>
            <p className="text-sm text-gray-600 mb-2">
              音色: {reader.voiceId}
              <span className="ml-2 inline-block px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-700">
                {reader.readerId === 'basic' ? 'Edge TTS' : '火山引擎'}
              </span>
              <span className="ml-2 inline-block px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-700">
                {selectedLanguage === 'zh' ? '中文' : selectedLanguage === 'en' ? '英语' : '日语'}
              </span>
            </p>
            
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">介绍脚本:</label>
              <textarea 
                className="w-full border rounded p-2"
                rows={4}
                value={scripts[reader.readerId] || ''}
                onChange={(e) => handleScriptChange(reader.readerId, e.target.value)}
              />
            </div>
            
            <div className="flex flex-wrap gap-2">
              <button
                className="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600"
                onClick={() => generateSingleTts(reader.readerId, scripts[reader.readerId] || '')}
              >
                生成语音
              </button>
              <button
                className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
                onClick={() => playAudio(reader.readerId)}
                disabled={!audioElements[reader.readerId]}
              >
                播放测试
              </button>
              <button
                className="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600"
                onClick={() => downloadAudio(reader.readerId)}
                disabled={!audioElements[reader.readerId]}
              >
                下载音频
              </button>
              <button
                className="bg-orange-500 text-white px-3 py-1 rounded text-sm hover:bg-orange-600"
                onClick={() => testSpecificTtsApi(reader.readerId)}
              >
                测试API
              </button>
            </div>
            
            {generationStatus[reader.readerId] && (
              <p className="mt-2 text-sm">
                状态: {generationStatus[reader.readerId]}
              </p>
            )}
          </div>
        ))}
      </div>
      
      {/* 查询TTS记录 */}
      <div className="mt-10 border-t pt-6">
        <h2 className="text-xl font-bold mb-4">查询TTS记录</h2>
        
        <div className="flex items-center mb-4">
          <input
            type="text"
            className="border rounded px-3 py-2 mr-3 flex-1"
            placeholder="输入会话ID"
            value={querySessionId}
            onChange={(e) => setQuerySessionId(e.target.value)}
          />
          <button
            className="bg-blue-600 text-white px-4 py-2 rounded shadow hover:bg-blue-700"
            onClick={() => fetchTtsRecords(querySessionId)}
          >
            查询记录
          </button>
        </div>
        
        {queryStatus && <p className="text-sm mb-4">{queryStatus}</p>}
        
        {ttsRecords.length > 0 && (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border">
              <thead>
                <tr>
                  <th className="border px-4 py-2">ID</th>
                  <th className="border px-4 py-2">文本</th>
                  <th className="border px-4 py-2">音色</th>
                  <th className="border px-4 py-2">占卜师ID</th>
                  <th className="border px-4 py-2">消息ID</th>
                  <th className="border px-4 py-2">创建时间</th>
                  <th className="border px-4 py-2">操作</th>
                </tr>
              </thead>
              <tbody>
                {ttsRecords.map(record => (
                  <tr key={record.id}>
                    <td className="border px-4 py-2 text-xs">{record.id}</td>
                    <td className="border px-4 py-2">{record.text}</td>
                    <td className="border px-4 py-2 text-xs">{record.voice}</td>
                    <td className="border px-4 py-2">{record.reader_id || '-'}</td>
                    <td className="border px-4 py-2">{record.message_id || '-'}</td>
                    <td className="border px-4 py-2 text-xs">{new Date(record.created_at).toLocaleString()}</td>
                    <td className="border px-4 py-2">
                      <button
                        className="bg-green-600 text-white px-2 py-1 rounded text-xs"
                        onClick={() => playAudioById(record.id)}
                      >
                        播放
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* 调试日志区域 */}
      <div className="mt-10 border-t pt-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">调试日志</h2>
          <button 
            className="bg-gray-500 text-white px-3 py-1 rounded text-sm"
            onClick={() => setLogs([])}
          >
            清空日志
          </button>
        </div>
        
        <div className="bg-gray-100 p-4 rounded-lg h-60 overflow-auto font-mono text-xs">
          {logs.length > 0 ? (
            logs.map((log, index) => (
              <div key={index} className="mb-1">{log}</div>
            ))
          ) : (
            <div className="text-gray-500">暂无日志</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TtsGeneratorPage; 