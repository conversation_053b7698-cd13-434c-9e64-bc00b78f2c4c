/* 基础变量 */
:root {
    /* 主色调 */
    --primary: #e2e8f0;
    --muted: #94a3b8;
    
    /* 强调色 */
    --mystic-purple: #c4b5fd;
    --wisdom-blue: #93c5fd;
    --spirit-teal: #5eead4;
    --energy-pink: #fda4af;
    --emotion-amber: #fcd34d;
    
    /* 背景色 */
    --bg-dark: rgba(30, 41, 59, 0.2);
    --bg-darker: rgba(15, 23, 42, 0.3);
    --bg-gradient: linear-gradient(180deg, 
      rgba(15, 23, 42, 0.3),
      rgba(30, 41, 59, 0.2)
    );
    
    /* 边框 */
    --border-light: rgba(148, 163, 184, 0.1);
    --border-medium: rgba(148, 163, 184, 0.2);
  }
  
  /* 容器样式 */
  .history-detail-container {
    min-height: 100vh;
    background: var(--bg-gradient);
    padding: 2rem;
    padding-top: 4rem;
  }
  
  .history-detail-content {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  /* 标题栏 */
  .history-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
  }
  
  .history-detail-title {
    @apply text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 text-transparent bg-clip-text;
  }
  
  .history-detail-timestamp {
    color: var(--mystic-purple);
    font-size: 1.1rem;
    font-weight: 500;
    margin-top: 0.5rem;
    letter-spacing: 0.05em;
    font-family: 'Helvetica Neue', Arial, sans-serif;
    display: inline-block;
  }
  
  /* 问题和牌阵容器 */
  .history-detail-section {
    background: var(--bg-darker);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid rgba(196, 181, 253, 0.2);
    margin-bottom: 2rem;
    backdrop-filter: blur(16px);
  }
  
  .history-detail-section-title {
    color: var(--mystic-purple);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }
  
  /* 卡牌网格 */
  .history-detail-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
    justify-items: center;
    max-width: 1200px;
    margin: 2rem auto;
  }
  
  .history-detail-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 200px;
    margin: 0;
    padding: 1rem;
    border-radius: 8px;
    background: var(--bg-darker);
    backdrop-filter: blur(16px);
  }
  
  .history-detail-card-image {
    width: 100%;
    height: auto;
    aspect-ratio: 1/1.67;
    object-fit: cover;
    margin-bottom: 1rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
  }
  
  .history-detail-card-name {
    color: var(--primary);
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    margin-bottom: 0.5rem;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .history-detail-card-info {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
  }
  
  .history-detail-card-info p {
    margin: 0;
    line-height: 1.2;
  }
  
  .history-detail-card.reversed .history-detail-card-image {
    transform: rotate(180deg);
  }
  
  /* 解读结果 */
  .history-detail-reading {
    background: var(--bg-darker);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid rgba(196, 181, 253, 0.2);
    backdrop-filter: blur(16px);
  }
  
  .history-detail-reading-section {
    background: var(--bg-dark);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1rem;
    backdrop-filter: blur(16px);
  }
  
  .history-detail-reading-title {
    background: linear-gradient(to right, var(--mystic-purple), var(--energy-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
  }
  
  .history-detail-reading-content {
    color: var(--primary);
    line-height: 1.7;
    font-size: 1rem;
  }
  
  .history-detail-reading-text {
    display: block;
    line-height: 1.6;
  }
  
  .history-detail-block-wrapper {
    margin: 0.8em 0;
    padding: 0;
    display: block;
    width: 100%;
  }
  
  .history-detail-quote {
    position: relative;
    padding: 0.8em 1.2em;
    background: rgba(15, 23, 42, 0.7);
    border-radius: 6px;
    color: var(--mystic-purple);
    font-style: italic;
    border-left: 3px solid var(--mystic-purple);
    display: block;
    width: 100%;
    line-height: 1.4;
    font-size: 1em;
    letter-spacing: 0.02em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 1em 0;
  }
  
  .history-detail-quote::before,
  .history-detail-quote::after {
    display: none;
  }
  
  .history-detail-quote-punctuation {
    display: inline-block;
    margin-left: -0.2em;
    color: var(--primary);
    position: relative;
    top: -0.1em;
  }
  
  .history-detail-quote-wrapper {
    display: inline-block;
    margin: 0 0.25em;
  }
  
  .history-detail-quote + .history-detail-quote {
    margin-left: 0.5em;
  }
  
  /* 隐藏文本样式 */
  .tarot-hide {
    display: none;
  }
  
  /* 特殊标记样式 */
  .history-detail-sparkles {
    color: var(--mystic-purple);
    font-weight: 500;
    padding: 0.2em 0.4em;
    background: linear-gradient(120deg, rgba(196, 181, 253, 0.1), transparent);
    border-radius: 4px;
    display: inline-block;
    margin: 0 0.2em;
  }
  
  .history-detail-card-text {
    color: var(--wisdom-blue);
    font-weight: 500;
    letter-spacing: 0.05em;
    padding: 0.3em 0.6em;
    background: linear-gradient(120deg, rgba(147, 197, 253, 0.1), transparent);
    border-radius: 4px;
    display: inline-block;
    margin: 0 0.2em;
  }
  
  .history-detail-highlight {
    color: var(--spirit-teal);
    font-weight: 500;
    padding: 0 0.2em;
  }
  
  .history-detail-energy {
    color: var(--energy-pink);
    font-weight: 500;
    padding: 0.2em 0.4em;
    background: linear-gradient(120deg, rgba(253, 164, 175, 0.1), transparent);
    border-radius: 4px;
    display: inline-block;
    margin: 0 0.2em;
  }
  
  .history-detail-transition {
    color: var(--muted);
    font-style: italic;
    text-align: center;
    padding: 1rem 0;
    margin: 1rem 0;
    position: relative;
    font-size: 1.1em;
    letter-spacing: 0.05em;
    display: block;
  }
  
  .history-detail-emotion {
    color: var(--emotion-amber);
    font-weight: 500;
    padding: 0.2em 0.4em;
    background: linear-gradient(120deg, rgba(252, 211, 77, 0.1), transparent);
    border-radius: 4px;
    display: inline-block;
    margin: 0 0.2em;
  }
  
  .history-detail-advice {
    padding: 2rem;
    margin: 2rem 0;
    background: var(--bg-darker);
    border: 1px solid var(--border-medium);
    border-radius: 16px;
    position: relative;
  }
  
  .history-detail-reading-text ol {
    list-style: none;
    counter-reset: advice-counter;
    padding: 0;
    margin: 1rem 0;
  }
  
  .history-detail-reading-text ol li {
    counter-increment: advice-counter;
    padding: 1rem 0;
    display: flex;
    align-items: flex-start;
    line-height: 1.8;
    color: var(--primary);
    letter-spacing: 0.01em;
    margin-left: 0;
    list-style-type: none;
  }
  
  .history-detail-reading-text ol li::marker {
    display: none;
  }
  
  .history-detail-reading-text ol li::before {
    content: counter(advice-counter);
    font-size: 0.9em;
    font-weight: 500;
    color: var(--spirit-teal);
    background: rgba(94, 234, 212, 0.1);
    border: 1px solid rgba(94, 234, 212, 0.2);
    border-radius: 50%;
    width: 1.8em;
    height: 1.8em;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
  }
  
  .history-detail-reading-text ol li + li {
    border-top: 1px solid var(--border-light);
  }
  
  .history-detail-reading-text ol .history-detail-card-text {
    color: var(--wisdom-blue);
    font-weight: 500;
    letter-spacing: 0.05em;
    padding: 0.3em 0.6em;
    background: linear-gradient(120deg, rgba(147, 197, 253, 0.1), transparent);
    border-radius: 4px;
    display: inline-block;
    margin: 0 0.2em;
  }
  
  .history-detail-reading-text ol .history-detail-highlight {
    color: var(--spirit-teal);
    font-weight: 500;
    padding: 0 0.2em;
  }
  
  /* emotion 标记样式 */
  .tarot-emotion {
    color: var(--emotion-amber);
    padding: 0 0.3em;
  }
  
  /* 响应式布局 */
  @media (max-width: 768px) {
    .history-detail-cards {
      grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
      gap: 1rem;
    }
  
    .history-detail-card {
      max-width: 110px;
    }
  
    .history-detail-card-name {
      font-size: 1rem;
    }
  
    .history-detail-card-info p {
      font-size: 0.9rem;
    }
  }
  
  @media (min-width: 1200px) {
    .history-detail-cards {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      max-width: 1000px;
    }
  }
  
  /* 对话框样式 */
  .dialog-section {
    margin-bottom: 1.5rem;
  }
  
  .dialog-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--spirit-teal);
    margin-bottom: 1rem;
  }
  
  .dialog-section-content {
    color: var(--text-primary);
    line-height: 1.8;
    white-space: pre-wrap;
  }
  
  .dialog-advice {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .dialog-advice-item {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    line-height: 1.8;
  }
  
  .dialog-card-text {
    color: var(--wisdom-blue);
    font-weight: 500;
    letter-spacing: 0.05em;
    padding: 0.3em 0.6em;
    background: linear-gradient(120deg, rgba(147, 197, 253, 0.1), transparent);
    border-radius: 4px;
    display: inline-block;
    margin: 0 0.2em;
  }
  
  /* 确保CardDisplay组件中的卡片宽度不被覆盖 */
  .history-detail-content .grid [class*="w-[110px]"] {
    width: 110px !important;
  }
  
  @media (min-width: 640px) {
    .history-detail-content .grid [class*="sm:w-[140px]"] {
      width: 140px !important;
    }
  }
  
  @media (min-width: 768px) {
    .history-detail-content .grid [class*="md:w-[160px]"] {
      width: 160px !important;
    }
  }
  
  @media (min-width: 1024px) {
    .history-detail-content .grid [class*="lg:w-[180px]"] {
      width: 180px !important;
    }
  }