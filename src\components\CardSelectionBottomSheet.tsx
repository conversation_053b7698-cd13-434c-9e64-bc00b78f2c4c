import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TAROT_CARDS } from '../data/tarot-cards';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSelectCard: (cardId: number) => void;
  cardImages: Map<number, string>;
}

const CardSelectionBottomSheet: React.FC<Props> = ({
  isOpen,
  onClose,
  onSelectCard,
  cardImages,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  // 根据类别筛选卡牌
  const filteredCards = TAROT_CARDS.filter(card => {
    if (selectedCategory === 'all') return true;
    if (selectedCategory === 'major') return card.id <= 21;
    
    // 根据卡牌名称判断类别
    const cardName = card.nameEn.toLowerCase();
    switch (selectedCategory) {
      case 'wands':
        return cardName.includes('wands');
      case 'cups':
        return cardName.includes('cups');
      case 'swords':
        return cardName.includes('swords');
      case 'pentacles':
        return cardName.includes('pentacles');
      default:
        return false;
    }
  });

  // 设置主题相关的样式类
  const darkModeClasses = {
    backdrop: "bg-black/60 backdrop-blur-sm",
    bottomSheet: "bg-gray-900/95 backdrop-blur-xl border-purple-500/20",
    header: "bg-gray-900/95 backdrop-blur-xl border-purple-500/20",
    categoryBtnActive: "bg-purple-600 text-white",
    categoryBtnInactive: "bg-gray-800/60 text-gray-400 hover:bg-gray-700/80 hover:text-white",
    cardNameText: "text-gray-300"
  };

  const lightModeClasses = {
    backdrop: "bg-black/30 backdrop-blur-sm",
    bottomSheet: "bg-white/95 backdrop-blur-xl border-purple-500/20",
    header: "bg-white/95 backdrop-blur-xl border-purple-500/20",
    categoryBtnActive: "bg-purple-600 text-white",
    categoryBtnInactive: "bg-gray-200/80 text-gray-600 hover:bg-gray-300/80 hover:text-gray-800",
    cardNameText: "text-gray-700"
  };

  const themeClasses = theme === 'dark' ? darkModeClasses : lightModeClasses;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={`fixed inset-0 ${themeClasses.backdrop} z-40`}
            onClick={onClose}
          />
          
          {/* 底部弹出层 */}
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className={`fixed left-0 right-0 bottom-0 h-[75vh] ${themeClasses.bottomSheet} rounded-t-3xl z-50 border-t`}
          >
            {/* 头部区域 */}
            <div className={`sticky top-0 px-4 py-2 border-b ${themeClasses.header} rounded-t-3xl`}>
              <div className="absolute left-1/2 -translate-x-1/2 -top-3">
                <div className="w-12 h-1 bg-gray-600 rounded-full"></div>
              </div>
              <div className="flex items-center justify-between mb-2">
                <h3 className={`text-lg font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-800'} font-sans`}>{t('reading.shuffle.card_selector.title')}</h3>
                <button
                  onClick={onClose}
                  className={`p-1.5 ${theme === 'dark' ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'} transition-colors`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              {/* 类别导航 */}
              <div className="grid grid-cols-2 sm:flex gap-2 pb-2">
                <div className="col-span-2 grid grid-cols-3 gap-2 sm:flex sm:gap-2">
                  <button
                    onClick={() => setSelectedCategory('all')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors w-full font-sans
                      ${selectedCategory === 'all'
                        ? themeClasses.categoryBtnActive
                        : themeClasses.categoryBtnInactive
                      }`}
                  >
                    {t('reading.shuffle.card_selector.categories.all')}
                  </button>
                  <button
                    onClick={() => setSelectedCategory('major')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors w-full font-sans
                      ${selectedCategory === 'major'
                        ? themeClasses.categoryBtnActive
                        : themeClasses.categoryBtnInactive
                      }`}
                  >
                    {t('reading.shuffle.card_selector.categories.major')}
                  </button>
                  <button
                    onClick={() => setSelectedCategory('wands')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors w-full font-sans
                      ${selectedCategory === 'wands'
                        ? themeClasses.categoryBtnActive
                        : themeClasses.categoryBtnInactive
                      }`}
                  >
                    {t('reading.shuffle.card_selector.categories.wands')}
                  </button>
                </div>
                <div className="col-span-2 grid grid-cols-3 gap-2 sm:flex sm:gap-2">
                  <button
                    onClick={() => setSelectedCategory('cups')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors w-full font-sans
                      ${selectedCategory === 'cups'
                        ? themeClasses.categoryBtnActive
                        : themeClasses.categoryBtnInactive
                      }`}
                  >
                    {t('reading.shuffle.card_selector.categories.cups')}
                  </button>
                  <button
                    onClick={() => setSelectedCategory('swords')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors w-full font-sans
                      ${selectedCategory === 'swords'
                        ? themeClasses.categoryBtnActive
                        : themeClasses.categoryBtnInactive
                      }`}
                  >
                    {t('reading.shuffle.card_selector.categories.swords')}
                  </button>
                  <button
                    onClick={() => setSelectedCategory('pentacles')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors w-full font-sans
                      ${selectedCategory === 'pentacles'
                        ? themeClasses.categoryBtnActive
                        : themeClasses.categoryBtnInactive
                      }`}
                  >
                    {t('reading.shuffle.card_selector.categories.pentacles')}
                  </button>
                </div>
              </div>
            </div>
            
            {/* 卡牌网格 */}
            <div className="overflow-y-auto h-[calc(75vh-180px)] p-4">
              <div className="grid grid-cols-3 sm:grid-cols-5 md:grid-cols-6 lg:grid-cols-8 gap-3 md:gap-4">
                {filteredCards.map(card => (
                  <div
                    key={card.id}
                    className="flex flex-col items-center gap-2 cursor-pointer group"
                    onClick={() => {
                      onSelectCard(card.id);
                      onClose();
                    }}
                  >
                    <div className="aspect-[2/3] relative w-full">
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-b from-purple-500/0 to-purple-500/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                      <img
                        src={cardImages.get(card.id)}
                        alt={card.name}
                        className="w-full h-full object-contain rounded-xl"
                      />
                    </div>
                    <div className={`text-center ${themeClasses.cardNameText} text-xs font-sans japanese`}>
                      {(() => {
                        if (card.id <= 21) {
                          // Major Arcana
                          return t(`reading.cards.major.${card.id}`);
                        } else {
                          // Minor Arcana
                          const nameEn = card.nameEn.toLowerCase();
                          const suit = nameEn.includes('wands') ? 'wands' :
                                     nameEn.includes('cups') ? 'cups' :
                                     nameEn.includes('swords') ? 'swords' :
                                     'pentacles';
                          
                          const rankMap: { [key: string]: string } = {
                            'ace': 'ace',
                            'two': '2',
                            'three': '3',
                            'four': '4',
                            'five': '5',
                            'six': '6',
                            'seven': '7',
                            'eight': '8',
                            'nine': '9',
                            'ten': '10',
                            'page': 'page',
                            'knight': 'knight',
                            'queen': 'queen',
                            'king': 'king'
                          };
                          
                          const rank = Object.keys(rankMap).find(r => nameEn.startsWith(r));
                          return rank ? t(`reading.cards.${suit}.${rankMap[rank]}`) : card.name;
                        }
                      })()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default CardSelectionBottomSheet; 