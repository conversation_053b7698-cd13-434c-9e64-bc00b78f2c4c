import uuid
import asyncio
import threading
import time
from typing import Dict, Any, Optional, List

from .realtime_dialog import DialogService
from .config import VOLCANO_CONFIG, TTS_CONFIG

class SessionManager:
    """用户会话管理器，负责管理所有语音对话会话"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls) -> 'SessionManager':
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
        
    def __init__(self):
        """初始化会话管理器"""
        if SessionManager._instance is not None:
            raise RuntimeError("SessionManager是单例类，请使用get_instance()方法获取实例")
            
        self.dialog_service = DialogService(VOLCANO_CONFIG, TTS_CONFIG)
        self.user_sessions: Dict[str, List[str]] = {}  # 用户ID到会话ID的映射
        self.cleanup_task = None
        
    async def start_cleanup_task(self):
        """启动会话清理任务"""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
    async def _cleanup_loop(self):
        """会话清理循环"""
        while True:
            try:
                await self.dialog_service.cleanup_expired_sessions()
                await asyncio.sleep(300)  # 5分钟检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"会话清理出错: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再试
    
    async def create_user_session(self, user_id: str) -> Dict[str, Any]:
        """为用户创建新会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            包含会话信息的字典
        """
        result = await self.dialog_service.create_session(user_id)
        
        # 如果创建成功，记录用户会话
        if 'session_id' in result and 'error' not in result:
            if user_id not in self.user_sessions:
                self.user_sessions[user_id] = []
            self.user_sessions[user_id].append(result['session_id'])
            
        return result
    
    async def close_user_session(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """关闭用户会话
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            操作结果
        """
        result = await self.dialog_service.close_session(session_id)
        
        # 如果关闭成功，从用户会话列表中移除
        if 'error' not in result and user_id in self.user_sessions:
            if session_id in self.user_sessions[user_id]:
                self.user_sessions[user_id].remove(session_id)
                
        return result
    
    async def close_all_user_sessions(self, user_id: str) -> Dict[str, Any]:
        """关闭用户的所有会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            操作结果
        """
        if user_id not in self.user_sessions:
            return {'message': '用户没有活动会话'}
            
        session_ids = self.user_sessions[user_id].copy()
        results = []
        
        for session_id in session_ids:
            result = await self.dialog_service.close_session(session_id)
            results.append(result)
            
        self.user_sessions[user_id] = []
        
        return {
            'message': f'已关闭用户 {user_id} 的所有会话',
            'details': results
        }
    
    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户的所有会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户的会话列表
        """
        if user_id not in self.user_sessions:
            return []
            
        active_sessions = self.dialog_service.get_active_sessions()
        user_session_ids = set(self.user_sessions[user_id])
        
        return [
            session for session in active_sessions
            if session['session_id'] in user_session_ids
        ]
    
    async def send_audio(self, session_id: str, audio_data: bytes) -> Dict[str, Any]:
        """发送音频数据到会话
        
        Args:
            session_id: 会话ID
            audio_data: 音频数据
            
        Returns:
            操作结果
        """
        return await self.dialog_service.send_audio(session_id, audio_data)
    
    def get_session_data(self, session_id: str, timeout: float = 0.1) -> Optional[Dict[str, Any]]:
        """获取会话数据
        
        Args:
            session_id: 会话ID
            timeout: 超时时间（秒）
            
        Returns:
            会话数据或None
        """
        return self.dialog_service.get_session_data(session_id, timeout) 