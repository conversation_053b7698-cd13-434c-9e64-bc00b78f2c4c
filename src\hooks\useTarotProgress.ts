import { useState, useRef, useEffect } from 'react';

interface UseTarotProgressProps {
  onComplete?: () => void;
  duration?: number; // 总持续时间（毫秒）
}

export const useTarotProgress = ({ onComplete, duration = 30000 }: UseTarotProgressProps = {}) => {
  const [progress, setProgress] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const progressInterval = useRef<NodeJS.Timeout | null>(null);
  const apiResponseReceived = useRef(false);

  // 清理函数
  const cleanup = () => {
    if (progressInterval.current) {
      clearInterval(progressInterval.current);
      progressInterval.current = null;
    }
  };

  // 组件卸载时清理
  useEffect(() => {
    return cleanup;
  }, []);

  // 平滑完成进度
  const completeProgress = (callback?: () => void) => {
    cleanup();
    
    // 获取当前进度
    let currentProgress = 0;
    setProgress(prev => {
      currentProgress = prev;
      return prev;
    });

    // 计算从当前进度到100%需要的步数
    const remainingProgress = 100 - currentProgress;
    const smoothDuration = 1000; // 1秒内平滑完成
    const updateInterval = 30; // 每30ms更新一次
    const totalSteps = smoothDuration / updateInterval;
    const progressPerStep = remainingProgress / totalSteps;
    
    // 使用新的间隔器平滑增长到100%
    let steps = 0;
    progressInterval.current = setInterval(() => {
      steps++;
      if (steps >= totalSteps) {
        // 最后一步，确保到达100%
        cleanup();
        setProgress(100);
        
        setTimeout(() => {
          setIsGenerating(false);
          onComplete?.();
          callback?.();
        }, 200); // 延迟200ms执行回调
      } else {
        setProgress(prev => Math.min(100, prev + progressPerStep));
      }
    }, updateInterval);
  };

  // 开始进度模拟
  const startProgress = () => {
    cleanup();
    setProgress(0);
    setIsGenerating(true);
    apiResponseReceived.current = false;

    // 设置匀速增长
    const updateInterval = 50; // 每50ms更新一次
    const totalSteps = duration / updateInterval; // 总步数
    const progressPerStep = 95 / totalSteps; // 每步增加的进度值（最多到95%）

    progressInterval.current = setInterval(() => {
      if (apiResponseReceived.current) {
        // 当API响应返回时，不要立即调用completeProgress
        // 而是继续逐步增加进度，直到达到95%后再平滑完成
        setProgress(prev => {
          // 加速增长到95%
          const acceleratedStep = Math.max(progressPerStep * 2, (95 - prev) / 10);
          const next = Math.min(95, prev + acceleratedStep);
          
          // 当达到95%时，调用平滑完成函数
          if (next >= 95) {
            setTimeout(() => {
              completeProgress();
            }, 300);
          }
          return next;
        });
        return;
      }

      setProgress(prev => {
        const next = Math.min(95, prev + progressPerStep);
        return next;
      });
    }, updateInterval);
  };

  // 标记API响应已收到
  const markApiReceived = () => {
    apiResponseReceived.current = true;
  };

  return {
    progress,
    isGenerating,
    startProgress,
    markApiReceived,
    completeProgress
  };
}; 