import axiosInstance from '../utils/axios';

// 本地缓存点赞状态的接口
interface CommentLikeCache {
  commentId: string;
  count: number;  // 点赞次数（累计）
  timestamp: number;
}

// 用于存储点赞/取消点赞的本地缓存
const pendingLikes: CommentLikeCache[] = [];

// 在本地添加对评论的点赞（支持连击）
export const addCommentLikeLocal = (commentId: string, count: number = 1): {totalCount: number} => {
  try {
    // 查找此评论在本地缓存中的记录
    const existingLikeIndex = pendingLikes.findIndex(like => like.commentId === commentId);

    if (existingLikeIndex !== -1) {
      // 如果已有记录，累加点赞次数
      pendingLikes[existingLikeIndex].count += count;
      pendingLikes[existingLikeIndex].timestamp = Date.now();
    } else {
      // 如果没有记录，创建新记录
      pendingLikes.push({
        commentId,
        count,
        timestamp: Date.now()
      });
    }

    // 返回本地累计的点赞次数
    const localCount = getUserLikeCountLocal(commentId);
    return {
      totalCount: localCount
    };
  } catch (error) {
    console.log('本地添加点赞出错:', error);
    throw error;
  }
};

// 获取用户在本地缓存中对指定评论的点赞次数
export const getUserLikeCountLocal = (commentId: string): number => {
  // 查找该评论的本地点赞记录
  const likeRecord = pendingLikes.find(like => like.commentId === commentId);

  // 返回本地点赞次数，如果没有记录则返回0
  return likeRecord ? likeRecord.count : 0;
};

// 提交所有待处理的点赞到服务器
export const submitPendingLikes = async (): Promise<{success: boolean, message: string}> => {
  if (pendingLikes.length === 0) {
    return { success: true, message: '没有待处理的点赞操作' };
  }

  try {
    // 克隆当前队列，以便在提交后清空
    const likesToSubmit = [...pendingLikes];

    // 调用API提交所有待处理的点赞
    const response = await axiosInstance.post('/api/comments/batch-likes', { likes: likesToSubmit });

    // 提交成功后清空本地缓存
    if (response.data.success) {
      pendingLikes.length = 0;
    } else {
      console.log('提交点赞操作失败:', response.data.message);
    }

    return {
      success: response.data.success,
      message: response.data.message || '点赞操作已提交'
    };
  } catch (error) {
    console.log('提交点赞操作时发生错误:', error);
    return {
      success: false,
      message: '提交点赞操作时出错'
    };
  }
};

// 获取待处理的点赞操作数量
export const getPendingLikesCount = (): number => {
  return pendingLikes.length;
};

// 获取用户对所有评论的点赞次数
export const getUserLikeCounts = async (): Promise<{[commentId: string]: number}> => {
  try {
    // 如果用户未登录，直接返回空对象
    if (!localStorage.getItem('token')) {
      return {};
    }

    const response = await axiosInstance.get('/api/comments/user-likes');
    return response.data.userLikeCounts || {};
  } catch (error) {
    console.log('获取用户点赞次数失败:', error);
    return {};
  }
};

// 批量获取评论点赞次数（结合服务器状态和本地缓存）
export const getCommentsLikeCounts = async (commentIds: string[]): Promise<{[commentId: string]: number}> => {
  if (!commentIds.length) {
    return {};
  }

  try {
    // 获取服务器端的用户点赞次数
    const serverUserLikeCounts = await getUserLikeCounts();
    const result: {[commentId: string]: number} = {};

    // 对每个评论ID，结合服务器状态和本地缓存状态
    commentIds.forEach(commentId => {
      const serverCount = serverUserLikeCounts[commentId] || 0;
      const localCount = getUserLikeCountLocal(commentId);

      // 服务器次数 + 本地待提交次数
      result[commentId] = serverCount + localCount;
    });

    return result;
  } catch (error) {
    console.log('获取评论点赞次数失败:', error);
    // 出错时，只返回本地缓存状态
    const result: {[commentId: string]: number} = {};
    commentIds.forEach(commentId => {
      result[commentId] = getUserLikeCountLocal(commentId);
    });
    return result;
  }
};
