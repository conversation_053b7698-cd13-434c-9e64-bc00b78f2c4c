/* DatePicker 自定义样式 */
.history-page .react-datepicker {
  background-color: rgba(0, 0, 0, 0.8) !important;
  border: 1px solid rgba(168, 85, 247, 0.3) !important;
  border-radius: 0.75rem !important;
  font-family: 'Noto Sans SC', sans-serif !important;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 20px rgba(168, 85, 247, 0.15) !important;
}

.history-page .react-datepicker__header {
  background-color: rgba(0, 0, 0, 0.6) !important;
  border-bottom: 1px solid rgba(168, 85, 247, 0.3) !important;
  border-top-left-radius: 0.75rem !important;
  border-top-right-radius: 0.75rem !important;
}

.history-page .react-datepicker__current-month,
.history-page .react-datepicker__day-name {
  color: #fff !important;
}

.history-page .react-datepicker__day {
  color: #fff !important;
}

.history-page .react-datepicker__day:hover {
  background-color: rgba(168, 85, 247, 0.3) !important;
  border-radius: 0.375rem !important;
}

.history-page .react-datepicker__day--selected,
.history-page .react-datepicker__day--keyboard-selected {
  background: linear-gradient(to right, rgba(168, 85, 247, 0.8), rgba(236, 72, 153, 0.8)) !important;
  border-radius: 0.375rem !important;
}

.history-page .react-datepicker__day--disabled {
  color: rgba(255, 255, 255, 0.3) !important;
}

.history-page .react-datepicker__navigation-icon::before {
  border-color: #fff !important;
}

.history-page .react-datepicker__navigation:hover *::before {
  border-color: rgba(168, 85, 247, 0.8) !important;
}

/* 输入框占位符颜色 */
.history-page .react-datepicker__input-container input::placeholder {
  color: rgba(156, 163, 175, 1) !important;
}

/* 输入框全局焦点样式 */
.history-page input:focus,
.history-page .react-datepicker__input-container input:focus,
.history-page button:focus {
  outline: none !important;
  box-shadow: 0 0 15px rgba(168, 85, 247, 0.15) !important;
  border-color: rgba(168, 85, 247, 0.6) !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.history-page input:focus-visible,
.history-page .react-datepicker__input-container input:focus-visible,
.history-page button:focus-visible {
  outline: none !important;
  box-shadow: 0 0 15px rgba(168, 85, 247, 0.15) !important;
  border-color: rgba(168, 85, 247, 0.6) !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* 输入框悬停效果 */
.history-page input:hover,
.history-page .react-datepicker__input-container input:hover,
.history-page button:hover {
  border-color: rgba(168, 85, 247, 0.5) !important;
  background-color: rgba(0, 0, 0, 0.45) !important;
  transition: all 0.3s ease !important;
}

/* 按钮点击样式 */
.history-page button:active {
  outline: none !important;
  border-color: rgba(168, 85, 247, 0.6) !important;
  box-shadow: 0 0 15px rgba(168, 85, 247, 0.15) !important;
  transform: scale(0.98) !important;
  transition: all 0.1s ease !important;
}

/* 移动端日期选择器样式 */
.history-page input[type="date"] {
  color-scheme: dark;
}

.history-page input[type="date"]::-webkit-calendar-picker-indicator {
  background: transparent;
  bottom: 0;
  color: transparent;
  cursor: pointer;
  height: auto;
  left: auto;
  position: absolute;
  right: 0;
  top: 0;
  width: 2.5rem;
  opacity: 0;
}

.history-page input[type="date"]::-webkit-datetime-edit {
  color: transparent;
}

.history-page input[type="date"]:valid::-webkit-datetime-edit {
  color: white;
}

.history-page input[type="date"]:focus::-webkit-datetime-edit {
  color: white;
} 