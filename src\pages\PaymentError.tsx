import React, { useEffect } from 'react';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { useTranslation } from 'react-i18next';

const PaymentError: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { t } = useTranslation();

  useEffect(() => {
    // 5秒后跳转到会员页面
    const timer = setTimeout(() => {
      navigate('/membership');
    }, 5000);

    return () => clearTimeout(timer);
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="text-center">
        <div className="mb-8">
          <svg
            className="mx-auto h-24 w-24 text-red-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </div>
        <h2 className="text-3xl font-bold text-white mb-4">{t('payment.error.title')}</h2>
        <p className="text-gray-400 mb-8">
          {t('payment.error.message')}
          <br />
          {t('payment.error.redirect')}
        </p>
        <button
          onClick={() => navigate('/membership')}
          className="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
        >
          {t('payment.error.return_now')}
        </button>
      </div>
    </div>
  );
};

export default PaymentError; 