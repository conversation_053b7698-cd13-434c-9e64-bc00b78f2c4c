import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';

interface ReadingResultProps {
  readingResult: any;
  safetyIssue: boolean;
  safetyMessage: string;
  onShare?: () => void;
  onComment?: () => void;
  hasReceivedShareReward?: boolean;
  sessionId?: string;
  onBackToSelection?: () => void;
}

const ReadingResult: React.FC<ReadingResultProps> = ({
  readingResult,
  safetyIssue,
  safetyMessage,
  onShare,
  onComment,
  hasReceivedShareReward,
  sessionId,
  onBackToSelection // 暂时不使用，但保留接口兼容性
}) => {
  const { t } = useTranslation();

  // 避免未使用变量警告
  void onBackToSelection;

  return (
    <motion.div 
      className="mt-8 reading-result-area"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <h3 className="text-xl font-bold text-center mb-4 text-white">
        {safetyIssue 
          ? t('yes_no_tarot.safety_issue.title', '安全提示') 
          : t('yes_no_tarot.reading_result.title', '塔罗解读结果')}
      </h3>
      
      {/* 安全检测结果 */}
      {safetyIssue && (
        <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 mb-4">
          <div className="whitespace-pre-line text-gray-300">
            {safetyMessage}
          </div>
        </div>
      )}
      
      {/* 解读结果 - 根据返回格式显示 */}
      {!safetyIssue && (
        typeof readingResult === 'string' ? (
          <p className="text-gray-300">{readingResult}</p>
        ) : readingResult.format === 'text' ? (
          <p className="text-gray-300">{readingResult.content}</p>
        ) : (
          <div className="space-y-6">
            {/* 答案和信心度 */}
            <div className="text-center mb-6">
              <div className={`text-4xl font-bold mb-2 ${
                readingResult.answer_color 
                  ? '' 
                  : readingResult.answer === '是' || readingResult.answer === 'Yes' || readingResult.answer === 'はい' 
                  ? 'text-green-400' 
                  : readingResult.answer === '否' || readingResult.answer === 'No' || readingResult.answer === 'いいえ'
                    ? 'text-red-400'
                    : 'text-yellow-400'
              }`}
              style={{ 
                color: readingResult.answer_color || '',
                textShadow: '0 0 10px rgba(0,0,0,0.3)'
              }}>
                <div className="inline-block px-8 py-2 rounded-full border-2" 
                  style={{ 
                    borderColor: readingResult.answer_color || 'currentColor',
                    background: 'rgba(0,0,0,0.2)'
                  }}>
                {readingResult.answer}
                </div>
              </div>
              <div className="text-sm text-gray-400">
                {t('yes_no_tarot.reading_result.confidence', '信心指数')}: 
                <span className="ml-1 font-medium">
                  {readingResult.confidence}
                </span>
              </div>
            </div>
            
            {/* 解读 */}
            {readingResult.interpretation && (
              <div className="mb-4">
                <h4 className="text-lg font-semibold text-purple-300 mb-2">
                  {t('yes_no_tarot.reading_result.interpretation', '解读')}
                </h4>
                <p className="text-gray-300">
                  {typeof readingResult.interpretation === 'string' 
                    ? readingResult.interpretation 
                    : readingResult.interpretation.summary}
                </p>
              </div>
            )}

            {/* 建议 */}
            {readingResult.advice && (
              <div className="mb-4">
                <h4 className="text-lg font-semibold text-purple-300 mb-2">
                  {t('yes_no_tarot.reading_result.advice', '建议')}
                </h4>
                <p className="text-gray-300">
                  {typeof readingResult.advice === 'string'
                    ? readingResult.advice
                    : readingResult.advice.practical}
                </p>
              </div>
            )}
            
            {/* 结论 */}
            {readingResult.conclusion && (
              <div className="mt-6 pt-4 border-t border-purple-500/30">
                <p className="text-gray-300 italic">{readingResult.conclusion}</p>
              </div>
            )}
          </div>
        )
      )}
      
      {/* 按钮区域 */}
      <div className="flex justify-end items-center gap-4 mt-8">
        {/* 分享按钮 - 只有在有解读结果且没有安全问题时显示 */}
        {!safetyIssue && onShare && (
          <button
            onClick={onShare}
            className={`px-4 py-2 rounded-full flex items-center justify-center transition-all duration-300 text-xs sm:text-sm ${
              hasReceivedShareReward
                ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-500/30'
                : 'bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-green-500/30'
            }`}
            style={{color: 'white'}}
          >
            <span className="flex items-center text-white">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              <span style={{color: 'white'}}>
                {hasReceivedShareReward
                  ? t('share.pc_share', '分享')
                  : t('share.pc_share_reward', '首次分享得VIP')}
              </span>
            </span>
          </button>
        )}

        {/* 评价按钮 - 放在分享按钮右侧，使用TarotResultPage的样式 */}
        {!safetyIssue && sessionId && onComment && (
          <button
            onClick={onComment}
            className="flex px-4 py-2 rounded-full items-center justify-center transition-all duration-300 text-xs sm:text-sm bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-500/30"
            style={{color: 'white'}}
          >
            <span className="flex items-center text-white">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
              </svg>
              <span style={{color: 'white'}}>{t('feedback.mobile_feedback', '评价')}</span>
            </span>
          </button>
        )}
      </div>
    </motion.div>
  );
};

export default ReadingResult;