import React, { Suspense, lazy } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { motion } from 'framer-motion';
import { scrollToTop } from '../../utils/scrollHelper';

// 延迟加载SpotlightCard组件
const SpotlightCard = lazy(
  () => import("../../blocks/Components/SpotlightCard/SpotlightCard")
);

interface GeneralSpotlightSectionProps {
  onStartReading?: () => void; // 保留但不使用
}

const GeneralSpotlightSection: React.FC<GeneralSpotlightSectionProps> = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  
  // 处理按钮点击事件 - 只滚动到页面顶部
  const handleButtonClick = () => {
    // 滚动到页面顶部
    scrollToTop();
  };

  return (
    <Suspense
      fallback={
        <div className="w-full h-[200px] bg-gray-900 rounded-lg animate-pulse" />
      }
    >
      <div className="spotlight-section py-24 md:py-32 mt-16">
        <div className="max-w-3xl mx-auto px-2 sm:px-4">
          <SpotlightCard
            className="custom-spotlight-card"
            spotlightColor="rgba(0, 229, 255, 0.2)"
          >
            <div className="p-4 sm:p-8 text-center">
              <h3
                className="text-2xl md:text-3xl font-semibold mb-4"
                style={{
                  background: theme === 'light' 
                    ? "none" 
                    : "linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)",
                  WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                  WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                  color: theme === 'light' ? "#000" : "inherit"
                }}
              >
                {t("yes_no_tarot.general_spotlight.title", "Start Your Free Yes No Tarot Reading")}
              </h3>
              <p className={`${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              } text-lg md:text-xl mb-6 px-1`}>
                {t("yes_no_tarot.general_spotlight.description", "Ready to receive clear guidance for your important decisions? Choose your preferred tarot spread and let our AI tarot reader reveal the answers hidden in the cards.")}
              </p>
              <div className="flex justify-center">
                <motion.button
                  onClick={handleButtonClick}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-6 py-3 rounded-full"
                  style={{
                    background:
                      theme === 'light'
                        ? "linear-gradient(135deg, rgba(124, 58, 237, 0.7), rgba(168, 85, 247, 0.7))"
                        : "linear-gradient(135deg, rgba(124, 58, 237, 0.8), rgba(168, 85, 247, 0.8))",
                    boxShadow: theme === 'light' 
                      ? "0 0 20px rgba(168, 85, 247, 0.4)"
                      : "0 0 20px rgba(168, 85, 247, 0.5)",
                    color: 'white',
                  }}
                >
                  {t("yes_no_tarot.general_spotlight.button", "Start Your Yes/No Reading Now")}
                </motion.button>
              </div>
            </div>
          </SpotlightCard>
        </div>
      </div>
    </Suspense>
  );
};

export default GeneralSpotlightSection; 