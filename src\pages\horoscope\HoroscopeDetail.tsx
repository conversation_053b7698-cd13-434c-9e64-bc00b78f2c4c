import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { useHoroscopeData, horoscopePeriods, dailyOptions } from '../../data/horoscopes';
import { useTheme } from '../../contexts/ThemeContext';
import { getFontClass } from '../../utils/fontUtils';
import LandingBackground from '../../components/LandingBackground';
import Footer from '../../components/Footer';
import SEO from '../../components/SEO';
import CdnLazyImage from '../../components/CdnLazyImage';
import LanguageLink from '../../components/LanguageLink';

const HoroscopeDetail: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const navigate = useNavigate();
  const isDark = theme === 'dark';
  
  // 获取URL参数
  const { signId, periodType = 'today' } = useParams<{ 
    signId: string;
    periodType: string;
  }>();
  
  // 判断当前是哪种类型的运势
  const isPeriodType = periodType === 'weekly' || periodType === 'yearly';
  const isDailyOption = periodType === 'today' || periodType === 'yesterday' || periodType === 'tomorrow';
  
  // 状态管理
  const [selectedPeriod, setSelectedPeriod] = useState<string>(isPeriodType ? periodType : 'daily');
  const [selectedDay, setSelectedDay] = useState<string>(isDailyOption ? periodType : 'today');
  const [showContent, setShowContent] = useState<boolean>(true);
  
  // 获取星座数据
  const { getSignName, getPeriodName, getDailyOptionName, signs } = useHoroscopeData();
  
  // 当URL参数变化时更新状态
  useEffect(() => {
    if (isPeriodType) {
      setSelectedPeriod(periodType);
      setSelectedDay('today'); // 默认值
    } else if (isDailyOption) {
      setSelectedPeriod('daily');
      setSelectedDay(periodType);
    }
    setShowContent(true);
  }, [periodType, isPeriodType, isDailyOption]);
  
  // 验证星座ID是否有效
  useEffect(() => {
    const validSign = signs.find(s => s.id === signId);
    if (!validSign) {
      navigate('/blog');
    }
  }, [signId, signs, navigate]);
  
  // 获取当前星座信息
  const currentSign = signs.find(s => s.id === signId);
  
  // 如果星座无效，显示加载状态
  if (!currentSign) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }
  
  // 处理周期选择
  const handlePeriodChange = (periodId: string) => {
    // 根据选择的周期更新URL
    if (periodId === 'daily') {
      navigate(`/blog/horoscope/${signId}/today`);
    } else {
      navigate(`/blog/horoscope/${signId}/${periodId}`);
    }
  };
  
  // 处理日期选择
  const handleDayChange = (dayId: string) => {
    // 更新URL
    navigate(`/blog/horoscope/${signId}/${dayId}`);
  };
  
  // 获取当前年份
  const currentYear = new Date().getFullYear();

  return (
    <div className={`min-h-screen flex flex-col relative antialiased ${isDark ? 'text-white' : 'text-gray-800'}`}>
      <SEO 
        title={`${getSignName(signId || '')} ${selectedPeriod === 'yearly' ? currentYear : ''} ${getPeriodName(selectedPeriod)} - ${t('blog.categories.horoscope', '星座运势')}`}
        description={`${getSignName(signId || '')}${selectedPeriod === 'daily' ? getDailyOptionName(selectedDay) : ''}${selectedPeriod === 'yearly' ? currentYear + '年' : ''}${getPeriodName(selectedPeriod)}详细解读，包括爱情、事业、财运和健康运势分析`}
      />
      <LandingBackground />
      
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          {/* 返回按钮 */}
          <div className="mt-4 mb-2">
            <LanguageLink 
              to="/blog" 
              className={`inline-flex items-center ${isDark ? 'text-purple-400 hover:text-purple-300' : 'text-purple-600 hover:text-purple-500'}`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              {t('blog.back_to_blog', '返回博客')}
            </LanguageLink>
          </div>
          
          {/* 星座信息头部 */}
          <div className="flex flex-col sm:flex-row items-center mb-6 sm:mb-8">
            <CdnLazyImage 
              src={currentSign.iconPath} 
              alt={getSignName(currentSign.id)} 
              className="w-24 h-24 sm:w-32 sm:h-32 mb-4 sm:mb-0 sm:mr-6 object-contain"
            />
            <div className="text-center sm:text-left">
              <h1 className={`text-2xl sm:text-3xl md:text-4xl font-bold mb-1 ${getFontClass(i18n.language)}`}>
                {getSignName(currentSign.id)}
              </h1>
              <p className={`text-sm sm:text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                {currentSign.dateRange}
              </p>
            </div>
          </div>

          {/* 运势周期选择 */}
          <div className="mb-6">
            <h2 className={`text-lg font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
              {t('horoscope.select_period', '选择运势周期')}
            </h2>
            <div className="flex flex-wrap gap-2 sm:gap-4">
              {horoscopePeriods.map((period) => (
                <button
                  key={period.id}
                  className={`px-4 py-2 rounded-full text-sm sm:text-base transition-colors duration-200 ${
                    selectedPeriod === period.id
                      ? 'bg-purple-600 text-white'
                      : isDark
                        ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  onClick={() => handlePeriodChange(period.id)}
                >
                  {t(period.nameKey, period.defaultName)}
                </button>
              ))}
            </div>
          </div>

          {/* 每日运势日期选择 - 仅当选择了"每日运势"时显示 */}
          {selectedPeriod === 'daily' && (
            <div className="mb-6">
              <h2 className={`text-lg font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                {t('horoscope.select_day', '选择日期')}
              </h2>
              <div className="flex flex-wrap gap-2 sm:gap-4">
                {dailyOptions.map((option) => (
                  <button
                    key={option.id}
                    className={`px-4 py-2 rounded-full text-sm sm:text-base transition-colors duration-200 ${
                      selectedDay === option.id
                        ? 'bg-purple-600 text-white'
                        : isDark
                          ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                    onClick={() => handleDayChange(option.id)}
                  >
                    {t(option.nameKey, option.defaultName)}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* 运势内容区域 */}
          {showContent && (
            <div className={`p-6 rounded-xl ${
              isDark 
                ? 'bg-gray-800/30 backdrop-blur-sm border border-white/10' 
                : 'bg-white/80 backdrop-blur-sm border border-gray-200'
            }`}>
              {/* 标题 */}
              <div className="mb-6 text-center">
                <h2 className={`text-xl sm:text-2xl font-bold ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {getSignName(signId || '')} 
                  {selectedPeriod === 'daily' && ` ${getDailyOptionName(selectedDay)}`}
                  {selectedPeriod === 'yearly' && ` ${currentYear}年`}
                  {` ${getPeriodName(selectedPeriod)}`}
                </h2>
                
                {/* 日期范围显示 - 仅当选择了"每周运势"时显示 */}
                {selectedPeriod === 'weekly' && (
                  <p className={`mt-2 ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                    {new Date().toLocaleDateString(i18n.language === 'zh-CN' ? 'zh-CN' : 'en-US', { 
                      month: 'short', 
                      day: 'numeric' 
                    })} - {
                      new Date(new Date().setDate(new Date().getDate() + 6)).toLocaleDateString(
                        i18n.language === 'zh-CN' ? 'zh-CN' : 'en-US', 
                        { month: 'short', day: 'numeric' }
                      )
                    }
                  </p>
                )}
              </div>
              
              {/* 根据选择的周期显示不同的内容 */}
              {selectedPeriod === 'daily' && (
                <>
                  <div className="mb-6">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.overview', '综合运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-3/4 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div className="mb-6">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.love', '爱情运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-2/3 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div className="mb-6">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.career', '事业运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-4/5 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div className="mb-6">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.wealth', '财富运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-1/2 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div>
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.health', '健康运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-3/5 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                </>
              )}
              
              {selectedPeriod === 'weekly' && (
                <>
                  <div className="mb-6">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.weekly_overview', '本周总览')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-3/4 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div className="mb-6">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.love', '爱情运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-2/3 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div className="mb-6">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.career', '事业运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-4/5 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div className="mb-6">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.wealth', '财富运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-1/2 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div>
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.health', '健康运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-3/5 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                </>
              )}
              
              {selectedPeriod === 'yearly' && (
                <>
                  <div className="mb-8">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.yearly_overview', '年度概述')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-3/4 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div className="mb-8">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.love', '爱情运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-2/3 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div className="mb-8">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.career', '事业运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-4/5 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div className="mb-8">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.wealth', '财富运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-1/2 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div className="mb-8">
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.health', '健康运势')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-3/5 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                  
                  <div>
                    <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {t('horoscope.sections.lucky_months', '幸运月份')}
                    </h3>
                    <div className={`h-4 w-full rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse mb-2`}></div>
                    <div className={`h-4 w-4/5 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-300'} animate-pulse`}></div>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default HoroscopeDetail; 