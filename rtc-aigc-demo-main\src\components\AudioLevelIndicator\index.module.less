.container {
  margin: 10px 0;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  width: 100%;
  max-width: 300px;
}

.micDisabled {
  background-color: #ffeeee;
  border: 1px dashed #ff9999;
}

.label {
  margin-bottom: 5px;
  font-size: 14px;
  color: #333;
}

.indicator {
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
  position: relative;
}

.level {
  height: 100%;
  transition: width 0.1s ease, background-color 0.2s ease;
  border-radius: 5px;
  min-width: 3px;
}

.inactive {
  background-color: #ccc;
  width: 3px !important;
}

.disabled {
  background-color: #ff9999;
}

.low {
  background-color: #4caf50;
}

.medium {
  background-color: #ff9800;
}

.high {
  background-color: #f44336;
} 