:root {
  --pointer-x: 50%;
  --pointer-y: 50%;
  --pointer-from-center: 0;
  --pointer-from-top: 0.5;
  --pointer-from-left: 0.5;
  --rotate-x: 0deg;
  --rotate-y: 0deg;
  --card-opacity: 0;
  --background-x: 50%;
  --background-y: 50%;
  --behind-gradient: none;
  --purple-glow-1: hsla(280, 100%, 90%, var(--card-opacity));
  --purple-glow-2: hsla(280, 80%, 80%, calc(var(--card-opacity) * 0.75));
  --purple-glow-3: hsla(280, 70%, 70%, calc(var(--card-opacity) * 0.5));
  --purple-glow-4: hsla(280, 60%, 60%, 0);
}

.tilt-wrapper {
  perspective: 500px;
  transform: translate3d(0, 0, 0.1px);
  position: relative;
  touch-action: none;
  margin: 0 auto;
  width: 100%;
  height: 100%;
}

.tilt-wrapper::before {
  content: '';
  position: absolute;
  inset: -10px;
  background: inherit;
  background-position: inherit;
  border-radius: inherit;
  transition: all 0.5s ease;
  filter: contrast(2) saturate(2) blur(36px);
  transform: scale(0.8) translate3d(0, 0, 0.1px);
  background-size: 100% 100%;
  background-image: var(--behind-gradient, radial-gradient(farthest-side circle at var(--pointer-x) var(--pointer-y), 
    var(--purple-glow-1) 4%, 
    var(--purple-glow-2) 10%, 
    var(--purple-glow-3) 50%, 
    var(--purple-glow-4) 100%));
}

.tilt-wrapper:hover,
.tilt-wrapper.active {
  --card-opacity: 1;
}

.tilt-wrapper:hover::before,
.tilt-wrapper.active::before {
  filter: contrast(1) saturate(2) blur(40px) opacity(1);
  transform: scale(0.9) translate3d(0, 0, 0.1px);
}

.tilt-container {
  height: 100%;
  width: 100%;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 1s ease;
  transform: translate3d(0, 0, 0.1px) rotateX(0deg) rotateY(0deg);
  box-shadow: rgba(0, 0, 0, 0.2) calc((var(--pointer-from-left) * 10px) - 3px) calc((var(--pointer-from-top) * 20px) - 6px) 20px -5px;
  background-blend-mode: color-dodge, normal, normal, normal;
  animation: glow-bg 12s linear infinite;
}

.tilt-container:hover,
.tilt-container.active {
  transition: none;
  transform: translate3d(0, 0, 0.1px) rotateX(var(--rotate-y)) rotateY(var(--rotate-x));
}

.tilt-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transform: translate3d(calc(var(--pointer-from-left) * -6px + 3px), calc(var(--pointer-from-top) * -6px + 3px), 0.1px);
}

@keyframes glow-bg {
  0% {
    --bgrotate: 0deg;
  }

  100% {
    --bgrotate: 360deg;
  }
}

@media (max-width: 768px) {
  .tilt-wrapper {
    perspective: 400px;
  }
}

@media (max-width: 480px) {
  .tilt-wrapper {
    perspective: 300px;
  }
} 