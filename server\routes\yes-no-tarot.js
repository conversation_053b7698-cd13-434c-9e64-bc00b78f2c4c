const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../utils/asyncHandler');
const { authenticateToken } = require('../middleware/auth');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');
// const { checkEthicalIssues } = require('../services/ethicsCheck'); // 已禁用安全检测
const { TAROT_CARDS } = require('../constants/tarot');
const { User } = require('../models/User');

// 创建用于调用 Qwen API 的 axios 实例
const qwenAPI = axios.create({
  baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
  timeout: 500000,
  headers: {
    'Authorization': `Bearer ${process.env.QWEN_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

/**
 * @api {post} /api/yes-no-tarot 获取是非塔罗牌解读
 * @apiName GetYesNoTarotReading
 * @apiGroup YesNoTarot
 * @apiDescription 根据用户问题和抽取的卡牌生成是非塔罗牌解读，支持进度条显示
 * 
 * @apiParam {String} question 用户问题
 * @apiParam {Object} card 卡牌信息
 * @apiParam {String} language 语言代码
 * @apiParam {String} [sessionId] 会话ID
 * 
 * @apiSuccess {Boolean} success 是否成功
 * @apiSuccess {Object} reading 解读结果
 */
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const { question, card, language = 'zh-TW', sessionId } = req.body;
  const userId = req.user.userId;

  if (!question || !card) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数'
    });
  }

  try {
    // 获取用户信息并验证权限
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查VIP状态和剩余次数
    let pool = await getConnection();
    const [allSessions] = await pool.query(
      'SELECT COUNT(*) as count FROM sessions WHERE user_id = ? AND spread_id != "daily-fortune"',
      [userId]
    );
    const sessionCount = allSessions[0].count;

    // 计算实际剩余次数
    const actualRemainingReads = Math.max(0, user.remaining_reads - sessionCount);

    // 检查用户权限：VIP用户或有剩余次数的普通用户
    if (actualRemainingReads <= 0 && user.vip_status === 'none') {
      return res.status(400).json({
        success: false,
        message: '您的免费占卜次数已用完，请升级到VIP继续使用'
      });
    }

    console.log('用户权限验证通过:', {
      userId,
      vipStatus: user.vip_status,
      remainingReads: user.remaining_reads,
      sessionCount,
      actualRemainingReads
    });

    // 安全检查 - 已禁用安全检测
    // const ethicsCheckResult = await checkEthicalIssues(question, language);

    // 模拟安全检测结果，直接返回无安全问题
    const ethicsCheckResult = {
      whetherOrNot: false,
      category: '无安全问题',
      confidence: 0,
      reason: '安全检测已禁用',
      input_tokens: 0,
      output_tokens: 0
    };

    // 如果检测到安全问题且置信度高，直接返回干预消息 - 已禁用
    // if (ethicsCheckResult.whetherOrNot === true) {
    //   // 准备保存到数据库的会话ID
    //   const sessionId = req.body.sessionId || uuidv4();
    //
    //   // 保存到数据库
    //   pool = await getConnection();
    //   await pool.query(
    //     `INSERT INTO sessions
    //      (id, user_id, question, spread_id, spread_name, selected_cards, status, reading_result,
    //       ethical_status, Ethical_category, Ethical_reason, Ethical_confidence, Ethical_input_token, Ethical_output_token)
    //      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    //     [
    //       sessionId,
    //       userId,
    //       question,
    //       'yes-no-single-card',
    //       '是非单卡占卜',
    //       JSON.stringify([card]),
    //       'ethical_intervention', // 标记为伦理干预
    //       JSON.stringify({
    //         interventionMessage: ethicsCheckResult.interventionMessage
    //       }),
    //       'ethical_intervention',
    //       ethicsCheckResult.category,
    //       ethicsCheckResult.reason,
    //       ethicsCheckResult.confidence,
    //       ethicsCheckResult.input_tokens,
    //       ethicsCheckResult.output_tokens
    //     ]
    //   );
    //
    //   return res.json({
    //     success: false,
    //     sessionId: sessionId,
    //     ethicalIssue: true,
    //     message: ethicsCheckResult.interventionMessage || '您的问题可能涉及敏感内容，无法提供解读'
    //   });
    // }

    // 安全检测已禁用，直接继续处理是非塔罗
    
    // 构建提示词
    const promptLanguage = language || 'zh-CN';
    
    // 获取翻译后的卡牌名称
    const getTranslatedCardName = (card, language) => {
      try {
        // 标准化language参数
        let normalizedLang = language;
        if (normalizedLang.startsWith('en-')) normalizedLang = 'en';
        if (normalizedLang.startsWith('ja-')) normalizedLang = 'ja';
        if (normalizedLang.startsWith('zh-TW')) normalizedLang = 'zh-TW';
        if (normalizedLang.startsWith('zh-CN')) normalizedLang = 'zh-CN';
        
        // 如果语言是中文，直接返回原始名称
        if (normalizedLang === 'zh-CN') {
          return card.name;
        }
        
        // 尝试从i18n文件中获取翻译
        const i18nFile = require(`../i18n/locales/${normalizedLang}.json`);
        
        // 大阿卡纳牌名称映射
        const majorArcanaMap = {
          '愚者': '0',
          '魔术师': '1',
          '女祭司': '2',
          '女皇': '3',
          '皇帝': '4',
          '教皇': '5',
          '恋人': '6',
          '战车': '7',
          '力量': '8',
          '隐士': '9',
          '命运之轮': '10',
          '正义': '11',
          '倒吊人': '12',
          '死神': '13',
          '节制': '14',
          '恶魔': '15',
          '塔': '16',
          '星星': '17',
          '月亮': '18',
          '太阳': '19',
          '审判': '20',
          '世界': '21'
        };
        
        // 检查是否是大阿卡纳牌
        const majorId = majorArcanaMap[card.name];
        if (majorId) {
          // 大阿卡纳牌，通过i18n文件获取翻译
          if (i18nFile.reading && i18nFile.reading.cards && i18nFile.reading.cards.major && i18nFile.reading.cards.major[majorId]) {
            return i18nFile.reading.cards.major[majorId];
          }
        } else {
          // 小阿卡纳牌，需要解析花色和数字
          const suitMap = {
            '权杖': 'wands',
            '圣杯': 'cups',
            '宝剑': 'swords',
            '钱币': 'pentacles'
          };
          
          const valueMap = {
            '一': 'ace',
            '二': '2',
            '三': '3',
            '四': '4',
            '五': '5',
            '六': '6',
            '七': '7',
            '八': '8',
            '九': '9',
            '十': '10',
            '侍从': 'page',
            '骑士': 'knight',
            '王后': 'queen',
            '国王': 'king'
          };
          
          // 解析卡牌名称中的花色和数字
          let suit, value;
          for (const [zhSuit, enSuit] of Object.entries(suitMap)) {
            if (card.name.includes(zhSuit)) {
              suit = enSuit;
              break;
            }
          }
          
          for (const [zhValue, enValue] of Object.entries(valueMap)) {
            if (card.name.includes(zhValue)) {
              value = enValue;
              break;
            }
          }
          
          // 如果找到了花色和数字，尝试从i18n文件中获取翻译
          if (suit && value && i18nFile.reading && i18nFile.reading.cards && i18nFile.reading.cards[suit]) {
            return i18nFile.reading.cards[suit][value];
          }
        }
      } catch (error) {
        console.error('获取卡牌翻译失败:', error);
      }
      
      // 如果无法获取翻译，返回原始名称
      return card.name;
    };
    
    // 获取翻译后的位置文本
    const getPositionText = (isReversed, language) => {
      switch (language) {
        case 'en':
          return isReversed ? 'reversed' : 'upright';
        case 'ja':
          return isReversed ? '逆位置' : '正位置';
        case 'zh-TW':
          return isReversed ? '逆位' : '正位';
        case 'zh-CN':
        default:
          return isReversed ? '逆位' : '正位';
      }
    };
    
    const translatedCardName = getTranslatedCardName(card, promptLanguage);
    const translatedPosition = getPositionText(card.isReversed, promptLanguage);
    
    // 根据语言选择提示词
    let systemPrompt, userPrompt;
    
    // 系统提示词
    if (language === 'en') {
      systemPrompt = `You are an expert tarot reader with deep knowledge of tarot symbolism and interpretation, the language style should suit English-speaking habits. Your task is to provide a definitive Yes/No tarot reading based on the user's question and the drawn card.

IMPORTANT: You MUST provide a clear "Yes" or "No" answer. Only use "Maybe" if the card absolutely cannot be interpreted as a clear yes or no.

Please provide your reading in the following JSON structure:
{
  "answer": "Yes" or "No" or "Maybe", // MUST be one of these exact values - be decisive!
  "confidence": "High", "Medium", or "Low", // How confident this answer is
  "interpretation": "", // Detailed explanation of why this card indicates this answer (100-150 words)
  "advice": "", // Practical advice based on the reading
  "conclusion": "" // Final thoughts and encouragement
}

Respond with ONLY this JSON structure, properly formatted. Include rich, insightful content in each section while maintaining a mystical yet practical tone.`;
    } else if (language === 'ja') {
      systemPrompt = `あなたはタロットのシンボリズムと解釈に深い知識を持つ専門家です，日本語の表現に従ってください。あなたの任務は、ユーザーの質問と引いたカードに基づいて、明確なYes/Noタロットリーディングを提供することです。

重要：あなたは明確な「はい」または「いいえ」の回答を提供する必要があります。カードが明確なはいまたはいいえとして解釈できない場合にのみ「たぶん」を使用してください。

以下のJSON構造で解読を提供してください：
{
  "answer": "はい" または "いいえ" または "たぶん", // これらの値のいずれかでなければなりません - 決断力を持ってください！
  "confidence": "高", "中", または "低", // この回答の確信度
  "interpretation": "", // このカードがなぜこの回答を示すのかの詳細な説明（100〜150字）
  "advice": "", // リーディングに基づく実践的なアドバイス
  "conclusion": "" // 最終的な考えと励まし
}

このJSON構造のみで、適切にフォーマットして回答してください。神秘的でありながら実用的なトーンを保ちながら、各セクションに豊かで洞察に満ちた内容を含めてください。`;
    } else if (language === 'zh-TW') {
      // 繁体中文
      systemPrompt = `你是一位擁有深厚塔羅象徵和解讀知識的專業塔羅師，語言風格符合臺灣人的習慣。你的任務是根據用戶的問題和抽取的卡牌提供明確的是/否塔羅解讀。

重要：你必須提供一個明確的"是"或"否"答案。只有在卡牌絕對無法被解讀為明確的是或否時，才使用"也許"。

請按照以下JSON結構提供你的解讀：
{
  "answer": "是" 或 "否" 或 "也許", // 必須是這些確切的值之一 - 請務必做出決斷！
  "confidence": "高"、"中"或"低", // 這個答案的確信度
  "interpretation": "", // 為什麼這張牌表示這個答案的詳細解釋，100-150字
  "advice": "", // 基於解讀的實用建議
  "conclusion": "" // 最終想法和鼓勵
}

只回覆這個JSON結構，格式正確。在保持神秘而實用的語調的同時，在每個部分包含豐富、有洞察力的內容。`;
    } else {
      // 简体中文
      systemPrompt = `你是一位拥有深厚塔罗象征和解读知识的专业塔罗师。你的任务是根据用户的问题和抽取的卡牌提供明确的是/否塔罗解读。

重要：你必须提供一个明确的"是"或"否"答案。只有在卡牌绝对无法被解读为明确的是或否时，才使用"也许"。

请按照以下JSON结构提供你的解读：
{
  "answer": "是" 或 "否" 或 "也许", // 必须是这些确切的值之一 - 请务必做出决断！
  "confidence": "高"、"中"或"低", // 这个答案的确信度
  "interpretation": "", // 为什么这张牌表示这个答案的详细解释，100-150字
  "advice": "", // 基于解读的实用建议
  "conclusion": "" // 最终想法和鼓励
}

只回复这个JSON结构，格式正确。在保持神秘而实用的语调的同时，在每个部分包含丰富、有洞察力的内容。`;
    }
    
    // 用户提示词
    if (language === 'en') {
      userPrompt = `I have drawn the card "${translatedCardName}" in ${translatedPosition} position for the question: "${question}".

Please provide a definitive Yes/No tarot reading based on this card. Be decisive in your interpretation.`;
    } else if (language === 'ja') {
      userPrompt = `質問「${question}」に対して、「${translatedCardName}」のカードを${translatedPosition}で引きました。

このカードに基づいて明確なYes/Noタロットリーディングを提供してください。解釈において決断力を持ってください。`;
    } else if (language === 'zh-TW') {
      // 繁体中文
      userPrompt = `我對問題"${question}"抽到了"${translatedCardName}"牌，${translatedPosition}。

請根據這張牌提供一個明確的是/否塔羅解讀。在你的解釋中請做出明確的判斷。`;
    } else {
      // 简体中文
      userPrompt = `我对问题"${question}"抽到了"${translatedCardName}"牌，${translatedPosition}。

请根据这张牌提供一个明确的是/否塔罗解读。在你的解释中请做出明确的判断。`;
    }
    
    // 记录开始时间，用于计算响应时间
    const startTime = Date.now();
    
    console.log('Yes/No Tarot System Prompt:', systemPrompt.substring(0, 50) + '...');
    console.log('Yes/No Tarot User Prompt:', userPrompt.substring(0, 50) + '...');

    // 调用Qwen API
    const response = await qwenAPI.post('/chat/completions', {
      model: "qwen-max-latest",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ],
      stream: false,
      response_format: { type: "json_object" },
      parameters: {
        response_language: language
      }
    });
    
    // 计算响应时间（毫秒）
    const responseTime = Date.now() - startTime;
    
    // 提取生成的内容
    let reading;
    let parsedSuccessfully = false;
    
    if (!response.data || !response.data.choices || !response.data.choices[0] || !response.data.choices[0].message) {
      throw new Error('API 返回格式错误');
    }
    
    const content = response.data.choices[0].message.content.trim();
    
    try {
      // 尝试解析JSON响应
      reading = JSON.parse(content);
      
      // 检查必要字段是否存在且内容长度足够
      const checkParagraphDetectionStatus = () => {
        // 必要字段列表
        const requiredFields = [
          'answer',
          'confidence',
          'interpretation',
          'advice',
          'conclusion'
        ];
        
        // 检查每个字段是否存在且内容长度足够
        for (const field of requiredFields) {
          // 处理顶级字段
          if (!reading[field] || 
              typeof reading[field] !== 'string' || 
              reading[field].length < 1) {
            return false;
          }
        }
        
        // 特别检查answer字段的值是否符合预期
        const validAnswers = ['Yes', 'No', 'Maybe', '是', '否', '也許', '也许', 'はい', 'いいえ', 'たぶん'];
        if (!validAnswers.includes(reading.answer)) {
          return false;
        }
        
        return true;
      };
      
      // 设置解析成功状态
      parsedSuccessfully = checkParagraphDetectionStatus();
      
      // 根据answer添加对应的颜色
      const answerColorMap = {
        'Yes': '#4ADE80',
        'No': '#F87171',
        'Maybe': '#FBBF24',
        '是': '#4ADE80',
        '否': '#F87171',
        '也許': '#FBBF24',
        '也许': '#FBBF24',
        'はい': '#4ADE80',
        'いいえ': '#F87171',
        'たぶん': '#FBBF24'
      };
      
      // 添加颜色属性
      if (reading.answer && answerColorMap[reading.answer]) {
        reading.answer_color = answerColorMap[reading.answer];
      } else {
        // 默认颜色
        reading.answer_color = '#FBBF24';
      }
    } catch (parseError) {
      // 如果解析失败，直接使用文本响应
      reading = {
        content: content,
        format: 'text'
      };
      parsedSuccessfully = false;
    }
    
    // 获取输入和输出的tokens
    const inputTokens = response.data.usage?.prompt_tokens || 0;
    const outputTokens = response.data.usage?.completion_tokens || 0;
    
    // 准备保存到数据库的会话ID
    const sessionId = req.body.sessionId || uuidv4();
    
    // 保存到数据库
    pool = await getConnection();
    await pool.query(
      `INSERT INTO sessions 
       (id, user_id, question, spread_id, spread_name, selected_cards, status, reading_result, 
        input_tokens, output_tokens, llm_model, response_time, paragraph_detection_status,
        Ethical_input_token, Ethical_output_token, Ethical_category, Ethical_reason, Ethical_confidence) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        sessionId,
        userId,
        question,
        'yes-no-single-card',
        '是非单卡占卜',
        JSON.stringify([card]),
        'completed',
        JSON.stringify({
          original: content,  // 原始API返回
          parsed: reading     // 解析后的结果
        }),
        inputTokens,
        outputTokens,
        'qwen-plus-latest',  // 更新为plus模型
        Math.floor(responseTime / 1000), // 转换为秒
        parsedSuccessfully ? 1 : 0,      // 解析成功为1，失败为0
        ethicsCheckResult.input_tokens || 0,
        ethicsCheckResult.output_tokens || 0,
        ethicsCheckResult.category || '无安全问题',
        ethicsCheckResult.reason || '未检测到安全问题',
        ethicsCheckResult.confidence || 0
      ]
    );
    
    return res.json({
      success: true,
      sessionId: sessionId,
      reading: {
        content: reading,
        card: card,
        question: question
      }
    });
    
  } catch (error) {
    console.error('是非塔罗牌解读生成失败:', error);
    return res.status(500).json({
      success: false,
      message: '解读生成失败，请稍后再试'
    });
  }
}));

module.exports = router; 