#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级版TTS脚本(pro-tts)，使用火山引擎(字节跳动)实现高质量语音合成
"""

import os
import sys
import asyncio
import uuid
import json
import gzip
import copy
import traceback
import websockets

# 火山引擎TTS的配置信息
appid = "7120470259"  # 填写您的应用ID
token = "ZjLnjw_gedm_HYuGRK06SP3RVKhOhiO-"  # 填写您的访问令牌
cluster = "volcano_tts"  # 填写您的集群信息

# 默认语音类型，如果未指定会使用这个
DEFAULT_VOICE = "zh_female_wanwanxiaohe_moon_bigtts"  

# 火山引擎API地址
host = "openspeech.bytedance.com"
api_url = f"wss://{host}/api/v1/tts/ws_binary"

# WebSocket请求头设置
default_header = bytearray(b'\x11\x10\x11\x00')

# 基础请求模板
request_json = {
    "app": {
        "appid": appid,
        "token": token,
        "cluster": cluster
    },
    "user": {
        "uid": "388808087185088"
    },
    "audio": {
        "voice_type": DEFAULT_VOICE,  # 这里会在函数中被替换为实际使用的音色
        "encoding": "mp3",
        "speed_ratio": 1.0,
        "volume_ratio": 1.0,
        "pitch_ratio": 1.0,
    },
    "request": {
        "reqid": "xxx",
        "text": "",
        "text_type": "plain",
        "operation": "xxx"
    }
}


def parse_response(res, file):
    """解析火山引擎TTS服务的WebSocket响应"""
    message_type = res[1] >> 4
    message_type_specific_flags = res[1] & 0x0f
    header_size = res[0] & 0x0f
    payload = res[header_size*4:]
    
    if message_type == 0xb:  # audio-only server response
        if message_type_specific_flags == 0:  # no sequence number as ACK
            return False
        else:
            sequence_number = int.from_bytes(payload[:4], "big", signed=True)
            payload_size = int.from_bytes(payload[4:8], "big", signed=False)
            payload = payload[8:]
            print(f"[Pro-TTS] 接收音频数据: 序列号={sequence_number}, 大小={payload_size}字节")
        file.write(payload)
        if sequence_number < 0:
            return True
        else:
            return False
    elif message_type == 0xf:  # error message
        code = int.from_bytes(payload[:4], "big", signed=False)
        msg_size = int.from_bytes(payload[4:8], "big", signed=False)
        error_msg = payload[8:]
        if res[2] & 0x0f == 1:  # gzip compression
            error_msg = gzip.decompress(error_msg)
        error_msg = str(error_msg, "utf-8")
        print(f"[Pro-TTS] 错误: 代码={code}, 消息={error_msg}")
        return True
    else:
        print(f"[Pro-TTS] 未定义的消息类型: {message_type}")
        return True


async def generate_speech(text, voice, output_path):
    """
    使用火山引擎生成语音文件
    
    Args:
        text (str): 需要转换为语音的文本
        voice (str): 语音角色
        output_path (str): 输出文件路径
    
    Returns:
        bool: 是否成功
    """
    try:
        # 创建输出目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        print(f"[Pro-TTS] 使用火山引擎生成语音，声音: {voice}")
        print(f"[Pro-TTS] 文本片段: {text[:50]}...")
        
        # 准备请求数据
        submit_request_json = copy.deepcopy(request_json)
        submit_request_json["audio"]["voice_type"] = voice
        submit_request_json["request"]["reqid"] = str(uuid.uuid4())
        submit_request_json["request"]["operation"] = "submit"
        submit_request_json["request"]["text"] = text
        
        # 压缩和准备请求
        payload_bytes = str.encode(json.dumps(submit_request_json))
        payload_bytes = gzip.compress(payload_bytes)  # 使用gzip压缩
        full_client_request = bytearray(default_header)
        full_client_request.extend((len(payload_bytes)).to_bytes(4, 'big'))  # payload size(4 bytes)
        full_client_request.extend(payload_bytes)  # payload
        
        print(f"[Pro-TTS] 开始向火山引擎发送WebSocket请求...")
        
        # 打开输出文件
        with open(output_path, "wb") as file_to_save:
            header = {"Authorization": f"Bearer; {token}"}
            async with websockets.connect(api_url, extra_headers=header, ping_interval=None) as ws:
                await ws.send(full_client_request)
                print(f"[Pro-TTS] 请求已发送，等待响应...")
                
                while True:
                    res = await ws.recv()
                    done = parse_response(res, file_to_save)
                    if done:
                        break
        
        # 检查文件是否创建成功
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"[Pro-TTS] 音频文件生成成功: {output_path}, 大小: {file_size} 字节")
            return True
        else:
            print(f"[Pro-TTS] 音频文件生成失败，未找到文件: {output_path}")
            return False
            
    except Exception as e:
        print(f"[Pro-TTS] 生成语音时出错: {str(e)}")
        traceback.print_exc()
        return False


def main():
    """主函数入口点"""
    if len(sys.argv) < 4:
        print("使用方法: python pro_tts.py <文本文件路径> <声音类型> <输出路径>", file=sys.stderr)
        sys.exit(1)
    
    text_file_path = sys.argv[1]
    voice = sys.argv[2]
    output_path = sys.argv[3]
    
    print(f"[Pro-TTS] 参数: 文本文件={text_file_path}, 声音={voice}, 输出路径={output_path}")
    
    # 从文件中读取文本
    try:
        with open(text_file_path, 'r', encoding='utf-8') as f:
            text = f.read().strip()
        print(f"[Pro-TTS] 成功从文件读取文本，长度: {len(text)}")
        if len(text) > 50:
            print(f"[Pro-TTS] 文本内容片段: {text[:50]}...")
    except Exception as e:
        print(f"[Pro-TTS] 读取文本文件时出错: {e}")
        sys.exit(1)
    
    # 运行异步函数
    try:
        success = asyncio.run(generate_speech(text, voice, output_path))
        # 根据结果设置退出代码
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"[Pro-TTS] 执行过程中发生错误: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 