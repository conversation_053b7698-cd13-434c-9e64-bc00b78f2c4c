import { Crown } from "lucide-react"

interface VipBadgeProps {
  className?: string
  variant?: 'default' | 'badge' | 'small'
}

export function VipBadge({ className = "", variant = 'default' }: VipBadgeProps) {
  if (variant === 'badge') {
    return (
      <div
        className={`inline-flex items-center justify-center p-1 rounded-full bg-gradient-to-r from-amber-500 to-yellow-500 ${className}`}
      >
        <div className="bg-gray-900 rounded-full p-1">
          <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-full">
            <Crown className="w-5 h-5 text-black" />
          </div>
        </div>
      </div>
    )
  }

  if (variant === 'small') {
    return (
      <div
        className={`absolute -top-2.5 -right-2.5 rotate-[45deg] ${className}`}
      >
        <div className="flex items-center justify-center">
          <Crown className="w-4 h-4 text-amber-400" strokeWidth={2.5} />
        </div>
      </div>
    )
  }

  return (
    <div
      className={`absolute -top-3 -right-1 rotate-[30deg] ${className}`}
    >
      <div className="flex items-center justify-center">
        <Crown className="w-6 h-6 text-amber-400" strokeWidth={2.5} />
      </div>
    </div>
  )
}
