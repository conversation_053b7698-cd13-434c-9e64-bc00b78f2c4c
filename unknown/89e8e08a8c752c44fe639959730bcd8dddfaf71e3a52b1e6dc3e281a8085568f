import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { HistorySession, Message } from '../types/history';
import { READING_FIELD_KEYS, ALL_READING_FIELDS, containsRepeatedQuestionWarning } from '../utils/historyDetailUtils';

export const useMessageProcessor = (session: HistorySession | null) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const { t } = useTranslation();
  
  useEffect(() => {
    if (!session) return;
    
    const processMessages = () => {
      // 1. 初始化数据结构
      const processedMessages = [
        { type: 'user', content: session.question }
      ];
      
      let readingResult = null;

      // 确保session.readingResult存在
      if (!session.readingResult) {
        readingResult = {}; // 空对象作为后续处理的基础
      } else {
        try {
          readingResult = typeof session.readingResult === 'string' 
            ? JSON.parse(session.readingResult) 
            : session.readingResult;
        } catch (error) {
          // 如果是字符串但解析失败，则使用原始字符串作为prologue
          readingResult = { prologue: session.readingResult };
        }
      }

      // 处理特殊情况：如果readingResult只有content字段
      if (readingResult && readingResult.content && Object.keys(readingResult).length === 1) {
        const contentString = readingResult.content;
        
        // 检查content是否包含任何reading字段标记
        if (containsRepeatedQuestionWarning(contentString)) {
          // 如果不包含任何字段标记，则整个内容作为reader消息显示
          processedMessages.push({ type: 'reader', content: contentString });
        } else {
          // 如果包含字段标记，解析它们
          const parsedResult: {[key: string]: string} = {};
          
          // 使用定义好的字段列表
          const possibleFields = ALL_READING_FIELDS;
          
          // 按顺序提取字段
          let lastFieldName = '';
          let lastFieldIndex = -1;
          
          possibleFields.forEach(field => {
            const fieldIndex = contentString.indexOf(field);
            if (fieldIndex > -1) {
              // 如果这是找到的第一个字段，并且索引不是0，则前面部分作为警告
              if (lastFieldName === '' && fieldIndex > 0) {
                parsedResult.repeatedWarning = contentString.substring(0, fieldIndex).trim();
              }
              
              // 如果已经找到了前一个字段，计算前一个字段的内容
              if (lastFieldName && lastFieldIndex > -1) {
                parsedResult[lastFieldName] = contentString.substring(
                  lastFieldIndex + lastFieldName.length + 1, 
                  fieldIndex
                ).trim();
              }
              
              // 更新当前字段信息
              lastFieldName = field.replace(':', '');
              lastFieldIndex = fieldIndex;
            }
          });
          
          // 处理最后一个字段
          if (lastFieldName && lastFieldIndex > -1) {
            parsedResult[lastFieldName] = contentString.substring(
              lastFieldIndex + lastFieldName.length + 1
            ).trim();
          }
          
          // 如果没有找到任何字段，则把整个content作为answer
          if (Object.keys(parsedResult).length === 0) {
            parsedResult.answer = contentString;
          }
          
          // 用解析出的结果替换原始的readingResult
          readingResult = parsedResult;
        }
      }

      // 2. 处理基本解读结果
      // 检查并处理重复提问警告
      if (readingResult && readingResult.repeatedWarning) {
        // 将重复提问警告根据双换行符分隔成多个段落
        const warningParagraphs = readingResult.repeatedWarning.split('\n\n');
        // 为每个段落创建一个单独的reader消息
        warningParagraphs.forEach((paragraph: string) => {
          if (paragraph.trim()) {
            processedMessages.push({ type: 'reader', content: paragraph.trim() });
          }
        });
      }

      if (readingResult && readingResult.prologue) {
        processedMessages.push({ type: 'reader', content: readingResult.prologue });
      }

      if (readingResult && readingResult.answer) {
        processedMessages.push({ type: 'reader', content: readingResult.answer });
      }

      // 处理基础解读分析部分
      const analysisKeys = Object.keys(readingResult || {})
        .filter(key => key.startsWith(READING_FIELD_KEYS.ANALYSIS_PREFIX.replace(':', '')))
        .sort((a, b) => {
          const numA = parseInt(a.replace(READING_FIELD_KEYS.ANALYSIS_PREFIX.replace(':', ''), '')) || 0;
          const numB = parseInt(b.replace(READING_FIELD_KEYS.ANALYSIS_PREFIX.replace(':', ''), '')) || 0;
          return numA - numB;
        });

      // 如果没有找到分析键，但有content字段，则将content作为reader信息显示
      if (analysisKeys.length === 0 && readingResult && readingResult.content && 
          !processedMessages.find(msg => msg.type === 'reader' && msg.content === readingResult.content)) {
        processedMessages.push({ type: 'reader', content: readingResult.content });
      } else {
        // 正常处理分析键
        analysisKeys.forEach(key => {
          if (readingResult[key]) {
            processedMessages.push({ type: 'reader', content: readingResult[key] });
          }
        });
      }

      if (readingResult && readingResult.summary) {
        processedMessages.push({ type: 'reader', content: readingResult.summary });
      }

      // 3. 处理建议内容
      const adviceKeys = Object.keys(readingResult || {})
        .filter(key => key.startsWith(READING_FIELD_KEYS.ADVICE_PREFIX.replace(':', '')))
        .sort((a, b) => {
          const numA = parseInt(a.replace(READING_FIELD_KEYS.ADVICE_PREFIX.replace(':', ''), '')) || 0;
          const numB = parseInt(b.replace(READING_FIELD_KEYS.ADVICE_PREFIX.replace(':', ''), '')) || 0;
          return numA - numB;
        });

      if (adviceKeys.length > 0) {
        processedMessages.push({ type: 'user', content: t('reading.advice.request') });
        adviceKeys.forEach(key => {
          if (readingResult && readingResult[key]) {
            processedMessages.push({ type: 'reader', content: readingResult[key] });
          }
        });
      }

      // 4. 处理深度分析
      if (session.deepAnalysis) {
        try {
          const deepAnalysisData = JSON.parse(session.deepAnalysis);
          
          // 添加前言
          if (deepAnalysisData && deepAnalysisData.prologue) {
            // 对前言部分也进行分段处理
            const prologueParagraphs = deepAnalysisData.prologue.split('\n\n');
            if (prologueParagraphs.length > 1) {
              prologueParagraphs.forEach((paragraph: string) => {
                if (paragraph.trim()) {
                  processedMessages.push({ type: 'reader', content: paragraph.trim() });
                }
              });
            } else {
              processedMessages.push({ type: 'reader', content: deepAnalysisData.prologue.trim() });
            }
          }

          // 处理分析部分
          const deepAnalysisKeys = Object.keys(deepAnalysisData || {})
            .filter(key => key.startsWith(READING_FIELD_KEYS.ANALYSIS_PREFIX.replace(':', '')))
            .sort((a, b) => {
              const numA = parseInt(a.replace(READING_FIELD_KEYS.ANALYSIS_PREFIX.replace(':', ''), '')) || 0;
              const numB = parseInt(b.replace(READING_FIELD_KEYS.ANALYSIS_PREFIX.replace(':', ''), '')) || 0;
              return numA - numB;
            });

          for (const key of deepAnalysisKeys) {
            if (deepAnalysisData[key]) {
              // 根据双换行符分段处理深度解析内容
              const paragraphs = deepAnalysisData[key].split('\n\n');
              if (paragraphs.length > 1) {
                // 如果有多个段落，分别添加
                paragraphs.forEach((paragraph: string) => {
                  if (paragraph.trim()) {
                    processedMessages.push({ type: 'reader', content: paragraph.trim() });
                  }
                });
              } else {
                // 如果只有一个段落，直接添加
                processedMessages.push({ type: 'reader', content: deepAnalysisData[key].trim() });
              }
            }
          }

          // 添加总结
          if (deepAnalysisData && deepAnalysisData.summary) {
            // 对总结部分也进行分段处理
            const summaryParagraphs = deepAnalysisData.summary.split('\n\n');
            if (summaryParagraphs.length > 1) {
              summaryParagraphs.forEach((paragraph: string) => {
                if (paragraph.trim()) {
                  processedMessages.push({ type: 'reader', content: paragraph.trim() });
                }
              });
            } else {
              processedMessages.push({ type: 'reader', content: deepAnalysisData.summary.trim() });
            }
          }
        } catch (error) {
          // 对字符串形式的深度解析也进行分段处理
          const deepAnalysisParagraphs = session.deepAnalysis.split('\n\n');
          if (deepAnalysisParagraphs.length > 1) {
            deepAnalysisParagraphs.forEach((paragraph: string) => {
              if (paragraph.trim()) {
                processedMessages.push({ type: 'reader', content: paragraph.trim() });
              }
            });
          } else {
            processedMessages.push({ type: 'reader', content: session.deepAnalysis });
          }
        }
      }

      // 5. 处理对话历史
      const dialogHistory = session.dialogHistory || [];
      
      if (dialogHistory && dialogHistory.length > 0) {
        dialogHistory.forEach((msg) => {
          if (!msg || !msg.type || !msg.content) {
            return;
          }
          
          // 如果是用户消息，检查是否是特定的请求类型
          if (msg.type === 'user') {
            let translatedContent = msg.content;
            if (msg.content.includes('高维建议')) {
              translatedContent = t('reading.advice.request');
            } else if (msg.content.includes('进一步分析') || msg.content.includes('深度分析')) {
              translatedContent = t('reading.deep_analysis.waiting');
            }
            processedMessages.push({ type: 'user', content: translatedContent });
          } else {
            // 处理所有非用户消息，根据双换行符分段
            const paragraphs = msg.content.split('\n\n');
            if (paragraphs.length > 1) {
              // 如果有多个段落，分别添加
              paragraphs.forEach((paragraph) => {
                if (paragraph.trim()) {
                  processedMessages.push({ type: 'reader', content: paragraph.trim() });
                }
              });
            } else {
              // 如果只有一个段落，直接添加
              processedMessages.push({ type: 'reader', content: msg.content.trim() });
            }
          }
        });
      }

      // 检查是否存在reader类型的消息
      const hasReaderMessages = processedMessages.some(msg => msg.type === 'reader');
      
      // 确保至少有欢迎消息和问题解答
      if (!hasReaderMessages || processedMessages.length <= 1) { // 只有用户问题，没有解答
        // 如果仍然有content字段但未被处理，直接使用它
        if (readingResult && readingResult.content && typeof readingResult.content === 'string') {
          processedMessages.push({ type: 'reader', content: readingResult.content });
        } else {
          // 添加默认解读消息
          processedMessages.push({ 
            type: 'reader', 
            content: t('reading.result.default_reading', { defaultValue: '您好，感谢您的咨询。很抱歉，系统未能正确加载您的塔罗解读结果。请返回主页重新进行咨询，或联系客服获取支持。' }) 
          });
        }
      }
      
      return processedMessages;
    };

    setMessages(processMessages());
  // 移除t依赖，避免不必要的重新渲染
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session]);

  return messages;
}; 