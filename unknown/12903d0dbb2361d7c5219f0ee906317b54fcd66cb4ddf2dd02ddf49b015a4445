const { qwenAPI } = require('./apiClients');
const { parseHoroscopeJson, formatSingleHoroscope } = require('./horoscopeJsonParser');
const { saveHoroscopeToFile, getSignHoroscopeFromFile } = require('./horoscopeFileService');
const logger = require('../utils/logger');

// 星座ID列表
const ZODIAC_SIGNS = [
  'aries', 'taurus', 'gemini', 'cancer', 
  'leo', 'virgo', 'libra', 'scorpio', 
  'sagittarius', 'capricorn', 'aquarius', 'pisces'
];

// 运势类型
const HOROSCOPE_TYPES = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
  LOVE: 'love'
};

/**
 * 格式化日期为YYYY-MM-DD格式
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDateForDb(date) {
  const d = new Date(date);
  // 使用UTC方法获取年月日，避免时区影响
  const year = d.getUTCFullYear();
  const month = String(d.getUTCMonth() + 1).padStart(2, '0');
  const day = String(d.getUTCDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 获取星座名称
 * @param {string} sign 星座ID
 * @param {string} language 语言
 * @returns {string} 星座名称
 */
function getSignName(sign, language = 'zh-CN') {
  const signNames = {
    'zh-CN': {
      aries: '白羊座',
      taurus: '金牛座',
      gemini: '双子座',
      cancer: '巨蟹座',
      leo: '狮子座',
      virgo: '处女座',
      libra: '天秤座',
      scorpio: '天蝎座',
      sagittarius: '射手座',
      capricorn: '摩羯座',
      aquarius: '水瓶座',
      pisces: '双鱼座'
    },
    'en': {
      aries: 'Aries',
      taurus: 'Taurus',
      gemini: 'Gemini',
      cancer: 'Cancer',
      leo: 'Leo',
      virgo: 'Virgo',
      libra: 'Libra',
      scorpio: 'Scorpio',
      sagittarius: 'Sagittarius',
      capricorn: 'Capricorn',
      aquarius: 'Aquarius',
      pisces: 'Pisces'
    },
    'ja': {
      aries: '牡羊座',
      taurus: '牡牛座',
      gemini: '双子座',
      cancer: '蟹座',
      leo: '獅子座',
      virgo: '乙女座',
      libra: '天秤座',
      scorpio: '蠍座',
      sagittarius: '射手座',
      capricorn: '山羊座',
      aquarius: '水瓶座',
      pisces: '魚座'
    },
    'zh-TW': {
      aries: '牡羊座',
      taurus: '金牛座',
      gemini: '雙子座',
      cancer: '巨蟹座',
      leo: '獅子座',
      virgo: '處女座',
      libra: '天秤座',
      scorpio: '天蠍座',
      sagittarius: '射手座',
      capricorn: '摩羯座',
      aquarius: '水瓶座',
      pisces: '雙魚座'
    }
  };
  
  // 确保语言存在，如果不存在则使用默认语言
  const langMap = signNames[language] || signNames['zh-CN'];
  return langMap[sign] || sign;
}

/**
 * 使用qwen-turbo-latest API生成星座运势
 * @param {string} type 运势类型
 * @param {string} sign 星座ID
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<Object>} 生成的运势数据
 */
async function generateHoroscope(type, sign, date, language = 'zh-CN') {
  // 格式化日期为字符串
  const dateStr = formatDateForDb(date);
  
  // 根据不同的运势类型和语言，构建不同的提示词
  let prompt = '';
  
  // 设置年月
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  
  // 根据运势类型设置提示词
  switch (type) {
    case HOROSCOPE_TYPES.DAILY:
      prompt = language === 'en' 
        ? `Please generate daily horoscopes for all 12 zodiac signs. You must return data directly in JSON format as follows:
{
  "aries": "Aries horoscope content",
  "taurus": "Taurus horoscope content",
  "gemini": "Gemini horoscope content",
  "cancer": "Cancer horoscope content",
  "leo": "Leo horoscope content",
  "virgo": "Virgo horoscope content",
  "libra": "Libra horoscope content",
  "scorpio": "Scorpio horoscope content",
  "sagittarius": "Sagittarius horoscope content",
  "capricorn": "Capricorn horoscope content",
  "aquarius": "Aquarius horoscope content",
  "pisces": "Pisces horoscope content"
}

Content Structure Guidelines:
Begin each horoscope with a brief explanation of the current planetary movements, zodiac positions, and house influences that are affecting this sign today. Move naturally into identifying any current challenges, misconceptions, or blind spots that may be present in their life. Explore the underlying causes and internal mechanisms behind these issues, helping readers understand the deeper patterns at work. Present a thoughtful comparison between their current reality and their ideal situation, highlighting areas for growth. Conclude with specific, actionable advice that readers can implement today. Throughout the reading, maintain a balance between encouraging care for others and emphasizing the importance of self-care and personal boundaries.

Writing Style:
Tone should be gentle yet insightful, both authoritative and empathetic. Use words like "perhaps" and "maybe" to maintain appropriate uncertainty. Transition smoothly from macro astrological background to micro life details. Avoid absolute predictions, focus on inspiration and guidance. Text should flow naturally with narrative quality, avoiding bullet points or structured lists.

Length Requirement:
About 200 words for each zodiac sign.

Format Requirement:
Use plain text format with double line breaks between paragraphs. Do not use markdown formatting.`
        : language === 'ja'
        ? `12星座の今日の運勢を生成してください。以下のJSON形式で直接データを返してください：
{
  "aries": "牡羊座の運勢内容",
  "taurus": "牡牛座の運勢内容",
  "gemini": "双子座の運勢内容",
  "cancer": "蟹座の運勢内容",
  "leo": "獅子座の運勢内容",
  "virgo": "乙女座の運勢内容",
  "libra": "天秤座の運勢内容",
  "scorpio": "蠍座の運勢内容",
  "sagittarius": "射手座の運勢内容",
  "capricorn": "山羊座の運勢内容",
  "aquarius": "水瓶座の運勢内容",
  "pisces": "魚座の運勢内容"
}

内容構成ガイドライン：
各星座の運勢は、今日その星座に影響を与える具体的な惑星の動き、星座の位置、ハウスの影響について簡潔に説明することから始めてください。その後、現在の生活で存在する可能性のある課題、誤解、または盲点を自然に特定していきます。これらの問題の根本的な原因と内部メカニズムを探り、より深いパターンを理解できるよう読者を導きます。現在の現実と理想的な状況を思慮深く比較し、成長のための領域を明確にします。最後に、読者が今日実践できる具体的で実行可能なアドバイスで締めくくります。読書全体を通じて、他者への思いやりと自己ケアや個人的な境界の重要性のバランスを保ちます。

文章スタイル：
洞察力があり、権威的でありながら共感的な穏やかな口調で書きます。「かもしれない」「おそらく」などの言葉を使って適度な不確実性を保ちます。マクロの占星術的背景からミクロの生活の詳細へとスムーズに移行します。絶対的な予測を避け、インスピレーションとガイダンスに重点を置きます。文章は自然に流れ、物語性を持ち、箇条書きや構造化されたリストは避けます。

文字数要件：
各星座約200字程度。

フォーマット要件：
段落間に二重改行を使用したプレーンテキスト形式を使用してください。マークダウン形式は使用しないでください。`
        : language === 'zh-TW'
        ? `請為12個星座生成今日運勢文本，你必須直接返回JSON格式數據，格式如下：
{
  "aries": "牡羊座運勢內容",
  "taurus": "金牛座運勢內容",
  "gemini": "雙子座運勢內容",
  "cancer": "巨蟹座運勢內容",
  "leo": "獅子座運勢內容",
  "virgo": "處女座運勢內容",
  "libra": "天秤座運勢內容",
  "scorpio": "天蠍座運勢內容",
  "sagittarius": "射手座運勢內容",
  "capricorn": "摩羯座運勢內容",
  "aquarius": "水瓶座運勢內容",
  "pisces": "雙魚座運勢內容"
}

內容結構指導：
每個星座的運勢應該以簡潔說明當前影響該星座的具體行星運行、星座位置和宮位影響作為開始。然後自然地識別出當前生活中可能存在的挑戰、誤解或盲點。深入探討這些問題的根本原因和內在機制，幫助讀者理解更深層的運作模式。提供一個深思熟慮的比較，對比當前現實與理想狀況，突出成長的領域。以讀者今天可以實施的具體、可操作的建議作為結尾。在整個解讀過程中，保持對他人關懷和強調自我照顧及個人界限重要性之間的平衡。

寫作風格：
語調溫和而有洞察力，既有權威性又富有同理心。使用「可能」、「或許」等詞彙保持適度的不確定性。從宏觀的占星背景順暢過渡到微觀的生活細節。避免過於絕對的預測，注重啟發和引導。文字流暢自然，具有敘事性，避免使用條列式或結構化列表。

字數要求：
每個星座解读不少于200字。

格式要求：
使用段落間雙換行的純文本格式。不要使用markdown格式。`
        : `请为12个星座生成今日运势文本，你必须直接返回JSON格式数据，格式如下：
{
  "aries": "白羊座运势内容",
  "taurus": "金牛座运势内容",
  "gemini": "双子座运势内容",
  "cancer": "巨蟹座运势内容",
  "leo": "狮子座运势内容",
  "virgo": "处女座运势内容",
  "libra": "天秤座运势内容",
  "scorpio": "天蝎座运势内容",
  "sagittarius": "射手座运势内容",
  "capricorn": "摩羯座运势内容",
  "aquarius": "水瓶座运势内容",
  "pisces": "双鱼座运势内容"
}

内容结构指导：
每个星座的运势应该以简洁说明当前影响该星座的具体行星运行、星座位置和宫位影响作为开始。然后自然地识别出当前生活中可能存在的挑战、误解或盲点。深入探讨这些问题的根本原因和内在机制，帮助读者理解更深层的运作模式。提供一个深思熟虑的比较，对比当前现实与理想状况，突出成长的领域。以读者今天可以实施的具体、可操作的建议作为结尾。在整个解读过程中，保持对他人关怀和强调自我照顾及个人界限重要性之间的平衡。

写作风格：
语调温和而有洞察力，既有权威性又富有同理心。使用"可能"、"或许"等词汇保持适度的不确定性。从宏观的占星背景顺畅过渡到微观的生活细节。避免过于绝对的预测，注重启发和引导。文字流畅自然，具有叙事性，避免使用条列式或结构化列表。

字数要求：
每个星座解读不少于200字。

格式要求：
使用段落间双换行的纯文本格式。不要使用markdown格式。`;
      break;
      case HOROSCOPE_TYPES.WEEKLY:
        prompt = language === 'en'
          ? `Please generate weekly horoscopes for all 12 zodiac signs for the week starting ${dateStr}. You must return data directly in JSON format as follows:
  {
    "aries": "Aries weekly horoscope content",
    "taurus": "Taurus weekly horoscope content",
    "gemini": "Gemini weekly horoscope content",
    "cancer": "Cancer weekly horoscope content",
    "leo": "Leo weekly horoscope content",
    "virgo": "Virgo weekly horoscope content",
    "libra": "Libra weekly horoscope content",
    "scorpio": "Scorpio weekly horoscope content",
    "sagittarius": "Sagittarius weekly horoscope content",
    "capricorn": "Capricorn weekly horoscope content",
    "aquarius": "Aquarius weekly horoscope content",
    "pisces": "Pisces weekly horoscope content"
  }
  
  Content Structure Guidelines:
  Weave specific time references naturally throughout the text, mentioning particular days like "Friday," "weekend," or "mid-week" to give readers clear temporal anchors. Ground all predictions in concrete astrological events by referencing specific planetary movements, aspects, and transits as the driving forces behind the week's developments. Focus predominantly on positive opportunities and possibilities that may emerge, highlighting the potential for growth and advancement. Place special emphasis on social connections, friendships, and romantic relationships, as these tend to be the most meaningful aspects of weekly experiences. Structure the narrative to unfold chronologically, describing how events and energies will develop and shift throughout the seven-day period, creating a sense of anticipation and forward momentum.
  
  Writing Style:
  Tone should be positive and optimistic, full of anticipation and excitement for what the week may bring. Use words like "will" and "may" to indicate possibilities while maintaining an upbeat, encouraging voice. Text should flow naturally with a storytelling quality, avoiding rigid structures or bullet-pointed lists in favor of engaging narrative prose.
  
  Length Requirement:
  Keep it around 200 words per sign.
  
  Format Requirement:
  Use plain text format with double line breaks between paragraphs. Do not use markdown formatting.`
          : language === 'ja'
          ? `${dateStr}から始まる週の12星座の週間運勢を生成してください。以下のJSON形式で直接データを返してください：
  {
    "aries": "牡羊座の週間運勢内容",
    "taurus": "牡牛座の週間運勢内容",
    "gemini": "双子座の週間運勢内容",
    "cancer": "蟹座の週間運勢内容",
    "leo": "獅子座の週間運勢内容",
    "virgo": "乙女座の週間運勢内容",
    "libra": "天秤座の週間運勢内容",
    "scorpio": "蠍座の週間運勢内容",
    "sagittarius": "射手座の週間運勢内容",
    "capricorn": "山羊座の週間運勢内容",
    "aquarius": "水瓶座の週間運勢内容",
    "pisces": "魚座の週間運勢内容"
  }
  
  内容構成ガイドライン：
  文章全体を通じて「金曜日」「週末」「週の中頃」など具体的な時間の参照を自然に織り込み、読者に明確な時間的な目印を提供します。すべての予測を具体的な占星術的事象に基づかせ、特定の惑星の動き、相位、トランジットを週の展開の原動力として言及します。成長と向上の可能性を強調し、現れる可能性のある積極的な機会と可能性に主に焦点を当てます。社会的なつながり、友情、恋愛関係に特別な重点を置き、これらは週間体験の中で最も意味のある側面である傾向があります。7日間の期間を通じて事象とエネルギーがどのように発展し変化するかを時系列で描写し、期待感と前進の勢いを創出するように物語を構成します。
  
  文章スタイル：
  その週がもたらすかもしれないことに対する期待感と興奮に満ちた、前向きで楽観的な口調で書きます。明るく励ましの声を保ちながら、「でしょう」や「かもしれない」などの可能性を示す言葉を使用します。魅力的な物語的散文を支持し、硬直した構造や箇条書きリストを避けて、自然に流れるストーリーテリングの質を持つテキストにします。
  
  文字数要件：
  各星座約200字程度。
  
  フォーマット要件：
  段落間に二重改行を使用したプレーンテキスト形式を使用してください。マークダウン形式は使用しないでください。`
          : language === 'zh-TW'
          ? `請為所有12個星座生成從${dateStr}開始的一週運勢文本，你必須直接返回JSON格式數據，格式如下：
  {
    "aries": "牡羊座週運內容",
    "taurus": "金牛座週運內容",
    "gemini": "雙子座週運內容",
    "cancer": "巨蟹座週運內容",
    "leo": "獅子座週運內容",
    "virgo": "處女座週運內容",
    "libra": "天秤座週運內容",
    "scorpio": "天蠍座週運內容",
    "sagittarius": "射手座週運內容",
    "capricorn": "摩羯座週運內容",
    "aquarius": "水瓶座週運內容",
    "pisces": "雙魚座週運內容"
  }
  
  內容結構指導：
  在整個文本中自然地織入具體的時間參照，提到特定的日子如「週五」、「週末」或「週中」，為讀者提供清晰的時間錨點。將所有預測建立在具體的占星事件基礎上，引用特定的行星運動、相位和行運作為本週發展的驅動力。主要關注可能出現的積極機會和可能性，突出成長和進步的潛力。特別強調社交聯繫、友誼和浪漫關係，因為這些往往是週間體驗中最有意義的方面。按時間順序構建敘事，描述事件和能量如何在七天期間內發展和轉變，創造期待感和前進的動力。
  
  寫作風格：
  語調應該積極樂觀，充滿對本週可能帶來的期待和興奮。使用「將會」和「可能」等詞彙來表示可能性，同時保持樂觀、鼓勵的語調。文字應該自然流暢，具有故事性質，避免僵化的結構或條列式列表，而是採用引人入勝的敘述性散文。
  
  字數要求：
  每個星座不少於200字。
  
  格式要求：
  使用段落間雙換行的純文本格式。不要使用markdown格式。`
          : `请为所有12个星座生成从${dateStr}开始的一周运势文本，你必须直接返回JSON格式数据，格式如下：
  {
    "aries": "白羊座周运内容",
    "taurus": "金牛座周运内容",
    "gemini": "双子座周运内容",
    "cancer": "巨蟹座周运内容",
    "leo": "狮子座周运内容",
    "virgo": "处女座周运内容",
    "libra": "天秤座周运内容",
    "scorpio": "天蝎座周运内容",
    "sagittarius": "射手座周运内容",
    "capricorn": "摩羯座周运内容",
    "aquarius": "水瓶座周运内容",
    "pisces": "双鱼座周运内容"
  }
  
  内容结构指导：
  在整个文本中自然地织入具体的时间参照，提到特定的日子如"周五"、"周末"或"周中"，为读者提供清晰的时间锚点。将所有预测建立在具体的占星事件基础上，引用特定的行星运动、相位和行运作为本周发展的驱动力。主要关注可能出现的积极机会和可能性，突出成长和进步的潜力。特别强调社交联系、友谊和浪漫关系，因为这些往往是周间体验中最有意义的方面。按时间顺序构建叙事，描述事件和能量如何在七天期间内发展和转变，创造期待感和前进的动力。
  
  写作风格：
  语调应该积极乐观，充满对本周可能带来的期待和兴奋。使用"将会"和"可能"等词汇来表示可能性，同时保持乐观、鼓励的语调。文字应该自然流畅，具有故事性质，避免僵化的结构或条列式列表，而是采用引人入胜的叙述性散文。
  
  字数要求：
  每個星座不少于200字。
  
  格式要求：
  使用段落间双换行的纯文本格式。不要使用markdown格式。`;
        break;
        case HOROSCOPE_TYPES.MONTHLY:
          prompt = language === 'en'
            ? `Please generate monthly horoscopes for all 12 zodiac signs for ${month} ${year}. You must return data directly in JSON format as follows:
    {
      "aries": "Aries monthly horoscope content",
      "taurus": "Taurus monthly horoscope content",
      "gemini": "Gemini monthly horoscope content",
      "cancer": "Cancer monthly horoscope content",
      "leo": "Leo monthly horoscope content",
      "virgo": "Virgo monthly horoscope content",
      "libra": "Libra monthly horoscope content",
      "scorpio": "Scorpio monthly horoscope content",
      "sagittarius": "Sagittarius monthly horoscope content",
      "capricorn": "Capricorn monthly horoscope content",
      "aquarius": "Aquarius monthly horoscope content",
      "pisces": "Pisces monthly horoscope content"
    }
    
    Core Requirements:
    Divide the month into different time periods with specific dates to create a chronological narrative flow. Use each zodiac sign's ruling planet as the central guiding force throughout the monthly forecast. Incorporate specific astrological houses and clearly explain their areas of influence on daily life. Describe important turning points and energy shifts that occur within the month. Include multiple planetary aspects and their interactions to provide depth and complexity. Emphasize themes of personal growth, transformation, and future planning opportunities.
    
    Writing Style:
    Maintain a profound and insightful tone while using professional astrological terminology. Structure the content chronologically, progressing from internal emotional states to external environmental influences to create a comprehensive monthly narrative.
    
    Length Requirement:
    Keep each zodiac sign around 200 words.
    
    Format Requirement:
    Use plain text format with double line breaks between paragraphs for readability. Do not use any markdown formatting.`
            : language === 'ja'
            ? `${year}年${month}の12星座の月間運勢を生成してください。以下のJSON形式で直接データを返してください：
    {
      "aries": "牡羊座の月間運勢内容",
      "taurus": "牡牛座の月間運勢内容",
      "gemini": "双子座の月間運勢内容",
      "cancer": "蟹座の月間運勢内容",
      "leo": "獅子座の月間運勢内容",
      "virgo": "乙女座の月間運勢内容",
      "libra": "天秤座の月間運勢内容",
      "scorpio": "蠍座の月間運勢内容",
      "sagittarius": "射手座の月間運勢内容",
      "capricorn": "山羊座の月間運勢内容",
      "aquarius": "水瓶座の月間運勢内容",
      "pisces": "魚座の月間運勢内容"
    }
    
    核心要求：
    月全体を異なる時間帯に分割し、具体的な日付を明記して時系列的な物語の流れを作成してください。各星座の支配星を月間予報全体の中心的な指導力として使用してください。具体的な占星術ハウスを組み込み、日常生活への影響領域を明確に説明してください。月内で発生する重要な転換点とエネルギーシフトを描写してください。複数の惑星相位とその相互作用を含めて、深さと複雑さを提供してください。個人の成長、変容、未来計画の機会のテーマを強調してください。
    
    文章スタイル：
    専門的な占星術用語を使用しながら、深遠で洞察力のある口調を維持してください。内面の感情状態から外部環境の影響へと時系列順に構成し、包括的な月間叙述を作成してください。
    
    文字数要件：
    各星座約200字程度にしてください。
    
    フォーマット要件：
    読みやすさのために段落間に二重改行を使用した平文形式を使用してください。マークダウン形式は使用しないでください。`
            : language === 'zh-TW'
            ? `請為所有12個星座生成${year}年${month}的月度運勢文本，你必須直接返回JSON格式數據，格式如下：
    {
      "aries": "牡羊座月運內容",
      "taurus": "金牛座月運內容",
      "gemini": "雙子座月運內容",
      "cancer": "巨蟹座月運內容",
      "leo": "獅子座月運內容",
      "virgo": "處女座月運內容",
      "libra": "天秤座月運內容",
      "scorpio": "天蠍座月運內容",
      "sagittarius": "射手座月運內容",
      "capricorn": "摩羯座月運內容",
      "aquarius": "水瓶座月運內容",
      "pisces": "雙魚座月運內容"
    }
    
    核心要求：
    將月份劃分為不同時間段並標註具體日期，以建立時間序列的敘述流程。以各星座守護星作為整個月度預測的核心引導力量。融入具體的占星宮位並清楚說明其對日常生活的影響領域。描述月內發生的重要轉折點和能量變化。包含多重行星相位及其相互作用以提供深度和複雜性。強調個人成長、轉變和未來規劃機會的主題。
    
    寫作風格：
    使用占星學專業術語的同時保持深邃而富有洞察力的語調。按時間順序構建內容，從內在情感狀態進展到外在環境影響，創造完整的月運敘述。
    
    字數要求：
    每個星座不少於200字。
    
    格式要求：
    使用段落間雙重換行的純文本格式以提升可讀性。請勿使用任何markdown格式。`
            : `请为所有12个星座生成${year}年${month}的月度运势文本，你必须直接返回JSON格式数据，格式如下：
    {
      "aries": "白羊座月运内容",
      "taurus": "金牛座月运内容",
      "gemini": "双子座月运内容",
      "cancer": "巨蟹座月运内容",
      "leo": "狮子座月运内容",
      "virgo": "处女座月运内容",
      "libra": "天秤座月运内容",
      "scorpio": "天蝎座月运内容",
      "sagittarius": "射手座月运内容",
      "capricorn": "摩羯座月运内容",
      "aquarius": "水瓶座月运内容",
      "pisces": "双鱼座月运内容"
    }
    
    核心要求：
    将月份划分为不同时间段并标注具体日期，以建立时间序列的叙述流程。以各星座守护星作为整个月度预测的核心引导力量。融入具体的占星宫位并清楚说明其对日常生活的影响领域。描述月内发生的重要转折点和能量变化。包含多重行星相位及其相互作用以提供深度和复杂性。强调个人成长、转变和未来规划机会的主题。
    
    写作风格：
    使用占星学专业术语的同时保持深邃而富有洞察力的语调。按时间顺序构建内容，从内在情感状态进展到外在环境影响，创造完整的月运叙述。
    
    字数要求：
    每个星座不少于200字。
    
    格式要求：
    使用段落间双重换行的纯文本格式以提升可读性。请勿使用任何markdown格式。`;
        break;
        case HOROSCOPE_TYPES.YEARLY:
          prompt = language === 'en'
            ? `Please generate yearly horoscopes for all 12 zodiac signs for ${year}. You must return data directly in JSON format as follows:
    {
      "aries": "Aries yearly horoscope content",
      "taurus": "Taurus yearly horoscope content",
      "gemini": "Gemini yearly horoscope content",
      "cancer": "Cancer yearly horoscope content",
      "leo": "Leo yearly horoscope content",
      "virgo": "Virgo yearly horoscope content",
      "libra": "Libra yearly horoscope content",
      "scorpio": "Scorpio yearly horoscope content",
      "sagittarius": "Sagittarius yearly horoscope content",
      "capricorn": "Capricorn yearly horoscope content",
      "aquarius": "Aquarius yearly horoscope content",
      "pisces": "Pisces yearly horoscope content"
    }
    
    Content Structure Requirements:
    Write each horoscope as a flowing narrative that covers the entire year ahead. Begin with an overview of the major themes and planetary influences that will shape this zodiac sign's journey throughout the year. Discuss the positive developments, opportunities, and areas of growth that await, while also addressing potential challenges and areas requiring extra attention or caution. Include detailed analysis of romantic relationships and partnership dynamics, covering both single individuals seeking love and those already in committed relationships. Provide comprehensive career guidance covering professional advancement, workplace dynamics, financial prospects, and entrepreneurial opportunities. Weave in specific astrological events, planetary transits, and their corresponding dates that will be particularly significant for this sign.
    
    Core Features to Include:
    Incorporate long-term astrological perspectives by discussing major planetary movements and their multi-month influences throughout the year. Organize the content by examining different life domains including personal growth, relationships, career, health, and spiritual development. Provide specific timing guidance by mentioning key dates, planetary stations, and astrological seasons that will be most impactful. Emphasize transformational themes and inner growth opportunities that will emerge during this period. Maintain a balanced perspective by honestly discussing both favorable and challenging aspects while offering practical wisdom. Include actionable advice and coping strategies that readers can implement in their daily lives.
    
    Writing Style:
    Use an authoritative yet approachable tone that demonstrates deep astrological knowledge while remaining accessible to general readers. Blend broad cosmic perspectives with specific practical applications. Write in a predictive yet empowering manner that guides readers toward making informed decisions.
    
    Length Requirement:
    Keep each sign's horoscope around 300 words, ensuring comprehensive coverage across all mentioned areas.
    
    Format Requirement:
    Use plain text format with double line breaks between paragraphs for readability. Do not use any markdown formatting.`
            : language === 'ja'
            ? `${year}年の12星座の年間運勢を生成してください。以下のJSON形式で直接データを返してください：
    {
      "aries": "牡羊座の年間運勢内容",
      "taurus": "牡牛座の年間運勢内容",
      "gemini": "双子座の年間運勢内容",
      "cancer": "蟹座の年間運勢内容",
      "leo": "獅子座の年間運勢内容",
      "virgo": "乙女座の年間運勢内容",
      "libra": "天秤座の年間運勢内容",
      "scorpio": "蠍座の年間運勢内容",
      "sagittarius": "射手座の年間運勢内容",
      "capricorn": "山羊座の年間運勢内容",
      "aquarius": "水瓶座の年間運勢内容",
      "pisces": "魚座の年間運勢内容"
    }
    
    内容構成要件：
    各星座の運勢を一年間の流れを通した物語として書いてください。まず、その星座の一年の歩みを形作る主要なテーマと惑星の影響について概観を述べることから始めます。待ち受けている前向きな発展、機会、成長の領域について論じる一方で、潜在的な課題や特別な注意や警戒が必要な領域についても取り上げます。恋愛関係とパートナーシップの力学について詳細に分析し、恋人を求める独身者と既に真剣な関係にある人々の両方を対象とします。職業的な進歩、職場の力学、金銭的見通し、起業の機会を含む包括的なキャリア指導を提供します。この星座にとって特に重要となる具体的な占星術的イベント、惑星の通過、およびそれらの対応する日付を織り込みます。
    
    含めるべき核心特徴：
    年間を通じた主要な惑星の動きとその数ヶ月にわたる影響を論じることで、長期的な占星術的視点を組み込みます。個人的成長、人間関係、キャリア、健康、精神的発展を含む異なる生活領域を検討することで内容を整理します。最もインパクトのある重要な日付、惑星の配置、占星術的季節について言及することで具体的なタイミング指導を提供します。この期間中に現れる変容的なテーマと内面的成長の機会を強調します。実用的な知恵を提供しながら、好ましい面と挑戦的な面の両方を正直に論じることでバランスの取れた視点を維持します。読者が日常生活で実践できる実行可能なアドバイスと対処戦略を含めます。
    
    文章スタイル：
    深い占星術的知識を示しながらも一般読者にとって理解しやすい、権威的でありながら親しみやすい口調を使用します。広い宇宙的視点と具体的な実用的応用を融合させます。読者が情報に基づいた決定を下すよう導く予測的でありながら力を与えるような方法で書きます。
    
    文字数要件：
    各星座の運勢を約300字程度に保ち、言及されたすべての領域を包括的にカバーします。
    
    フォーマット要件：
    読みやすさのために段落間に二重改行を使用した平文形式を使用してください。マークダウン形式は使用しないでください。`
            : language === 'zh-TW'
            ? `請為所有12個星座生成${year}年的年度運勢文本，你必須直接返回JSON格式數據，格式如下：
    {
      "aries": "牡羊座年運內容",
      "taurus": "金牛座年運內容",
      "gemini": "雙子座年運內容",
      "cancer": "巨蟹座年運內容",
      "leo": "獅子座年運內容",
      "virgo": "處女座年運內容",
      "libra": "天秤座年運內容",
      "scorpio": "天蠍座年運內容",
      "sagittarius": "射手座年運內容",
      "capricorn": "摩羯座年運內容",
      "aquarius": "水瓶座年運內容",
      "pisces": "雙魚座年運內容"
    }
    
    內容結構要求：
    將每個星座的運勢寫成一個貫穿全年的完整敘述。首先概述將塑造該星座整年運程的主要主題和行星影響力。討論等待著的積極發展、機會和成長領域，同時也要處理潛在的挑戰和需要額外關注或謹慎的方面。包含對感情關係和伴侶關係動態的詳細分析，既涵蓋尋找愛情的單身者，也包括已經處於穩定關係中的人群。提供全面的事業指導，涵蓋職業發展、職場動態、財務前景和創業機會。融入對該星座特別重要的具體占星事件、行星轉換及其相應日期。
    
    需要包含的核心特徵：
    通過討論主要行星運動及其在全年中長達數月的影響力來融入長期占星術視角。通過檢視包括個人成長、人際關係、事業、健康和精神發展在內的不同生活領域來組織內容。通過提及關鍵日期、行星位置和對該星座最具影響力的占星季節來提供具體的時機指導。強調在此期間將出現的轉型主題和內在成長機會。通過誠實地討論有利和挑戰性的方面同時提供實用智慧來保持平衡的視角。包含讀者可以在日常生活中實施的可行建議和應對策略。
    
    寫作風格：
    使用權威但平易近人的語調，展示深厚的占星學知識，同時保持一般讀者的可理解性。將廣闊的宇宙視角與具體的實用應用相結合。以預測性但賦權的方式寫作，引導讀者做出明智的決策。
    
    字數要求：
    每個星座的運勢保持在不少於400字，確保全面覆蓋所有提及的領域。
    
    格式要求：
    使用段落間雙重換行的純文本格式以提升可讀性。請勿使用任何markdown格式。`
            : `请为所有12个星座生成${year}年的年度运势文本，你必须直接返回JSON格式数据，格式如下：
    {
      "aries": "白羊座年运内容",
      "taurus": "金牛座年运内容",
      "gemini": "双子座年运内容",
      "cancer": "巨蟹座年运内容",
      "leo": "狮子座年运内容",
      "virgo": "处女座年运内容",
      "libra": "天秤座年运内容",
      "scorpio": "天蝎座年运内容",
      "sagittarius": "射手座年运内容",
      "capricorn": "摩羯座年运内容",
      "aquarius": "水瓶座年运内容",
      "pisces": "双鱼座年运内容"
    }
    
    内容结构要求：
    将每个星座的运势写成一个贯穿全年的完整叙述。首先概述将塑造该星座整年运程的主要主题和行星影响力。讨论等待着的积极发展、机会和成长领域，同时也要处理潜在的挑战和需要额外关注或谨慎的方面。包含对感情关系和伴侣关系动态的详细分析，既涵盖寻找爱情的单身者，也包括已经处于稳定关系中的人群。提供全面的事业指导，涵盖职业发展、职场动态、财务前景和创业机会。融入对该星座特别重要的具体占星事件、行星转换及其相应日期。
      
    写作风格：
    使用权威但平易近人的语调，展示深厚的占星学知识，同时保持一般读者的可理解性。将广阔的宇宙视角与具体的实用应用相结合。以预测性但赋权的方式写作，引导读者做出明智的决策。
    
    字数要求：
    每个星座的运势保持在不少于400字，确保全面覆盖所有提及的领域。
    
    格式要求：
    使用段落间双重换行的纯文本格式以提升可读性。请勿使用任何markdown格式。`;
            break;
        case HOROSCOPE_TYPES.LOVE:
          prompt = language === 'en'
            ? `Please generate love horoscopes for all 12 zodiac signs for ${month} ${year}. You must return data directly in JSON format as follows:
    {
      "aries": "Aries love horoscope content",
      "taurus": "Taurus love horoscope content",
      "gemini": "Gemini love horoscope content",
      "cancer": "Cancer love horoscope content",
      "leo": "Leo love horoscope content",
      "virgo": "Virgo love horoscope content",
      "libra": "Libra love horoscope content",
      "scorpio": "Scorpio love horoscope content",
      "sagittarius": "Sagittarius love horoscope content",
      "capricorn": "Capricorn love horoscope content",
      "aquarius": "Aquarius love horoscope content",
      "pisces": "Pisces love horoscope content"
    }
    
    Core Features to Include:
    Use each zodiac sign's ruling planet as the primary narrative thread to guide the emotional journey throughout the month. Incorporate specific key dates when planetary aspects will be most influential for romantic matters and relationship developments. Analyze the emotional spectrum from deep inner feelings and subconscious desires to outward expressions and behavioral patterns in romantic situations. Address various relationship dynamics including romantic partnerships, intimate friendships, potential new connections, and existing committed relationships. Focus deeply on psychological undercurrents, exploring how inner thoughts and emotional transformations will manifest in love life. Provide concrete, actionable advice for navigating emotional challenges and maximizing romantic opportunities during this period.
    
    Writing Style:
    Adopt a nuanced and insightful tone that emphasizes the richness of emotional experiences and romantic connections. Approach the content from psychological perspectives that encourage readers to reflect on their inner emotional landscape. Write in flowing, contemplative prose that invites introspection and deeper understanding of romantic patterns.
    
    Length Requirement:
    Keep each sign's love horoscope around 200 words, ensuring comprehensive emotional guidance.
    
    Format Requirement:
    Use plain text format with double line breaks between paragraphs. Do not use markdown formatting.`
            : language === 'ja'
            ? `${year}年${month}の12星座の恋愛運を生成してください。以下のJSON形式で直接データを返してください：
    {
      "aries": "牡羊座の恋愛運内容",
      "taurus": "牡牛座の恋愛運内容",
      "gemini": "双子座の恋愛運内容",
      "cancer": "蟹座の恋愛運内容",
      "leo": "獅子座の恋愛運内容",
      "virgo": "乙女座の恋愛運内容",
      "libra": "天秤座の恋愛運内容",
      "scorpio": "蠍座の恋愛運内容",
      "sagittarius": "射手座の恋愛運内容",
      "capricorn": "山羊座の恋愛運内容",
      "aquarius": "水瓶座の恋愛運内容",
      "pisces": "魚座の恋愛運内容"
    }
    
    含めるべき核心特徴：
    各星座の守護星を主要な物語の糸として使用し、月全体を通じて感情的な旅路を導きます。恋愛問題や関係の発展に最も影響を与える惑星のアスペクトがある具体的な重要日付を組み込みます。恋愛状況における深い内面的感情や無意識の願望から外向的な表現や行動パターンまで、感情のスペクトラムを分析します。恋愛パートナーシップ、親密な友情、潜在的な新しいつながり、既存の真剣な関係を含む様々な関係の力学に取り組みます。心理的な底流に深く焦点を当て、内面の思考と感情の変容が恋愛生活にどのように現れるかを探求します。この期間中の感情的な挑戦を乗り越え、恋愛の機会を最大化するための具体的で実行可能なアドバイスを提供します。
    
    文章スタイル：
    感情体験と恋愛関係の豊かさを強調する繊細で洞察力のある口調を採用します。読者が内面の感情的な風景について反省することを促す心理学的視点からコンテンツにアプローチします。内省とロマンチックなパターンのより深い理解を誘う流れるような思索的な文章で書きます。
    
    文字数要件：
    各星座の恋愛運を約200字程度に保ち、包括的な感情指導を確保します。
    
    フォーマット要件：
    段落間に二重改行を使用したプレーンテキスト形式を使用してください。マークダウン形式は使用しないでください。`
            : language === 'zh-TW'
            ? `請為所有12個星座生成${year}年${month}的愛情運勢文本，你必須直接返回JSON格式數據，格式如下：
    {
      "aries": "牡羊座愛情運內容",
      "taurus": "金牛座愛情運內容",
      "gemini": "雙子座愛情運內容",
      "cancer": "巨蟹座愛情運內容",
      "leo": "獅子座愛情運內容",
      "virgo": "處女座愛情運內容",
      "libra": "天秤座愛情運內容",
      "scorpio": "天蠍座愛情運內容",
      "sagittarius": "射手座愛情運內容",
      "capricorn": "摩羯座愛情運內容",
      "aquarius": "水瓶座愛情運內容",
      "pisces": "雙魚座愛情運內容"
    }
    
    需要包含的核心特徵：
    使用每個星座的守護星作為主要的敘述線索，引導整個月份的情感旅程。融入當行星相位對愛情事務和關係發展最具影響力的具體關鍵日期。分析從深層內在感受和潛意識慾望到在浪漫情境中的外在表達和行為模式的情感光譜。處理各種關係動態，包括浪漫伴侶關係、親密友誼、潛在的新連結，以及現有的承諾關係。深度關注心理暗流，探索內在思考和情感轉化如何在愛情生活中顯現。提供具體可行的建議，幫助在此期間克服情感挑戰並最大化浪漫機會。
    
    寫作風格：
    採用細膩而富有洞察力的語調，強調情感體驗和浪漫連結的豐富性。從心理學角度處理內容，鼓勵讀者反思其內在情感景觀。以流暢、深思的文字書寫，邀請讀者進行內省並更深入地理解浪漫模式。
    
    字數要求：
    每個星座的愛情運勢保持在不少於200字，確保提供全面的情感指導。
    
    格式要求：
    使用段落間雙換行的純文本格式。不要使用markdown格式。`
            : `请为所有12个星座生成${year}年${month}的爱情运势文本，你必须直接返回JSON格式数据，格式如下：
    {
      "aries": "白羊座爱情运内容",
      "taurus": "金牛座爱情运内容",
      "gemini": "双子座爱情运内容",
      "cancer": "巨蟹座爱情运内容",
      "leo": "狮子座爱情运内容",
      "virgo": "处女座爱情运内容",
      "libra": "天秤座爱情运内容",
      "scorpio": "天蝎座爱情运内容",
      "sagittarius": "射手座爱情运内容",
      "capricorn": "摩羯座爱情运内容",
      "aquarius": "水瓶座爱情运内容",
      "pisces": "双鱼座爱情运内容"
    }
    
    需要包含的核心特征：
    使用每个星座的守护星作为主要的叙述线索，引导整个月份的情感旅程。融入当行星相位对爱情事务和关系发展最具影响力的具体关键日期。分析从深层内在感受和潜意识欲望到在浪漫情境中的外在表达和行为模式的情感光谱。处理各种关系动态，包括浪漫伴侣关系、亲密友谊、潜在的新连结，以及现有的承诺关系。深度关注心理暗流，探索内在思考和情感转化如何在爱情生活中显现。提供具体可行的建议，帮助在此期间克服情感挑战并最大化浪漫机会。
    
    写作风格：
    采用细腻而富有洞察力的语调，强调情感体验和浪漫连结的丰富性。从心理学角度处理内容，鼓励读者反思其内在情感景观。以流畅、深思的文字书写，邀请读者进行内省并更深入地理解浪漫模式。
    
    字数要求：
    每个星座的爱情运势保持在不少于200字，确保提供全面的情感指导。
    
    格式要求：
    使用段落间双换行的纯文本格式。不要使用markdown格式。`;
      break;
    default:
      throw new Error(`Invalid horoscope type: ${type}`);
  }
  
  try {
    // 调用qwen-turbo-latest API
    const response = await qwenAPI.post('/chat/completions', {
      model: 'qwen-plus-latest',
      messages: [
        {
          role: 'system',
          content: '你是一位专业的占星师，擅长撰写深刻而实用的星座运势文本。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      stream: false,
      response_format: { type: "json_object" },
      parameters: {
        response_language: language || 'zh-TW'
      }
    });
    
    // 解析API响应
    const content = response.data.choices[0].message.content;
    
    // 记录token使用量
    const inputTokens = response.data.usage?.prompt_tokens || 0;
    const outputTokens = response.data.usage?.completion_tokens || 0;
    
    // 解析内容
    let parsedContent = content;
    const parsedData = await parseHoroscopeJson(content);
    
    // 如果成功解析出所有星座数据，则保存所有星座数据到本地文件
    if (parsedData) {
      // 保存到本地文件，同时保存原始API返回文本
      try {
        await saveHoroscopeToFile(type, new Date(dateStr), parsedData, language, content);
      } catch (fileError) {
        console.error(`保存星座运势数据到本地文件失败: ${fileError.message}`);
      }
      
      // 保存到数据库
      try {
        // 引入数据库服务
        const { saveHoroscopeToDb } = require('./horoscopeDbService');
        
        // 对每个星座数据分别保存到数据库
        for (const zodiacSign of ZODIAC_SIGNS) {
          if (parsedData[zodiacSign]) {
            await saveHoroscopeToDb(
              zodiacSign, 
              type, 
              new Date(dateStr), 
              parsedData[zodiacSign], 
              content, 
              language,
              inputTokens,
              outputTokens
            );
          }
        }
        console.log(`成功将所有星座运势数据保存到数据库，类型: ${type}, 日期: ${dateStr}, 语言: ${language}`);
      } catch (dbError) {
        console.error(`保存星座运势数据到数据库失败: ${dbError.message}`);
      }
      
      // 获取当前请求的星座数据
      if (parsedData[sign]) {
        parsedContent = parsedData[sign];
      }
    } else {
      // 如果解析失败，尝试格式化单个星座的运势
      parsedContent = await formatSingleHoroscope(content, sign);
      
      // 如果格式化成功，保存到本地文件
      if (parsedContent) {
        // 创建一个只包含当前星座数据的对象
        const singleSignData = {
          [sign]: parsedContent
        };
        
        // 保存到本地文件，同时保存原始API返回文本
        try {
          await saveHoroscopeToFile(type, new Date(dateStr), singleSignData, language, content);
        } catch (fileError) {
          console.error(`保存单个星座运势数据到本地文件失败: ${fileError.message}`);
        }
        
        // 保存到数据库
        try {
          // 引入数据库服务
          const { saveHoroscopeToDb } = require('./horoscopeDbService');
          
          await saveHoroscopeToDb(
            sign, 
            type, 
            new Date(dateStr), 
            parsedContent, 
            content, 
            language,
            inputTokens,
            outputTokens
          );
          console.log(`成功将单个星座运势数据保存到数据库，星座: ${sign}, 类型: ${type}, 日期: ${dateStr}, 语言: ${language}`);
        } catch (dbError) {
          console.error(`保存单个星座运势数据到数据库失败: ${dbError.message}`);
        }
      } else {
        console.error('格式化单个星座的运势也失败了');
      }
    }
    
    // 返回星座运势数据
    return {
      sign,
      type,
      date: dateStr,
      content: parsedContent,
      language,
      generated_at: new Date(),
      input_tokens: inputTokens,
      output_tokens: outputTokens
    };
  } catch (error) {
    console.error(`生成星座运势失败: ${error.message}`);
    throw error;
  }
}

/**
 * 获取星座运势
 * @param {string} type 运势类型
 * @param {string} sign 星座ID
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<Object>} 运势数据
 */
async function getHoroscope(type, sign, date, language = 'zh-CN') {
  // 从本地文件获取数据
  const fileData = await getSignHoroscopeFromFile(sign, type, date, language);
  if (fileData) {
    return fileData;
  }
  
  // 本地文件中没有数据，生成新的运势数据
  const horoscopeData = await generateHoroscope(type, sign, date, language);
  
  return horoscopeData;
}

module.exports = {
  ZODIAC_SIGNS,
  HOROSCOPE_TYPES,
  getHoroscope,
  generateHoroscope,
  formatDateForDb
}; 