// 定义解读结果中的字段名常量
export const READING_FIELD_KEYS = {
  PROLOGUE: 'prologue:',
  ANSWER: 'answer:',
  ANALYSIS_PREFIX: 'analysis',
  ADVICE_PREFIX: 'advice',
  SUMMARY: 'summary:',
  RECORD: 'record:'
};

// 定义所有可能的字段列表
export const ALL_READING_FIELDS = [
  READING_FIELD_KEYS.PROLOGUE,
  READING_FIELD_KEYS.ANSWER,
  READING_FIELD_KEYS.ANALYSIS_PREFIX + '1:',
  READING_FIELD_KEYS.ANALYSIS_PREFIX + '2:',
  READING_FIELD_KEYS.ANALYSIS_PREFIX + '3:',
  READING_FIELD_KEYS.ANALYSIS_PREFIX + '4:',
  READING_FIELD_KEYS.ANALYSIS_PREFIX + '5:',
  READING_FIELD_KEYS.ANALYSIS_PREFIX + '6:',
  READING_FIELD_KEYS.ANALYSIS_PREFIX + '7:',
  READING_FIELD_KEYS.SUMMARY,
  READING_FIELD_KEYS.RECORD
];

// 获取卡牌图片路径
export const getCardImagePath = (cardName: string) => {
  // 获取英文名称
  const nameMapping: { [key: string]: string } = {
    // 大阿卡纳牌组 (0-21)
    '愚者': 'The_Fool',
    '魔术师': 'The_Magician',
    '女祭司': 'The_High_Priestess',
    '女皇': 'The_Empress',
    '皇帝': 'The_Emperor',
    '国王': 'The_Emperor',
    '教皇': 'The_Hierophant',
    '恋人': 'The_Lovers',
    '战车': 'The_Chariot',
    '力量': 'Strength',
    '隐士': 'The_Hermit',
    '命运之轮': 'Wheel_of_Fortune',
    '正义': 'Justice',
    '倒吊人': 'The_Hanged_Man',
    '死神': 'Death',
    '节制': 'Temperance',
    '恶魔': 'The_Devil',
    '塔': 'The_Tower',
    '高塔': 'The_Tower',
    '星星': 'The_Star',
    '月亮': 'The_Moon',
    '太阳': 'The_Sun',
    '审判': 'Judgement',
    '世界': 'The_World',
    
    // 权杖牌组
    '权杖王牌': 'Ace_of_Wands',
    '权杖一': 'Ace_of_Wands',
    '权杖二': 'Two_of_Wands',
    '权杖三': 'Three_of_Wands',
    '权杖四': 'Four_of_Wands',
    '权杖五': 'Five_of_Wands',
    '权杖六': 'Six_of_Wands',
    '权杖七': 'Seven_of_Wands',
    '权杖八': 'Eight_of_Wands',
    '权杖九': 'Nine_of_Wands',
    '权杖十': 'Ten_of_Wands',
    '权杖侍者': 'Page_of_Wands',
    '权杖侍从': 'Page_of_Wands',
    '权杖骑士': 'Knight_of_Wands',
    '权杖皇后': 'Queen_of_Wands',
    '权杖国王': 'King_of_Wands',
    
    // 圣杯牌组
    '圣杯王牌': 'Ace_of_Cups',
    '圣杯一': 'Ace_of_Cups',
    '圣杯二': 'Two_of_Cups',
    '圣杯三': 'Three_of_Cups',
    '圣杯四': 'Four_of_Cups',
    '圣杯五': 'Five_of_Cups',
    '圣杯六': 'Six_of_Cups',
    '圣杯七': 'Seven_of_Cups',
    '圣杯八': 'Eight_of_Cups',
    '圣杯九': 'Nine_of_Cups',
    '圣杯十': 'Ten_of_Cups',
    '圣杯侍者': 'Page_of_Cups',
    '圣杯侍从': 'Page_of_Cups',
    '圣杯骑士': 'Knight_of_Cups',
    '圣杯皇后': 'Queen_of_Cups',
    '圣杯国王': 'King_of_Cups',
    
    // 宝剑牌组
    '宝剑王牌': 'Ace_of_Swords',
    '宝剑一': 'Ace_of_Swords',
    '宝剑二': 'Two_of_Swords',
    '宝剑三': 'Three_of_Swords',
    '宝剑四': 'Four_of_Swords',
    '宝剑五': 'Five_of_Swords',
    '宝剑六': 'Six_of_Swords',
    '宝剑七': 'Seven_of_Swords',
    '宝剑八': 'Eight_of_Swords',
    '宝剑九': 'Nine_of_Swords',
    '宝剑十': 'Ten_of_Swords',
    '宝剑侍者': 'Page_of_Swords',
    '宝剑侍从': 'Page_of_Swords',
    '宝剑骑士': 'Knight_of_Swords',
    '宝剑皇后': 'Queen_of_Swords',
    '宝剑国王': 'King_of_Swords',
    
    // 钱币牌组
    '钱币王牌': 'Ace_of_Pentacles',
    '钱币一': 'Ace_of_Pentacles',
    '星币一': 'Ace_of_Pentacles',
    '钱币二': 'Two_of_Pentacles',
    '星币二': 'Two_of_Pentacles',
    '钱币三': 'Three_of_Pentacles',
    '星币三': 'Three_of_Pentacles',
    '钱币四': 'Four_of_Pentacles',
    '星币四': 'Four_of_Pentacles',
    '钱币五': 'Five_of_Pentacles',
    '星币五': 'Five_of_Pentacles',
    '钱币六': 'Six_of_Pentacles',
    '星币六': 'Six_of_Pentacles',
    '钱币七': 'Seven_of_Pentacles',
    '星币七': 'Seven_of_Pentacles',
    '钱币八': 'Eight_of_Pentacles',
    '星币八': 'Eight_of_Pentacles',
    '钱币九': 'Nine_of_Pentacles',
    '星币九': 'Nine_of_Pentacles',
    '钱币十': 'Ten_of_Pentacles',
    '星币十': 'Ten_of_Pentacles',
    '钱币侍者': 'Page_of_Pentacles',
    '星币侍从': 'Page_of_Pentacles',
    '钱币骑士': 'Knight_of_Pentacles',
    '星币骑士': 'Knight_of_Pentacles',
    '钱币皇后': 'Queen_of_Pentacles',
    '星币皇后': 'Queen_of_Pentacles',
    '钱币国王': 'King_of_Pentacles',
    '星币国王': 'King_of_Pentacles'
  };
  
  // 使用映射表获取英文名称，如果没有对应的英文名称，则使用原名称
  const englishName = nameMapping[cardName] || cardName;
  return `/images-optimized/tarot/${encodeURIComponent(englishName)}.webp`;
};

// 获取翻译后的卡牌名称
export const getTranslatedCardName = (cardName: string, t: (key: string, options?: any) => any) => {
  const majorArcanaMap: { [key: string]: number } = {
    '愚者': 0, '魔术师': 1, '女祭司': 2, '女皇': 3, '皇帝': 4,
    '教皇': 5, '恋人': 6, '战车': 7, '力量': 8, '隐士': 9,
    '命运之轮': 10, '正义': 11, '倒吊人': 12, '死神': 13,
    '节制': 14, '恶魔': 15, '塔': 16, '星星': 17, '月亮': 18,
    '太阳': 19, '审判': 20, '世界': 21
  };

  if (majorArcanaMap[cardName] !== undefined) {
    return t(`reading.cards.major.${majorArcanaMap[cardName]}`);
  }

  const suits = {
    '权杖': 'wands',
    '圣杯': 'cups',
    '宝剑': 'swords',
    '钱币': 'pentacles'
  };

  const ranks = {
    '王牌': 'ace',
    '二': '2',
    '三': '3',
    '四': '4',
    '五': '5',
    '六': '6',
    '七': '7',
    '八': '8',
    '九': '9',
    '十': '10',
    '侍者': 'page',
    '骑士': 'knight',
    '皇后': 'queen',
    '国王': 'king'
  };

  for (const [suitCh, suitEn] of Object.entries(suits)) {
    if (cardName.includes(suitCh)) {
      for (const [rankCh, rankEn] of Object.entries(ranks)) {
        if (cardName.includes(rankCh)) {
          return t(`reading.cards.${suitEn}.${rankEn}`);
        }
      }
    }
  }

  return cardName;
};

// 获取翻译后的牌阵信息
export const getTranslatedSpreadInfo = (spread: any, t: (key: string, options?: any) => any) => {
  if (!spread) return null;
  
  const spreadId = spread.id.replace(/-/g, '_'); // 将 id 中的连字符替换为下划线
  const spreads = t('spreads', { returnObjects: true });
  
  if (spreads && typeof spreads === 'object' && Object.keys(spreads).includes(spreadId)) {
    // 如果在翻译文件中存在该牌阵的翻译
    const positions = t(`spreads.${spreadId}.positions`, { returnObjects: true });
    if (positions && typeof positions === 'object') {
      return {
        name: t(`spreads.${spreadId}.name`),
        positions: Object.values(positions) as string[]
      };
    }
  }
  // 如果没有翻译，返回原始值
  return {
    name: spread.name,
    positions: []
  };
};

// 获取占卜师英文名称
export const getReaderNameEn = (reader: any): string => {
  // 首先检查reader是否存在
  if (!reader) {
    return 'Molly'; // 默认返回Molly
  }
  
  // 处理reader可能是字符串的情况（老版本可能直接存储id或nameEn）
  if (typeof reader === 'string') {
    // 尝试将字符串作为id或nameEn处理
    const idToNameMap: { [key: string]: string } = {
      'basic': 'Molly',
      'molly': 'Molly',
      'elias': 'Elias',
      'claire': 'Claire',
      'raven': 'Raven',
      'aurora': 'Aurora',
      'vincent': 'Vincent'
    };
    
    // 检查小写版本的字符串是否匹配任何ID
    const lowercaseReader = reader.toLowerCase();
    if (idToNameMap[lowercaseReader]) {
      return idToNameMap[lowercaseReader];
    }
    
    // 可用的图片名称（大小写敏感）
    const availableReaderImages = ['Molly', 'Elias', 'Claire', 'Raven', 'Aurora', 'Vincent'];
    
    // 检查字符串是否匹配任何图片名称（不区分大小写）
    const matchedImage = availableReaderImages.find(
      img => img.toLowerCase() === lowercaseReader
    );
    
    if (matchedImage) {
      return matchedImage;
    }
    
    // 如果字符串无法匹配，返回默认值
    return 'Molly';
  }
  
  // 特殊处理: 对于'raven'和'vincent'的id，无论nameEn是什么，都优先使用对应的图像名
  if (reader.id === 'raven') {
    return 'Raven';
  }
  
  if (reader.id === 'vincent') {
    return 'Vincent';
  }
  
  // 确认实际存在的图片文件名称
  const availableReaderImages = ['Molly', 'Elias', 'Claire', 'Raven', 'Aurora', 'Vincent'];
  
  // 检查nameEn属性
  if (reader.nameEn) {
    const nameEn = reader.nameEn;
    // 检查nameEn是否在可用图片列表中（不区分大小写）
    const matchedImage = availableReaderImages.find(
      img => img.toLowerCase() === nameEn.toLowerCase()
    );
    if (matchedImage) {
      return matchedImage; // 返回正确的大小写形式
    }
  }
  
  // 根据id获取英文名称
  const idToNameMap: { [key: string]: string } = {
    'basic': 'Molly',
    'elias': 'Elias',
    'claire': 'Claire',
    'raven': 'Raven',
    'aurora': 'Aurora',
    'vincent': 'Vincent'
  };

  if (reader.id && idToNameMap[reader.id]) {
    const result = idToNameMap[reader.id];
    return result;
  }
  
  // 如果无法通过ID匹配到占卜师，返回默认值
  return 'Molly';  // 默认返回 Molly
};

// 添加检测重复提问警告的辅助函数
export const containsRepeatedQuestionWarning = (content: string): boolean => {
  // 检查内容是否包含任何定义在ALL_READING_FIELDS中的字段
  return !ALL_READING_FIELDS.some(field => content.includes(field));
};

// 获取卡牌英文名称
export const getCardEnglishName = (cardName: string): string => {
  const nameMapping: { [key: string]: string } = {
    // 大阿卡纳牌组 (0-21)
    '愚者': 'The_Fool',
    '魔术师': 'The_Magician',
    '女祭司': 'The_High_Priestess',
    '女皇': 'The_Empress',
    '皇帝': 'The_Emperor',
    '国王': 'The_Emperor',
    '教皇': 'The_Hierophant',
    '恋人': 'The_Lovers',
    '战车': 'The_Chariot',
    '力量': 'Strength',
    '隐士': 'The_Hermit',
    '命运之轮': 'Wheel_of_Fortune',
    '正义': 'Justice',
    '倒吊人': 'The_Hanged_Man',
    '死神': 'Death',
    '节制': 'Temperance',
    '恶魔': 'The_Devil',
    '塔': 'The_Tower',
    '高塔': 'The_Tower',
    '星星': 'The_Star',
    '月亮': 'The_Moon',
    '太阳': 'The_Sun',
    '审判': 'Judgement',
    '世界': 'The_World',
    
    // 权杖牌组
    '权杖王牌': 'Ace_of_Wands',
    '权杖一': 'Ace_of_Wands',
    '权杖二': 'Two_of_Wands',
    '权杖三': 'Three_of_Wands',
    '权杖四': 'Four_of_Wands',
    '权杖五': 'Five_of_Wands',
    '权杖六': 'Six_of_Wands',
    '权杖七': 'Seven_of_Wands',
    '权杖八': 'Eight_of_Wands',
    '权杖九': 'Nine_of_Wands',
    '权杖十': 'Ten_of_Wands',
    '权杖侍者': 'Page_of_Wands',
    '权杖侍从': 'Page_of_Wands',
    '权杖骑士': 'Knight_of_Wands',
    '权杖皇后': 'Queen_of_Wands',
    '权杖国王': 'King_of_Wands',
    
    // 圣杯牌组
    '圣杯王牌': 'Ace_of_Cups',
    '圣杯一': 'Ace_of_Cups',
    '圣杯二': 'Two_of_Cups',
    '圣杯三': 'Three_of_Cups',
    '圣杯四': 'Four_of_Cups',
    '圣杯五': 'Five_of_Cups',
    '圣杯六': 'Six_of_Cups',
    '圣杯七': 'Seven_of_Cups',
    '圣杯八': 'Eight_of_Cups',
    '圣杯九': 'Nine_of_Cups',
    '圣杯十': 'Ten_of_Cups',
    '圣杯侍者': 'Page_of_Cups',
    '圣杯侍从': 'Page_of_Cups',
    '圣杯骑士': 'Knight_of_Cups',
    '圣杯皇后': 'Queen_of_Cups',
    '圣杯国王': 'King_of_Cups',
    
    // 宝剑牌组
    '宝剑王牌': 'Ace_of_Swords',
    '宝剑一': 'Ace_of_Swords',
    '宝剑二': 'Two_of_Swords',
    '宝剑三': 'Three_of_Swords',
    '宝剑四': 'Four_of_Swords',
    '宝剑五': 'Five_of_Swords',
    '宝剑六': 'Six_of_Swords',
    '宝剑七': 'Seven_of_Swords',
    '宝剑八': 'Eight_of_Swords',
    '宝剑九': 'Nine_of_Swords',
    '宝剑十': 'Ten_of_Swords',
    '宝剑侍者': 'Page_of_Swords',
    '宝剑侍从': 'Page_of_Swords',
    '宝剑骑士': 'Knight_of_Swords',
    '宝剑皇后': 'Queen_of_Swords',
    '宝剑国王': 'King_of_Swords',
    
    // 钱币牌组
    '钱币王牌': 'Ace_of_Pentacles',
    '钱币一': 'Ace_of_Pentacles',
    '星币一': 'Ace_of_Pentacles',
    '钱币二': 'Two_of_Pentacles',
    '星币二': 'Two_of_Pentacles',
    '钱币三': 'Three_of_Pentacles',
    '星币三': 'Three_of_Pentacles',
    '钱币四': 'Four_of_Pentacles',
    '星币四': 'Four_of_Pentacles',
    '钱币五': 'Five_of_Pentacles',
    '星币五': 'Five_of_Pentacles',
    '钱币六': 'Six_of_Pentacles',
    '星币六': 'Six_of_Pentacles',
    '钱币七': 'Seven_of_Pentacles',
    '星币七': 'Seven_of_Pentacles',
    '钱币八': 'Eight_of_Pentacles',
    '星币八': 'Eight_of_Pentacles',
    '钱币九': 'Nine_of_Pentacles',
    '星币九': 'Nine_of_Pentacles',
    '钱币十': 'Ten_of_Pentacles',
    '星币十': 'Ten_of_Pentacles',
    '钱币侍者': 'Page_of_Pentacles',
    '星币侍从': 'Page_of_Pentacles',
    '钱币骑士': 'Knight_of_Pentacles',
    '星币骑士': 'Knight_of_Pentacles',
    '钱币皇后': 'Queen_of_Pentacles',
    '星币皇后': 'Queen_of_Pentacles',
    '钱币国王': 'King_of_Pentacles',
    '星币国王': 'King_of_Pentacles'
  };
  
  // 使用映射表获取英文名称，如果没有对应的英文名称，则使用原名称
  const englishName = nameMapping[cardName] || cardName;
  // 将下划线替换为空格，使alt文本更友好
  return englishName.replace(/_/g, ' ');
}; 