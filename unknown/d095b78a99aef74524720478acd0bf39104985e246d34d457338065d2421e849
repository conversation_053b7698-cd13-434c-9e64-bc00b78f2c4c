import axios from 'axios';

interface FingerprintData {
  fingerprint: string;
  blogId: string;
  question: string;
  promptContent?: string;
  responseContent?: string;
  inputTokens?: number;
  outputTokens?: number;
}

interface FingerprintRecord {
  id: number;
  question: string;
  response_content: string;
  input_tokens: number;
  output_tokens: number;
  created_at: string;
  selected_cards?: string;
  spread_type?: string;
}

interface CheckFingerprintResponse {
  exists: boolean;
  record?: FingerprintRecord;
}

// 保存指纹数据
export const saveFingerprintData = async (data: FingerprintData) => {
  try {
    const response = await axios.post('/api/fingerprint', data);
    return response.data;
  } catch (error) {
    // console.error('保存指纹数据失败:', error);
    throw error;
  }
};

// 根据指纹获取历史记录
export const getFingerprintHistory = async (fingerprint: string) => {
  try {
    const response = await axios.get(`/api/fingerprint/${fingerprint}`);
    return response.data;
  } catch (error) {
    // console.error('获取指纹历史记录失败:', error);
    throw error;
  }
};

// 检查指纹和博客ID组合是否存在
export const checkFingerprintBlogCombination = async (fingerprint: string, blogId: string): Promise<CheckFingerprintResponse> => {
  try {
    const response = await axios.get(`/api/fingerprint/check/${fingerprint}/${blogId}`);
    return response.data;
  } catch (error) {
    // console.error('检查指纹博客组合失败:', error);
    // 发生错误时返回不存在
    return { exists: false };
  }
}; 