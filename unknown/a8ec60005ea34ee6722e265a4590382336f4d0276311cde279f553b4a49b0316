const express = require('express');
const router = express.Router();
const { ZODIAC_SIGNS, HOROSCOPE_TYPES } = require('../services/horoscopeService');
const { asyncHandler } = require('../utils/asyncHandler');
const { getSignHoroscopeFromFile, getAllHoroscopesFromFile } = require('../services/horoscopeFileService');

/**
 * 获取单个星座的运势
 * GET /api/horoscope/:sign/:type
 * 从本地文件获取星座运势数据
 */
router.get('/:sign/:type', asyncHandler(async (req, res) => {
  const { sign, type } = req.params;
  const { date, language = 'zh-CN' } = req.query;
    
  // 验证星座ID
  if (!ZODIAC_SIGNS.includes(sign)) {
    return res.status(400).json({ error: '无效的星座ID' });
  }
  
  // 验证运势类型
  if (!Object.values(HOROSCOPE_TYPES).includes(type)) {
    return res.status(400).json({ error: '无效的运势类型' });
  }
  
  // 解析日期，默认为当前日期
  let targetDate;
  if (date) {
    targetDate = new Date(date);
    if (isNaN(targetDate.getTime())) {
      return res.status(400).json({ error: '无效的日期格式' });
    }
  } else {
    targetDate = new Date();
  }
  
  // 确保日期是当地时区的日期，而不是UTC
  const localDate = new Date(targetDate.getTime() - targetDate.getTimezoneOffset() * 60000);
  
  try {
    // 从本地文件获取星座数据
    const fileData = await getSignHoroscopeFromFile(sign, type, localDate, language);
    
    if (fileData) {
    //   console.log(`API响应: 从文件中找到数据 ✓`);
      return res.json(fileData);
    }
    
    // 本地文件中没有数据，返回404
    // console.log(`API响应: 未找到数据 ✗`);
    return res.status(404).json({ 
      error: '未找到星座运势数据', 
      message: '本地文件中不存在该星座运势数据' 
    });
  } catch (error) {
    console.error(`API错误: ${error.message}`);
    res.status(500).json({ error: '获取星座运势失败', message: error.message });
  }
}));

module.exports = router; 