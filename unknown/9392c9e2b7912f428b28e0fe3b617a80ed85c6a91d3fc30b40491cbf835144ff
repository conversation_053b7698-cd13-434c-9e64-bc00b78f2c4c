/** @type {import('tailwindcss').Config} */
export default {
    darkMode: ["class"],
    content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
  	extend: {
  		fontFamily: {
  			inter: [
  				'-apple-system',
  				'BlinkMacSystemFont',
  				'Segoe UI',
  				'Roboto',
  				'Helvetica Neue',
  				'Arial',
  				'sans-serif'
  			]
  		},
  		colors: {
  			mystic: {
  				'700': '#1C1C2E',
  				'800': '#13131F',
  				'900': '#0A0A0F',
  				'950': '#000000'
  			},
  			cosmic: {
  				'300': '#c7d2fe',
  				'400': '#818cf8',
  				'500': '#6366f1'
  			},
  			starlight: {
  				'300': '#ddd6fe',
  				'400': '#a78bfa',
  				'500': '#8b5cf6'
  			},
  			moonlight: {
  				'300': '#bae6fd',
  				'400': '#e0f2fe',
  				'500': '#f0f9ff'
  			},
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		animation: {
  			float: 'float 6s ease-in-out infinite',
  			glow: 'glow 2s ease-in-out infinite',
  			'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite'
  		},
  		keyframes: {
  			float: {
  				'0%, 100%': {
  					transform: 'translateY(0)'
  				},
  				'50%': {
  					transform: 'translateY(-10px)'
  				}
  			},
  			glow: {
  				'0%, 100%': {
  					opacity: 1,
  					boxShadow: '0 0 20px rgba(99, 102, 241, 0.3)'
  				},
  				'50%': {
  					opacity: 0.8,
  					boxShadow: '0 0 40px rgba(99, 102, 241, 0.5)'
  				}
  			}
  		},
  		typography: {
  			DEFAULT: {
  				css: {
  					color: '#fff',
  					a: {
  						color: '#A855F7',
  						'&:hover': {
  							color: '#D8B4FE'
  						}
  					},
  					strong: {
  						color: '#A855F7'
  					},
  					em: {
  						color: '#EC4899'
  					},
  					h1: {
  						color: '#fff'
  					},
  					h2: {
  						color: '#fff'
  					},
  					h3: {
  						color: '#fff'
  					},
  					h4: {
  						color: '#fff'
  					},
  					code: {
  						color: '#EC4899'
  					},
  					blockquote: {
  						borderLeftColor: '#A855F7',
  						color: '#fff'
  					}
  				}
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		screens: {
  			'xs': '410px',
  		},
  	}
  },
  plugins: [
    require('@tailwindcss/typography'),
      require("tailwindcss-animate")
],
}
