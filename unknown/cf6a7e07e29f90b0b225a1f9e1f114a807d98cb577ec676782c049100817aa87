import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { CdnLazyImage } from '../CdnImageExport';
import { Link } from 'react-router-dom';
import { generateCardPath } from '../../utils/tarotUtils';

const SingleCardUnderstanding: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  // 定义不同类型的卡牌
  const cardTypes = [
    {
      title: t('yes_no_tarot.single_card_understanding.yes_cards_title', 'Strong Yes Cards'),
      description: t('yes_no_tarot.single_card_understanding.yes_cards_description', 'Cards that typically indicate positive outcomes:'),
      icon: '✅',
      color: 'green',
      cards: [
        {
          name: t('yes_no_tarot.single_card_understanding.yes_card1_name', 'The Sun'),
          meaning: t('yes_no_tarot.single_card_understanding.yes_card1_meaning', 'Overwhelming success and joy'),
          image: 'The_Sun',
          path: 'the-sun'
        },
        {
          name: t('yes_no_tarot.single_card_understanding.yes_card2_name', 'The Star'),
          meaning: t('yes_no_tarot.single_card_understanding.yes_card2_meaning', 'Hope and divine guidance favor you'),
          image: 'The_Star',
          path: 'the-star'
        },
        {
          name: t('yes_no_tarot.single_card_understanding.yes_card3_name', 'Ace of Cups'),
          meaning: t('yes_no_tarot.single_card_understanding.yes_card3_meaning', 'New emotional beginnings await'),
          image: 'Ace_of_Cups',
          path: 'ace-of-cups'
        },
        {
          name: t('yes_no_tarot.single_card_understanding.yes_card4_name', 'Ten of Pentacles'),
          meaning: t('yes_no_tarot.single_card_understanding.yes_card4_meaning', 'Material and spiritual abundance'),
          image: 'Ten_of_Pentacles',
          path: 'ten-of-pentacles'
        }
      ]
    },
    {
      title: t('yes_no_tarot.single_card_understanding.no_cards_title', 'Strong No Cards'),
      description: t('yes_no_tarot.single_card_understanding.no_cards_description', 'Cards suggesting caution or negative outcomes:'),
      icon: '❌',
      color: 'red',
      cards: [
        {
          name: t('yes_no_tarot.single_card_understanding.no_card1_name', 'The Tower'),
          meaning: t('yes_no_tarot.single_card_understanding.no_card1_meaning', 'Dramatic change, avoid for now'),
          image: 'The_Tower',
          path: 'the-tower'
        },
        {
          name: t('yes_no_tarot.single_card_understanding.no_card2_name', 'Five of Swords'),
          meaning: t('yes_no_tarot.single_card_understanding.no_card2_meaning', 'Conflict and defeat likely'),
          image: 'Five_of_Swords',
          path: 'five-of-swords'
        },
        {
          name: t('yes_no_tarot.single_card_understanding.no_card3_name', 'Nine of Swords'),
          meaning: t('yes_no_tarot.single_card_understanding.no_card3_meaning', 'Anxiety and worry ahead'),
          image: 'Nine_of_Swords',
          path: 'nine-of-swords'
        },
        {
          name: t('yes_no_tarot.single_card_understanding.no_card4_name', 'The Devil'),
          meaning: t('yes_no_tarot.single_card_understanding.no_card4_meaning', 'Trapped energy, not the right time'),
          image: 'The_Devil',
          path: 'the-devil'
        }
      ]
    },
    {
      title: t('yes_no_tarot.single_card_understanding.neutral_cards_title', 'Neutral Cards Requiring Interpretation'),
      description: t('yes_no_tarot.single_card_understanding.neutral_cards_description', 'Some cards depend on your specific situation:'),
      icon: '⚖️',
      color: 'blue',
      cards: [
        {
          name: t('yes_no_tarot.single_card_understanding.neutral_card1_name', 'The Fool'),
          meaning: t('yes_no_tarot.single_card_understanding.neutral_card1_meaning', 'New beginnings (yes) or reckless behavior (no)'),
          image: 'The_Fool',
          path: 'the-fool'
        },
        {
          name: t('yes_no_tarot.single_card_understanding.neutral_card2_name', 'Justice'),
          meaning: t('yes_no_tarot.single_card_understanding.neutral_card2_meaning', 'Fair outcome if you\'ve been honest'),
          image: 'Justice',
          path: 'justice'
        },
        {
          name: t('yes_no_tarot.single_card_understanding.neutral_card3_name', 'The Hermit'),
          meaning: t('yes_no_tarot.single_card_understanding.neutral_card3_meaning', 'Inner wisdom needed before deciding'),
          image: 'The_Hermit',
          path: 'the-hermit'
        }
      ]
    }
  ];

  return (
    <div className="mt-24 sm:mt-32">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('yes_no_tarot.single_card_understanding.title', 'Understanding Your One Card Reading')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
      </div>
      
      {/* 卡牌类型展示 */}
      <div className="max-w-5xl mx-auto space-y-12">
        {cardTypes.map((type, index) => (
          <div key={index} className="mb-10">
            {/* 类型标题 */}
            <div className="flex items-center justify-center mb-6">
              <span className="text-2xl mr-2">{type.icon}</span>
              <h3 className={`text-xl sm:text-2xl font-semibold ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {type.title}
              </h3>
            </div>
            
            {/* 类型描述 */}
            <p className={`text-center mb-6 ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {type.description}
            </p>
            
            {/* 卡牌列表 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
              {type.cards.map((card, cardIndex) => (
                <div key={cardIndex} className="flex flex-col items-center">
                  {/* 卡牌图片 - 可点击 */}
                  <Link 
                    to={`/gallery/card/${generateCardPath(card.image)}`} 
                    className="mb-2 md:mb-3 cursor-pointer relative group"
                    aria-label={`View details for ${card.name}`}
                  >
                    <CdnLazyImage 
                      src={`/images-optimized/tarot/${card.image}.webp`}
                      alt={card.name}
                      className="w-28 md:w-44 h-auto rounded shadow-lg transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 rounded"></div>
                  </Link>
                  
                  {/* 卡牌名称和含义 */}
                  <div className="text-center mt-1 md:mt-2">
                    <Link 
                      to={`/gallery/card/${generateCardPath(card.image)}`}
                      className={`font-bold text-sm md:text-lg mb-1 md:mb-2 hover:underline ${
                        type.color === 'green' ? 'text-green-600 dark:text-green-400' : 
                        type.color === 'red' ? 'text-red-600 dark:text-red-400' : 
                        'text-blue-600 dark:text-blue-400'
                      }`}
                    >
                      {card.name}
                    </Link>
                    <p className={`${
                      theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                    } text-xs md:text-sm max-w-xs`}>
                      {card.meaning}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SingleCardUnderstanding; 