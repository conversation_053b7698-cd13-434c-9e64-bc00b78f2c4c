import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { availableLanguages } from '../i18n';

/**
 * 自定义导航钩子，用于处理带有语言参数的导航
 * 
 * 如果当前URL已经包含语言参数，则保持该语言参数
 * 如果当前URL不包含语言参数，则不添加语言参数
 */
export function useLanguageNavigate() {
  const navigate = useNavigate();
  const location = useLocation();
  const { i18n } = useTranslation();

  /**
   * 处理导航，自动处理语言参数
   * @param to 目标路径
   * @param options 导航选项
   */
  const languageNavigate = (to: string, options?: { replace?: boolean }) => {
    // 检查当前URL是否已经包含语言参数
    const currentPath = location.pathname;
    const pathMatch = currentPath.match(/^\/([^\/]+)(\/.*)?$/);
    const isInLanguageRoute = pathMatch && availableLanguages.includes(pathMatch[1]);
    
    let targetPath = to;
    
    // 只有当当前URL带有语言参数时，才在导航中保持该语言
    if (isInLanguageRoute) {
      const currentLang = pathMatch[1];
      // 如果目标路径不是以当前语言开头，则添加当前语言
      if (!targetPath.startsWith(`/${currentLang}`)) {
        // 处理根路径的特殊情况
        if (targetPath === '/') {
          targetPath = `/${currentLang}`;
        } else if (targetPath.startsWith('/')) {
          // 移除开头的斜杠，避免出现双斜杠
          const cleanPath = targetPath.substring(1);
          targetPath = `/${currentLang}/${cleanPath}`;
        } else {
          targetPath = `/${currentLang}/${targetPath}`;
        }
      }
    }
    
    navigate(targetPath, options);
  };

  /**
   * 强制切换语言并更新URL
   * @param lang 目标语言
   */
  const changeLanguage = (lang: string) => {
    // 先切换语言，确保i18n实例更新
    i18n.changeLanguage(lang).then(() => {
      // 保存语言设置到localStorage
      localStorage.setItem('i18nextLng', lang);
      
      const currentPath = location.pathname;
      
      // 检查当前路径是否已经包含语言参数
      const pathMatch = currentPath.match(/^\/([^\/]+)(\/.*)?$/);
      if (pathMatch && availableLanguages.includes(pathMatch[1])) {
        // 已经在带语言参数的路径下，更新为新语言的URL
        const pathWithoutLang = pathMatch[2] || '';
        navigate(`/${lang}${pathWithoutLang}`, { replace: true });
      } else {
        // 在根路径或非语言路径下，添加语言参数
        if (currentPath === '/') {
          // 根路径直接跳转到语言首页
          navigate(`/${lang}`, { replace: true });
        } else {
          // 移除开头的斜杠，避免出现双斜杠
          const cleanPath = currentPath.startsWith('/') ? currentPath.substring(1) : currentPath;
          navigate(`/${lang}/${cleanPath}`, { replace: true });
        }
      }
    }).catch(() => {
      // console.error('Failed to change language:', err);
    });
  };

  return { navigate: languageNavigate, changeLanguage };
} 