import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';

interface FutureSectionProps {
  getFontClass: () => string;
}

const FutureSection: React.FC<FutureSectionProps> = ({ getFontClass }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { navigate } = useLanguageNavigate();
  
  // 星座运势选项配置
  const horoscopeOptions = [
    {
      id: 'daily',
      title: t('daily.horoscope.daily.title', '每日星座運勢'),
      description: t('daily.horoscope.daily.description', '了解今日星座能量流動，獲取針對性的個人化指導，幫助您把握當下，度過完美的一天'),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      iconBgClass: 'bg-purple-600/20',
      onClick: () => navigate('/horoscope/daily-horoscope'),
    },
    {
      id: 'yearly',
      title: t('daily.horoscope.yearly.title', '年度星座運勢'),
      description: t('daily.horoscope.yearly.description', '探索您未來一年的星座運勢全景，了解重要時機與挑戰，助您制定長期規劃，全年運勢無憂'),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-pink-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
        </svg>
      ),
      iconBgClass: 'bg-pink-600/20',
      onClick: () => navigate('/horoscope/yearly-horoscope'),
    },
    {
      id: 'love',
      title: t('daily.horoscope.love.title', '愛情星座運勢'),
      description: t('daily.horoscope.love.description', '揭示您的愛情星座配對與戀愛潛能，無論是尋找真愛還是提升現有關係，專業解讀助您把握感情機遇'),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      ),
      iconBgClass: 'bg-indigo-600/20',
      onClick: () => navigate('/horoscope/love-horoscope'),
    }
  ];

  return (
    <div className="max-w-[95%] lg:max-w-5xl mx-auto mt-24 sm:mt-28 px-4 sm:px-6 relative z-10">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('daily.horoscope.title', '星座運勢解析')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
        <p className={`${
          theme === 'light' ? 'text-gray-700' : 'text-gray-300'
        } text-lg max-w-3xl mx-auto`}>
          {t('daily.horoscope.subtitle', '探索您的星座日運、年運與愛情運勢')}
        </p>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
        {horoscopeOptions.map(option => (
          <div 
            key={option.id}
            className="relative backdrop-blur-xl p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col cursor-pointer"
            onClick={option.onClick}
          >
            <div>
              <div className="flex items-center mb-2">
                <div className={`w-10 h-10 shrink-0 rounded-full ${option.iconBgClass} flex items-center justify-center mr-3`}>
                  {option.icon}
                </div>
                <h3 className="font-medium text-lg sm:text-xl dark:text-white text-gray-800">{option.title}</h3>
              </div>
              <p className={`${getFontClass()} dark:text-gray-300 text-gray-600`}>
                {option.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FutureSection; 