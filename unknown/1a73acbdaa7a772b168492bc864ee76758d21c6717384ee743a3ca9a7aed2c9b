/**
 * TTS统计管理路由
 * 提供TTS生成的字符统计和费用查询功能
 */

const express = require('express');
const router = express.Router();
const { getConnection } = require('../services/database');
const { authenticateAdmin } = require('../middleware/admin');

// 需要管理员权限
router.use(authenticateAdmin);

/**
 * 获取TTS使用统计摘要
 * GET /api/tts-statistics/summary
 */
router.get('/summary', async (req, res) => {
  try {
    const pool = await getConnection();
    
    // 获取总体统计信息
    const [totalStats] = await pool.query(`
      SELECT 
        COUNT(*) as total_records,
        SUM(character_count) as total_characters,
        SUM(cost) as total_cost,
        AVG(character_count) as avg_characters_per_record
      FROM tts_cache
    `);
    
    // 按语音类型统计
    const [voiceStats] = await pool.query(`
      SELECT 
        voice,
        COUNT(*) as record_count,
        SUM(character_count) as character_count,
        SUM(cost) as total_cost,
        AVG(character_count) as avg_characters
      FROM tts_cache
      GROUP BY voice
      ORDER BY record_count DESC
    `);
    
    // 按日期统计(最近30天)
    const [dailyStats] = await pool.query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as record_count,
        SUM(character_count) as character_count,
        SUM(cost) as daily_cost
      FROM tts_cache
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `);
    
    // 按用户统计(如果有用户ID)
    const [userStats] = await pool.query(`
      SELECT 
        user_id,
        COUNT(*) as record_count,
        SUM(character_count) as character_count,
        SUM(cost) as user_cost
      FROM tts_cache
      WHERE user_id IS NOT NULL
      GROUP BY user_id
      ORDER BY character_count DESC
      LIMIT 20
    `);
    
    res.json({
      success: true,
      data: {
        summary: totalStats[0],
        byVoice: voiceStats,
        byDate: dailyStats,
        byUser: userStats
      }
    });
  } catch (error) {
    console.error('获取TTS统计信息出错:', error);
    res.status(500).json({
      success: false,
      message: '获取统计信息失败',
      error: error.message
    });
  }
});

/**
 * 获取TTS使用记录
 * GET /api/tts-statistics/records
 * 
 * 查询参数:
 * - page: 页码 (默认: 1)
 * - limit: 每页数量 (默认: 50)
 * - voice: 过滤特定语音 (可选)
 * - user_id: 过滤特定用户 (可选)
 * - start_date: 开始日期 (可选, YYYY-MM-DD)
 * - end_date: 结束日期 (可选, YYYY-MM-DD)
 * - min_chars: 最小字符数 (可选)
 * - max_chars: 最大字符数 (可选)
 */
router.get('/records', async (req, res) => {
  try {
    const pool = await getConnection();
    
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const conditions = [];
    const params = [];
    
    if (req.query.voice) {
      conditions.push('voice = ?');
      params.push(req.query.voice);
    }
    
    if (req.query.user_id) {
      conditions.push('user_id = ?');
      params.push(req.query.user_id);
    }
    
    if (req.query.start_date) {
      conditions.push('created_at >= ?');
      params.push(`${req.query.start_date} 00:00:00`);
    }
    
    if (req.query.end_date) {
      conditions.push('created_at <= ?');
      params.push(`${req.query.end_date} 23:59:59`);
    }
    
    if (req.query.min_chars) {
      conditions.push('character_count >= ?');
      params.push(parseInt(req.query.min_chars));
    }
    
    if (req.query.max_chars) {
      conditions.push('character_count <= ?');
      params.push(parseInt(req.query.max_chars));
    }
    
    // 构建WHERE子句
    const whereClause = conditions.length > 0 
      ? `WHERE ${conditions.join(' AND ')}` 
      : '';
    
    // 查询总记录数
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM tts_cache ${whereClause}`,
      params
    );
    
    const total = countResult[0].total;
    
    // 查询分页数据
    const queryParams = [...params, limit, offset];
    const [records] = await pool.query(
      `SELECT 
        id, 
        LEFT(text, 100) as text_preview, 
        voice, 
        character_count, 
        cost, 
        created_at, 
        expires_at,
        session_id,
        user_id,
        message_id
      FROM tts_cache 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?`,
      queryParams
    );
    
    res.json({
      success: true,
      data: {
        records,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取TTS记录出错:', error);
    res.status(500).json({
      success: false,
      message: '获取记录失败',
      error: error.message
    });
  }
});

module.exports = router; 