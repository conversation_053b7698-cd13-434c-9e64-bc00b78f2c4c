import React from 'react';
import { motion } from 'framer-motion';

interface ActionButtonProps {
  onClick: () => void;
  isEnabled: boolean;
  isDark: boolean;
  t: (key: string, options?: any) => string;
  requiredCards: number;
  selectedCount: number;
}

const ActionButton: React.FC<ActionButtonProps> = ({ 
  onClick, 
  isEnabled, 
  isDark, 
  t,
  requiredCards,
  selectedCount
}) => {
  return (
    <motion.div 
      className="flex justify-center mt-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.6 }}
    >
      <motion.button
        className={`relative px-10 py-4 rounded-full text-white font-medium text-lg font-sans
          ${isEnabled 
            ? 'bg-purple-600 hover:bg-purple-500 transition-colors duration-200' 
            : `${isDark ? 'bg-gray-700' : 'bg-gray-400'} cursor-not-allowed`
          }`}
        whileHover={isEnabled ? { scale: 1.02 } : {}}
        whileTap={isEnabled ? { scale: 0.98 } : {}}
        onClick={onClick}
        disabled={!isEnabled}
      >
        <span className="relative z-10" style={{color: 'white'}}>
          {isEnabled 
            ? t('reading.shuffle.start_interpretation')
            : t('reading.shuffle.remaining_cards', { count: requiredCards - selectedCount })}
        </span>
      </motion.button>
    </motion.div>
  );
};

export default ActionButton; 