import nodemailer from 'nodemailer';
import { config } from '../config';

// 创建邮件传输器
const transporter = nodemailer.createTransport({
  service: config.email.service,
  auth: {
    user: config.email.user,
    pass: config.email.pass,
  },
  // Gmail 特定配置
  ...(config.email.service === 'gmail' && {
    port: 465,
    secure: true,
    tls: {
      rejectUnauthorized: false
    }
  })
});

// 生成验证码
export const generateVerificationCode = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 发送验证码邮件
export const sendVerificationEmail = async (to: string, code: string): Promise<boolean> => {
  try {
    await transporter.sendMail({
      from: config.email.from,
      to,
      subject: 'AI塔罗 - 邮箱验证码',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #9333ea; margin-bottom: 20px;">AI塔罗 邮箱验证</h2>
          <p style="color: #4b5563; font-size: 16px; line-height: 1.5;">
            您好！感谢您注册 AI塔罗。您的验证码是：
          </p>
          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <span style="color: #9333ea; font-size: 24px; font-weight: bold; letter-spacing: 4px;">${code}</span>
          </div>
          <p style="color: #4b5563; font-size: 14px;">
            验证码有效期为 5 分钟。如果不是您本人的操作，请忽略此邮件。
          </p>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 12px;">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      `,
    });
    return true;
  } catch (error) {
    // console.error('Failed to send verification email:', error);
    return false;
  }
};
