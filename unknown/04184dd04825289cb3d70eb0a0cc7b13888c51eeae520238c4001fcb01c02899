import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

interface HoverableTextProps {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  isTitle?: boolean;
  style?: React.CSSProperties;
}

/**
 * 可悬停文本组件，提供统一的悬停效果：下划线和文字颜色变化
 */
const HoverableText: React.FC<HoverableTextProps> = ({
  as = 'p',
  children,
  onClick,
  className = '',
  isTitle = false,
  style,
}) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // 分离基础颜色和悬停颜色，使悬停效果能覆盖任何基础颜色
  const baseClasses = `cursor-pointer transition-all duration-300`;
  const titleBaseClasses = isTitle 
    ? `font-bold ${isDark ? 'text-green-500' : 'text-green-700'}`
    : `${isDark ? 'text-gray-300' : 'text-gray-700'}`;
  
  // 使用!important确保悬停样式优先级更高
  const hoverClasses = `hover:underline hover:!text-purple-600 dark:hover:!text-purple-400`;
  
  const allClasses = `${baseClasses} ${titleBaseClasses} ${hoverClasses} ${className}`;
  
  const Component = as;
  
  return (
    <Component className={allClasses} onClick={onClick} style={style}>
      {children}
    </Component>
  );
};

export default HoverableText; 