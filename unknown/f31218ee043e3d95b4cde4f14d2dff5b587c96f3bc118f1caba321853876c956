/**
 * 全局音频管理器
 * 用于管理所有音频播放实例，确保同一时间只有一个音频在播放
 */

// 存储所有当前活跃的音频元素及其相关组件标识
export interface AudioItem {
  audio: HTMLAudioElement;
  id: string; // 用于标识音频来源的组件ID
  onPausedByOther?: () => void; // 当被其他音频暂停时的回调
}

export class AudioManager {
  private activeAudioElements: AudioItem[] = [];
  private static instance: AudioManager;

  private constructor() {}

  // 单例模式
  public static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager();
    }
    return AudioManager.instance;
  }

  /**
   * 查找音频项
   * @param id 组件ID
   * @returns 找到的音频项或undefined
   */
  public findAudioItem(id: string): AudioItem | undefined {
    return this.activeAudioElements.find(item => item.id === id);
  }

  /**
   * 注册音频元素
   * @param audio 音频元素
   * @param id 组件标识
   * @param onPausedByOther 当被其他音频暂停时的回调
   * @returns 传入的音频元素
   */
  public registerAudio(audio: HTMLAudioElement, id: string, onPausedByOther?: () => void): HTMLAudioElement {
    // 添加到活跃列表
    this.activeAudioElements.push({ audio, id, onPausedByOther });

    // 存储ID到音频元素上，用于事件处理
    (audio as any).__audioManagerId = id;

    // 当音频结束播放时从列表中移除
    const originalOnEnded = audio.onended;
    audio.onended = (e) => {
      // 调用原始的onended事件
      if (originalOnEnded) {
        originalOnEnded.call(audio, e);
      }
      
      // 从活跃列表中移除
      this.removeAudio(audio);
    };

    return audio;
  }

  /**
   * 从活跃列表中移除音频
   * @param audio 要移除的音频元素
   */
  private removeAudio(audio: HTMLAudioElement): void {
    this.activeAudioElements = this.activeAudioElements.filter(item => item.audio !== audio);
  }

  /**
   * 停止所有音频播放
   */
  public stopAllAudio(): void {
    this.activeAudioElements.forEach(item => {
      try {
        item.audio.pause();
        // 调用暂停回调
        if (item.onPausedByOther) {
          item.onPausedByOther();
        }
      } catch (err) {
        // console.error(`[AudioManager] 停止音频出错:`, err);
      }
    });
  }

  /**
   * 停止除了指定ID外的所有音频播放
   * @param exceptId 不需要停止的组件ID
   */
  public stopOtherAudio(exceptId: string): void {
    this.activeAudioElements.forEach(item => {
      if (item.id !== exceptId) {
        try {
          item.audio.pause();
          // 调用暂停回调
          if (item.onPausedByOther) {
            item.onPausedByOther();
          }
        } catch (err) {
          // console.error(`[AudioManager] 停止其他音频出错:`, err);
        }
      }
    });
  }

  /**
   * 更新音频的回调函数
   * @param audioOrId 音频元素或组件ID
   * @param onPausedByOther 新的回调函数
   */
  public updateCallback(audioOrId: HTMLAudioElement | string, onPausedByOther: () => void): void {
    if (typeof audioOrId === 'string') {
      // 按ID查找
      const item = this.activeAudioElements.find(item => item.id === audioOrId);
      if (item) {
        item.onPausedByOther = onPausedByOther;
      }
    } else {
      // 按音频元素查找
      const item = this.activeAudioElements.find(item => item.audio === audioOrId);
      if (item) {
        item.onPausedByOther = onPausedByOther;
      }
    }
  }

  /**
   * 开始播放音频，并停止其他所有音频
   * @param audio 要播放的音频元素
   * @param id 组件标识
   * @param onPausedByOther 当被其他音频暂停时的回调
   */
  public playAudio(audio: HTMLAudioElement, id: string, onPausedByOther?: () => void): void {
    // 先停止其他音频
    this.stopOtherAudio(id);

    // 更新或注册音频
    const existingItem = this.activeAudioElements.find(item => item.audio === audio);
    if (existingItem) {
      // 更新回调
      if (onPausedByOther) {
        existingItem.onPausedByOther = onPausedByOther;
      }
    } else {
      // 注册新音频
      this.registerAudio(audio, id, onPausedByOther);
    }

    // 播放前确保所有事件正确设置
    const ensureEventHandlers = () => {
      // 添加或更新播放事件监听器
      const playHandler = () => {
        // 适当的地方可以添加播放开始的逻辑
      };
      
      // 确保移除旧的事件监听器，避免重复
      audio.removeEventListener('play', playHandler);
      audio.addEventListener('play', playHandler);
    };
    
    // 确保事件监听器正确设置
    ensureEventHandlers();
    
    // 播放音频
    try {
      // 手动触发一次play事件，以确保状态更新
      // 这有助于在继续播放暂停的音频时触发相关回调
      const firePlayEvent = () => {
        try {
          // 创建并分发play事件
          const playEvent = new Event('play');
          audio.dispatchEvent(playEvent);
        } catch (err) {
          // console.error('[AudioManager] 触发播放事件失败:', err);
        }
      };
      
      const playPromise = audio.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
          // 播放成功后触发事件
          setTimeout(firePlayEvent, 0);
        }).catch(() => {
          // console.error(`[AudioManager] 播放音频失败:`, err);
          this.removeAudio(audio);
        });
      } else {
        // 对于不支持Promise的浏览器，延时触发事件
        setTimeout(firePlayEvent, 50);
      }
    } catch (err) {
      // console.error(`[AudioManager] 播放音频出错:`, err);
      this.removeAudio(audio);
    }
  }
}

// 为全局音频播放添加事件监听
if (typeof window !== 'undefined') {
  // 覆盖原生Audio构造函数，以便自动注册所有新创建的音频元素
  const originalAudio = window.Audio;
  
  window.Audio = function(src?: string) {
    const audio = new originalAudio(src);
    
    // 添加播放事件监听
    audio.addEventListener('play', () => {
      // 当有新的音频开始播放时，尝试停止其他音频
      const currentId = (audio as any).__audioManagerId;
      if (currentId) {
        audioManager.stopOtherAudio(currentId);
      }
    });
    
    // 添加暂停事件监听
    audio.addEventListener('pause', () => {
      // 如果是外部手动暂停（例如用户点击暂停或其他代码暂停），
      // 确保相关组件能意识到状态变化
      const currentId = (audio as any).__audioManagerId;
      if (currentId) {
        const item = audioManager.findAudioItem(currentId);
        if (item && item.onPausedByOther) {
          item.onPausedByOther();
        }
      }
    });
    
    return audio;
  } as any;
  
  // 保持原型链一致
  window.Audio.prototype = originalAudio.prototype;
}

// 导出单例
export const audioManager = AudioManager.getInstance(); 