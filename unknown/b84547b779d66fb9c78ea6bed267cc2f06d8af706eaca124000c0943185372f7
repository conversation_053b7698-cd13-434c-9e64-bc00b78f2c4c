import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { getFontClass } from '../../utils/tarotUtils';

interface ContinueExploreButtonProps {
  onButtonClick: () => void;
}

const ContinueExploreButton: React.FC<ContinueExploreButtonProps> = ({ 
  onButtonClick 
}) => {
  const { t, i18n } = useTranslation();
  
  return (
    <motion.div 
      className="flex justify-center py-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6, delay: 0.6 }}
    >
      <motion.button
        className={`px-8 py-4 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-500/30 transition-all duration-300 flex items-center space-x-2 ${getFontClass(i18n.language)}`}
        onClick={onButtonClick}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        style={{color: 'white'}}
      >
        <span className="text-lg font-medium text-white" style={{color: 'white'}}>{t('reading.action.continue_explore')}</span>
      </motion.button>
    </motion.div>
  );
};

export default ContinueExploreButton; 