

/**
 * 解析星座运势JSON数据
 * 
 * 该函数用于解析API返回的包含所有12个星座运势的JSON数据
 * 
 * @param {string|Object} jsonContent - 包含所有星座运势的JSON字符串或对象
 * @returns {Object|null} - 解析后的星座运势对象，如果解析失败则返回null
 */
async function parseHoroscopeJson(jsonContent) {
  
  try {
    // 定义所有星座的键名
    const requiredSigns = [
      'aries', 'taurus', 'gemini', 'cancer', 
      'leo', 'virgo', 'libra', 'scorpio', 
      'sagittarius', 'capricorn', 'aquarius', 'pisces'
    ];
    
    let horoscopeData;
    
    // 如果传入的是对象，直接处理
    if (jsonContent && typeof jsonContent === 'object') {
      horoscopeData = jsonContent;
    } 
    // 如果是字符串，尝试解析为JSON对象
    else if (typeof jsonContent === 'string') {
      try {
        horoscopeData = JSON.parse(jsonContent);
      } catch (error) {
        console.error(`JSON解析失败: ${error.message}`);
        // 尝试手动提取星座数据
        const manualJson = {};
        let hasAnySign = false;
        
        for (const sign of requiredSigns) {
          // 使用正则表达式查找 "sign": "content" 模式
          const signRegex = new RegExp(`["']${sign}["']\\s*:\\s*["']([^"']*)["']`, 'i');
          const match = jsonContent.match(signRegex);
          
          if (match && match[1]) {
            manualJson[sign] = match[1];
            hasAnySign = true;
          }
        }
        
        if (hasAnySign) {
          horoscopeData = manualJson;
        } else {
          throw new Error('无法手动提取星座数据');
        }
      }
    } else {
      console.error('输入内容既不是对象也不是字符串');
      throw new Error('输入内容既不是对象也不是字符串');
    }
    
    // 检查解析结果
    if (!horoscopeData) {
      console.error('解析后的数据为空');
      throw new Error('解析后的数据为空');
    }
    
    // 检查是否包含horoscopes对象
    if (horoscopeData.horoscopes) {
      return horoscopeData.horoscopes;
    }
    
    // 检查是否直接包含了星座键
    const hasZodiacKeys = requiredSigns.some(sign => horoscopeData[sign]);
    if (hasZodiacKeys) {
      // 验证数据格式是否符合预期
      let validFormat = true;
      let missingKeys = [];
      
      for (const sign of requiredSigns) {
        if (!horoscopeData[sign]) {
          validFormat = false;
          missingKeys.push(sign);
        }
      }
      
      if (!validFormat) {
        console.warn(`星座数据不完整，缺少以下星座: ${missingKeys.join(', ')}`);
        // 即使数据不完整，也返回已有的数据
      } else {
      }
      
      return horoscopeData;
    }
    
    console.error('JSON数据中既不包含horoscopes对象也不包含星座数据');
    throw new Error('JSON数据中既不包含horoscopes对象也不包含星座数据');
  } catch (error) {
    console.error(`解析星座运势JSON数据失败: ${error.message}`);
    // 记录原始内容的一部分，帮助调试
    if (typeof jsonContent === 'string') {
      console.error(`原始内容片段: ${jsonContent.substring(0, 200)}...`);
    } else {
      console.error(`原始内容类型: ${typeof jsonContent}`);
    }
    return null;
  } finally {
  }
}

/**
 * 将解析后的星座运势数据格式化为单个星座的运势对象
 * 
 * @param {string} sign - 星座ID
 * @param {string} type - 运势类型
 * @param {Date} date - 日期
 * @param {string} content - 星座运势内容
 * @param {string} language - 语言
 * @returns {Object} - 格式化后的星座运势对象
 */
function formatSingleHoroscope(sign, type, date, content, language) {
  const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD
  
  return {
    sign,
    type,
    date: dateStr,
    content,
    language,
    generated_at: new Date().toISOString()
  };
}

module.exports = {
  parseHoroscopeJson,
  formatSingleHoroscope
}; 