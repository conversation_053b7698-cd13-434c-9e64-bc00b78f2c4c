import React, { useEffect, useRef, useCallback, useMemo } from "react";
import "./TiltImage.css";
import { CdnImage } from "../../../components/CdnImageExport";

interface TiltImageProps {
  imageUrl: string;
  alt?: string;
  className?: string;
  enableTilt?: boolean;
  onClick?: () => void;
  behindGradient?: string;
}

const ANIMATION_CONFIG = {
  SMOOTH_DURATION: 600,
  INITIAL_DURATION: 1500,
  INITIAL_X_OFFSET: 70,
  INITIAL_Y_OFFSET: 60,
} as const;

const clamp = (value: number, min = 0, max = 100): number =>
  Math.min(Math.max(value, min), max);

const round = (value: number, precision = 3): number =>
  parseFloat(value.toFixed(precision));

const adjust = (
  value: number,
  fromMin: number,
  fromMax: number,
  toMin: number,
  toMax: number
): number =>
  round(toMin + ((toMax - toMin) * (value - fromMin)) / (fromMax - fromMin));

const easeInOutCubic = (x: number): number =>
  x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2;

const TiltImage: React.FC<TiltImageProps> = ({
  imageUrl,
  alt = "Tilt image",
  className = "",
  enableTilt = true,
  onClick,
  behindGradient,
}) => {
  const wrapRef = useRef<HTMLDivElement>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);

  const animationHandlers = useMemo(() => {
    if (!enableTilt) return null;

    let rafId: number | null = null;

    const updateTransform = (
      offsetX: number,
      offsetY: number,
      imageContainer: HTMLElement,
      wrap: HTMLElement
    ) => {
      const width = imageContainer.clientWidth;
      const height = imageContainer.clientHeight;

      const percentX = clamp((100 / width) * offsetX);
      const percentY = clamp((100 / height) * offsetY);

      const centerX = percentX - 50;
      const centerY = percentY - 50;

      const properties = {
        "--pointer-x": `${percentX}%`,
        "--pointer-y": `${percentY}%`,
        "--background-x": `${adjust(percentX, 0, 100, 35, 65)}%`,
        "--background-y": `${adjust(percentY, 0, 100, 35, 65)}%`,
        "--pointer-from-center": `${clamp(Math.hypot(percentY - 50, percentX - 50) / 50, 0, 1)}`,
        "--pointer-from-top": `${percentY / 100}`,
        "--pointer-from-left": `${percentX / 100}`,
        "--rotate-x": `${round(-(centerX / 5))}deg`,
        "--rotate-y": `${round(centerY / 4)}deg`,
      };

      Object.entries(properties).forEach(([property, value]) => {
        wrap.style.setProperty(property, value);
      });
    };

    const createSmoothAnimation = (
      duration: number,
      startX: number,
      startY: number,
      imageContainer: HTMLElement,
      wrap: HTMLElement
    ) => {
      const startTime = performance.now();
      const targetX = wrap.clientWidth / 2;
      const targetY = wrap.clientHeight / 2;

      const animationLoop = (currentTime: number) => {
        const elapsed = currentTime - startTime;
        const progress = clamp(elapsed / duration);
        const easedProgress = easeInOutCubic(progress);

        const currentX = adjust(easedProgress, 0, 1, startX, targetX);
        const currentY = adjust(easedProgress, 0, 1, startY, targetY);

        updateTransform(currentX, currentY, imageContainer, wrap);

        if (progress < 1) {
          rafId = requestAnimationFrame(animationLoop);
        }
      };

      rafId = requestAnimationFrame(animationLoop);
    };

    return {
      updateTransform,
      createSmoothAnimation,
      cancelAnimation: () => {
        if (rafId) {
          cancelAnimationFrame(rafId);
          rafId = null;
        }
      },
    };
  }, [enableTilt]);

  const handlePointerMove = useCallback(
    (event: PointerEvent) => {
      const imageContainer = imageContainerRef.current;
      const wrap = wrapRef.current;

      if (!imageContainer || !wrap || !animationHandlers) return;

      const rect = imageContainer.getBoundingClientRect();
      animationHandlers.updateTransform(
        event.clientX - rect.left,
        event.clientY - rect.top,
        imageContainer,
        wrap
      );
    },
    [animationHandlers]
  );

  const handlePointerEnter = useCallback(() => {
    const imageContainer = imageContainerRef.current;
    const wrap = wrapRef.current;

    if (!imageContainer || !wrap || !animationHandlers) return;

    animationHandlers.cancelAnimation();
    wrap.classList.add("active");
    imageContainer.classList.add("active");
  }, [animationHandlers]);

  const handlePointerLeave = useCallback(
    (event: PointerEvent) => {
      const imageContainer = imageContainerRef.current;
      const wrap = wrapRef.current;

      if (!imageContainer || !wrap || !animationHandlers) return;

      animationHandlers.createSmoothAnimation(
        ANIMATION_CONFIG.SMOOTH_DURATION,
        event.offsetX,
        event.offsetY,
        imageContainer,
        wrap
      );
      wrap.classList.remove("active");
      imageContainer.classList.remove("active");
    },
    [animationHandlers]
  );

  useEffect(() => {
    if (!enableTilt || !animationHandlers) return;

    const imageContainer = imageContainerRef.current;
    const wrap = wrapRef.current;

    if (!imageContainer || !wrap) return;

    const pointerMoveHandler = handlePointerMove as EventListener;
    const pointerEnterHandler = handlePointerEnter as EventListener;
    const pointerLeaveHandler = handlePointerLeave as EventListener;

    imageContainer.addEventListener("pointerenter", pointerEnterHandler);
    imageContainer.addEventListener("pointermove", pointerMoveHandler);
    imageContainer.addEventListener("pointerleave", pointerLeaveHandler);

    const initialX = wrap.clientWidth - ANIMATION_CONFIG.INITIAL_X_OFFSET;
    const initialY = ANIMATION_CONFIG.INITIAL_Y_OFFSET;

    animationHandlers.updateTransform(initialX, initialY, imageContainer, wrap);
    animationHandlers.createSmoothAnimation(
      ANIMATION_CONFIG.INITIAL_DURATION,
      initialX,
      initialY,
      imageContainer,
      wrap
    );

    return () => {
      imageContainer.removeEventListener("pointerenter", pointerEnterHandler);
      imageContainer.removeEventListener("pointermove", pointerMoveHandler);
      imageContainer.removeEventListener("pointerleave", pointerLeaveHandler);
      animationHandlers.cancelAnimation();
    };
  }, [
    enableTilt,
    animationHandlers,
    handlePointerMove,
    handlePointerEnter,
    handlePointerLeave,
  ]);

  const wrapperStyle = useMemo(
    () => ({
      "--behind-gradient": behindGradient || "radial-gradient(farthest-side circle at var(--pointer-x) var(--pointer-y), hsla(280, 100%, 90%, var(--card-opacity)) 4%, hsla(280, 80%, 80%, calc(var(--card-opacity) * 0.75)) 10%, hsla(280, 70%, 70%, calc(var(--card-opacity) * 0.5)) 50%, hsla(280, 60%, 60%, 0) 100%), radial-gradient(35% 52% at 55% 20%, #c137ff94 0%, #9d37ff00 100%), radial-gradient(100% 100% at 50% 50%, #a537ffff 1%, #8437ff00 76%), conic-gradient(from 124deg at 50% 50%, #c137ffff 0%, #9537ffff 40%, #9537ffff 60%, #c137ffff 100%)",
    } as React.CSSProperties),
    [behindGradient]
  );

  return (
    <div
      ref={wrapRef}
      className={`tilt-wrapper ${className}`.trim()}
      onClick={onClick}
      style={{ 
        cursor: onClick ? 'pointer' : 'default',
        ...wrapperStyle
      }}
    >
      <div ref={imageContainerRef} className="tilt-container">
        <CdnImage
          src={imageUrl}
          alt={alt}
          className="tilt-image"
          onError={() => {
            const imgElement = imageContainerRef.current?.querySelector('img');
            if (imgElement) {
              imgElement.style.opacity = "0.5";
            }
          }}
        />
      </div>
    </div>
  );
};

export default React.memo(TiltImage); 