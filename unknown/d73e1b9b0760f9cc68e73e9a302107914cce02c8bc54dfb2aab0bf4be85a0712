import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

const HowItWorks: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <div className="max-w-[95%] lg:max-w-5xl mx-auto mt-24 sm:mt-32">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('yes_no_tarot.how_it_works.title')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
      </div>
        
      {/* 两个卡片水平并列 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* 单卡塔罗卡片 */}
        <div className="relative backdrop-blur-xl rounded-xl overflow-hidden dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col h-full">
          <div className="p-6">
            <h3 className={`text-xl font-bold mb-4 ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
              {t('yes_no_tarot.how_it_works.one_card.title')}
            </h3>
            <div className="body-text dark:text-gray-300 text-gray-600 space-y-4">
              <p>
                {t('yes_no_tarot.how_it_works.one_card.description')}
              </p>
              <div className="flex items-center mt-4 pt-4 border-t border-purple-200/30 dark:border-purple-800/30">
                <div className="w-12 h-12 rounded-full flex items-center justify-center mr-4 bg-gradient-to-br from-purple-200 to-pink-200 dark:from-purple-500 dark:to-pink-500 border-2 border-purple-300 dark:border-purple-400 shadow-md">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-700 dark:text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium text-purple-600 dark:text-purple-400">{t('yes_no_tarot.how_it_works.one_card.feature')}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{t('yes_no_tarot.how_it_works.one_card.sub_feature')}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 三卡塔罗卡片 */}
        <div className="relative backdrop-blur-xl rounded-xl overflow-hidden dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col h-full">
          <div className="p-6">
            <h3 className={`text-xl font-bold mb-4 ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
              {t('yes_no_tarot.how_it_works.three_card.title')}
            </h3>
            <div className="body-text dark:text-gray-300 text-gray-600 space-y-3">
              <p>
                {t('yes_no_tarot.how_it_works.three_card.description')}
              </p>
              <ul className="list-disc pl-5 space-y-2">
                <li><span className="font-medium text-green-600 dark:text-green-400">{t('yes_no_tarot.how_it_works.three_card.definite_yes')}</span> {t('yes_no_tarot.how_it_works.three_card.definite_yes_desc')}</li>
                <li><span className="font-medium text-yellow-600 dark:text-yellow-400">{t('yes_no_tarot.how_it_works.three_card.likely_yes')}</span> {t('yes_no_tarot.how_it_works.three_card.likely_yes_desc')}</li>
                <li><span className="font-medium text-red-600 dark:text-red-400">{t('yes_no_tarot.how_it_works.three_card.no_answer')}</span> {t('yes_no_tarot.how_it_works.three_card.no_answer_desc')}</li>
              </ul>
              <div className="flex items-center mt-4 pt-4 border-t border-purple-200/30 dark:border-purple-800/30">
                <div className="flex -space-x-2 mr-4">
                  <div className="w-10 h-10 rounded-full flex items-center justify-center bg-gradient-to-br from-blue-200 to-indigo-200 dark:from-blue-500 dark:to-indigo-500 border-2 border-blue-300 dark:border-blue-400 shadow-md z-30">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-700 dark:text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                  </div>
                  <div className="w-10 h-10 rounded-full flex items-center justify-center bg-gradient-to-br from-purple-200 to-pink-200 dark:from-purple-500 dark:to-pink-500 border-2 border-purple-300 dark:border-purple-400 shadow-md z-20">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-700 dark:text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                  </div>
                  <div className="w-10 h-10 rounded-full flex items-center justify-center bg-gradient-to-br from-yellow-200 to-amber-200 dark:from-yellow-500 dark:to-amber-500 border-2 border-yellow-300 dark:border-yellow-400 shadow-md z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-amber-700 dark:text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                </div>
                <div>
                  <p className="font-medium text-purple-600 dark:text-purple-400">{t('yes_no_tarot.how_it_works.three_card.feature')}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{t('yes_no_tarot.how_it_works.three_card.sub_feature')}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HowItWorks; 