import React, { useState, useEffect, useRef, useContext } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { fetchParagraphAudio } from './audioUtils';
import { Message } from '../../types/tarot';
import { audioManager } from './audioManager';
import { PlayingContext } from './TarotSpeech';

interface TarotBlockSpeechProps {
  messages: Message[];
  sessionId?: string;
  readerId?: string;
  blockId: string; // 板块ID，用于区分不同板块
  paragraphRefs?: React.RefObject<HTMLDivElement>[]; // 段落引用数组
  user?: any; // 添加用户信息
  setShowVipPrompt?: (show: boolean) => void; // 添加显示VIP提示的函数
  language?: string; // 添加语言参数
}

/**
 * 板块语音朗读组件，支持播放整个板块的消息
 */
const TarotBlockSpeech: React.FC<TarotBlockSpeechProps> = ({
  messages,
  sessionId,
  readerId,
  blockId,
  paragraphRefs,
  user,
  setShowVipPrompt,
  language
}) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isPreloading, setIsPreloading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(-1);

  // 从当前界面语言获取语言代码
  const currentLanguage = React.useMemo(() => {
    if (language) return language;
    
    // 从i18n.language获取语言代码
    const langCode = i18n.language.split('-')[0]; // 获取主要语言代码部分
    if (langCode === 'zh') {
      return 'zh';
    } else if (langCode === 'en') {
      return 'en';
    } else if (langCode === 'ja') {
      return 'ja';
    }
    return 'zh'; // 默认中文
  }, [i18n.language, language]);

  // 访问全局播放状态上下文
  const { setPlayingId } = useContext(PlayingContext);

  // 当前播放的音频元素
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const isMountedRef = useRef(true);
  // 存储所有预加载的音频
  const preloadedAudiosRef = useRef<HTMLAudioElement[]>([]);

  // 筛选出AI消息
  const aiMessages = messages.filter(msg => msg.type === 'reader');

  // 创建组件唯一ID，用于音频管理
  const componentId = React.useMemo(() => `tarot-block-speech-${blockId || Math.random().toString(36).substring(2, 9)}`, [blockId]);

  // 设置高亮效果的函数
  const highlightCurrentParagraph = (index: number) => {
    // 确定板块类型
    const blockType = blockId.includes('followup') ? 'followup' : 'base';
    
    // 设置当前播放段落的ID
    if (sessionId) {
      const messageId = aiMessages[index]?.id || `${blockType}-block_${index}`;
      const paragraphId = `${sessionId}_${messageId}_${blockType}`;
      setPlayingId(paragraphId);
    }
    
    // 如果有段落引用数组，应用高亮效果
    if (paragraphRefs && paragraphRefs[index]?.current) {
      // 先清除所有段落的高亮
      paragraphRefs.forEach(ref => {
        if (ref.current) {
          ref.current.style.boxShadow = '';
          ref.current.style.border = '';
        }
      });
      
      // 为当前段落添加高亮
      const currentRef = paragraphRefs[index];
      if (currentRef.current) {
        currentRef.current.style.transition = 'box-shadow 0.3s ease, border 0.3s ease';
        currentRef.current.style.boxShadow = '0 0 0 2px rgba(139, 92, 246, 0.5)';
        currentRef.current.style.border = '1px solid rgba(139, 92, 246, 0.8)';
        
        // 确保段落可见（滚动到视图中央）
        currentRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  };
  
  // 清除高亮效果
  const clearHighlights = () => {
    setPlayingId(null);
    
    if (paragraphRefs) {
      paragraphRefs.forEach(ref => {
        if (ref.current) {
          ref.current.style.boxShadow = '';
          ref.current.style.border = '';
        }
      });
    }
  };

  // 组件卸载时清理
  useEffect(() => {
    isMountedRef.current = true;
    
    return () => {
      isMountedRef.current = false;
      stopPlayback();
      // 清理所有预加载的音频
      cleanupPreloadedAudios();
      // 清除高亮
      clearHighlights();
    };
  }, []);

  // 清理预加载的所有音频
  const cleanupPreloadedAudios = () => {
    preloadedAudiosRef.current.forEach(audio => {
      try {
        audio.onended = null;
        audio.oncanplay = null;
        audio.onerror = null;
        audio.oncanplaythrough = null;
        audio.pause();
        audio.src = '';
        audio.load();
      } catch (err) {
        // console.log(`[TarotBlockSpeech] 清理预加载音频元素出错: ${err}`);
      }
    });
    preloadedAudiosRef.current = [];
  };

  // 停止当前播放
  const stopPlayback = () => {
    if (audioRef.current) {
      try {
        // 移除所有事件监听器
        const audio = audioRef.current;
        audio.onended = null;
        audio.oncanplay = null;
        audio.onerror = null;
        audio.oncanplaythrough = null;
        
        // 暂停播放
        audio.pause();
        
        // 重置音频元素
        audio.src = '';
        audio.load();
        audioRef.current = null;
      } catch (err) {
        // console.log(`[TarotBlockSpeech] 清理音频元素出错: ${err}`);
      }
    }
    
    // 重置状态
    if (isMountedRef.current) {
      setIsPlaying(false);
      setIsLoading(false);
      setCurrentMessageIndex(-1);
      clearHighlights(); // 清除高亮
    }
  };

  // 预加载所有段落的音频
  const preloadAllAudios = async () => {
    if (isPreloading || isPlaying) return;
    
    try {
      setIsLoading(true);
      setIsPreloading(true);
      setError(null);
      
      // console.log(`[TarotBlockSpeech] 开始预加载所有段落音频，共${aiMessages.length}段`);
      
      // 清理之前可能存在的预加载音频
      cleanupPreloadedAudios();
      
      const preloadedAudios: HTMLAudioElement[] = [];
      const oneFourthCount = Math.ceil(aiMessages.length / 4);
      let loadedCount = 0;
      let canStartPlayback = false;
      
      // 顺序预加载音频 - 一次加载一个段落
      for (let index = 0; index < aiMessages.length; index++) {
        try {
          const message = aiMessages[index];
          // console.log(`[TarotBlockSpeech] 预加载段落 #${index}: ${message.id || `block-${blockId}-msg-${index}`}`);
          
          const audio = await fetchParagraphAudio(
            message.content, 
            index,
            readerId,
            sessionId,
            message.id || `${blockId}-block_${index}`, // 使用新格式的带板块类型前缀的ID
            blockId,
            componentId, // 传递组件ID用于音频管理
            currentLanguage // 传递当前语言
          );
          
          if (!isMountedRef.current) return;
          
          // 为音频元素添加自定义暂停处理函数
          audioManager.updateCallback(audio, () => {
            // 当被其他音频暂停时
            if (isMountedRef.current && isPlaying && audioRef.current === audio) {
              // console.log(`[TarotBlockSpeech] 预加载的音频 #${index} 被其他音频暂停`);
              setIsPlaying(false);
            }
          });
          
          // 将音频添加到预加载列表
          preloadedAudios[index] = audio;
          loadedCount++;
          
          // 同时也更新 preloadedAudiosRef，这样即使在过程中开始播放，也能用上最新的音频
          preloadedAudiosRef.current = [...preloadedAudios];
          
          // 检查是否已达到1/4阈值并且还未开始播放
          if (!canStartPlayback && loadedCount >= oneFourthCount) {
            canStartPlayback = true;
            // console.log(`[TarotBlockSpeech] 已预加载1/4段落音频 (${loadedCount}/${aiMessages.length})，可以开始播放`);
            
            // 如果当前还在预加载中，并且未开始播放，则开始播放
            if (isMountedRef.current && !isPlaying && currentMessageIndex === -1) {
              // 使用setTimeout确保React状态更新后再开始播放
              const currentPreloadedAudios = [...preloadedAudios];
              
              // 在当前预加载循环之外尝试开始播放，防止状态不一致
              setTimeout(() => {
                if (isMountedRef.current && !isPlaying && currentMessageIndex === -1) {
                  // console.log(`[TarotBlockSpeech] 1/4加载完成后立即开始播放，当前有 ${currentPreloadedAudios.filter(a => a).length} 个可用音频`);
                  
                  // 为了确保播放能正常进行，我们可能需要重新赋值
                  if (preloadedAudiosRef.current.filter(a => a).length === 0) {
                    preloadedAudiosRef.current = currentPreloadedAudios;
                  }
                  
                  // 强制设置状态
                  setIsPreloading(false);
                  startPlayback();
                }
              }, 10); // 使用较短的延迟
            }
          }
        } catch (error) {
          // console.error(`[TarotBlockSpeech] 预加载段落#${index}失败:`, error);
          // 继续加载下一个，不中断整个加载过程
        }
      }
      
      if (!isMountedRef.current) return;
      
      // 保存所有预加载的音频
      preloadedAudiosRef.current = preloadedAudios;
      
      // console.log(`[TarotBlockSpeech] 所有段落音频预加载完成，共${loadedCount}/${aiMessages.length}段成功`);
      
      // 结束预加载状态
      setIsPreloading(false);
      setIsLoading(false);
      
      // 如果还没开始播放（例如没有达到1/4检查点或者预加载太快），则开始播放
      if (!isPlaying && currentMessageIndex === -1 && !canStartPlayback) {
        // console.log(`[TarotBlockSpeech] 预加载完成后发现尚未开始播放，现在开始播放`);
        startPlayback();
      } else if (canStartPlayback) {
        // console.log(`[TarotBlockSpeech] 已经在1/4处开始播放，不再重复启动播放`);
      }
    } catch (err: any) {
      // console.error(`[TarotBlockSpeech] 预加载音频失败: ${err.message}`);
      if (isMountedRef.current) {
        setError(t('speech.generation_error', '语音生成失败，请重试'));
        setIsLoading(false);
        setIsPreloading(false);
      }
    }
  };

  // 播放指定消息
  const playMessage = (index: number) => {
    if (index >= aiMessages.length) {
      // 所有消息播放完成
      stopPlayback();
      return;
    }

    // 检查是否有预加载的音频
    if (preloadedAudiosRef.current.length > 0 && preloadedAudiosRef.current[index]) {
      const audio = preloadedAudiosRef.current[index];
      
      // 检查音频元素是否可用
      if (!audio || audio.error) {
        playMessageOriginal(index);
        return;
      }
      
      // 设置高亮显示当前播放段落
      highlightCurrentParagraph(index);
      
      if (!isMountedRef.current) return;
      
      setIsLoading(false);
      setIsPlaying(true);
      setCurrentMessageIndex(index);
      
      audioRef.current = audio;
      
      // 设置播放完成回调
      audio.onended = () => {
        if (isMountedRef.current) {
          // 播放下一条消息
          playMessage(index + 1);
        }
      };
      
      // 设置错误处理
      audio.onerror = () => {
        if (isMountedRef.current) {
          setError(t('speech.playback_error', '播放错误，请重试'));
          setIsPlaying(false);
          setIsLoading(false);
          clearHighlights(); // 清除高亮
        }
      };
      
      // 注册到音频管理器并播放，同时提供暂停回调
      audioManager.playAudio(audio, componentId, () => {
        // 当被其他音频暂停时，更新状态
        if (isMountedRef.current) {
          setIsPlaying(false);
          clearHighlights(); // 清除高亮
        }
      });
    } else {
      // 如果没有预加载，回退到原始逻辑
      playMessageOriginal(index);
    }
  };
  
  // 使用原始获取音频和播放方法
  const playMessageOriginal = async (index: number) => {
    if (index >= aiMessages.length) {
      stopPlayback();
      return;
    }
    
    try {
      setIsLoading(true);
      setCurrentMessageIndex(index);
      setError(null);
      
      // 获取当前要播放的消息
      const message = aiMessages[index];
      
      // 从 API 获取音频
      const audio = await fetchParagraphAudio(
        message.content, 
        index,
        readerId,
        sessionId,
        message.id || `${blockId}-block_${index}`, // 使用新格式的带板块类型前缀的ID
        blockId,
        componentId, // 传递组件ID用于音频管理
        currentLanguage // 传递当前语言
      );
      
      if (!isMountedRef.current) return;
      
      // 设置高亮显示当前播放段落
      highlightCurrentParagraph(index);
      
      audioRef.current = audio;
      
      // 设置播放完成回调
      audio.onended = () => {
        if (isMountedRef.current) {
          // 播放下一条消息
          playMessage(index + 1);
        }
      };
      
      // 设置错误处理
      audio.onerror = () => {
        if (isMountedRef.current) {
          setError(t('speech.playback_error', '播放错误，请重试'));
          setIsPlaying(false);
          setIsLoading(false);
          clearHighlights(); // 清除高亮
        }
      };
      
      // 注册到音频管理器并播放，同时提供暂停回调
      audioManager.playAudio(audio, componentId, () => {
        // 当被其他音频暂停时，更新状态
        if (isMountedRef.current) {
          setIsPlaying(false);
          clearHighlights(); // 清除高亮
        }
      });
      
      setIsLoading(false);
      setIsPlaying(true);
    } catch (err: any) {
      // console.error(`[TarotBlockSpeech] 请求失败: ${err.message}`);
      if (isMountedRef.current) {
        setError(t('speech.generation_error', '语音生成失败，请重试'));
        setIsLoading(false);
        setIsPlaying(false);
        clearHighlights(); // 清除高亮
      }
    }
  };

  // 开始播放整个板块
  const startPlayback = () => {
    // 停止预加载状态（无论之前是什么状态）
    setIsPreloading(false);
    
    // 如果有暂停的音频，直接继续播放
    if (audioRef.current && !isPlaying && audioRef.current.duration > 0 && audioRef.current.currentTime < audioRef.current.duration) {
      audioRef.current.play();
      setIsPlaying(true);
      
      // 高亮当前播放的段落
      if (currentMessageIndex >= 0) {
        highlightCurrentParagraph(currentMessageIndex);
      }
      
      return;
    }
    
    // 否则停止当前播放（如果有）并从头开始
    stopPlayback();
    
    // 开始播放第一条消息
    playMessage(0);
  };

  // 暂停播放
  const pausePlayback = () => {
    if (audioRef.current && isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
      clearHighlights(); // 清除高亮
    }
  };

  // 切换播放/暂停
  const togglePlay = () => {
    // 检查VIP状态 - 对于非basic占卜师，只有VIP用户可以使用语音功能
    if (readerId && readerId !== 'basic' && user) {
      // 检查VIP状态是否为active
      if (user.vipStatus !== 'active') {
        // 非VIP用户点击非basic占卜师的语音，显示VIP提示
        if (setShowVipPrompt) {
          setShowVipPrompt(true);
        }
        return;
      }
      
      // 检查VIP是否过期
      if (user.vipEndDate) {
        const now = new Date();
        const vipEndDate = new Date(user.vipEndDate);
        
        if (now > vipEndDate) {
          // VIP已过期，显示VIP提示
          console.log('用户VIP已过期:', {
            vipEndDate: user.vipEndDate,
            currentDate: now.toISOString()
          });
          if (setShowVipPrompt) {
            setShowVipPrompt(true);
          }
          return;
        }
      }
    }
    
    if (isPlaying) {
      pausePlayback();
    } else if (isPreloading) {
      // 正在预加载，但用户希望立即开始播放已加载的部分
      
      // 立即停止预加载状态，强制开始播放
      setIsPreloading(false);
      
      // 等待状态更新后再开始播放
      setTimeout(() => {
        if (isMountedRef.current && !isPlaying) {
          const availableAudios = preloadedAudiosRef.current.filter(a => a).length;
          
          // 即使只有一个音频也开始播放
          if (availableAudios > 0) {
            startPlayback();
          } else {
            playMessageOriginal(0);
          }
        }
      }, 20);
    } else if (preloadedAudiosRef.current.filter(a => a).length === 0) {
      // 如果还未预加载，开始预加载
      preloadAllAudios();
    } else {
      // 判断是否有暂停的音频
      if (audioRef.current && audioRef.current.duration > 0 && audioRef.current.currentTime < audioRef.current.duration && currentMessageIndex >= 0) {
        // 有暂停的音频，继续播放
        audioRef.current.play();
        setIsPlaying(true);
        
        // 确保继续播放时也高亮当前段落
        highlightCurrentParagraph(currentMessageIndex);
      } else {
        // 没有暂停的音频或播放已完成，从头开始播放
        startPlayback();
      }
    }
  };

  return (
    <>
      {/* 移除语言限制，在所有语言环境下显示播放按钮 */}
      <div className={`flex items-center justify-center mt-5 mb-3 ${isDark ? 'text-purple-300' : 'text-purple-700'}`}>
        {/* PC端显示分割线 */}
        <div className="hidden sm:flex w-full md:w-4/5 lg:w-3/4 items-center px-4">
          <div className="flex-grow h-[1.5px] bg-gradient-to-r from-transparent via-blue-600/70 to-transparent"></div>
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={togglePlay}
            disabled={isLoading && !isPreloading}
            className={`px-4 py-2 rounded-full flex items-center justify-center transition-all duration-300 text-xs sm:text-sm ${
              isPlaying 
                ? isDark 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-blue-600 text-white' 
                : isDark 
                  ? 'bg-blue-600 text-white hover:bg-blue-700' 
                  : 'bg-blue-500 text-white hover:bg-blue-600'
            } shadow-md ${isPlaying ? 'shadow-blue-500/30' : 'shadow-blue-500/20'}`}
          >
            {isLoading ? (
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-1 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>{t('speech.loading', '加载中...')}</span>
              </div>
            ) : isPlaying ? (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{t('speech.pause_all', '暂停解读')}</span>
              </div>
            ) : (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                </svg>
                <span>{t('speech.play_all_above', '聆听上述解读')}</span>
              </div>
            )}
          </motion.button>
          <div className="flex-grow h-[1.5px] bg-gradient-to-r from-blue-600/70 via-transparent to-transparent"></div>
        </div>
        
        {/* 移动端显示简化版按钮 */}
        <div className="flex sm:hidden w-full items-center justify-center">
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={togglePlay}
            disabled={isLoading && !isPreloading}
            className={`px-4 py-2 rounded-full flex items-center justify-center transition-all duration-300 text-xs ${
              isPlaying 
                ? isDark 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-blue-600 text-white' 
                : isDark 
                  ? 'bg-blue-600 text-white hover:bg-blue-700' 
                  : 'bg-blue-500 text-white hover:bg-blue-600'
            } shadow-md ${isPlaying ? 'shadow-blue-500/30' : 'shadow-blue-500/20'}`}
          >
            {isLoading ? (
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-1 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>{t('speech.loading', '加载中...')}</span>
              </div>
            ) : isPlaying ? (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{t('speech.pause', '暂停朗读')}</span>
              </div>
            ) : (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                </svg>
                <span>{t('speech.listen', '聆听解读')}</span>
              </div>
            )}
          </motion.button>
        </div>
      </div>
      
      {/* 错误提示 */}
      {error && (
        <div className="text-center text-red-500 text-sm mt-2">
          {error}
        </div>
      )}
    </>
  );
};

export default TarotBlockSpeech; 