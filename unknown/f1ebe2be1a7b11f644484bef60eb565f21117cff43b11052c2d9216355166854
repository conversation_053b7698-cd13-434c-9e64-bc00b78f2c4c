import axiosInstance from '../utils/axios';

export interface CreateOrderParams {
  amount: number;
  productId: string;
  productName: string;
  userId: string;
  currency: 'CNY' | 'USD';
  paymentMethod?: 'wechat' | 'alipay' | 'paypal';
  paypalOrderId?: string;  // PayPal订单ID
}

export interface OrderResponse {
  success: boolean;
  data: {
    orderId: string;
    qrCodeUrl?: string;
    payUrl?: string;
    paymentMethod: 'wechat' | 'alipay' | 'paypal';
    raw?: any; // For PayPal debug information
  };
}

export interface PayPalVerifyResponse {
  success: boolean;
  message?: string;
  data?: {
    orderStatus: string;
    paymentStatus: string;
    paymentDetails?: any;
  };
}

// 检测是否为移动设备
const isMobileDevice = (): boolean => {
  const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
};

export const createOrder = async (params: CreateOrderParams): Promise<OrderResponse> => {
  try {
    // If PayPal is selected, use USD as currency
    if (params.paymentMethod === 'paypal') {
      params.currency = 'USD';
      // 确保PayPal金额最多只有两位小数
      params.amount = parseFloat(params.amount.toFixed(2));
    }

    // 检测设备类型，在移动设备上使用WAP支付
    const isMobile = isMobileDevice();
    
    const response = await axiosInstance.post(`/api/payment/create-order`, {
      ...params,
      isMobile // 传递设备类型给后端
    });
    
    // If it's a PayPal payment and we have a valid payUrl, redirect to PayPal
    if (response.data.success && 
        response.data.data.paymentMethod === 'paypal' && 
        response.data.data.payUrl) {
      window.location.href = response.data.data.payUrl;
    }
    
    return response.data;
  } catch (error: any) {
    // console.error('创建订单失败:', error);
    // console.error('错误详情:', {
        // message: error.message,
        // response: error.response?.data,
        // status: error.response?.status
    // });
    throw error;
  }
};

export const checkOrderStatus = async (orderId: string, paymentMethod: 'wechat' | 'alipay' | 'paypal' = 'wechat'): Promise<{tradeState: string; tradeStateDesc: string}> => {
  try {


    const response = await axiosInstance.get(`/api/payment/order-status/${orderId}?paymentMethod=${paymentMethod}`);


    return response.data.data;
  } catch (error: any) {
    // console.error('检查订单状态失败:', error);
    // console.error('错误详情:', {
      // message: error.message,
      // response: error.response?.data,
      // status: error.response?.status
    // });
    throw error;
  }
};

export const closeOrder = async (orderId: string, paymentMethod: 'wechat' | 'alipay' | 'paypal' = 'wechat'): Promise<{ success: boolean }> => {
  try {


    const response = await axiosInstance.post(`/api/payment/close-order`, { orderId, paymentMethod });


    return response.data;
  } catch (error: any) {
    // console.error('关闭订单失败:', error);
    // console.error('错误详情:', {
    //   message: error.message,
    //   response: error.response?.data,
    //   status: error.response?.status
    // });
    throw error;
  }
};

// New function to handle PayPal payment capture
export const capturePaypalPayment = async (orderId: string): Promise<{ success: boolean }> => {
  try {
    const response = await axiosInstance.post(`/api/payment/paypal/capture-payment/${orderId}`);
    return response.data;
  } catch (error) {
    // console.error('PayPal capture payment error:', error);
    throw error;
  }
};

// 验证PayPal支付结果
export const verifyPaypalPayment = async (params: {
  orderId: string;
  paypalOrderId?: string;
  paypalPaymentId?: string;
  subscriptionId?: string;
  isSubscription?: boolean;
}) => {
  try {
    const endpoint = params.isSubscription ? '/verify-subscription' : '/verify-payment';
    const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payment/paypal${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to verify payment');
    }

    const result = await response.json();
    return result;
  } catch (error: any) {
    // console.error('验证PayPal支付失败:', error);
    throw error;
  }
};