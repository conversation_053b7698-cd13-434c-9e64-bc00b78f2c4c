/**
 * 百度URL推送脚本 - 手动执行版
 * 
 * 使用方法：
 * node scripts/baiduPush.js
 */

// 引入百度URL推送服务
const baiduUrlService = require('../services/baiduUrlService');

// 执行推送并显示结果
async function runPush() {
  console.log('===== 开始手动执行百度URL推送 =====\n');
  
  try {
    // 执行推送
    const result = await baiduUrlService.executeFullPushProcess();
    
    if (result.error) {
      console.error(`推送出错: ${result.error}`);
      return;
    }
    
    if (result.message) {
      console.log(`\n执行状态: ${result.message}`);
      return;
    }
    
    // 显示推送摘要
    console.log('\n===== 推送摘要 =====');
    console.log(`成功推送: ${result.success} 个URL`);
    console.log(`提交总数: ${result.totalSubmitted} 个URL`);
    
    if (result.overQuota) {
      console.log('\n注意: 百度API返回配额已用完，已停止推送剩余URL');
    }
    
    // 显示详细推送记录
    console.log('\n===== 推送详情 =====');
    const pushLog = baiduUrlService.getTodayPushLog();
    
    if (pushLog) {
      console.log(`当前配额状态: 已使用 ${pushLog.success || 0} 个，剩余 ${pushLog.remain || 0} 个`);
      console.log(`今日已推送URL总数: ${pushLog.total || 0}`);
    } else {
      console.log('无法获取推送记录');
    }
    
    console.log('\n===== 百度URL推送完成 =====');
  } catch (error) {
    console.error(`执行出错: ${error.message}`);
  }
}

// 执行推送
runPush().catch(error => {
  console.error('脚本执行失败:', error);
  process.exit(1);
}); 