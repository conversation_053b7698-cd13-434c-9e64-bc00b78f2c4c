const { getConnection } = require('./database');

// 星座ID列表
const ZODIAC_SIGNS = [
  'aries', 'taurus', 'gemini', 'cancer', 
  'leo', 'virgo', 'libra', 'scorpio', 
  'sagittarius', 'capricorn', 'aquarius', 'pisces'
];

/**
 * 从数据库获取星座运势数据
 * @param {string} sign 星座ID
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<Object|null>} 星座运势数据或null
 */
async function getHoroscopeFromDb(sign, type, date, language = 'zh-CN') {
  try {
    // 使用本地时区格式化日期，而不是UTC
    const formattedDate = formatDateForDb(date);
    
    console.log(`\n------- DB查询 -------\nsign=${sign}, type=${type}, date=${formattedDate}`);
    
    // 确保sign是有效的星座ID
    if (!ZODIAC_SIGNS.includes(sign)) {
      return null;
    }
    
    const query = `
      SELECT ${sign}, raw_content, generated_at, input_tokens, output_tokens 
      FROM tarot_horoscopes 
      WHERE type = ? AND date = ? AND language = ?
      LIMIT 1
    `;
    
    const db = await getConnection();
    const [rows] = await db.query(query, [type, formattedDate, language]);
    
    if (rows && rows.length > 0 && rows[0][sign]) {
      console.log(`DB结果: 找到数据 ✓`);
      return {
        sign,
        type,
        date: formattedDate,
        content: rows[0][sign],
        raw_content: rows[0].raw_content,
        language,
        generated_at: rows[0].generated_at,
        input_tokens: rows[0].input_tokens || 0,
        output_tokens: rows[0].output_tokens || 0
      };
    }
    
    console.log(`DB结果: 未找到数据 ✗`);
    return null;
  } catch (error) {
    console.error(`数据库查询失败: ${error.message}`);
    return null;
  }
}

/**
 * 保存星座运势数据到数据库
 * @param {string} sign 星座ID
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} content 解析后的内容
 * @param {string} rawContent API返回的原始内容
 * @param {string} language 语言
 * @param {number} inputTokens 输入token数量
 * @param {number} outputTokens 输出token数量
 * @returns {Promise<boolean>} 是否成功
 */
async function saveHoroscopeToDb(sign, type, date, content, rawContent, language = 'zh-CN', inputTokens = 0, outputTokens = 0) {
  try {
    // 使用本地时区格式化日期，而不是UTC
    const formattedDate = formatDateForDb(date);
    // 使用本地时区格式化当前时间
    const now = formatDateTimeForDb(new Date());
    
    // 检查数据库连接
    const db = await getConnection();
    if (!db) {
      throw new Error('无法获取数据库连接');
    }
    
    // 确保sign是有效的星座ID
    if (!ZODIAC_SIGNS.includes(sign)) {
      throw new Error(`Invalid zodiac sign: ${sign}`);
    }
    
    
    // 首先检查记录是否已存在
    const checkQuery = `
      SELECT id FROM tarot_horoscopes 
      WHERE type = ? AND date = ? AND language = ?
      LIMIT 1
    `;
    
    const [existingRows] = await db.query(checkQuery, [type, formattedDate, language]);
    
    if (existingRows && existingRows.length > 0) {
      // 记录已存在，更新特定星座的列
      const updateQuery = `
        UPDATE tarot_horoscopes 
        SET ${sign} = ?, raw_content = ?, generated_at = ?, input_tokens = ?, output_tokens = ? 
        WHERE type = ? AND date = ? AND language = ?
      `;
      
      const [updateResult] = await db.query(updateQuery, [
        content,
        rawContent || content,
        now,
        inputTokens,
        outputTokens,
        type,
        formattedDate,
        language
      ]);
      
    } else {
      // 记录不存在，创建新记录
      const insertQuery = `
        INSERT INTO tarot_horoscopes 
          (type, date, ${sign}, raw_content, language, generated_at, input_tokens, output_tokens) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      
      const [insertResult] = await db.query(insertQuery, [
        type,
        formattedDate,
        content,
        rawContent || content,
        language,
        now,
        inputTokens,
        outputTokens
      ]);
      
    }
    
    return true;
  } catch (error) {
    console.error(`保存星座运势到数据库失败: ${error.message}`);
    
    // 添加更多错误信息以便调试
    if (error.sql) {
      console.error(`错误的SQL语句: ${error.sql}`);
    }
    
    return false;
  }
}

/**
 * 获取所有星座特定类型的运势数据
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<Object|null>} 所有星座运势数据或null
 */
async function getAllHoroscopesFromDb(type, date, language = 'zh-CN') {
  try {
    // 使用本地时区格式化日期，而不是UTC
    const formattedDate = formatDateForDb(date);
    
    console.log(`\n------- DB查询(全部) -------\ntype=${type}, date=${formattedDate}`);
    
    const signColumns = ZODIAC_SIGNS.join(', ');
    const query = `
      SELECT ${signColumns}, raw_content, generated_at, input_tokens, output_tokens
      FROM tarot_horoscopes 
      WHERE type = ? AND date = ? AND language = ?
      LIMIT 1
    `;
    
    const db = await getConnection();
    const [rows] = await db.query(query, [type, formattedDate, language]);
    
    if (rows && rows.length > 0) {
      const row = rows[0];
      
      // 检查是否有任何星座数据
      const hasAnyData = ZODIAC_SIGNS.some(sign => row[sign] !== null);
      
      if (!hasAnyData) {
        console.log(`DB结果: 未找到任何数据 ✗`);
        return null;
      }
      
      // 转换为以星座ID为键的对象
      const results = {};
      let foundCount = 0;
      ZODIAC_SIGNS.forEach(sign => {
        if (row[sign]) {
          results[sign] = {
            sign,
            type,
            date: formattedDate,
            content: row[sign],
            language,
            generated_at: row.generated_at,
            input_tokens: row.input_tokens || 0,
            output_tokens: row.output_tokens || 0
          };
          foundCount++;
        }
      });
      
      console.log(`DB结果: 找到 ${foundCount} 个星座数据 ✓`);
      return results;
    }
    
    console.log(`DB结果: 未找到数据 ✗`);
    return null;
  } catch (error) {
    console.error(`获取星座运势数据失败: ${error.message}`);
    return null;
  }
}

/**
 * 使用本地时区格式化日期为YYYY-MM-DD格式
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串YYYY-MM-DD
 */
function formatDateForDb(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 使用本地时区格式化日期时间为MySQL datetime格式 YYYY-MM-DD HH:MM:SS
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期时间字符串 YYYY-MM-DD HH:MM:SS
 */
function formatDateTimeForDb(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 检查数据库表是否存在，如果不存在则创建
 * @returns {Promise<boolean>} 是否成功
 */
async function ensureHoroscopeTableExists() {
  try {
    const db = await getConnection();
    
    // 检查表是否存在
    const [tables] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'tarot_horoscopes'
    `);
    
    if (tables.length === 0) {
      
      // 创建表
      await db.query(`
        CREATE TABLE IF NOT EXISTS tarot_horoscopes (
          id INT AUTO_INCREMENT PRIMARY KEY,
          type VARCHAR(20) NOT NULL COMMENT '运势类型(daily/weekly/monthly/yearly/love)',
          date DATE NOT NULL COMMENT '运势日期',
          aries TEXT COMMENT '白羊座运势内容',
          taurus TEXT COMMENT '金牛座运势内容',
          gemini TEXT COMMENT '双子座运势内容',
          cancer TEXT COMMENT '巨蟹座运势内容',
          leo TEXT COMMENT '狮子座运势内容',
          virgo TEXT COMMENT '处女座运势内容',
          libra TEXT COMMENT '天秤座运势内容',
          scorpio TEXT COMMENT '天蝎座运势内容',
          sagittarius TEXT COMMENT '射手座运势内容',
          capricorn TEXT COMMENT '摩羯座运势内容',
          aquarius TEXT COMMENT '水瓶座运势内容',
          pisces TEXT COMMENT '双鱼座运势内容',
          raw_content TEXT NOT NULL COMMENT 'API返回的原始内容',
          language VARCHAR(10) NOT NULL DEFAULT 'zh-CN' COMMENT '语言',
          input_tokens INT NOT NULL DEFAULT 0 COMMENT '输入token数量',
          output_tokens INT NOT NULL DEFAULT 0 COMMENT '输出token数量',
          generated_at DATETIME NOT NULL COMMENT '生成时间',
          updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          
          UNIQUE KEY idx_type_date_lang (type, date, language),
          INDEX idx_date (date),
          INDEX idx_type_date (type, date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='星座运势数据表'
      `);
      
      return true;
    } else {
      // 检查是否需要添加input_tokens和output_tokens字段
      const [columns] = await db.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'tarot_horoscopes' 
        AND COLUMN_NAME = 'input_tokens'
      `);
      
      if (columns.length === 0) {
        // 添加字段
        await db.query(`
          ALTER TABLE tarot_horoscopes 
          ADD COLUMN input_tokens INT NOT NULL DEFAULT 0 COMMENT '输入token数量' AFTER language,
          ADD COLUMN output_tokens INT NOT NULL DEFAULT 0 COMMENT '输出token数量' AFTER input_tokens
        `);
        console.log('已添加input_tokens和output_tokens字段到tarot_horoscopes表');
      }
    }
    
    return true;
  } catch (error) {
    console.error(`确保星座运势表存在时发生错误: ${error.message}`);
    return false;
  }
}

// 初始化时检查表是否存在
ensureHoroscopeTableExists().catch(err => {
});

module.exports = {
  getHoroscopeFromDb,
  saveHoroscopeToDb,
  getAllHoroscopesFromDb,
  ensureHoroscopeTableExists,
  formatDateForDb,
  formatDateTimeForDb
}; 