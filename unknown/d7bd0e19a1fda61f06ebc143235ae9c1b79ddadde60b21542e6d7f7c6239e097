import axiosInstance from '../utils/axios';

export interface InvitationCode {
  id: number;
  code: string;
  sales_id: number;
  is_used: boolean;
  used_by: string | null;
  used_by_username?: string | null;
  created_at: string;
  used_at: string | null;
  payment_amount?: number | null;
}

export interface SalesPerson {
  id: number;
  name: string;
  created_at: string;
}

export interface PrivilegeInfo {
  hasInternalPrivilege: boolean;
  privilegeInfo: {
    has_internal_privilege: boolean;
    privilege_granted_at: string | null;
    code: string | null;
    code_created_at: string | null;
    sales_person_name: string | null;
    has_used_discount?: boolean;
  } | null;
}

export interface StatsInfo {
  totalCodes: number;
  usedCodes: number;
  unusedCodes: number;
  totalPayment?: number; // 总支付金额
}

// 兑换邀请码
export const redeemInvitationCode = async (code: string): Promise<{ message: string }> => {
  const response = await axiosInstance.post('/api/invitation/redeem', { code });
  return response.data;
};

// 检查用户特权状态
export const checkPrivilegeStatus = async (): Promise<PrivilegeInfo> => {
  const response = await axiosInstance.get('/api/invitation/check-privilege');
  return response.data;
};

// 获取销售人员列表（管理员使用）
export const getSalesPersons = async (): Promise<SalesPerson[]> => {
  const response = await axiosInstance.get('/api/invitation/sales');
  return response.data;
};

// 创建销售人员（管理员使用）
export const createSalesPerson = async (name: string): Promise<SalesPerson> => {
  const response = await axiosInstance.post('/api/invitation/sales', { name });
  return response.data;
};

// 删除销售人员（管理员使用）
export const deleteSalesPerson = async (id: number): Promise<{ message: string }> => {
  const response = await axiosInstance.delete(`/api/invitation/sales/${id}`);
  return response.data;
};

// 生成邀请码
export const generateInvitationCode = async (userId: string): Promise<InvitationCode> => {
  const response = await axiosInstance.post('/api/invitation/codes', { userId });
  return response.data;
};

// 获取销售人员的邀请码列表
export const getInvitationCodes = async (userId: string): Promise<InvitationCode[]> => {
  const response = await axiosInstance.get(`/api/invitation/codes/${userId}`);
  return response.data;
};

// 获取销售人员的统计数据
export const getSalesStats = async (userId: string): Promise<StatsInfo> => {
  const response = await axiosInstance.get(`/api/invitation/stats/${userId}`);
  return response.data;
};

// 检查用户是否为销售人员
export const checkIsSalesPerson = async (userId: string): Promise<boolean> => {
  try {

    const response = await axiosInstance.get(`/api/invitation/check-sales/${userId}`);

    return response.data.isSalesPerson;
  } catch (error: any) {
    // console.error('检查销售权限出错:', error);
    
    if (error.response) {
      // console.log('错误响应详情:', {
        // status: error.response.status,
        // data: error.response.data,
        // headers: error.response.headers
      // });
      
      if (error.response.status === 403) {
        throw new Error('权限不足');
      }
    }
    
    return false;
  }
}; 