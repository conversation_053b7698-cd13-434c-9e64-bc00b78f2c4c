const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { recommendSpreadCategory, recommendSpreadWithAI } = require('../services/spreadRecommendationService');
const { SPREAD_OPTIONS } = require('../constants/spreads');
const { getConnection } = require('../services/database');
const { Session } = require('../models/tarot');

// 添加请求计数器用于调试
let requestCounter = 0;

router.post('/', authenticateToken, async (req, res) => {
  const requestId = ++requestCounter;
  // console.log(`\n========== 收到牌阵推荐请求 #${requestId} ==========`);
  const startTime = Date.now();
  
  try {
    const { question, language } = req.body;
    const userId = req.user.userId;
    const sessionId = req.body.sessionId;
    
    // Token统计
    let totalInputTokens = 0;
    let totalOutputTokens = 0;
    
    if (!question) {
      console.log('错误: 缺少问题内容');
      return res.status(400).json({ error: '请提供问题内容' });
    }

    // 第一步：根据问题推荐牌阵类型 (异步)
    // console.log('\n1. 开始推荐牌阵类型...');
    const { category, matchDetails, tokens: categoryTokens } = await recommendSpreadCategory(question, language);
    
    // 累计token使用
    totalInputTokens += categoryTokens?.input || 0;
    totalOutputTokens += categoryTokens?.output || 0;

    // 获取该类型下的所有牌阵
    // console.log('\n2. 获取类型下的牌阵...');
    
    // 从CATEGORY_MAP获取类别名称
    const categoryMap = {
      'love_relationships': '爱情人际',
      'self-exploration': '自我探索',
      'career_wealth': '事业财富',
      'general': '通用'
    };
    
    const categoryName = categoryMap[category] || '通用';
    
    // 当类别为"通用"时，直接获取所有通用牌阵
    const categorySpreads = SPREAD_OPTIONS.filter(spread => spread.category.includes(categoryName));
      
    // console.log(`找到 ${categorySpreads.length} 个匹配的牌阵`);

    // 准备记录推荐结果的对象
    let finalRecommendation = {};
    let finalCategory = category;

    if (categorySpreads.length === 0) {
      console.log('\n没有找到匹配的牌阵，尝试使用通用牌阵...');
      // 如果找不到匹配的牌阵，使用通用牌阵
      const generalSpreads = SPREAD_OPTIONS.filter(spread => spread.category.includes('通用'));
      
      if (generalSpreads.length === 0) {
        console.log('错误: 找不到通用牌阵');
        return res.status(500).json({ error: '找不到匹配的牌阵' });
      }
      
      const defaultRecommendation = {
        recommendedSpreadId: generalSpreads[0].id,
        reason: "未找到完全匹配的牌阵，推荐使用通用牌阵。",
        confidence: 0.5
      };

      finalRecommendation = defaultRecommendation;
      finalCategory = 'general';

      console.log('返回默认通用牌阵:', defaultRecommendation);
      
      const response = {
        category: 'general',
        categoryName: '通用',
        recommendation: defaultRecommendation,
        availableSpreads: generalSpreads.map(spread => ({
          id: spread.id,
          name: spread.name,
          description: spread.description
        }))
      };
      
      // 记录到数据库
      if (sessionId) {
        await recordRecommendationStats(sessionId, {
          category: 'general',
          spreadId: defaultRecommendation.recommendedSpreadId,
          inputTokens: totalInputTokens,
          outputTokens: totalOutputTokens,
          time: Math.round((Date.now() - startTime) / 1000)
        });
      }
      
      return res.json(response);
    }

    // 第二步：使用 AI 从该类型中推荐具体牌阵
    // console.log('\n3. 使用AI推荐具体牌阵...');
    const recommendation = await recommendSpreadWithAI(question, categorySpreads, language);
    
    // 累计token使用
    totalInputTokens += recommendation.tokens?.input || 0;
    totalOutputTokens += recommendation.tokens?.output || 0;

    // 确保推荐的牌阵ID存在于当前类型中
    const isValidRecommendation = categorySpreads.some(spread => spread.id === recommendation.recommendedSpreadId);
    if (!isValidRecommendation) {
      // console.log('\n警告: AI推荐的牌阵不在当前类型中，使用默认推荐');
      recommendation.recommendedSpreadId = categorySpreads[0].id;
      recommendation.reason = "AI推荐的牌阵不在当前类型中，已更正为默认推荐。";
      recommendation.confidence = 0.5;
    }
    
    finalRecommendation = recommendation;

    const response = {
      category,
      categoryName,
      recommendation,
      availableSpreads: categorySpreads.map(spread => ({
        id: spread.id,
        name: spread.name,
        description: spread.description
      }))
    };

    const endTime = Date.now();
    const processingTimeSeconds = Math.round((endTime - startTime) / 1000);
    // console.log('\n推荐完成');
    // console.log('处理时间:', processingTimeSeconds, '秒');
    // console.log('总输入tokens:', totalInputTokens);
    // console.log('总输出tokens:', totalOutputTokens);
    // console.log(`=============== 请求 #${requestId} 结束 ===============\n`);
    
    // 记录到数据库
    if (sessionId) {
      await recordRecommendationStats(sessionId, {
        category: finalCategory,
        spreadId: finalRecommendation.recommendedSpreadId,
        inputTokens: totalInputTokens,
        outputTokens: totalOutputTokens,
        time: processingTimeSeconds
      });
    }

    res.json(response);

  } catch (error) {
    const endTime = Date.now();
    console.error('\n推荐过程出错:', error);
    console.log('处理时间:', (endTime - startTime) / 1000, '秒');
    // console.log(`=============== 请求 #${requestId} 结束 ===============\n`);
    
    res.status(500).json({ 
      error: '生成牌阵推荐时出现了问题，请稍后再试。' 
    });
  }
});

// 记录推荐统计信息到数据库
async function recordRecommendationStats(sessionId, stats) {
  try {
    const pool = await getConnection();
    
    // 更新session表中的推荐信息
    await pool.query(`
      UPDATE sessions SET
        spread_recommendation_input_tokens = ?,
        spread_recommendation_output_tokens = ?,
        spread_recommendation_time = ?,
        spread_recommendation_category = ?,
        spread_recommendation_spread_id = ?
      WHERE id = ?
    `, [
      stats.inputTokens,
      stats.outputTokens,
      stats.time,
      stats.category,
      stats.spreadId,
      sessionId
    ]);
    
    console.log(`已记录牌阵推荐统计信息到会话ID: ${sessionId}`);
  } catch (error) {
    console.error('记录牌阵推荐统计信息失败:', error);
  }
}

module.exports = router; 