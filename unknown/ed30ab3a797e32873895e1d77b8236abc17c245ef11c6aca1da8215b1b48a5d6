import React, { useRef, useLayoutEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useUser } from '../contexts/UserContext';
import { VipBadge } from './VipBadge';
import { useTranslation } from 'react-i18next';
import { useDropdown } from '../contexts/DropdownContext';
import LanguageLink from './LanguageLink';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

// 定义菜单项接口
interface MenuItem {
  label: string;
  path?: string;
  onClick?: () => void;
}

const UserAuth: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { user, logout } = useUser();
  const { openDropdown, setOpenDropdown, registerDropdownRef } = useDropdown();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { t } = useTranslation();

  // Register refs with context using useLayoutEffect
  useLayoutEffect(() => {
    if (buttonRef.current && dropdownRef.current) {
      registerDropdownRef('user', buttonRef, dropdownRef);
    }
    return () => {
      // Clean up by registering null refs
      registerDropdownRef('user', { current: null }, { current: null });
    };
  }, [registerDropdownRef]); // Add registerDropdownRef to dependencies

  const handleLogout = () => {
    logout();
    setOpenDropdown(null);
    navigate('/login');
  };

  // 构建菜单项
  const getMenuItems = (): MenuItem[] => {
    if (!user) {
      return [
        { label: t('auth.login'), path: '/login' },
        { label: t('auth.register'), path: '/register' },
        { label: t('nav.history'), path: '/history' },
      ];
    }
    
    const items: MenuItem[] = [
      { label: t('nav.profile'), path: '/user-card' },
      { label: t('nav.history'), path: '/history' },
      { label: t('nav.settings'), path: '/card-back-settings' },
      { label: t('nav.feedback'), path: '/feedback' },
    ];
    
    // 添加注销选项
    items.push({ label: t('auth.logout'), onClick: handleLogout });
    
    return items;
  };
      
  const handleNavigation = (e: React.MouseEvent, path: string) => {
    e.preventDefault();
    setOpenDropdown(null);

    // 使用自定义导航钩子
    navigate(path);
  };

  const menuItems = getMenuItems();

  return (
    <div className="relative">
      <div className="relative flex flex-col items-end">
        {user ? (
          <button
            ref={buttonRef}
            onClick={() => setOpenDropdown(openDropdown === 'user' ? null : 'user')}
            onMouseEnter={() => setOpenDropdown('user')}
            className="flex items-center"
          >
            {/* Mobile & Tablet Button */}
            <div className="lg:hidden w-8 h-8 rounded-full 
                      flex items-center justify-center overflow-visible transition-all duration-200
                      dark:hover:text-white hover:text-black hover:bg-purple-500/10
                      relative border ${user.vipStatus === 'active' ? 'border-yellow-500/50 hover:border-yellow-400/70' : 'dark:border-black border-gray-300 dark:hover:border-gray-800 hover:border-gray-400'}">
              <span className="text-sm font-medium dark:text-white text-gray-800">
                {user.username.charAt(0).toUpperCase()}
              </span>
              {/* VIP Badge */}
              {user.vipStatus === 'active' && (
                <VipBadge variant="small" />
              )}
            </div>

            {/* Desktop Full Button */}
            <div className="hidden lg:flex items-center px-4 py-1.5 rounded-lg
                      dark:text-white text-gray-800 dark:hover:text-white hover:text-black 
                      dark:hover:bg-purple-500/10 hover:bg-purple-500/10
                      transition-all duration-200 text-sm font-medium font-['Inter']">
              <span className="flex items-center gap-2">
                <span className="flex items-center gap-1.5">
                  <span className="font-medium font-['Inter']">{user.username}</span>
                  {user.vipStatus === 'active' ? (
                    <VipBadge variant="badge" className="scale-[0.55] -my-2.5 -mx-1" />
                  ) : (
                    <span className="text-sm dark:text-gray-300 text-gray-600 font-['Inter']">
                      {t('common.remaining_reads', { count: user.remainingReads })}
                    </span>
                  )}
                </span>
              </span>
            </div>
          </button>
        ) : (
          <LanguageLink to="/login" className="flex items-center">
            {/* Mobile & Tablet Button */}
            <div className="lg:hidden px-4 py-1.5 rounded-full bg-[#9333EA] hover:bg-[#7E22CE] transition-all duration-200 flex items-center justify-center">
              <span className="text-sm font-bold text-white tracking-wide" style={{ color: '#FFFFFF' }}>
                {t('auth.login')}
              </span>
            </div>
            
            {/* Desktop Full Button */}
            <div className="hidden lg:flex items-center px-6 py-1.5 rounded-full
                      bg-[#9333EA] hover:bg-[#7E22CE] text-white
                      transition-all duration-200 text-xs font-bold font-['Inter']">
              <span className="flex items-center">
                <span className="font-bold text-xs font-['Inter']" style={{ color: '#FFFFFF' }}>{t('auth.login')}</span>
              </span>
            </div>
          </LanguageLink>
        )}

        <AnimatePresence>
          {openDropdown === 'user' && (
            <>
              {/* PC端下拉菜单 - 使用right-0对齐 */}
              <motion.div
                ref={dropdownRef}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="hidden lg:block absolute top-full right-0 mt-1 w-56 rounded-xl shadow-xl dark:shadow-purple-500/20 shadow-purple-500/10 
                         dark:bg-black bg-white z-50 border border-purple-500/30 overflow-hidden"
                onMouseEnter={() => setOpenDropdown('user')}
              >
                <div className="relative py-2">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 pointer-events-none rounded-xl" />
                  <div className="absolute -top-10 -right-10 w-24 h-24 bg-purple-500/10 rounded-full blur-xl pointer-events-none"></div>
                  <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-pink-500/10 rounded-full blur-xl pointer-events-none"></div>
                  
                  <div className="relative space-y-0 px-2">
                    {menuItems.map((item) => (
                      item.path ? (
                        <LanguageLink
                          key={item.path}
                          to={item.path}
                          onClick={(e) => handleNavigation(e, item.path as string)}
                          className="block w-full px-4 py-3 rounded-lg text-base xl:text-lg transition-all duration-300 text-center blog-dropdown-item
                                   font-medium
                                   dark:text-gray-100 text-gray-800 
                                   dark:hover:text-white hover:text-black hover:font-bold"
                        >
                          {item.label}
                        </LanguageLink>
                      ) : (
                        <button
                          key={item.label}
                          onClick={() => {
                            setOpenDropdown(null);
                            item.onClick?.();
                          }}
                          className="block w-full px-4 py-3 rounded-lg text-base xl:text-lg transition-all duration-300 text-center blog-dropdown-item
                                   font-medium
                                   dark:text-gray-100 text-gray-800 
                                   dark:hover:text-white hover:text-black hover:font-bold"
                        >
                          {item.label}
                        </button>
                      )
                    ))}
                  </div>
                </div>
              </motion.div>
              
              {/* 移动端下拉菜单 - 保持全宽 */}
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="lg:hidden fixed left-0 right-0 top-14 
                         dark:bg-black bg-white backdrop-blur-xl shadow-lg z-[999]
                         border-t border-purple-500/20"
              >
                <div className="relative">
                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-b from-purple-500/5 to-pink-500/5 pointer-events-none" />
                  
                  {/* Menu Items */}
                  <div className="relative px-4 py-3 space-y-1.5">
                    {menuItems.map((item) => (
                      item.path ? (
                        <LanguageLink
                          key={item.path}
                          to={item.path}
                          onClick={(e) => handleNavigation(e, item.path as string)}
                          className="block w-full px-4 py-2.5 rounded-xl text-base font-medium text-center 
                                   transition-all duration-200 border border-purple-500/20
                                   dark:text-gray-100 text-gray-800 dark:hover:text-white hover:text-black hover:bg-purple-500/10 hover:border-purple-500/30
                                   font-['Inter'] whitespace-nowrap"
                        >
                          {item.label}
                        </LanguageLink>
                      ) : (
                        <button
                          key={item.label}
                          onClick={() => {
                            setOpenDropdown(null);
                            item.onClick?.();
                          }}
                          className="block w-full px-4 py-2.5 rounded-xl text-base font-medium text-center 
                                   transition-all duration-200 border border-purple-500/20
                                   dark:text-gray-100 text-gray-800 dark:hover:text-white hover:text-black hover:bg-purple-500/10 hover:border-purple-500/30
                                   font-['Inter'] whitespace-nowrap"
                        >
                          {item.label}
                        </button>
                      )
                    ))}
                  </div>

                  {/* Bottom Shadow */}
                  <div className="absolute -bottom-6 left-0 right-0 h-6 bg-gradient-to-b dark:from-black/20 from-white/20 to-transparent pointer-events-none" />
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default UserAuth;
