const express = require('express');
const router = express.Router();
const { authenticateToken, isAdmin } = require('../middleware/auth');
const { SalesPerson } = require('../models/SalesPerson');
const { InvitationCode } = require('../models/InvitationCode');
const { User } = require('../models/User');

// 管理员获取所有销售人员列表
router.get('/sales', authenticateToken, isAdmin, async (req, res) => {
  try {
    const salesPersons = await SalesPerson.findAll();
    res.json(salesPersons);
  } catch (error) {
    console.error('获取销售人员列表失败:', error);
    res.status(500).json({ error: '获取销售人员列表失败' });
  }
});

// 管理员添加销售人员
router.post('/sales', authenticateToken, isAdmin, async (req, res) => {
  const { name } = req.body;
  
  if (!name || name.trim() === '') {
    return res.status(400).json({ error: '销售人员姓名不能为空' });
  }
  
  try {
    const salesPerson = await SalesPerson.create(name);
    res.status(201).json(salesPerson);
  } catch (error) {
    console.error('添加销售人员失败:', error);
    res.status(500).json({ error: '添加销售人员失败' });
  }
});

// 管理员删除销售人员
router.delete('/sales/:id', authenticateToken, isAdmin, async (req, res) => {
  try {
    const success = await SalesPerson.delete(req.params.id);
    if (success) {
      res.json({ message: '销售人员删除成功' });
    } else {
      res.status(404).json({ error: '销售人员不存在' });
    }
  } catch (error) {
    console.error('删除销售人员失败:', error);
    res.status(500).json({ error: '删除销售人员失败' });
  }
});

// 销售人员生成邀请码
router.post('/codes', authenticateToken, async (req, res) => {
  const { userId } = req.body;
  
  // 检查用户是否为管理员或该销售人员本人
  if (!req.user.isAdmin && req.user.userId !== userId) {
    return res.status(403).json({ error: '无权生成邀请码' });
  }
  
  try {
    // 先通过user_id查找销售人员记录
    const salesPerson = await SalesPerson.findByUserId(userId);
    if (!salesPerson) {
      return res.status(404).json({ error: '销售人员不存在' });
    }
    
    const invitationCode = await InvitationCode.create(salesPerson.id);
    res.status(201).json(invitationCode);
  } catch (error) {
    console.error('生成邀请码失败:', error);
    res.status(500).json({ error: '生成邀请码失败' });
  }
});

// 获取销售人员的邀请码列表
router.get('/codes/:userId', authenticateToken, async (req, res) => {
  const userId = req.params.userId;
  
  // 检查用户是否为管理员或该销售人员本人
  if (!req.user.isAdmin && req.user.userId !== userId) {
    return res.status(403).json({ error: '无权查看该销售人员的邀请码' });
  }
  
  try {
    // 先通过user_id查找销售人员记录
    const salesPerson = await SalesPerson.findByUserId(userId);
    if (!salesPerson) {
      return res.status(404).json({ error: '销售人员不存在' });
    }
    
    const codes = await InvitationCode.findBySalesId(salesPerson.id);
    res.json(codes);
  } catch (error) {
    console.error('获取邀请码列表失败:', error);
    res.status(500).json({ error: '获取邀请码列表失败' });
  }
});

// 获取销售人员统计数据
router.get('/stats/:userId', authenticateToken, async (req, res) => {
  const userId = req.params.userId;
  
  // 检查用户是否为管理员或该销售人员本人
  if (!req.user.isAdmin && req.user.userId !== userId) {
    return res.status(403).json({ error: '无权查看该销售人员的统计数据' });
  }
  
  try {
    // 先通过user_id查找销售人员记录
    const salesPerson = await SalesPerson.findByUserId(userId);
    if (!salesPerson) {
      return res.status(404).json({ error: '销售人员不存在' });
    }
    
    const stats = await SalesPerson.getStats(salesPerson.id);
    res.json(stats);
  } catch (error) {
    console.error('获取统计数据失败:', error);
    res.status(500).json({ error: '获取统计数据失败' });
  }
});

// 用户兑换邀请码
router.post('/redeem', authenticateToken, async (req, res) => {
  const { code } = req.body;
  const userId = req.user.userId;
  
  if (!code) {
    return res.status(400).json({ error: '邀请码不能为空' });
  }
  
  try {
    // 检查用户是否已经有内部权限
    const hasPrivilege = await User.hasInternalPrivilege(userId);
    if (hasPrivilege) {
      return res.status(400).json({ error: '您已经拥有内部权限' });
    }
    
    const result = await InvitationCode.use(code, userId);
    if (result.success) {
      res.json({ message: '邀请码兑换成功，您已获得内部权限' });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('兑换邀请码失败:', error);
    res.status(500).json({ error: '兑换邀请码失败' });
  }
});

// 检查用户内部权限
router.get('/check-privilege', authenticateToken, async (req, res) => {
  try {
    const hasPrivilege = await User.hasInternalPrivilege(req.user.userId);
    const privilegeInfo = await User.getInvitationCodeInfo(req.user.userId);
    
    // 检查用户是否已使用过折扣
    const hasUsedDiscount = await User.hasUsedDiscount(req.user.userId);
    
    // 如果privilegeInfo存在，添加has_used_discount字段
    if (privilegeInfo) {
      privilegeInfo.has_used_discount = hasUsedDiscount;
    }
    
    res.json({
      hasInternalPrivilege: hasPrivilege,
      privilegeInfo: privilegeInfo
    });
  } catch (error) {
    console.error('检查用户权限失败:', error);
    res.status(500).json({ error: '检查用户权限失败' });
  }
});

// 检查用户是否为销售人员
router.get('/check-sales/:userId', authenticateToken, async (req, res) => {
  const userId = req.params.userId;
  

  
  // 确保用户只能查询自己的销售状态，或管理员可查询任何人
  // 使用==而不是===，以允许类型转换，或者对两个值进行字符串化比较
  if (!req.user.isAdmin && String(req.user.userId) !== String(userId)) {
    return res.status(403).json({ error: '无权访问此信息' });
  }
  
  try {
    const isSalesPerson = await SalesPerson.existsByUserId(userId);
    // console.log('销售人员检查结果:', { userId, isSalesPerson });
    res.json({ isSalesPerson });
  } catch (error) {
    console.error('检查销售权限失败:', error);
    res.status(500).json({ error: '检查销售权限失败' });
  }
});

module.exports = router; 