import * as React from "react"
import { <PERSON>evronLeft, ChevronRight, MoreHorizontal } from "lucide-react"
import { useTheme } from "../../contexts/ThemeContext"

const Pagination = ({ className, ...props }: React.HTMLAttributes<HTMLElement>) => (
  <nav
    role="navigation"
    aria-label="pagination"
    className={`mx-auto flex w-full justify-center ${className}`}
    {...props}
  />
)

const PaginationContent = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={`flex flex-row items-center gap-1 ${className}`}
    {...props}
  />
))
PaginationContent.displayName = "PaginationContent"

const PaginationItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={`${className}`} {...props} />
))
PaginationItem.displayName = "PaginationItem"

type PaginationLinkProps = {
  isActive?: boolean
  disabled?: boolean
} & Pick<React.ComponentProps<"button">, "onClick" | "children" | "className">

const PaginationLink = ({
  className,
  isActive,
  disabled,
  onClick,
  children,
  ...props
}: PaginationLinkProps) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`w-10 h-10 rounded-xl flex items-center justify-center border 
              ${isActive
          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white border-transparent'
          : `${isDark ? 'bg-black/40 text-white border-purple-500/30' : 'bg-white/70 text-gray-800 border-purple-300/50'} hover:border-purple-500/50 hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]`
        }
              disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:border-purple-500/30
              disabled:hover:shadow-none transition-[background,box-shadow,opacity] duration-300 ${className}`}
      {...props}
    >
      {children}
    </button>
  )
}
PaginationLink.displayName = "PaginationLink"

const PaginationPrevious = ({
  className,
  onClick,
  disabled,
  children,
  ...props
}: React.ComponentProps<typeof PaginationLink>) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <PaginationLink
      onClick={onClick}
      disabled={disabled}
      className={`px-4 gap-1 rounded-xl ${isDark ? 'bg-black/40 text-white border-purple-500/30' : 'bg-white/70 text-gray-800 border-purple-300/50'} w-auto
              hover:border-purple-500/50 hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]
              backdrop-blur-sm transition-[background,box-shadow,opacity] duration-300 font-['Noto_Sans_SC'] ${className}`}
      {...props}
    >
      <ChevronLeft className="h-4 w-4" />
      {children}
    </PaginationLink>
  )
}
PaginationPrevious.displayName = "PaginationPrevious"

const PaginationNext = ({
  className,
  onClick,
  disabled,
  children,
  ...props
}: React.ComponentProps<typeof PaginationLink>) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <PaginationLink
      onClick={onClick}
      disabled={disabled}
      className={`px-4 gap-1 rounded-xl ${isDark ? 'bg-black/40 text-white border-purple-500/30' : 'bg-white/70 text-gray-800 border-purple-300/50'} w-auto
              hover:border-purple-500/50 hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]
              backdrop-blur-sm transition-[background,box-shadow,opacity] duration-300 font-['Noto_Sans_SC'] ${className}`}
      {...props}
    >
      {children}
      <ChevronRight className="h-4 w-4" />
    </PaginationLink>
  )
}
PaginationNext.displayName = "PaginationNext"

const PaginationEllipsis = ({
  className,
  ...props
}: React.ComponentProps<"span">) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <span
      className={`w-10 h-10 flex items-center justify-center ${isDark ? 'text-white' : 'text-gray-800'} ${className}`}
      {...props}
    >
      <MoreHorizontal className="h-4 w-4" />
      <span className="sr-only">More pages</span>
    </span>
  )
}
PaginationEllipsis.displayName = "PaginationEllipsis"

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} 