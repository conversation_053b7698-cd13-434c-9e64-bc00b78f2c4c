// TarotSpeech组件类型定义

// TarotSpeech组件的属性接口
export interface TarotSpeechProps {
  text: string; // 要朗读的文本
  className?: string; // 自定义样式
  voice?: string; // 语音角色
  sessionId?: string; // 关联的会话ID
  readerId?: string; // 占卜师ID
  messageId?: string; // 消息ID，用于缓存识别
  cacheKey?: string; // 自定义缓存键，用于区分不同板块的音频
  blockType?: string; // 板块类型，例如base/followup/deep_analysis
  isParagraph?: boolean; // 是否为段落朗读
} 