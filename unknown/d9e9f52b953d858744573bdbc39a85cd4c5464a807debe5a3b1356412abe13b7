const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { ReaderVote } = require('../models/ReaderVote');

// 确保表已创建
ReaderVote.createTable().catch(err => {
  console.error('Error creating reader votes table:', err);
});

// 批量处理点赞/取消点赞操作
router.post('/batch-votes', authenticateToken, async (req, res) => {
  try {
    const { votes } = req.body;
    const userId = req.user.userId;
    
    // 验证请求数据
    if (!Array.isArray(votes) || votes.length === 0) {
      return res.status(400).json({ 
        success: false, 
        message: '无效的请求数据，votes必须是非空数组' 
      });
    }
    
    // 按时间戳排序，保证按照操作顺序处理
    const sortedVotes = [...votes].sort((a, b) => a.timestamp - b.timestamp);
    
    // 跟踪已处理的占卜师ID，确保每个占卜师只执行最后一次操作
    const processedReaders = new Map();
    
    // 首先过滤出每个占卜师的最后一次操作
    for (const vote of sortedVotes) {
      processedReaders.set(vote.readerId, vote);
    }
    
    // 准备要处理的最终操作列表
    const finalOperations = Array.from(processedReaders.values());
    
    // 调用模型的批量处理方法
    const batchResults = await ReaderVote.batchProcessVotes(userId, finalOperations);
    
    res.json({
      success: true,
      message: '批量点赞操作已处理',
      results: {
        total: finalOperations.length,
        addedVotes: batchResults.added,
        removedVotes: batchResults.removed
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: '处理批量点赞操作时出错' 
    });
  }
});

// 添加或移除对占卜师的投票
router.post('/vote/:readerId', authenticateToken, async (req, res) => {
  try {
    const { readerId } = req.params;
    const userId = req.user.userId;

    // 检查是否已经投过票
    const hasVoted = await ReaderVote.hasUserVotedReader(userId, readerId);

    // 添加前置检查：确保请求中包含时间戳，用于防止频繁点赞
    const { timestamp } = req.body;
    if (!timestamp) {
      return res.status(400).json({ error: '请求缺少时间戳' });
    }

    // 获取上次点赞/取消点赞的时间
    const lastActionTime = req.session?.lastVoteAction?.[readerId] || 0;
    const currentTime = parseInt(timestamp);
    
    // 检查是否间隔太短（至少间隔1秒）
    if (currentTime - lastActionTime < 1000) {
      return res.status(429).json({ error: '操作太频繁，请稍后再试' });
    }
    
    // 保存本次操作时间
    if (!req.session) req.session = {};
    if (!req.session.lastVoteAction) req.session.lastVoteAction = {};
    req.session.lastVoteAction[readerId] = currentTime;

    if (hasVoted) {
      // 如果已经投过票，移除投票
      await ReaderVote.removeVote(userId, readerId);
      
      // 获取更新后的投票数
      const voteCount = await ReaderVote.getVotesByReaderId(readerId);
      res.json({ voted: false, voteCount });
    } else {
      // 修改：不再移除用户对其他占卜师的投票，允许多个点赞
      // 直接添加新的投票
      await ReaderVote.addVote(userId, readerId);
      
      // 获取更新后的投票数
      const voteCount = await ReaderVote.getVotesByReaderId(readerId);
      res.json({ voted: true, voteCount });
    }
  } catch (error) {
    console.error('Error handling reader vote:', error);
    res.status(500).json({ error: '处理投票时出错' });
  }
});

// 获取指定占卜师的投票数
router.get('/votes/:readerId', async (req, res) => {
  try {
    const { readerId } = req.params;
    const voteCount = await ReaderVote.getVotesByReaderId(readerId);
    res.json({ voteCount });
  } catch (error) {
    console.error('Error getting reader votes:', error);
    res.status(500).json({ error: '获取投票数时出错' });
  }
});

// 获取所有占卜师的投票数
router.get('/votes', async (req, res) => {
  try {
    const votes = await ReaderVote.getAllReadersVotes();
    res.json(votes);
  } catch (error) {
    console.error('Error getting all reader votes:', error);
    res.status(500).json({ error: '获取所有投票数时出错' });
  }
});

// 获取用户对指定占卜师的投票状态
router.get('/user-vote/:readerId', authenticateToken, async (req, res) => {
  try {
    const { readerId } = req.params;
    const userId = req.user.userId;
    const hasVoted = await ReaderVote.hasUserVotedReader(userId, readerId);
    res.json({ hasVoted });
  } catch (error) {
    console.error('Error checking user vote:', error);
    res.status(500).json({ error: '检查用户投票状态时出错' });
  }
});

// 获取用户所有投票过的占卜师
router.get('/user-votes', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const votedReaders = await ReaderVote.getUserVotedReader(userId);
    res.json({ votedReaders });
  } catch (error) {
    console.error('Error getting user votes:', error);
    res.status(500).json({ error: '获取用户投票时出错' });
  }
});

module.exports = router; 