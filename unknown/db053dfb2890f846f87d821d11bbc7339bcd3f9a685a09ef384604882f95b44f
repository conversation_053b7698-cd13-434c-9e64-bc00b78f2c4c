import { useRef } from 'react';
import axios from 'axios';
import { generateTarotReading } from '../services/deepseekService';
import { Message } from '../types/tarot';

// 移除段落标识文本的辅助函数，用于清除如prologue:, answer:, analysis1:等前缀
const removeSectionPrefix = (text: string): string => {
  // 匹配常见的段落前缀，如prologue:, answer:, analysis1:, summary:, advice1:等
  const prefixRegex = /^(prologue|answer|analysis\d*|summary|advice\d*|record):\s*/i;
  return text.replace(prefixRegex, '').trim();
};

interface UseTarotReadingProps {
  setShowContent: (show: boolean) => void;
  setMessages: (updater: React.SetStateAction<Message[]>) => void;
  setShouldAutoScroll: (should: boolean) => void;
  setInitialReadingCompleted: (completed: boolean) => void;
  setDeepAnalysisLoading: (loading: boolean) => void;
  setIsGeneratingDeepAnalysis: (generating: boolean) => void;
  markApiReceived: () => void;
  completeProgress: (callback?: () => void) => void;
}

export const useTarotReading = ({
  setShowContent,
  setMessages,
  setShouldAutoScroll,
  setInitialReadingCompleted,
  setDeepAnalysisLoading,
  setIsGeneratingDeepAnalysis,
  markApiReceived,
  completeProgress
}: UseTarotReadingProps) => {
  const requestInProgressRef = useRef(false);
  // 添加一个标记来记录流式处理是否完成
  const streamCompletedRef = useRef(false);

  const generateReading = async (controller: AbortController) => {
    try {
      // 确保基础解读阶段不显示深度解析进度条
      setDeepAnalysisLoading(false);
      setIsGeneratingDeepAnalysis(false);
      
      // 标记API调用正在进行中
      localStorage.setItem('apiCallInProgress', 'true');
      localStorage.setItem('apiCallLastUpdate', Date.now().toString());
      
      // 存储已接收的完整段落，避免重复添加
      const receivedParagraphs: string[] = [];
      // 标记是否已通过流式API显示内容
      let hasDisplayedContent = false;
      
      const userQuestion = localStorage.getItem('userQuestion') || '';
      
      setShowContent(true);
      setMessages([
        { type: 'user', content: userQuestion },
        { 
          type: 'reader', 
          content: '', 
          className: 'font-sans loading-message' 
        }
      ]);
      // 只在初始时自动滚动一次，显示用户问题
      setShouldAutoScroll(true);
      // 确保进度状态重置，直接调用completeProgress()来重置进度条状态
      completeProgress();
      
      // 处理流式内容
      const handleStreamContent = (content: string, isParagraphComplete?: boolean, isStreamComplete?: boolean) => {
        // 只有在塔罗解读中才会收到流式处理完成信号，深度解析不会发送第三个参数
        if (isStreamComplete) {
          streamCompletedRef.current = true;
          // 立即移除所有加载动画，不等待React状态更新
          const loadingElements = document.querySelectorAll('.loading-message');
          loadingElements.forEach(el => {
            el.classList.add('hidden');
          });
          
          // 同时通过状态更新确保动画被移除
          setMessages(prev => prev.filter(msg => !msg.className?.includes('loading-message')));
          // 设置初始解读完成，允许点击深度解析按钮
          setInitialReadingCompleted(true);
          setDeepAnalysisLoading(false);
          setIsGeneratingDeepAnalysis(false);
          
          // 确保进度完成，解锁UI
          completeProgress();
          return;
        }
        
        // 如果流式处理已完成，不再处理新内容（对塔罗解读有效）
        if (streamCompletedRef.current) {
          return;
        }
        
        // 标记已通过流式API显示内容
        if (!hasDisplayedContent) {
          hasDisplayedContent = true;
          
          // 初次接收到内容时，检查是否已存在loading-message
          setMessages(prev => {
            // 检查是否已经有loading-message
            const hasLoadingMessage = prev.some(msg => msg.className?.includes('loading-message'));
            if (!hasLoadingMessage) {
              return [...prev, {
                type: 'reader',
                content: '',
                className: 'font-sans loading-message'
              }];
            }
            return prev;
          });
        }
        
        // 检查内容是否为record部分
        if (content.toLowerCase().startsWith('record:')) {
          // 对于record部分，我们不显示，也不处理
          // 确保加载动画继续显示
          setMessages(prev => {
            // 检查是否已存在加载动画
            const hasLoadingMessage = prev.some(msg => msg.className?.includes('loading-message'));
            if (!hasLoadingMessage) {
              return [...prev, {
                type: 'reader',
                content: '',
                className: 'font-sans loading-message'
              }];
            }
            return prev;
          });
          return;
        }
        
        // 检查内容是否为高维建议部分
        if (content.toLowerCase().startsWith('advice')) {
          // 跳过高维建议段落
          return;
        } else if (isParagraphComplete) {
          // 如果不是高维建议且是完整段落，继续正常处理
          // 仅当该段落不在已接收列表中才处理
          if (!receivedParagraphs.includes(content)) {
            receivedParagraphs.push(content);
            
            // 移除段落标识文本
            const cleanedContent = removeSectionPrefix(content);
            
            // 更新消息显示，添加带读者头像的新段落
            setMessages(prev => {
              // 移除加载中的消息
              const messagesWithoutLoading = prev.filter(msg => !msg.className?.includes('loading-message'));
              
              // 只有在流式处理未完成时才添加加载动画
              if (!streamCompletedRef.current) {
                return [...messagesWithoutLoading, {
                  type: 'reader',
                  content: cleanedContent,
                  className: 'font-sans',
                  isParagraph: true
                }, {
                  type: 'reader',
                  content: '',
                  className: 'font-sans loading-message'
                }];
              } else {
                // 流式处理已完成，不再添加加载动画
                return [...messagesWithoutLoading, {
                  type: 'reader',
                  content: cleanedContent,
                  className: 'font-sans',
                  isParagraph: true
                }];
              }
            });
          } else {
            
            // 即使跳过这个段落，如果流式处理未完成，也要确保加载动画继续显示
            if (!streamCompletedRef.current) {
              setMessages(prev => {
                // 检查是否已存在加载动画
                const hasLoadingMessage = prev.some(msg => msg.className?.includes('loading-message'));
                if (!hasLoadingMessage) {
                  return [...prev, {
                    type: 'reader',
                    content: '',
                    className: 'font-sans loading-message'
                  }];
                }
                return prev;
              });
            }
          }
        } else {
          // 非完整段落内容不直接显示
          
          // 确保加载动画一直显示（如果流式处理未完成）
          if (!streamCompletedRef.current) {
            setMessages(prev => {
              // 检查是否已存在加载动画
              const hasLoadingMessage = prev.some(msg => msg.className?.includes('loading-message'));
              if (!hasLoadingMessage) {
                return [...prev, {
                  type: 'reader',
                  content: '',
                  className: 'font-sans loading-message'
                }];
              }
              return prev;
            });
          }
        }
        
        // 移除自动滚动到底部，使其与深度解析行为一致
        // setShouldAutoScroll(true);
        
        // 更新API调用的最后更新时间
        localStorage.setItem('apiCallLastUpdate', Date.now().toString());
      };

      // 使用流式API调用
      const reading = await generateTarotReading(
        userQuestion, 
        controller.signal,
        handleStreamContent
      );
      
      
      if (reading === 'REQUEST_CANCELED') {
        return;
      }

      if (reading === 'SESSION_IN_PROGRESS') {
        // 标记为初始解读已完成，这样用户可以看到当前界面而不是加载状态
        // 我们会在后续检查有无解读结果
        setInitialReadingCompleted(true);
        
        // 记录开始检查的时间
        const checkStartTime = Date.now();
        
        // 设置一个定时器，每隔1秒检查一次是否有解读结果
        const checkResultInterval = setInterval(async () => {
          try {
            // 从后端获取解读结果
            const sessionId = localStorage.getItem('sessionId');
            if (!sessionId) {
              clearInterval(checkResultInterval);
              return;
            }
            
            const response = await axios.get(`/api/session/${sessionId}/reading`);
            if (response.data && response.data.readingResult) {
              // 如果已有解读结果，清除定时器并更新状态
              clearInterval(checkResultInterval);
              
              // 保存结果到localStorage
              localStorage.setItem('readingResult', response.data.readingResult);
              localStorage.setItem('lastSessionId', sessionId);
              
              // 处理解读结果
              // 这里可以复用之前的解析逻辑
              try {
                const data = JSON.parse(response.data.readingResult);
                // 更新UI
                setMessages(() => {
                  // 为简单起见，这里只添加完整的解读内容
                  return [
                    { type: 'user', content: userQuestion },
                    { 
                      type: 'reader',
                      content: data.content || response.data.readingResult
                    }
                  ];
                });
              } catch (parseError) {
                // console.error('解析已获取的解读结果失败:', parseError);
                // 如果解析失败，至少显示原始内容
                setMessages([
                  { type: 'user', content: userQuestion },
                  { 
                    type: 'reader',
                    content: response.data.readingResult
                  }
                ]);
              }
            }
          } catch (error) {
            // console.error('获取解读结果失败:', error);
            // 如果多次失败，考虑停止检查
            if (Date.now() - checkStartTime > 30000) { // 30秒后停止检查
              clearInterval(checkResultInterval);
              setMessages(prev => {
                // 移除加载中的消息
                const withoutLoading = prev.filter(msg => !msg.className?.includes('loading-message'));
                
                return [...withoutLoading, { 
                  type: 'reader', 
                  content: '获取解读结果失败，请刷新页面重试',
                  className: 'font-sans japanese'
                }];
              });
            }
          }
        }, 1000);
        
        return () => {
          clearInterval(checkResultInterval);
        };
      }

      markApiReceived();

      // API调用成功后，设置会话标记，防止重复调用
      const sessionId = localStorage.getItem('sessionId');
      if (sessionId) {
        const sessionCallKey = `${sessionId}_api_called`;
        sessionStorage.setItem(sessionCallKey, 'true');
      }

      localStorage.setItem('readingResult', reading);
      localStorage.setItem('lastSessionId', sessionId || '');

      // 只有在流式处理尚未标记为完成时才移除加载动画
      // 避免与handleStreamContent中的isStreamComplete处理冲突
      if (!streamCompletedRef.current) {
        streamCompletedRef.current = true;
        // 立即移除所有加载动画，不依赖状态更新
        const loadingElements = document.querySelectorAll('.loading-message');
        loadingElements.forEach(el => {
          el.classList.add('hidden');
        });
        
        setMessages(prev => prev.filter(msg => !msg.className?.includes('loading-message')));
      }

      try {
        // 尝试解析JSON，如果不是JSON则直接使用文本内容
        let orderedContent: string[] = [];
        let analysisContent: string[] = [];
        
        // 直接将基础解读的响应当作纯文本处理，不再尝试解析为JSON
        if (!hasDisplayedContent) {
          // 过滤掉可能包含的record部分
          const contentWithoutRecord = reading.replace(/record:[\s\S]*?(?=\n\n\w+:|$)/gi, '').trim();
          orderedContent = [contentWithoutRecord];
        }

        // 保存初步解读的analysis部分到localStorage
        if (sessionId && analysisContent.length > 0) {
          localStorage.setItem(`${sessionId}_initial_analysis`, JSON.stringify(analysisContent));
        }

        // 直接设置读取完成状态，不使用进度条效果
        setInitialReadingCompleted(true);
        // 确保深度解析按钮可点击
        setDeepAnalysisLoading(false);
        setIsGeneratingDeepAnalysis(false);
        
        // 确保进度完成，解锁UI
        completeProgress();
        
        // 流式处理完成后，如果尚未显示内容，则添加解析后的内容
        if (!hasDisplayedContent && orderedContent.length > 0) {
          setMessages(prev => {
            const newMessages = orderedContent
              .filter(content => content.trim())
              .map(content => ({
                type: 'reader' as const,
                content: content.trim()
              }));
            
            return [...prev, ...newMessages];
          });
        }
      } catch (parseError) {
        // console.error('Error parsing reading:', parseError);
        setMessages(prev => {
          return [...prev, { 
            type: 'reader', 
            content: reading || '解析失败，请刷新页面重试',
            className: 'font-sans japanese'
          }];
        });
      }
    } catch (error) {
      // console.error('解读生成失败:', error);
      
      // 错误处理...
      setMessages(prev => {
        // 移除加载中的消息
        const filteredMessages = prev.filter(msg => !msg.className?.includes('loading-message'));
        
        return [...filteredMessages, { 
          type: 'reader', 
          content: error instanceof Error ? '解读生成失败，请稍后重试' : String(error),
          className: 'font-sans japanese'
        }];
      });
      
      // 出错时不设置会话标记，允许用户重试
    } finally {
      // 清除 API 调用状态
      localStorage.removeItem('apiCallInProgress');
      
      // 更新UI状态
      setDeepAnalysisLoading(false);
      setIsGeneratingDeepAnalysis(false);
      setInitialReadingCompleted(true);
    }
  };

  // 在组件中调用的执行函数
  const executeTarotReading = () => {
    const controller = new AbortController();
    
    // 重置请求状态
    streamCompletedRef.current = false;
    requestInProgressRef.current = false;
    
    // 检查是否是新的占卜会话
    const currentSessionId = localStorage.getItem('sessionId');
    const lastSessionId = localStorage.getItem('lastSessionId');
    const savedReading = localStorage.getItem('readingResult');
    const savedMessages = localStorage.getItem(`${currentSessionId}_messages`);
    const userQuestion = localStorage.getItem('userQuestion') || '';
    
    // 如果有用户问题，即使是新会话也显示内容
    if (userQuestion) {
      setShowContent(true);
      if (!savedMessages) {
        setMessages([{ type: 'user', content: userQuestion }]);
      }
    }
    
    // 如果不是新会话且有保存的解读结果，直接使用保存的结果
    if (currentSessionId === lastSessionId && savedReading) {
      setShowContent(true);
      
      // 恢复所有消息历史
      if (savedMessages) {
        try {
          const parsedMessages = JSON.parse(savedMessages);
          setMessages(parsedMessages);
          setInitialReadingCompleted(true);
        } catch (error) {
          // console.error('Error parsing saved messages:', error);
          // 解析失败时至少显示用户问题
          setMessages([{ type: 'user', content: userQuestion }]);
        }
      } else {
        // 如果没有保存的消息历史，至少显示用户问题
        setMessages([{ type: 'user', content: userQuestion }]);
      }
      
      return; // 直接返回，不继续执行后面的 API 调用
    }
    
    // 如果是新的会话，清除所有状态
    if (currentSessionId !== lastSessionId) {
      // 清除所有相关的 localStorage 数据
      localStorage.removeItem('apiCallInProgress');
      localStorage.removeItem('apiCallLastUpdate');
      localStorage.removeItem('readingResult');
      localStorage.removeItem('lastSessionId');
      if (currentSessionId) {
        localStorage.removeItem(`${currentSessionId}_messages`);
        localStorage.removeItem(`${currentSessionId}_analysisShown`);
      }
      
      // 对于新会话，强制重置请求状态
      requestInProgressRef.current = false;
      
      // 不要立即清除 showContent，因为我们可能需要显示用户问题
      if (userQuestion) {
        setShowContent(true);
        setMessages([{ type: 'user', content: userQuestion }]);
      }
    }

    // ===== 加强防重复调用机制 =====
    // 内存级检查: 检查全局变量
    if (!window.tarotGlobalLocks) {
      window.tarotGlobalLocks = {};
    }
    
    // 如果全局锁中已存在该会话ID，直接返回
    if (currentSessionId && window.tarotGlobalLocks[currentSessionId]) {
      return;
    }

    // 存储级检查: localStorage 标记
    const isApiCallInProgress = localStorage.getItem('apiCallInProgress');
    const lastUpdateTime = localStorage.getItem('apiCallLastUpdate');
    const currentTime = Date.now();
    
    // 检查 API 调用状态
    if (isApiCallInProgress === 'true') {
      // 如果上次更新时间超过30秒或者不存在上次更新时间，重置状态
      if (!lastUpdateTime || (currentTime - parseInt(lastUpdateTime)) > 30000) {
        localStorage.removeItem('apiCallInProgress');
        localStorage.removeItem('apiCallLastUpdate');
      } else {
        if (userQuestion) {
          return;
        }
      }
    }

    // 会话级检查: sessionStorage 标记
    const sessionCallKey = `${currentSessionId}_api_called`;
    const apiCalled = sessionStorage.getItem(sessionCallKey) === 'true';
    
    
    // 重要：如果没有已保存的解读结果，即使有sessionStorage标记也允许调用API
    // 这确保至少有一次API调用可以成功
    if (apiCalled) {
      // 仅当有已保存的解读结果时跳过API调用
      if (savedReading) {
        return;
      } else {
        // 移除错误的标记，允许API调用
        sessionStorage.removeItem(sessionCallKey);
      }
    }

    // 请求进行中检查
    if (requestInProgressRef.current) {
      return;
    }

    // 使用setTimeout增加一个微小延迟，避免潜在的重复调用
    // 这种技术可以处理由React严格模式或快速连续渲染造成的重复调用
    const timeoutId = setTimeout(() => {
      // 二次检查，避免timeout期间其他部分已发起请求
      if (requestInProgressRef.current) {
        return;
      }

      // 设置请求锁定机制
      if (currentSessionId) {
        // 确保全局锁对象存在
        if (!window.tarotGlobalLocks) {
          window.tarotGlobalLocks = {};
        }
        
        // 设置全局锁
        window.tarotGlobalLocks[currentSessionId] = true;
        // 设置本地存储
        localStorage.setItem('apiCallInProgress', 'true');
        localStorage.setItem('apiCallLastUpdate', currentTime.toString());
        // 设置请求进行中标记
        requestInProgressRef.current = true;
        
      }

      // 只有当有用户问题时才执行API调用
      if (userQuestion) {
        generateReading(controller);
      }
    }, 50);

    // 返回清理函数
    return () => {
      clearTimeout(timeoutId);
      controller.abort();
      
      // 清理全局锁
      if (currentSessionId && window.tarotGlobalLocks) {
        delete window.tarotGlobalLocks[currentSessionId];
      }
      
      // 只有在组件卸载时才清除 API 调用状态
      const currentTime = Date.now();
      const lastUpdateTime = localStorage.getItem('apiCallLastUpdate');
      if (lastUpdateTime && (currentTime - parseInt(lastUpdateTime)) > 5000) {
        localStorage.removeItem('apiCallInProgress');
        localStorage.removeItem('apiCallLastUpdate');
      }
      
      // 用户离开页面时清除sessionStorage中的API调用标记
      if (currentSessionId) {
        const sessionCallKey = `${currentSessionId}_api_called`;
        // 仅在用户真正离开到其他页面时清除
        const isLeavingPage = !window.location.pathname.includes('tarot-result');
        
        if (isLeavingPage) {
          sessionStorage.removeItem(sessionCallKey);
        }
      }
    };
  };

  return { executeTarotReading };
}; 