@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.spread-card {
  position: relative;
  overflow: hidden;
}

.spread-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  transform: scale(0);
  transition: transform 0.2s ease-out;
}

.spread-card:hover::before {
  transform: scale(1);
}

.gradient-text {
  background: linear-gradient(45deg, #a855f7, #ec4899);
  background-size: 200% 200%;
  animation: gradientBG 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.confirm-button {
  position: relative;
  overflow: hidden;
}

.confirm-button::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  transform: scale(0);
  transition: transform 0.6s ease-out;
}

.confirm-button:hover::after {
  transform: scale(1);
}

.tag {
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-feature-settings: "tnum" on, "lnum" on;
  -webkit-font-feature-settings: "tnum" on, "lnum" on;
  font-variant-numeric: tabular-nums;
  backdrop-filter: blur(4px);
}

.tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: 0.5s;
}

.tag:hover {
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.2);
}

.tag:hover::before {
  left: 100%;
}

.spread-card .tag {
  font-size: 0.95rem !important;
  padding: 0.35rem 0.85rem !important;
}

.spread-tag {
  font-size: 1.05rem !important;
  padding: 0.35rem 0.85rem !important;
}

.position-chip {
  backdrop-filter: blur(4px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.75rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-feature-settings: "tnum" on, "lnum" on;
  -webkit-font-feature-settings: "tnum" on, "lnum" on;
  font-variant-numeric: tabular-nums;
}

.position-chip:hover {
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.2);
}

.ai-recommended {
  position: relative;
}

.ai-recommended:hover {
  box-shadow: 0 0 12px rgba(168, 85, 247, 0.2);
}

.ai-recommended::after {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #a855f7, #ec4899, #a855f7);
  background-size: 200% 200%;
  animation: gradientBG 3s ease infinite;
  border-radius: 1rem;
  z-index: -1;
  opacity: 0.5;
}

.ai-badge {
  padding: 0.35rem 0.85rem;
  font-size: 0.95rem;
  background: linear-gradient(45deg, #a855f7, #ec4899);
  border-radius: 1rem;
  color: white;
  font-weight: 500;
  box-shadow: 0 0 15px rgba(168, 85, 247, 0.3);
  display: inline-flex;
  align-items: center;
  height: fit-content;
}

.spread-selection-container {
  padding-top: 0.5rem;
}

.spread-grid-container {
  width: 100%;
  height: 100%;
}

.category-selector {
  position: sticky;
  top: 64px; /* 与导航栏高度相同 */
  z-index: 40;
  padding: 1rem 0;
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.8);
}