import { BlogPost } from '../../types';

const NewZodiacArticleZhCN: BlogPost = {
  id: 'new-zodiac-article-zh-CN',
  title: '星座特质新文章（分类目录结构）',
  coverImage: '/images/blogs/tarot-spreads-beginners/tarot-spreads-beginners-cover.png',
  date: '2025-07-15',
  slug: 'new-zodiac-article',
  category: '星座特质',
  useNewUrlFormat: true, // 使用新的URL格式
  description: '这是一个存放在zodiac-traits目录下的星座特质文章示例',
  content: (
    <div className="blog-content">
      <h2>按分类存放博文</h2>
      <p>
        这篇文章演示了如何将博文按分类存放在对应的目录下。这篇文章存放在
        <code>src/data/blogs/zodiac-traits/</code>目录下，而不是直接放在
        <code>src/data/blogs/</code>目录下。
      </p>
      <p>
        这种目录结构更有利于管理大量的博文，特别是当博文数量增多时。
      </p>
      <h2>优势</h2>
      <p>
        1. 更好的代码组织结构，便于维护
      </p>
      <p>
        2. 更容易找到特定类别的文章
      </p>
      <p>
        3. 与URL结构保持一致，提高代码可读性
      </p>
    </div>
  )
};

export default NewZodiacArticleZhCN; 