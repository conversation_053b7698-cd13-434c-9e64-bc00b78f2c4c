export const isMobileDevice = (): boolean => {
  // 检查 navigator.userAgent
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
  
  // 检查 navigator.maxTouchPoints
  const hasTouchScreen = navigator.maxTouchPoints > 0;
  
  // 检查屏幕宽度
  const isNarrowScreen = window.innerWidth <= 768;
  
  return mobileRegex.test(userAgent) || (hasTouchScreen && isNarrowScreen);
};

/**
 * 获取适合移动端的繁体中文字体类
 * 确保在移动端繁体中文使用与简体中文相同的字体
 * 
 * @param language 当前语言
 * @returns 适合当前设备和语言的CSS类名
 */
export const getMobileFontClass = (language: string): string => {
  const isMobile = isMobileDevice();
  
  if (isMobile && language === 'zh-TW') {
    return 'font-sans chinese';
  }
  
  switch (language) {
    case 'en':
      return 'font-sans text-en';
    case 'ja':
      return 'font-sans japanese';
    case 'zh-CN':
    case 'zh-TW':
      return 'font-sans chinese';
    default:
      return 'font-sans';
  }
}; 