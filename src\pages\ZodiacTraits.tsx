import React, { useMemo, Suspense, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import LanguageLink from '../components/LanguageLink';
import SEO from '../components/SEO';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import { blogPostsByLang } from '../data/blogPosts';
import { getFontClass } from '../utils/fontUtils';
import { FaChevronRight } from 'react-icons/fa';
import { useTheme } from '../contexts/ThemeContext';
import { motion } from 'framer-motion';
import SpotlightCard from '../blocks/Components/SpotlightCard/SpotlightCard';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import CdnLazyImage from '../components/CdnLazyImage';
import { getSEOConfig } from '../lib/SEOConfig';

const ZodiacTraits: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const [_, setForceUpdate] = useState(0);

  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // 当语言变化时强制组件更新
  useEffect(() => {
    setForceUpdate(prev => prev + 1);
  }, [i18n.language]);
  
  // 获取当前语言的博客文章
  const currentLanguagePosts = useMemo(() => {
    // 尝试获取当前语言的文章
    const langPosts = blogPostsByLang[i18n.language] || [];
    
    // 如果当前语言没有文章，尝试回退到简体中文
    if (langPosts.length === 0 && i18n.language !== 'zh-CN') {
      return blogPostsByLang['zh-CN'] || [];
    }
    
    return langPosts;
  }, [i18n.language]);

  // 筛选星座特质类别的博客文章
  const filteredPosts = useMemo(() => {
    // 根据当前语言获取对应的类别名称
    const categoryMap: Record<string, string> = {
      'zh-CN': '星座特质',
      'zh-TW': '星座特質',
      'en': 'Zodiac Traits',
      'ja': '星座の特徴',
      'default': '星座特质'
    };
    
    const categoryName = categoryMap[i18n.language] || categoryMap['default'];
    
    return currentLanguagePosts.filter(post => 
      post.category === categoryName
    );
  }, [currentLanguagePosts, i18n.language]);

  const handleStartReading = () => {
    navigate('/horoscope');
  };

  return (
    <div className={`min-h-screen flex flex-col relative antialiased ${isDark ? 'text-white' : 'text-gray-800'}`}>
      <SEO />
      <LandingBackground />
      
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
            <h1 className={`main-title mb-1 sm:mb-2 ${getFontClass(i18n.language)}`}>
              {t('blog.zodiac_traits.heading', '星座特质')}
            </h1>
            <p className={`text-base sm:text-lg ${isDark ? 'text-purple-300' : 'text-purple-600'} italic ${getFontClass(i18n.language)}`}>
              {t('blog.zodiac_traits.subheading', '探索十二星座的性格特点和行为模式')}
            </p>
          </div>

          {/* 博客文章列表 */}
          {filteredPosts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
              {filteredPosts.map(post => (
                <LanguageLink to={post.useNewUrlFormat ? `/zodiac-traits/${post.slug}` : `/blog/${post.slug}`} key={post.id}>
                  <div className={`rounded-2xl overflow-hidden ${
                    isDark 
                      ? 'bg-gray-800/30 backdrop-blur-sm border border-white/10 hover:border-purple-500/50 hover:shadow-purple-500/20' 
                      : 'bg-white/80 backdrop-blur-sm border border-gray-200 hover:border-purple-400/50 hover:shadow-purple-300/20'
                  } transition-all duration-300 h-full flex flex-col shadow-lg`}>
                    {/* 文章封面图 */}
                    <div className="aspect-[16/9] overflow-hidden relative">
                      <CdnLazyImage 
                        src={post.coverImage} 
                        alt={`Blog cover - ${post.title}`} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    {/* 文章内容 */}
                    <div className="p-6 flex-1 flex flex-col">
                      <div className="flex justify-between items-center mb-2">
                        <div className={`${isDark ? 'text-gray-400' : 'text-gray-500'} text-sm`}>{post.date}</div>
                      </div>
                      <h2 className={`text-xl font-bold ${isDark ? 'text-white' : 'text-gray-800'} mb-2 line-clamp-2`}>
                        {post.title}
                      </h2>
                      <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'} text-sm line-clamp-3 mb-4 flex-1`}>
                        {(() => {
                          // 优先使用文章中定义的description
                          if (post.description) {
                            return post.description;
                          }
                          // 回退到从SEOConfig获取
                          const seoConfig = getSEOConfig(`/blog/${post.slug}`, i18n);
                          return seoConfig.description;
                        })()}
                      </p>
                      <div className="mt-auto">
                        <div className="inline-flex items-center text-purple-500 text-sm font-medium hover:text-purple-400 group">
                          {t('blog.read_more', '阅读更多')}
                          <FaChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </div>
                  </div>
                </LanguageLink>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <h2 className={`text-xl ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                {t('blog.no_posts', '暂无相关文章')}
              </h2>
            </div>
          )}
          
          {/* 添加星座探索区域 */}
          <Suspense
            fallback={
              <div className="w-full h-[200px] bg-gray-900 rounded-lg animate-pulse" />
            }
          >
            <div className="spotlight-section py-24 md:py-32 mt-16">
              <div className="max-w-3xl mx-auto px-2 sm:px-4">
                <SpotlightCard
                  className="custom-spotlight-card"
                  spotlightColor="rgba(59, 130, 246, 0.2)"
                >
                  <div className="p-4 sm:p-8 text-center">
                    <h3
                      className="text-2xl md:text-3xl font-semibold mb-4"
                      style={{
                        background: theme === 'light' 
                          ? "none" 
                          : "linear-gradient(135deg, #93C5FD, #C7D2FE, #DDD6FE)",
                        WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                        WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                        color: theme === 'light' ? "#000" : "inherit"
                      }}
                    >
                      {t("blog.zodiac_traits.cta_title", "探索你的星座运势")}
                    </h3>
                    <p className={`${
                      theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                    } text-lg md:text-xl mb-6 px-1`}>
                      {t("blog.zodiac_traits.cta_description", "查看今日、本周和本月的星座运势，了解你的爱情、事业和健康")}
                    </p>
                    <div className="flex justify-center">
                      <motion.button
                        onClick={handleStartReading}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-3 rounded-full"
                        style={{
                          background:
                            theme === 'light'
                              ? "linear-gradient(135deg, rgba(59, 130, 246, 0.7), rgba(79, 70, 229, 0.7))"
                              : "linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(79, 70, 229, 0.8))",
                          boxShadow: theme === 'light' 
                            ? "0 0 20px rgba(59, 130, 246, 0.4)"
                            : "0 0 20px rgba(59, 130, 246, 0.5)",
                          color: 'white',
                        }}
                      >
                        {t("blog.zodiac_traits.cta_button", "查看星座运势")}
                      </motion.button>
                    </div>
                  </div>
                </SpotlightCard>
              </div>
            </div>
          </Suspense>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default ZodiacTraits; 