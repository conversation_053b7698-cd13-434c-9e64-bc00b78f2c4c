import React, { useState, useEffect } from 'react';
import { getImageUrl } from '../utils/cdnImageUrl';

interface CdnImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  fallbackSrc?: string;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * CDN图片组件
 * 自动处理CDN URL和本地资源的切换
 * 支持加载失败时显示备用图片
 */
const CdnImage: React.FC<CdnImageProps> = ({
  src,
  alt,
  fallbackSrc,
  onLoad,
  onError,
  ...props
}) => {
  // 处理图片URL
  const processedSrc = getImageUrl(src);
  const processedFallbackSrc = fallbackSrc ? getImageUrl(fallbackSrc) : undefined;
  
  // 状态管理
  const [imgSrc, setImgSrc] = useState<string>(processedSrc);
  const [hasError, setHasError] = useState<boolean>(false);

  // 当src变化时更新状态
  useEffect(() => {
    setImgSrc(processedSrc);
    setHasError(false);
  }, [processedSrc]);

  // 处理图片加载错误
  const handleError = () => {
    if (!hasError && processedFallbackSrc) {
      setImgSrc(processedFallbackSrc);
      setHasError(true);
    }
    
    if (onError) {
      onError();
    }
  };

  // 处理图片加载完成
  const handleLoad = () => {
    if (onLoad) {
      onLoad();
    }
  };

  return (
    <img
      src={imgSrc}
      alt={alt}
      onError={handleError}
      onLoad={handleLoad}
      {...props}
    />
  );
};

export default CdnImage; 