import React, { useMemo, Suspense, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import LanguageLink from '../components/LanguageLink';
import SEO from '../components/SEO';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import { blogPostsByLang } from '../data/blogPosts';
import { getFontClass } from '../utils/fontUtils';
import { FaChevronRight } from 'react-icons/fa';
import { useTheme } from '../contexts/ThemeContext';
import { motion } from 'framer-motion';
import SpotlightCard from '../blocks/Components/SpotlightCard/SpotlightCard';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import CdnLazyImage from '../components/CdnLazyImage';
import { getSEOConfig } from '../lib/SEOConfig';

const Blog: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const [_, setForceUpdate] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // 当语言变化时强制组件更新
  useEffect(() => {
    setForceUpdate(prev => prev + 1);
  }, [i18n.language]);
  
  // 获取当前语言的博客文章
  const currentLanguagePosts = useMemo(() => {
    // 尝试获取当前语言的文章
    const langPosts = blogPostsByLang[i18n.language] || [];
    
    // 如果当前语言没有文章，尝试回退到简体中文
    if (langPosts.length === 0 && i18n.language !== 'zh-CN') {
      return blogPostsByLang['zh-CN'] || [];
    }
    
    return langPosts;
  }, [i18n.language]);

  // 获取所有可用的类别
  const availableCategories = useMemo(() => {
    const categories = new Set<string>();
    
    // 从博客文章中提取所有类别
    currentLanguagePosts.forEach(post => {
      if (post.category) {
        categories.add(post.category);
      }
    });
    
    // 添加"星座运势"作为固定分类
    categories.add(t('blog.categories.horoscope', '星座运势'));
    
    // 将类别转换为数组
    const categoriesArray = Array.from(categories);
    
    // 将"星座运势"移到数组的首位
    const horoscopeCategory = t('blog.categories.horoscope', '星座运势');
    const filteredCategories = categoriesArray.filter(cat => cat !== horoscopeCategory);
    
    // 返回重新排序的类别数组，"星座运势"在首位
    return [horoscopeCategory, ...filteredCategories];
  }, [currentLanguagePosts, t]);
  
  // 初始化选中的类别
  useEffect(() => {
    if (availableCategories.length > 0 && !availableCategories.includes(selectedCategory)) {
      setSelectedCategory(availableCategories[0]);
    }
  }, [availableCategories, selectedCategory, t]);
  
  // 处理类别选择
  const handleCategorySelect = (category: string) => {
    // 如果选择的是"星座运势"，导航到星座页面
    if (category === t('blog.categories.horoscope', '星座运势')) {
      navigate(`/${i18n.language}/horoscope`);
      return;
    }
    
    setSelectedCategory(category);
  };
  
  // 筛选博客文章
  const filteredPosts = useMemo(() => {
    // 如果选中的是"星座运势"，但当前没有这个分类的文章，则返回空数组
    if (selectedCategory === t('blog.categories.horoscope', '星座运势')) {
      return currentLanguagePosts.filter(post => 
        post.category === selectedCategory
      );
    }
    
    return currentLanguagePosts.filter(post => 
      !selectedCategory || post.category === selectedCategory
    );
  }, [currentLanguagePosts, selectedCategory, t]);

  const handleStartReading = () => {
    navigate('/');
  };

  return (
    <div className={`min-h-screen flex flex-col relative antialiased ${isDark ? 'text-white' : 'text-gray-800'}`}>
      <SEO />
      <LandingBackground />
      
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
            <h1 className={`main-title mb-1 sm:mb-2 ${getFontClass(i18n.language)}`}>
              {t('nav.blog', '博客')}
            </h1>
            <p className={`text-base sm:text-lg ${isDark ? 'text-purple-300' : 'text-purple-600'} italic ${getFontClass(i18n.language)}`}>
              {t('blog.description', '开启塔罗学习之旅，掌握简单实用的指南与技巧')}
            </p>
          </div>

          {/* 分类导航 - 使用与Spreads.tsx相同的样式 */}
          <div className="py-1 px-4 mb-4 sm:mb-6">
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:flex lg:flex-nowrap justify-center gap-2 md:gap-3">
              {availableCategories.map((category) => (
                <button
                  key={category}
                  className={`w-full sm:min-w-[140px] lg:w-[180px] h-[44px] flex items-center justify-center whitespace-normal text-center px-3 md:px-4 rounded-full text-sm font-medium transition-colors duration-200 font-['Inter'] ${
                    selectedCategory === category
                    ? 'bg-purple-600 text-white'
                    : theme === 'dark'
                      ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                      : 'bg-gray-200 text-white hover:bg-gray-300'
                  }`}
                  onClick={() => handleCategorySelect(category)}
                >
                  <span className="line-clamp-2" style={{color: selectedCategory === category || theme === 'dark' ? 'white' : '#4B5563'}}>
                    {category}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* 星座运势部分已移至独立页面 */}

          {/* 博客文章列表 */}
          {filteredPosts.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
              {filteredPosts.map(post => (
                <LanguageLink to={`/blog/${post.slug}`} key={post.id}>
                  <div className={`rounded-2xl overflow-hidden ${
                    isDark 
                      ? 'bg-gray-800/30 backdrop-blur-sm border border-white/10 hover:border-purple-500/50 hover:shadow-purple-500/20' 
                      : 'bg-white/80 backdrop-blur-sm border border-gray-200 hover:border-purple-400/50 hover:shadow-purple-300/20'
                  } transition-all duration-300 h-full flex flex-col shadow-lg`}>
                    {/* 文章封面图 */}
                    <div className="aspect-[16/9] overflow-hidden relative">
                      <CdnLazyImage 
                        src={post.coverImage} 
                        alt={`Blog cover - ${post.title}`} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    {/* 文章内容 */}
                    <div className="p-6 flex-1 flex flex-col">
                      <div className="flex justify-between items-center mb-2">
                        <div className={`${isDark ? 'text-gray-400' : 'text-gray-500'} text-sm`}>{post.date}</div>
                      </div>
                      <h2 className={`text-xl font-bold ${isDark ? 'text-white' : 'text-gray-800'} mb-2 line-clamp-2`}>
                        {post.title}
                      </h2>
                      <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'} text-sm line-clamp-3 mb-4 flex-1`}>
                        {(() => {
                          // 优先使用文章中定义的description
                          if (post.description) {
                            return post.description;
                          }
                          // 回退到从SEOConfig获取
                          const seoConfig = getSEOConfig(`/blog/${post.slug}`, i18n);
                          return seoConfig.description;
                        })()}
                      </p>
                      <div className="mt-auto">
                        <div className="inline-flex items-center text-purple-500 text-sm font-medium hover:text-purple-400 group">
                          {t('blog.read_more', '阅读更多')}
                          <FaChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </div>
                  </div>
                </LanguageLink>
              ))}
            </div>
          )}
          
          {/* 添加探索区域 */}
          <Suspense
            fallback={
              <div className="w-full h-[200px] bg-gray-900 rounded-lg animate-pulse" />
            }
          >
            <div className="spotlight-section py-24 md:py-32 mt-16">
              <div className="max-w-3xl mx-auto px-2 sm:px-4">
                <SpotlightCard
                  className="custom-spotlight-card"
                  spotlightColor="rgba(0, 229, 255, 0.2)"
                >
                  <div className="p-4 sm:p-8 text-center">
                    <h3
                      className="text-2xl md:text-3xl font-semibold mb-4"
                      style={{
                        background: theme === 'light' 
                          ? "none" 
                          : "linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)",
                        WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                        WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                        color: theme === 'light' ? "#000" : "inherit"
                      }}
                    >
                      {t("home.explore_section.title", "探索塔罗牌阅读")}
                    </h3>
                    <p className={`${
                      theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                    } text-lg md:text-xl mb-6 px-1`}>
                      {t("home.explore_section.description", "开始您的塔罗之旅，获取专属于您的塔罗牌阅读")}
                    </p>
                    <div className="flex justify-center">
                      <motion.button
                        onClick={handleStartReading}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-3 rounded-full"
                        style={{
                          background:
                            theme === 'light'
                              ? "linear-gradient(135deg, rgba(124, 58, 237, 0.7), rgba(168, 85, 247, 0.7))"
                              : "linear-gradient(135deg, rgba(124, 58, 237, 0.8), rgba(168, 85, 247, 0.8))",
                          boxShadow: theme === 'light' 
                            ? "0 0 20px rgba(168, 85, 247, 0.4)"
                            : "0 0 20px rgba(168, 85, 247, 0.5)",
                          color: 'white',
                        }}
                      >
                        {t("home.explore_section.button", "开始阅读")}
                      </motion.button>
                    </div>
                  </div>
                </SpotlightCard>
              </div>
            </div>
          </Suspense>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Blog; 