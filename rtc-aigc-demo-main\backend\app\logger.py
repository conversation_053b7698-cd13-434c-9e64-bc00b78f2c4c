import os
import logging
from logging.handlers import RotatingFileHandler
import time
import sys

# 日志目录
LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')

# 确保日志目录存在
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

# 日志格式
LOG_FORMAT = '%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# 日志级别
DEFAULT_LEVEL = logging.INFO


def setup_logger(name, log_file=None, level=DEFAULT_LEVEL):
    """设置日志记录器
    
    Args:
        name: 记录器名称
        log_file: 日志文件名
        level: 日志级别
        
    Returns:
        logger: 日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 清除现有处理程序
    if logger.handlers:
        logger.handlers = []
    
    # 创建格式化器
    formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
    
    # 添加控制台处理程序
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 添加文件处理程序（如果指定了日志文件）
    if log_file:
        file_path = os.path.join(LOG_DIR, log_file)
        file_handler = RotatingFileHandler(
            file_path, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


# 创建默认记录器
app_logger = setup_logger('app', 'app.log')
api_logger = setup_logger('api', 'api.log')
dialog_logger = setup_logger('dialog', 'dialog.log')
ws_logger = setup_logger('websocket', 'websocket.log')


class LogTimer:
    """计时器，用于记录操作耗时"""
    
    def __init__(self, logger, operation_name):
        self.logger = logger
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.debug(f"{self.operation_name} 开始")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = time.time()
        duration = end_time - self.start_time
        
        if exc_type:
            self.logger.error(f"{self.operation_name} 失败，耗时: {duration:.3f}秒，错误: {exc_val}")
        else:
            self.logger.debug(f"{self.operation_name} 完成，耗时: {duration:.3f}秒") 