import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { getSEOConfig } from '../lib/SEOConfig';
import { availableLanguages } from '../i18n';
import {
  getWebsiteStructuredData,
  getOrganizationStructuredData,
  getTarotServiceStructuredData,
  getTarotSpreadStructuredData,
  getTarotReadingResultStructuredData,
  getHomePageStructuredData,
  getSpreadPageStructuredData,
  getDailyFortunePageStructuredData,
  getYearlyFortunePageStructuredData,
  getTarotGalleryPageStructuredData,
  getPrivacyPolicyPageStructuredData,
  getTermsPageStructuredData,
  getPersonalityTestPageStructuredData,
  getDoesHeLikeMePageStructuredData,
  getYesNoTarotPageStructuredData
} from '../lib/structuredData';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  canonical?: string;
  ogType?: 'website' | 'article';
  noindex?: boolean;
  // 新增的塔罗牌相关属性
  spreadName?: string;
  spreadDescription?: string;
  question?: string;
  answer?: string;
  readingDate?: string;
  readerName?: string;
  readerDescription?: string;
  readerImage?: string;
}

/**
 * SEO组件用于设置页面的meta标签
 * 
 * 可以通过props传入特定的SEO配置来覆盖基于路由的默认配置
 */
const SEO: React.FC<SEOProps> = (props) => {
  const location = useLocation();
  const { i18n } = useTranslation();
  
  // 改进路径处理逻辑
  let pathname = location.pathname;
  
  // 1. 检查是否为语言路径模式（如 /zh-CN/home）
  const langPathMatch = pathname.match(/^\/([a-z]{2}(-[A-Z]{2})?)(\/.+)?$/);
  if (langPathMatch && availableLanguages.includes(langPathMatch[1])) {
    // 如果是语言路径，提取路径部分或设为根路径
    pathname = langPathMatch[3] || '/';
  }
  
  // 2. 对于不在语言路由中的静态路由，直接使用完整路径
  // 这确保 /terms, /register 等路径能被正确处理
  
  // 从配置中获取当前路由的SEO信息
  const defaultConfig = getSEOConfig(pathname, i18n);
  
  // 合并默认配置和props传入的配置
  const {
    title = defaultConfig.title,
    description = defaultConfig.description,
    keywords = defaultConfig.keywords,
    // ogImage = defaultConfig.ogImage || 'https://cdn.tarotqa.com/images-optimized/favicon_small.webp',
    // ogType = defaultConfig.ogType || 'website',
    noindex = false,
    // 解构新增的塔罗牌相关属性
    spreadName,
    spreadDescription,
    question,
    answer,
    readingDate
  } = props;

  // // 获取语言特定的Open Graph locale
  // const getOgLocale = () => {
  //   switch(i18n.language) {
  //     case 'zh-CN': return 'zh_CN';
  //     case 'zh-TW': return 'zh_TW';
  //     case 'ja': return 'ja_JP';
  //     default: return 'en_US';
  //   }
  // };

  // 基础结构化数据
  const websiteData = getWebsiteStructuredData(i18n.language);
  const organizationData = getOrganizationStructuredData(i18n.language);
  const serviceData = getTarotServiceStructuredData(i18n.language);

  // 根据页面类型生成特定的结构化数据
  const getPageSpecificStructuredData = () => {
    const pathname = location.pathname;
    
    // 提取不考虑语言前缀的路径部分
    let normalizedPath = pathname;
    const langPathMatch = pathname.match(/^\/([a-z]{2}(-[A-Z]{2})?)(\/.+)?$/);
    if (langPathMatch && availableLanguages.includes(langPathMatch[1])) {
      normalizedPath = langPathMatch[3] || '/';
    }
    
    // 检查是否为首页（根路径或语言根路径）
    const isHomePage = normalizedPath === '/' || normalizedPath === '/home';
    
    if (isHomePage) {
      return [getHomePageStructuredData(i18n.language), organizationData];
    }
    
    // 人格测试博客页面
    if (normalizedPath === '/blog/personality-test-guide') {
      return [getPersonalityTestPageStructuredData(i18n.language), websiteData];
    }
    
    // "他喜欢我吗"博客页面
    if (normalizedPath === '/blog/does-he-like-me-guide') {
      return [getDoesHeLikeMePageStructuredData(i18n.language), websiteData];
    }
    
    // "是否塔罗"博客页面
    if (normalizedPath === '/blog/yes-no-tarot-guide') {
      return [getYesNoTarotPageStructuredData(i18n.language), websiteData];
    }
    
    // 塔罗牌阵页面
    if (normalizedPath === '/spreads') {
      return [getSpreadPageStructuredData(i18n.language)];
    }
    
    // 每日运势页面
    if (normalizedPath === '/daily-fortune') {
      return [getDailyFortunePageStructuredData(i18n.language)];
    }
    
    // 每日运势结果页面
    if (normalizedPath === '/daily-fortune-result') {
      return [getDailyFortunePageStructuredData(i18n.language)]; // 使用同样的结构化数据
    }
    
    // 年度运势页面
    if (normalizedPath === '/yearly-fortune') {
      return [getYearlyFortunePageStructuredData(i18n.language)];
    }
    
    // 年度运势结果页面
    if (normalizedPath === '/yearly-fortune-result') {
      return [getYearlyFortunePageStructuredData(i18n.language)]; // 使用同样的结构化数据
    }
    
    // 塔罗牌库页面
    if (normalizedPath === '/gallery') {
      return [getTarotGalleryPageStructuredData(i18n.language)];
    }
    
    // 塔罗牌阵选择页面
    if (normalizedPath === '/reading/spread') {
      if (spreadName && spreadDescription) {
        // 如果有特定牌阵信息，使用牌阵结构化数据
        return [
          websiteData,
          getTarotSpreadStructuredData(spreadName, spreadDescription, i18n.language)
        ];
      }
      return [getSpreadPageStructuredData(i18n.language)];
    }
    
    // 塔罗牌占卜结果页面
    if (normalizedPath === '/tarot-result') {
      if (question && answer && readingDate) {
        return [
          websiteData,
          getTarotReadingResultStructuredData(question, answer, readingDate, i18n.language)
        ];
      }
      return [websiteData, serviceData];
    }
    
    // 隐私政策页面
    if (normalizedPath === '/privacy') {
      return [getPrivacyPolicyPageStructuredData(i18n.language)];
    }
    
    // 服务条款页面
    if (normalizedPath === '/terms') {
      return [getTermsPageStructuredData(i18n.language)];
    }
    
    // 默认返回网站和服务数据
    return [websiteData, serviceData, organizationData];
  };

  const structuredDataArray = getPageSpecificStructuredData();

// 为多语言版本生成规范链接和hreflang标签
const getLocalizedUrls = () => {
  // 检查当前路径格式
  const pathMatch = location.pathname.match(/^\/([^\/]+)(\/.*)?$/);
  const currentLang = pathMatch?.[1];
  const pathWithoutLang = pathMatch?.[2] || '';
  
  // 处理简化的语言代码
  const normalizedCurrentLang = currentLang === 'zh' ? 'zh-CN' : currentLang;
  
  // 检查是否在语言路由系统中（如 /zh-CN, /en, /zh）
  const isInLanguageSystem = availableLanguages.includes(normalizedCurrentLang || '') || normalizedCurrentLang === 'zh-CN';
  
  // 移除URL末尾的斜杠
  const normalizeUrl = (url: string) => {
    return url.endsWith('/') && url !== 'https://tarotqa.com/' ? url.slice(0, -1) : url;
  };
  
  // 生成完整的hreflang URL集合的辅助函数
  const generateHreflangUrls = (basePath = '') => {
    return [
      // x-default：指向zh-TW版本（根URL）
      { lang: 'x-default', url: normalizeUrl(`https://tarotqa.com${basePath}`) },
      // 所有语言版本
      { lang: 'zh-TW', url: normalizeUrl(`https://tarotqa.com${basePath}`) },
      { lang: 'zh-CN', url: normalizeUrl(`https://tarotqa.com/zh-CN${basePath}`) },
      { lang: 'en', url: normalizeUrl(`https://tarotqa.com/en${basePath}`) },
      { lang: 'ja', url: normalizeUrl(`https://tarotqa.com/ja${basePath}`) }
    ];
  };
  
  // 对于不带语言参数的页面，canonical指向自己
  if (!isInLanguageSystem) {
    return {
      canonical: normalizeUrl(`https://tarotqa.com${location.pathname}`),
      alternateUrls: generateHreflangUrls(location.pathname),
      isLanguageRoot: location.pathname === '/'
    };
  }
  
  // 对于zh-TW语言的页面，canonical指向根URL（不带语言参数的对应页面）
  if (normalizedCurrentLang === 'zh-TW') {
    // 如果是语言根路径（如 /zh-TW）
    if (!pathWithoutLang) {
      return {
        canonical: 'https://tarotqa.com',
        alternateUrls: generateHreflangUrls(''),
        isLanguageRoot: true
      };
    }
    
    // 对于zh-TW子页面（如 /zh-TW/home），canonical指向不带语言参数的对应页面
    return {
      canonical: normalizeUrl(`https://tarotqa.com${pathWithoutLang}`),
      alternateUrls: generateHreflangUrls(pathWithoutLang),
      isLanguageRoot: false
    };
  }
  
  // 对于其他语言的页面，canonical指向自己
  // 使用规范化的语言代码
  const canonicalLang = normalizedCurrentLang || i18n.language;
  
  // 如果是语言根路径（如 /zh-CN, /en, /ja）
  if (!pathWithoutLang) {
    return {
      canonical: normalizeUrl(`https://tarotqa.com/${canonicalLang}`),
      alternateUrls: generateHreflangUrls(''),
      isLanguageRoot: true
    };
  }
  
  // 对于语言路由中的子页面（如 /zh-CN/home, /en/reading/reader）
  return {
    canonical: normalizeUrl(`https://tarotqa.com/${canonicalLang}${pathWithoutLang}`),
    alternateUrls: generateHreflangUrls(pathWithoutLang),
    isLanguageRoot: false
  };
};  

  const { canonical: localizedCanonical, alternateUrls, } = getLocalizedUrls();
  // 使用本地化的canonical URL覆盖默认值
  const finalCanonical = props.canonical || localizedCanonical;

  return (
    <Helmet>
      {/* 基本meta标签 */}
      <title>{title}</title>
      <meta name="description" content={description} data-react-helmet="true" />
      {keywords && <meta name="keywords" content={keywords} data-react-helmet="true" />}
      
      {/* Open Graph标签
      <meta property="og:title" content={title} data-react-helmet="true" />
      <meta property="og:description" content={description} data-react-helmet="true" />
      <meta property="og:type" content={ogType} data-react-helmet="true" />
      <meta property="og:url" content={finalCanonical} data-react-helmet="true" />
      <meta property="og:image" content={ogImage} data-react-helmet="true" />
      <meta property="og:site_name" content="TarotQA" data-react-helmet="true" />
      <meta property="og:locale" content={getOgLocale()} /> */}
      
      {/* 添加替代语言版本 */}
      {i18n.language !== 'en' && <meta property="og:locale:alternate" content="en_US" />}
      {i18n.language !== 'zh-CN' && <meta property="og:locale:alternate" content="zh_CN" />}
      {i18n.language !== 'ja' && <meta property="og:locale:alternate" content="ja_JP" />}
      {i18n.language !== 'zh-TW' && <meta property="og:locale:alternate" content="zh_TW" />}
      
      {/* Twitter Card标签
      <meta name="twitter:card" content="summary_large_image" data-react-helmet="true" />
      <meta name="twitter:title" content={title} data-react-helmet="true" />
      <meta name="twitter:description" content={description} data-react-helmet="true" />
      <meta name="twitter:image" content={ogImage} data-react-helmet="true" /> */}
      
      {/* 规范链接 */}
      <link rel="canonical" href={finalCanonical} />
      
      {/* 语言标签 */}
      <html lang={i18n.language} />
      
      {/* 索引控制 */}
      {noindex && <meta name="robots" content="noindex, nofollow" />}

      {/* hreflang标签 - 为多语言版本添加替代链接 */}
      {alternateUrls.map(({ lang, url }) => (
        <link key={lang} rel="alternate" hrefLang={lang} href={url} />
      ))}

      {/* 结构化数据 */}
      {structuredDataArray.map((data, index) => (
        <script key={index} type="application/ld+json">
          {JSON.stringify(data)}
        </script>
      ))}
    </Helmet>
  );
};

export default SEO; 