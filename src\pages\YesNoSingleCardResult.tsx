import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '../contexts/UserContext';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import SEO from '../components/SEO';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { useSearchParams } from 'react-router-dom';
import LoginPrompt from '../components/LoginPrompt';
import VipPromptDialog from '../components/VipPromptDialog';
import { TAROT_CARDS } from '../data/tarot-cards';
import { useTarotProgress } from '../hooks/useTarotProgress';
import axiosInstance from '../utils/axios';
import { FortuneReadingModal } from '../components/daily-fortune';
import { getFontClass as getGlobalFontClass } from '../utils/fontUtils';
import ShareDialog from '../components/ShareDialog';
import { checkRewardStatus } from '../services/shareService';
import CommentSection from '../components/CommentSection';
import CommentDialog from '../components/CommentDialog';

// 导入组件
import TarotCardDisplay from '../components/yes-no-tarot/TarotCardDisplay';
import ReadingResult from '../components/yes-no-tarot/ReadingResult';
import SingleCardHowItWords from '../components/yes-no-tarot/SingleCardHowItWords';
import MoreTarotOptions from '../components/yes-no-tarot/MoreTarotOptions';
import SpotlightSection from '../components/yes-no-tarot/SingleCardSpotlightSection';
import SingleCardBenefits from '../components/yes-no-tarot/SingleCardBenefits';
import SingleCardBestQuestions from '../components/yes-no-tarot/SingleCardBestQuestions';
import SingleCardUnderstanding from '../components/yes-no-tarot/SingleCardUnderstanding';

const YesNoSingleCardResult: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { user, refreshUser } = useUser();
  const { navigate } = useLanguageNavigate();
  const [searchParams] = useSearchParams();
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  const [showFortuneModal, setShowFortuneModal] = useState(false);
  
  // 进度条相关状态和hooks
  const {
    progress,
    startProgress,
    markApiReceived,
    completeProgress
  } = useTarotProgress({
    duration: 30000, // 30秒
    onComplete: () => {
      // 进度条完成后的回调
    }
  });
  const [readingResult, setReadingResult] = useState<any>(null);
  const [, setIsGeneratingReading] = useState(false);

  // 用户问题状态
  const [userQuestion, setUserQuestion] = useState('');
  
  // 安全检测相关状态
  const [safetyIssue, setSafetyIssue] = useState(false);
  const [safetyMessage, setSafetyMessage] = useState('');
  
  // 卡牌相关状态
  const [cardBackImage] = useState<string>("https://cdn.tarotqa.com/public/home-images-001-sm.webp");
  const [flipped, setFlipped] = useState(false);
  const [processingCard, setProcessingCard] = useState(false);
  const [cardImage, setCardImage] = useState<string | null>(null);
  const [selectedCard, setSelectedCard] = useState<number | null>(null);
  const [cardOrientation, setCardOrientation] = useState(false); // false为正位，true为逆位

  // 分享相关状态
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [hasReceivedShareReward, setHasReceivedShareReward] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isSharedView, setIsSharedView] = useState(false);

  // 评论相关状态
  const [showCommentDialog, setShowCommentDialog] = useState(false);

  // 创建适配器函数，将带参数的getFontClass转换为不带参数的版本
  const getFontClass = useCallback(() => {
    return getGlobalFontClass(i18n.language);
  }, [i18n.language]);

  // 检查是否有用户问题，如果没有则跳转（除非是分享访问）
  useEffect(() => {
    const savedQuestion = localStorage.getItem('userQuestion');
    const isSharedViewParam = searchParams.get('shared') === 'true';
    const sharedSessionId = searchParams.get('sessionId');

    // 设置分享视图状态
    setIsSharedView(isSharedViewParam);

    // 如果不是分享访问且没有用户问题，则跳转回单卡页面
    if (!savedQuestion && !isSharedViewParam) {
      navigate('/yes-no-tarot/single-card');
    }

    // 如果是分享访问，设置sessionId
    if (isSharedViewParam && sharedSessionId) {
      setSessionId(sharedSessionId);
      localStorage.setItem('sessionId', sharedSessionId);
    }
  }, [navigate, searchParams]);

  // 从localStorage获取用户问题和解读结果，或从分享链接加载数据
  useEffect(() => {
    const isSharedView = searchParams.get('shared') === 'true';
    const sharedSessionId = searchParams.get('sessionId');

    if (isSharedView && sharedSessionId) {
      // 分享访问：从后端获取数据
      loadSharedReading(sharedSessionId);
    } else {
      // 正常访问：从localStorage恢复数据
      const savedQuestion = localStorage.getItem('userQuestion');
      if (!savedQuestion) {
        return;
      }

      setUserQuestion(savedQuestion);

      // 尝试恢复sessionId
      const savedSessionId = localStorage.getItem('sessionId');
      if (savedSessionId) {
        setSessionId(savedSessionId);
      }

      // 尝试恢复解读结果
      const savedReadingResult = localStorage.getItem('singleCardReadingResult');
      const savedSelectedCard = localStorage.getItem('singleCardSelectedCard');
      const savedCardOrientation = localStorage.getItem('singleCardOrientation');
      const savedSafetyIssue = localStorage.getItem('singleCardSafetyIssue');
      const savedSafetyMessage = localStorage.getItem('singleCardSafetyMessage');

      if (savedReadingResult && savedSelectedCard !== null && savedSelectedCard !== 'null') {
        try {
          // 恢复解读结果
          setReadingResult(JSON.parse(savedReadingResult));
          setSelectedCard(parseInt(savedSelectedCard));
          setCardOrientation(savedCardOrientation === 'true');
          setFlipped(true);

          // 设置卡牌图片
          const cardIndex = parseInt(savedSelectedCard);
          if (cardIndex >= 0 && cardIndex < TAROT_CARDS.length) {
            setCardImage(`/images-optimized/tarot/${TAROT_CARDS[cardIndex].nameEn}.webp`);
          }

          // 恢复安全检测结果
          if (savedSafetyIssue === 'true') {
            setSafetyIssue(true);
            setSafetyMessage(savedSafetyMessage || '');
          }
        } catch (error) {
          // 清理损坏的数据
          localStorage.removeItem('singleCardReadingResult');
          localStorage.removeItem('singleCardSelectedCard');
          localStorage.removeItem('singleCardOrientation');
          localStorage.removeItem('singleCardSafetyIssue');
          localStorage.removeItem('singleCardSafetyMessage');
        }
      }
    }
  }, [searchParams]); // 依赖searchParams，当URL参数变化时重新执行

  // 检查用户是否已经获得过分享奖励
  useEffect(() => {
    if (user) {
      checkRewardStatus()
        .then(hasReceived => {
          setHasReceivedShareReward(hasReceived);
        })
        .catch(error => {
          console.log('获取奖励状态失败:', error);
        });
    }
  }, [user]);

  // 从后端加载分享的解读数据
  const loadSharedReading = async (sharedSessionId: string) => {
    try {
      const response = await axiosInstance.get(`/api/reading/shared/${sharedSessionId}`);

      if (response.data && response.data.session) {
        const sharedSession = response.data.session;

        // 设置用户问题
        if (sharedSession.question) {
          setUserQuestion(sharedSession.question);
        }

        // 设置解读结果
        if (sharedSession.readingResult) {
          let readingData;
          let parsedResult;
          try {
            // sharedSession.readingResult 可能已经是对象或者是字符串
            if (typeof sharedSession.readingResult === 'string') {
              parsedResult = JSON.parse(sharedSession.readingResult);
            } else {
              parsedResult = sharedSession.readingResult;
            }

            // yes-no-tarot的数据格式是 {original: content, parsed: reading}
            if (parsedResult.parsed) {
              readingData = parsedResult.parsed;
            } else if (parsedResult.original) {
              // 如果没有parsed字段，尝试解析original字段
              try {
                readingData = JSON.parse(parsedResult.original);
              } catch {
                readingData = parsedResult.original;
              }
            } else {
              readingData = parsedResult;
            }

            setReadingResult(readingData);

            // 检查是否有安全问题（伦理干预）
            if (parsedResult.interventionMessage) {
              setSafetyIssue(true);
              setSafetyMessage(parsedResult.interventionMessage);
            }
          } catch (error) {
            readingData = sharedSession.readingResult;
            setReadingResult(readingData);
          }
        }

        // 设置卡牌信息
        if (sharedSession.selectedCards && sharedSession.selectedCards.length > 0) {
          const card = sharedSession.selectedCards[0];

          // 找到对应的卡牌索引
          let cardIndex = -1;

          // 尝试通过nameEn匹配
          if (card.nameEn) {
            cardIndex = TAROT_CARDS.findIndex(tarotCard =>
              tarotCard.nameEn === card.nameEn
            );
          }

          // 如果通过nameEn没找到，尝试通过name匹配
          if (cardIndex === -1 && card.name) {
            cardIndex = TAROT_CARDS.findIndex(tarotCard =>
              tarotCard.name === card.name
            );
          }

          if (cardIndex !== -1) {
            setSelectedCard(cardIndex);
            setCardOrientation(card.isReversed || false);
            setFlipped(true);
            setCardImage(`/images-optimized/tarot/${TAROT_CARDS[cardIndex].nameEn}.webp`);
          }
        }
      }
    } catch (error) {
      // 如果加载失败，跳转回单卡页面
      navigate('/yes-no-tarot/single-card');
    }
  };

  // 检查用户权限
  const checkUserPermission = () => {
    if (!user) {
      setShowLoginPrompt(true);
      return false;
    }
    if (user.vipStatus === 'active') return true;
    if (user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return false;
    }
    return true;
  };


  // 开始塔罗阅读 - 用于SpotlightCard组件
  const handleStartReading = () => {
    if (!checkUserPermission()) return;

    // 清理localStorage中的解读结果
    localStorage.removeItem('singleCardReadingResult');
    localStorage.removeItem('singleCardSelectedCard');
    localStorage.removeItem('singleCardOrientation');
    localStorage.removeItem('singleCardSafetyIssue');
    localStorage.removeItem('singleCardSafetyMessage');
    localStorage.removeItem('userQuestion');
    localStorage.removeItem('sessionId');

    // 直接导航到当前页面，强制刷新
    navigate('/yes-no-tarot/single-card');
  };

  // 处理卡牌翻转
  const handleCardFlip = () => {
    if (processingCard) {
      return;
    }

    // 清理之前的解读结果
    setReadingResult(null);
    setSafetyIssue(false);
    setSafetyMessage('');
    localStorage.removeItem('singleCardReadingResult');
    localStorage.removeItem('singleCardSafetyIssue');
    localStorage.removeItem('singleCardSafetyMessage');

    setProcessingCard(true);
    
    // 随机选择一张卡牌
    const randomCardIndex = Math.floor(Math.random() * TAROT_CARDS.length);
    setSelectedCard(randomCardIndex);
    
    // 随机确定卡牌正逆位（50%概率）
    const isReversed = Math.random() < 0.5;
    setCardOrientation(isReversed);
    
    // 设置为已翻开状态
    setFlipped(true);
    
    // 设置卡牌图片
    setCardImage(`/images-optimized/tarot/${TAROT_CARDS[randomCardIndex].nameEn}.webp`);
    
    // 卡片动画完成后，从处理中状态移除并开始解读
    setTimeout(() => {
      setProcessingCard(false);
      
      // 检查用户权限
      if (!checkUserPermission()) return;
      
      // 开始进度条动画并显示进度条模态框
      setShowFortuneModal(true);
      startProgress();
      setIsGeneratingReading(true);
      
      // 准备卡牌数据并调用API
      const card = TAROT_CARDS[randomCardIndex];
      const cardData = {
        name: card.name,
        nameEn: card.nameEn,
        isReversed: isReversed
      };
      
      // 调用API获取解读结果
      axiosInstance.post('/api/yes-no-tarot', {
        question: userQuestion,
        card: cardData,
        language: i18n.language
      })
      .then(response => {
        // 标记API响应已收到
        markApiReceived();

        // 保存sessionId
        if (response.data.sessionId) {
          setSessionId(response.data.sessionId);
          localStorage.setItem('sessionId', response.data.sessionId);
        }

        // 检查是否存在安全问题
        if (response.data.ethicalIssue) {
          setSafetyIssue(true);
          setSafetyMessage(response.data.message || '您的问题可能涉及敏感内容，无法提供解读');

          // 保存安全检测结果到localStorage
          localStorage.setItem('singleCardSafetyIssue', 'true');
          localStorage.setItem('singleCardSafetyMessage', response.data.message || '您的问题可能涉及敏感内容，无法提供解读');
          localStorage.setItem('singleCardSelectedCard', randomCardIndex.toString());
          localStorage.setItem('singleCardOrientation', isReversed.toString());

          // 完成进度条
          completeProgress(() => {
            setShowFortuneModal(false);
            setIsGeneratingReading(false);
          });
          return;
        }

        // 保存解读结果
        let readingData;
        if (response.data && response.data.reading && response.data.reading.content) {
          readingData = response.data.reading.content;
          setReadingResult(response.data.reading.content);
        } else {
          readingData = response.data.reading;
          setReadingResult(response.data.reading);
        }

        // 保存解读结果到localStorage
        localStorage.setItem('singleCardReadingResult', JSON.stringify(readingData));
        localStorage.setItem('singleCardSelectedCard', randomCardIndex.toString());
        localStorage.setItem('singleCardOrientation', isReversed.toString());
        localStorage.setItem('singleCardSafetyIssue', 'false');
        localStorage.removeItem('singleCardSafetyMessage');

        // 刷新用户信息以更新剩余次数
        refreshUser().catch(error => {
          console.log('刷新用户信息失败:', error);
        });

        // 完成进度条
        completeProgress(() => {
          setShowFortuneModal(false);
          setIsGeneratingReading(false);
        });
      })
      .catch(() => {
        completeProgress(() => {
          setShowFortuneModal(false);
          alert(t('yes_no_tarot.error.reading_failed', '很抱歉，生成解读时出现了问题，请稍后再试。'));
          setIsGeneratingReading(false);
        });
      });
    }, 1000);
  };

  return (
    <div className="main-container min-h-screen flex flex-col relative">
      <SEO />
      <LandingBackground />
      
      {/* 主要内容 */}
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
            <h1 className={`main-title mb-2 sm:mb-3 ${getGlobalFontClass(i18n.language)} dark:text-white text-gray-900`}>
              {t('yes_no_tarot.single_card_subtitle')}
            </h1>
            <p className={`text-base sm:text-lg dark:text-purple-300 text-purple-600 italic ${getGlobalFontClass(i18n.language)}`}>
              {t('yes_no_tarot.single_card_subtitle_description')}
            </p>
          </div>

          {/* 主页面内容 */}
          <div className="max-w-[95%] lg:max-w-5xl mx-auto mt-0 sm:mt-8">
            <div className="max-w-3xl mx-auto">
              {/* 单卡塔罗卡片 */}
              <div className="relative backdrop-blur-xl rounded-xl overflow-hidden dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 shadow-lg">
                <div className="p-6">
                  <div className="space-y-8">
                    {/* 显示用户问题 */}
                    <div className="text-left">
                      <h3 className="text-xl font-medium text-white mb-2">{t('yes_no_tarot.your_question')}</h3>
                      <div className="mb-2">
                        <p className="text-lg text-purple-100">{userQuestion}</p>
                      </div>
                    </div>
                    
                    {/* 卡牌展示区域 */}
                    <div id="card-display-area">
                      <TarotCardDisplay
                        flipped={flipped}
                        cardBackImage={cardBackImage}
                        selectedCard={selectedCard}
                        cardImage={cardImage}
                        cardOrientation={cardOrientation}
                        processingCard={processingCard}
                        handleCardFlip={handleCardFlip}
                        getFontClass={getFontClass}
                      />
                    </div>
                    
                    {/* 解读结果显示区域 */}
                    {flipped && (readingResult || safetyIssue) && (
                      <ReadingResult
                        readingResult={readingResult}
                        safetyIssue={safetyIssue}
                        safetyMessage={safetyMessage}
                        onShare={isSharedView ? undefined : () => setShowShareDialog(true)}
                        onComment={isSharedView ? undefined : () => setShowCommentDialog(true)}
                        hasReceivedShareReward={hasReceivedShareReward}
                        sessionId={sessionId || undefined}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* 返回选择按钮 */}
            {flipped && (readingResult || safetyIssue) && (
              <div className="max-w-3xl mx-auto mt-6 mb-8">
                <div className="text-center">
                  <button
                    onClick={() => {
                      // 清理localStorage中的解读结果
                      localStorage.removeItem('singleCardReadingResult');
                      localStorage.removeItem('singleCardSelectedCard');
                      localStorage.removeItem('singleCardOrientation');
                      localStorage.removeItem('singleCardSafetyIssue');
                      localStorage.removeItem('singleCardSafetyMessage');
                      localStorage.removeItem('userQuestion');
                      localStorage.removeItem('sessionId');

                      navigate('/yes-no-tarot/single-card');
                    }}
                    className="px-6 py-3 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-medium transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    {t('yes_no_tarot.back_button', '返回选择')}
                  </button>
                </div>
              </div>
            )}

            {/* Best Questions for One Card Tarot Yes/No 板块 */}
            <SingleCardBestQuestions />

            {/* How One Card Tarot Yes No Reading Works 板块 */}
            <SingleCardHowItWords />

            {/* Benefits of Single Card Yes No Tarot 板块 */}
            <SingleCardBenefits />

            {/* Understanding Your One Card Reading 板块 */}
            <SingleCardUnderstanding />

            {/* 更多塔罗占卜区域 */}
            <MoreTarotOptions onNavigate={navigate} pageType="single" />

            {/* 评论板块 - 只在有解读结果时显示 */}
            {flipped && (readingResult || safetyIssue) && (
              <div className="mt-24 sm:mt-32 mb-8">
                <CommentSection
                  sessionId={sessionId || undefined}
                  pageType="yes-no-tarot"
                  className=""
                  showWriteComment={!isSharedView && !!user}
                  onShowAllComments={() => setShowCommentDialog(true)}
                />
              </div>
            )}

            {/* SpotlightCard组件 */}
            <SpotlightSection onStartReading={handleStartReading} />
          </div>
        </div>
      </div>

      <Footer />

      {/* VIP提示弹窗 */}
      <VipPromptDialog isOpen={showVipPrompt} onCancel={() => setShowVipPrompt(false)} />
      
      {/* 登录提示弹窗 */}
      <LoginPrompt isOpen={showLoginPrompt} onClose={() => setShowLoginPrompt(false)} />
      
      {/* 运势解读弹窗 */}
      <FortuneReadingModal
        isOpen={showFortuneModal}
        progress={progress}
      />

      {/* 分享对话框 */}
      <ShareDialog
        isOpen={showShareDialog}
        onClose={() => setShowShareDialog(false)}
        pageType="yes-no-tarot"
        sessionId={sessionId || undefined}
        hasReceivedReward={hasReceivedShareReward}
      />

      {/* 评论对话框 */}
      <CommentDialog
        isOpen={showCommentDialog}
        onClose={() => setShowCommentDialog(false)}
        sessionId={sessionId || undefined}
        pageType="yes-no-tarot"
      />
    </div>
  );
};

export default YesNoSingleCardResult;
