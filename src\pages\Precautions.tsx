import React from 'react';
import { useTranslation } from 'react-i18next';
import StarryBackground from '../components/StarryBackground';
import Footer from '../components/Footer';
import SEO from '../components/SEO';

const Precautions: React.FC = () => {
  const { t } = useTranslation();

  const sections = [
    'mindset',
    'question_skills',
    'usage_guide',
    'result_cognition',
    'environment',
    'special_notes',
    'learning_tips'
  ];

  return (
    <>
      <SEO />
      <div className="relative text-white min-h-screen">
        <StarryBackground />
        <div className="w-full pt-1 pb-16 px-2 flex flex-col items-center">
          <div className="w-full max-w-6xl mx-auto space-y-6 sm:space-y-8">
            {/* Header Section */}
            <div className="max-w-3xl mx-auto text-center mb-12">
              <h1 className="text-4xl font-bold mb-6 text-purple-400 font-['Inter']">
                {t('precautions.title')}
              </h1>
              <p className="text-lg text-purple-300 italic px-4 font-['Inter']">
                {t('precautions.tips')}
              </p>
            </div>

            {/* Main Content */}
            <div className="max-w-3xl mx-auto">
              <div className="question-card relative bg-[#0D0C0F]/95 backdrop-blur-xl p-8 rounded-2xl">
                <style>
                  {`
                    .question-card {
                      position: relative;
                      background: rgba(13, 12, 15, 0.95);
                      backdrop-filter: blur(20px);
                      border: 1px solid rgba(236, 72, 153, 0.3);
                      box-shadow: 
                        0 0 0 1px rgba(168, 85, 247, 0.2),
                        0 0 15px rgba(168, 85, 247, 0.15),
                        0 0 30px rgba(236, 72, 153, 0.15),
                        inset 0 0 15px rgba(168, 85, 247, 0.1);
                    }
                    .question-card::before {
                      content: '';
                      position: absolute;
                      inset: -1px;
                      padding: 1px;
                      background: linear-gradient(
                        135deg,
                        rgba(168, 85, 247, 0.5),
                        rgba(236, 72, 153, 0.5)
                      );
                      -webkit-mask: 
                        linear-gradient(#fff 0 0) content-box, 
                        linear-gradient(#fff 0 0);
                      -webkit-mask-composite: xor;
                      mask-composite: exclude;
                      pointer-events: none;
                      border-radius: 1rem;
                    }
                  `}
                </style>
                {sections.map((section) => {
                  const items = t(`precautions.sections.${section}.items`, { returnObjects: true }) as string[];
                  return (
                    <div key={section} className="mb-8 last:mb-0">
                      <h2 className="text-2xl font-semibold text-purple-400 mb-6 font-['Inter']">
                        {t(`precautions.sections.${section}.title`)}
                      </h2>
                      <ul className="space-y-4 pl-1">
                        {items.map((item, index) => (
                          <li key={index} className="flex items-center space-x-3 text-gray-300">
                            <span className="text-purple-400 text-base flex-shrink-0">•</span>
                            <span className="flex-1 text-base leading-relaxed font-['Noto_Sans_SC']">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
};

export default Precautions; 