/**
 * 语音API适配接口
 * 提供与后端语音API交互的方法
 */

import axios from 'axios';
import { io, Socket } from 'socket.io-client';

// 后端API基础URL
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api';
// WebSocket基础URL
const WS_BASE_URL = process.env.REACT_APP_WS_BASE_URL || 'http://localhost:5000';

// 默认的用户ID
const DEFAULT_USER_ID = 'anonymous';

// 类型定义
interface SessionResponse {
  success: boolean;
  sessionId?: string;
  message?: string;
}

interface AudioData {
  audio: string;
}

interface TextData {
  text: any;
}

interface EventData {
  event: any;
  type?: string;
}

interface ErrorData {
  message: any;
  type?: string;
}

type EventCallback = (data: any) => void;

interface ListenerMap {
  audio: EventCallback[];
  text: EventCallback[];
  event: EventCallback[];
  error: EventCallback[];
  [key: string]: EventCallback[];
}

interface ClientOptions {
  userId?: string;
}

/**
 * 语音API客户端类
 */
class VoiceApiClient {
  private socket: Socket | null = null;
  private sessionId: string | undefined = undefined;
  private isConnected: boolean = false;
  private userId: string = DEFAULT_USER_ID;
  private listeners: ListenerMap = {
    audio: [],
    text: [],
    event: [],
    error: [],
  };

  /**
   * 初始化客户端
   * @param {ClientOptions} options - 配置选项
   */
  init(options: ClientOptions = {}): void {
    this.userId = options.userId || DEFAULT_USER_ID;
  }

  /**
   * 启动语音会话
   * @returns {Promise<SessionResponse>} 会话信息
   */
  async startSession(): Promise<SessionResponse> {
    try {
      const response = await axios.post(`${API_BASE_URL}/voice/start`, {
        user_id: this.userId,
      });

      if (response.data.status === 'ok') {
        this.sessionId = response.data.data.session_id;
        return {
          success: true,
          sessionId: this.sessionId,
        };
      } else {
        throw new Error(response.data.message || '启动会话失败');
      }
    } catch (error) {
      console.error('启动语音会话出错:', error);
      throw error;
    }
  }

  /**
   * 停止语音会话
   * @returns {Promise<SessionResponse>} 操作结果
   */
  async stopSession(): Promise<SessionResponse> {
    if (!this.sessionId) {
      return { success: false, message: '没有活动的会话' };
    }

    try {
      const response = await axios.post(`${API_BASE_URL}/voice/stop`, {
        user_id: this.userId,
        session_id: this.sessionId,
      });

      this.disconnect();
      this.sessionId = undefined;

      return {
        success: response.data.status === 'ok',
        message: response.data.message,
      };
    } catch (error) {
      console.error('停止语音会话出错:', error);
      throw error;
    }
  }

  /**
   * 连接到WebSocket服务
   * @returns {Promise<boolean>} 是否连接成功
   */
  connect(): Promise<boolean> {
    if (this.isConnected) {
      return Promise.resolve(true);
    }

    return new Promise((resolve, reject) => {
      try {
        this.socket = io(WS_BASE_URL);

        this.socket.on('connect', () => {
          console.log('WebSocket连接成功');
          this.isConnected = true;
          resolve(true);
        });

        this.socket.on('error', (error: any) => {
          console.error('WebSocket连接错误:', error);
          this._notifyListeners('error', { type: 'connection', message: error });
          reject(error);
        });

        this.socket.on('disconnect', () => {
          console.log('WebSocket断开连接');
          this.isConnected = false;
          this._notifyListeners('event', { type: 'disconnect' });
        });

        // 监听会话数据
        this.socket.on('session_data', (data: any) => {
          if (data.session_id === this.sessionId) {
            const eventData = data.data;
            if (eventData) {
              this._handleSessionData(eventData);
            }
          }
        });
      } catch (error) {
        console.error('初始化WebSocket连接出错:', error);
        reject(error);
      }
    });
  }

  /**
   * 处理会话数据
   * @param {any} data - 会话数据
   * @private
   */
  private _handleSessionData(data: any): void {
    const type = data.type;
    
    switch (type) {
      case 'audio':
        // 处理音频数据
        this._notifyListeners('audio', {
          audio: data.data, // base64编码的音频数据
        });
        break;
      case 'text':
        // 处理文本数据
        this._notifyListeners('text', {
          text: data.data,
        });
        break;
      case 'event':
        // 处理事件数据
        this._notifyListeners('event', {
          event: data.data,
        });
        break;
      case 'error':
        // 处理错误数据
        this._notifyListeners('error', {
          message: data.data,
        });
        break;
      default:
        console.warn('未知的数据类型:', type, data);
    }
  }

  /**
   * 加入会话
   * @returns {Promise<boolean>} 是否加入成功
   */
  joinSession(): Promise<boolean> {
    if (!this.socket || !this.isConnected || !this.sessionId) {
      return Promise.reject(new Error('WebSocket未连接或没有有效会话'));
    }

    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('WebSocket未连接'));
        return;
      }
      
      this.socket.emit('join_session', {
        user_id: this.userId,
        session_id: this.sessionId,
      });

      this.socket.once('joined', (data: any) => {
        if (data.session_id === this.sessionId) {
          console.log('已加入会话:', this.sessionId);
          resolve(true);
        } else {
          reject(new Error('加入会话失败，会话ID不匹配'));
        }
      });

      this.socket.once('error', (error: any) => {
        reject(error);
      });

      // 设置超时
      setTimeout(() => {
        reject(new Error('加入会话超时'));
      }, 5000);
    });
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    if (this.socket) {
      if (this.sessionId) {
        this.socket.emit('leave_session', {
          session_id: this.sessionId,
        });
      }
      this.socket.disconnect();
      this.isConnected = false;
      this.socket = null;
    }
  }

  /**
   * 发送音频数据
   * @param {string} audioData - base64编码的音频数据
   * @returns {Promise<boolean>} 是否发送成功
   */
  sendAudio(audioData: string): Promise<boolean> {
    if (!this.isConnected || !this.socket || !this.sessionId) {
      return Promise.reject(new Error('未连接或没有有效会话'));
    }

    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('WebSocket未连接'));
        return;
      }
      
      this.socket.emit('audio_data', {
        session_id: this.sessionId,
        audio: audioData,
      });

      this.socket.once('audio_received', (data: any) => {
        if (data.session_id === this.sessionId && data.status === 'ok') {
          resolve(true);
        } else {
          reject(new Error('发送音频失败'));
        }
      });

      this.socket.once('error', (error: any) => {
        reject(error);
      });

      // 设置超时
      setTimeout(() => {
        reject(new Error('发送音频超时'));
      }, 5000);
    });
  }

  /**
   * 通过HTTP发送音频数据（备用方法）
   * @param {string} audioData - base64编码的音频数据
   * @returns {Promise<SessionResponse>} 操作结果
   */
  async sendAudioHttp(audioData: string): Promise<SessionResponse> {
    if (!this.sessionId) {
      throw new Error('没有有效会话');
    }

    try {
      const response = await axios.post(`${API_BASE_URL}/voice/audio`, {
        session_id: this.sessionId,
        audio: audioData,
      });

      return {
        success: response.data.status === 'ok',
        message: response.data.message,
      };
    } catch (error) {
      console.error('发送音频数据出错:', error);
      throw error;
    }
  }

  /**
   * 获取会话数据（短轮询方式，备用方法）
   * @returns {Promise<any>} 会话数据
   */
  async getSessionData(): Promise<any> {
    if (!this.sessionId) {
      throw new Error('没有有效会话');
    }

    try {
      const response = await axios.get(`${API_BASE_URL}/voice/data`, {
        params: {
          session_id: this.sessionId,
        },
      });

      return response.data.data;
    } catch (error) {
      console.error('获取会话数据出错:', error);
      throw error;
    }
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名称 (audio, text, event, error)
   * @param {EventCallback} callback - 回调函数
   */
  addEventListener(event: string, callback: EventCallback): void {
    if (this.listeners[event]) {
      this.listeners[event].push(callback);
    }
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {EventCallback} callback - 回调函数
   */
  removeEventListener(event: string, callback: EventCallback): void {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(
        (listener) => listener !== callback
      );
    }
  }

  /**
   * 通知监听器
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   * @private
   */
  private _notifyListeners(event: string, data: any): void {
    if (this.listeners[event]) {
      this.listeners[event].forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error(`执行${event}事件监听器出错:`, error);
        }
      });
    }
  }
}

// 导出单例实例
const voiceApi = new VoiceApiClient();
export default voiceApi; 