import React from 'react';

type SelectionMode = 'slide' | 'number' | 'custom';

interface SelectionModeToggleProps {
  selectionMode: SelectionMode;
  setSelectionMode: (mode: SelectionMode) => void;
  isDark: boolean;
  t: (key: string) => string;
}

const SelectionModeToggle: React.FC<SelectionModeToggleProps> = ({ 
  selectionMode, 
  setSelectionMode, 
  isDark, 
  t 
}) => {
  return (
    <div className="flex flex-col items-center justify-center gap-2 mt-4">
      {/* 移动端切换按钮 */}
      <div className="flex sm:hidden w-full justify-center">
        <div className={`inline-flex rounded-full p-0.5 ${isDark ? 'bg-gray-800' : 'bg-gray-200'} w-full max-w-md`}>
          <button
            onClick={() => setSelectionMode('slide')}
            className={`flex-1 px-4 py-1.5 rounded-full text-sm transition-colors duration-200 ${
              selectionMode === 'slide' 
                ? 'bg-purple-600 text-white' 
                : `${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`
            }`}
          >
            {t('reading.shuffle.selection_mode.slide')}
          </button>
          <button
            onClick={() => setSelectionMode('number')}
            className={`flex-1 px-4 py-1.5 rounded-full text-sm transition-colors duration-200 ${
              selectionMode === 'number' 
                ? 'bg-purple-600 text-white' 
                : `${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`
            }`}
          >
            {t('reading.shuffle.selection_mode.number')}
          </button>
          <button
            onClick={() => setSelectionMode('custom')}
            className={`flex-1 px-4 py-1.5 rounded-full text-sm transition-colors duration-200 ${
              selectionMode === 'custom' 
                ? 'bg-purple-600 text-white' 
                : `${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`
            }`}
          >
            {t('reading.shuffle.selection_mode.custom')}
          </button>
        </div>
      </div>

      {/* 桌面端切换按钮 */}
      <div className="hidden sm:flex justify-center w-full">
        <div className={`inline-flex rounded-full p-0.5 ${isDark ? 'bg-gray-800' : 'bg-gray-200'} w-full max-w-lg`}>
          <button
            onClick={() => setSelectionMode('slide')}
            className={`flex-1 px-6 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
              selectionMode === 'slide' 
                ? 'bg-purple-600 text-white' 
                : `${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`
            }`}
          >
            {t('reading.shuffle.selection_mode.slide')}
          </button>
          <button
            onClick={() => setSelectionMode('number')}
            className={`flex-1 px-6 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
              selectionMode === 'number' 
                ? 'bg-purple-600 text-white' 
                : `${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`
            }`}
          >
            {t('reading.shuffle.selection_mode.number')}
          </button>
          <button
            onClick={() => setSelectionMode('custom')}
            className={`flex-1 px-6 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
              selectionMode === 'custom' 
                ? 'bg-purple-600 text-white' 
                : `${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`
            }`}
          >
            {t('reading.shuffle.selection_mode.custom')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SelectionModeToggle; 