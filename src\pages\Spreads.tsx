import React, { useState, useEffect } from 'react';
// import { motion } from 'framer-motion';
import {SPREAD_OPTIONS } from '../data/spreads';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import { IoChevronBackOutline, IoChevronForwardOutline } from 'react-icons/io5';
import { useTranslation } from 'react-i18next';
import '../styles/Spreads.css';
import SEO from '../components/SEO';
import CdnLazyImage from '../components/CdnLazyImage';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';


const CATEGORIES = ['all', 'general', 'love_relationships', 'fortune_prediction', 'self-exploration', 'career_wealth'];

// 添加辅助函数来转换分类名称
const getCategoryInChinese = (key: string): string => {
  const categoryMap: Record<string, string> = {
    'all': '全部',
    'general': '通用',
    'love_relationships': '爱情人际',
    'fortune_prediction': '运势预测',
    'self-exploration': '自我探索',
    'career_wealth': '事业财富'
  };
  return categoryMap[key] || key;
};

// 添加 getCategoryKey 函数
const getCategoryKey = (chineseCategory: string): string => {
  const reverseCategoryMap: Record<string, string> = {
    '全部': 'all',
    '通用': 'general',
    '爱情人际': 'love_relationships',
    '运势预测': 'fortune_prediction',
    '自我探索': 'self-exploration',
    '事业财富': 'career_wealth'
  };
  return reverseCategoryMap[chineseCategory] || chineseCategory;
};

const SpreadCard = ({ spread }: { spread: typeof SPREAD_OPTIONS[0] }) => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(0);
  const positionsPerPage = 4;
  const totalPages = Math.ceil(spread.positions.length / positionsPerPage);
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const { navigate } = useLanguageNavigate();

  const handlePrevPage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentPage(prev => (prev > 0 ? prev - 1 : prev));
  };

  const handleNextPage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentPage(prev => (prev < totalPages - 1 ? prev + 1 : prev));
  };

  // 添加点击卡片导航到详情页的处理函数
  const handleCardClick = () => {
    // 使用useLanguageNavigate钩子处理导航，自动处理语言参数
    navigate(`/spreads/${spread.id}`);
  };

  // 获取翻译后的牌阵信息
  const getTranslatedSpreadInfo = () => {
    const spreadId = spread.id.replace(/-/g, '_');
    if (Object.keys(t('spreads', { returnObjects: true })).includes(spreadId)) {
      return {
        name: t(`spreads.${spreadId}.name`),
        description: t(`spreads.${spreadId}.description`),
        positions: spread.positions.map((_, index) => {
          const positionKeys = Object.keys(t(`spreads.${spreadId}.positions`, { returnObjects: true }));
          return t(`spreads.${spreadId}.positions.${positionKeys[index]}`);
        })
      };
    }
    return {
      name: spread.name,
      description: spread.description,
      positions: spread.positions
    };
  };

  const { name: spreadName, description: spreadDescription, positions: spreadPositions } = getTranslatedSpreadInfo();

  const currentPositions = spreadPositions.slice(
    currentPage * positionsPerPage,
    (currentPage + 1) * positionsPerPage
  );

  return (
    <div 
      className={`relative w-full overflow-hidden transition-all duration-300 ${
        isDark 
          ? 'bg-black/40 backdrop-blur-xl border-purple-500/20 hover:border-purple-500/40' 
          : 'bg-white/80 backdrop-blur-xl border-purple-300/40 hover:border-purple-400/60'
        } rounded-2xl shadow-2xl border-2 cursor-pointer`}
      onClick={handleCardClick}
    >
      <div className={`absolute -top-32 -right-32 w-64 h-64 ${
        isDark ? 'bg-purple-500/10' : 'bg-purple-300/20'
      } rounded-full blur-3xl`}></div>
      <div className={`absolute -bottom-32 -left-32 w-64 h-64 ${
        isDark ? 'bg-pink-500/10' : 'bg-pink-300/20'
      } rounded-full blur-3xl`}></div>
      
      <div className="relative flex flex-col md:flex-row w-full">
        <div className="flex-1 p-4 md:p-6">
          <div className="flex flex-col h-full">
            <div className="mb-3 md:mb-4">
              <h3 className={`text-lg md:text-xl font-bold ${isDark ? 'text-white' : 'text-gray-800'} mb-1 md:mb-2`}>{spreadName}</h3>
              <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'} text-sm line-clamp-2`}>{spreadDescription}</p>
            </div>

            <div className="flex flex-wrap items-center gap-2 mb-2 md:mb-3">
              {spread.tags.map((tag) => {
                const tagText = t(`spread.tags.${tag}`, { defaultValue: tag });
                return (
                  <span
                    key={tag}
                    className={`px-2 py-0.5 ${
                      isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'
                    } rounded text-xs`}
                  >
                    {tagText}
                  </span>
                );
              })}
              {totalPages > 1 && (
                <div className="flex items-center gap-1 ml-auto">
                  <button
                    onClick={handlePrevPage}
                    disabled={currentPage === 0}
                    className={`p-1 rounded ${
                      currentPage === 0 
                        ? isDark ? 'text-gray-600' : 'text-gray-400'
                        : isDark ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    <IoChevronBackOutline size={16} />
                  </button>
                  <button
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages - 1}
                    className={`p-1 rounded ${
                      currentPage === totalPages - 1 
                        ? isDark ? 'text-gray-600' : 'text-gray-400'
                        : isDark ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    <IoChevronForwardOutline size={16} />
                  </button>
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-2 auto-rows-min content-start min-h-[80px]">
              {currentPositions.map((position, index) => (
                <div
                  key={currentPage * positionsPerPage + index}
                  className={`position-chip px-2 py-1.5 text-sm text-center rounded ${
                    isDark ? 'bg-gray-800 text-gray-200' : 'bg-gray-200 text-gray-700'
                  } truncate`}
                >
                  <span className="mr-1">{currentPage * positionsPerPage + index + 1}.</span>
                  {spread.id === 'time-flow' || spread.id === 'past-present-future' ? spreadPositions[currentPage * positionsPerPage + index] : position}
                </div>
              ))}
              {[...Array(positionsPerPage - currentPositions.length)].map((_, index) => (
                <div
                  key={`empty-${index}`}
                  className="invisible"
                />
                
              ))}
            </div>
          </div>
        </div>

        <div className={`w-full md:w-[200px] lg:w-[240px] p-4 md:pl-6 lg:pl-8 md:border-l border-t md:border-t-0 ${
          isDark ? 'border-gray-800' : 'border-gray-300'
        } flex items-center justify-center`}>
          <div className="w-[160px] md:w-[180px] lg:w-[200px] h-[140px] md:h-[160px] lg:h-[180px] flex items-center justify-center">
            <CdnLazyImage 
              src={spread.image}
              alt={`${spreadName} Layout`}
              className="max-w-full max-h-full object-contain opacity-100"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const Spreads: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [_, setForceUpdate] = useState(0);
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // 当语言变化时强制组件更新
  useEffect(() => {
    setForceUpdate(prev => prev + 1);
  }, [i18n.language]);

  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  // 根据语言环境决定显示的标题
  const getTitle = () => {
    switch (i18n.language) {
      case 'en':
        return 'Tarot Spread Introduction';
      case 'ja':
        return 'タロットスプレッド紹介';
      case 'zh-TW':
        return '塔羅牌陣介紹';
      default:
        return '塔罗牌阵介绍';
    }
  };

  // 根据语言环境决定显示的副标题
  const getSubtitle = () => {
    switch (i18n.language) {
      case 'en':
        return 'Explore different types of tarot spreads and their interpretations';
      case 'ja':
        return '様々なタロットスプレッドとその解釈方法を探る';
      case 'zh-TW':
        return '探索不同類型的塔羅牌陣及其解讀方式';
      default:
        return '探索不同类型的塔罗牌阵及其解读方式';
    }
  };

  const filteredSpreads = SPREAD_OPTIONS.filter(spread => 
    selectedCategory === 'all' || spread.category.includes(getCategoryInChinese(selectedCategory))
  );

  // 添加排序逻辑
  const sortedSpreads = [...filteredSpreads].sort((a, b) => {
    // 将多类别的牌阵放在后面
    if (a.category.length !== b.category.length) {
      return a.category.length - b.category.length;  // 类别数量少的排在前面
    }

    // 如果类别数量相同，按照类别顺序排序
    if (a.category[0] === b.category[0]) {
      return filteredSpreads.indexOf(a) - filteredSpreads.indexOf(b);
    }
    return CATEGORIES.indexOf(getCategoryKey(a.category[0])) - CATEGORIES.indexOf(getCategoryKey(b.category[0]));
  });

  // 构建SEO信息
  const generateSEOInfo = () => {
    // 根据当前语言选择适当的关键词
    let keywords = '';
    switch(i18n.language) {
      case 'en':
        keywords = 'tarot spreads, tarot layouts, tarot reading patterns, tarot card positions, love tarot spreads, career tarot spreads';
        break;
      case 'ja':
        keywords = 'タロットスプレッド, タロットレイアウト, タロット占い方法, タロットカード配置, 恋愛タロットスプレッド, 仕事タロットスプレッド';
        break;
      case 'zh-TW':
        keywords = '塔羅牌陣, 塔羅佈局, 塔羅占卜方式, 塔羅牌位置, 愛情塔羅牌陣, 事業塔羅牌陣';
        break;
      default:
        keywords = '塔罗牌阵, 塔罗布局, 塔罗占卜方式, 塔罗牌位置, 爱情塔罗牌阵, 事业塔罗牌阵';
    }
    
    return {
      title: getTitle(),
      description: getSubtitle(),
      keywords: keywords
    };
  };
  
  const seoInfo = generateSEOInfo();
  
  return (
    <>
      <SEO 
        title={seoInfo.title}
        description={seoInfo.description}
        keywords={seoInfo.keywords}
      />
      
      <div className="min-h-screen flex flex-col relative">
        <LandingBackground />
        <div className="flex-grow relative z-10">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8">
            <div className="text-center mt-8 sm:mt-10">
              <h1 className={`main-title mb-1 ${getFontClass()} ${isDark ? '' : 'text-gray-800'}`}>{getTitle()}</h1>
              <p className={`sub-title mb-4 sm:mb-6 ${getFontClass()} ${isDark ? '' : 'text-gray-600'}`}>{getSubtitle()}</p>
            </div>

            {/* 分类导航 - 移动端两行显示，桌面端居中 */}
            <div className="py-1 px-4 mb-4 sm:mb-6">
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:flex lg:flex-nowrap justify-center gap-2 md:gap-3">
                {CATEGORIES.map((category) => {
                  const translationKey = 'spread.categories.' + category;
                  return (
                    <button
                      key={category}
                      className={`w-full sm:min-w-[140px] lg:w-[180px] h-[44px] flex items-center justify-center whitespace-normal text-center px-3 md:px-4 rounded-full text-sm font-medium transition-colors duration-200 font-['Inter'] ${
                        selectedCategory === category
                        ? 'bg-purple-600 text-white'
                        : isDark
                          ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                          : 'bg-gray-200 text-white hover:bg-gray-300'
                      }`}
                      onClick={() => setSelectedCategory(category)}
                    >
                      <span className="line-clamp-2" style={{color: selectedCategory === category || isDark ? 'white' : '#4B5563'}}>
                        {t(translationKey)}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* 牌阵网格 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {sortedSpreads.map((spread) => (
                <div key={spread.id} className="relative group">
                  <SpreadCard spread={spread} />
                </div>
              ))}
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
};

export default Spreads; 