import React from 'react';
import { motion } from 'framer-motion';

interface CustomActionButtonProps {
  onClick: () => void;
  isEnabled: boolean;
  isDark: boolean;
  t: (key: string, options?: any) => string;
  requiredCards: number;
  selectedCount: number;
  hasExistingReading?: boolean;
  isLoading?: boolean;
  hasCompletedReading?: boolean;
}

const CustomActionButton: React.FC<CustomActionButtonProps> = ({ 
  onClick, 
  isEnabled, 
  isDark, 
  t,
  requiredCards,
  selectedCount,
  hasExistingReading = false,
  isLoading = false,
  hasCompletedReading = false
}) => {
  // 临时解决方案：为可能缺失的i18n键提供默认值
  const getTranslation = (key: string, defaultValue: string, options?: any) => {
    try {
      // 尝试使用t函数
      const translation = t(key, options);
      // 如果返回的是键名本身，则使用默认值
      return translation === key ? defaultValue : translation;
    } catch (error) {
      return defaultValue;
    }
  };

  // 只有在卡牌选择完成、没有现有解读、不在加载状态且未完成解读时按钮才可用
  const buttonEnabled = isEnabled && !hasExistingReading && !isLoading && !hasCompletedReading;
  
  // 按钮样式
  const getButtonStyle = () => {
    if (buttonEnabled) {
      return 'bg-purple-600 hover:bg-purple-500 transition-colors duration-200';
    } else if (isLoading) {
      return `${isDark ? 'bg-gray-600' : 'bg-gray-500'} cursor-wait`;
    } else {
      return `${isDark ? 'bg-gray-700' : 'bg-gray-400'} cursor-not-allowed`;
    }
  };

  // 按钮文本
  const getButtonText = () => {
    if (hasExistingReading) {
      return getTranslation('reading.shuffle.already_completed', '已完成解读');
    } else if (isLoading) {
      return getTranslation('reading.shuffle.loading', '解读中...');
    } else if (hasCompletedReading) {
      return getTranslation('reading.shuffle.completed', '解读已完成');
    } else if (isEnabled) {
      return getTranslation('reading.shuffle.start_interpretation', '开始解读');
    } else {
      return getTranslation('reading.shuffle.remaining_cards', `还需选择 ${requiredCards - selectedCount} 张牌`, { count: requiredCards - selectedCount });
    }
  };

  return (
    <motion.div 
      className="flex justify-center mt-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.6 }}
    >
      <motion.button
        className={`relative px-10 py-4 rounded-full text-white font-medium text-lg font-sans ${getButtonStyle()}`}
        whileHover={buttonEnabled ? { scale: 1.02 } : {}}
        whileTap={buttonEnabled ? { scale: 0.98 } : {}}
        onClick={onClick}
        disabled={!buttonEnabled}
      >
        <span className="relative z-10" style={{color: 'white'}}>
          {getButtonText()}
        </span>
      </motion.button>
    </motion.div>
  );
};

export default CustomActionButton; 