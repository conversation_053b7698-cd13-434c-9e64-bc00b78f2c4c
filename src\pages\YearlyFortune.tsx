import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import LandingBackground from '../components/LandingBackground';
import { DateInput } from '../components/DateInput';
import Footer from '../components/Footer';
import { occupationOptions, relationshipOptions } from '../data/options';
import VipPromptDialog from '../components/VipPromptDialog';
import { useUser } from '../contexts/UserContext';
import { useTheme } from '../contexts/ThemeContext';
import SEO from '../components/SEO';

interface FormData {
  birthDate: string;
  gender: string;
  occupation: string;
  relationship: string;
}

const YearlyFortune: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { navigate } = useLanguageNavigate();
  const { user } = useUser();
  const { theme } = useTheme();
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  // const [setIsProcessing] = useState(false);
  
  // 在组件加载时清除之前的年运占卜相关数据
  useEffect(() => {
    localStorage.removeItem('yearlyFortuneResult');
    localStorage.removeItem('selectedCards');
    localStorage.removeItem('yearlyFortuneData');
    localStorage.removeItem('selectedSpread');
    localStorage.removeItem('selectedCardsInfo');
  }, []);
  
  // 获取字体样式
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };
  
  // 表单状态
  const [formData, setFormData] = useState<FormData>({
    birthDate: '',
    gender: '',
    occupation: '',
    relationship: ''
  });
  
  // 表单错误状态
  const [errors, setErrors] = useState<{
    birthdate?: string;
    gender?: string;
    occupation?: string;
    relationshipStatus?: string;
    general?: string;
  }>({});

  // 验证表单
  const validateForm = () => {
    const newErrors: any = {};
    
    if (!formData.birthDate) {
      newErrors.birthdate = '请选择出生日期';
    }
    
    if (!formData.gender) {
      newErrors.gender = '请选择性别';
    }
    
    if (!formData.occupation) {
      newErrors.occupation = '请选择职业';
    }
    
    if (!formData.relationship) {
      newErrors.relationshipStatus = '请选择感情状态';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 获取按钮文本
  const getButtonText = () => {
    if (!user || user.vipStatus !== 'active') {
      return t('reader.button.upgrade');
    }
    return t('form.submit');
  };

  // 获取按钮样式
  const getButtonStyle = () => {
    return 'bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-400 hover:to-yellow-400 text-black shadow-amber-500/20';
  };

  // 处理按钮点击
  const handleButtonClick = () => {
    // 检查是否是VIP用户
    if (!user || user.vipStatus !== 'active') {
      setShowVipPrompt(true);
      return;
    }
    
    // 如果是VIP用户，允许填写表单并提交
    if (!validateForm()) {
      return;
    }
    
    // 继续执行表单提交的逻辑
    handleFormSubmit();
  };

  // 提交表单函数
  const handleFormSubmit = async () => {    
    try {
      // 存储用户数据到本地存储
      localStorage.setItem('yearlyFortuneData', JSON.stringify(formData));
      
      // 创建年运牌阵配置
      const yearlySpread = {
        id: "yearly-fortune",
        name: t('yearly.spread.name'),
        description: t('yearly.spread.description'),
        cardCount: 16,
        spreadType: "yearly-fortune",
        positions: [
          t('yearly.spread.positions.q1_career'), t('yearly.spread.positions.q1_love'), t('yearly.spread.positions.q1_health'), t('yearly.spread.positions.q1_achievement'),
          t('yearly.spread.positions.q2_career'), t('yearly.spread.positions.q2_love'), t('yearly.spread.positions.q2_health'), t('yearly.spread.positions.q2_achievement'),
          t('yearly.spread.positions.q3_career'), t('yearly.spread.positions.q3_love'), t('yearly.spread.positions.q3_health'), t('yearly.spread.positions.q3_achievement'),
          t('yearly.spread.positions.q4_career'), t('yearly.spread.positions.q4_love'), t('yearly.spread.positions.q4_health'), t('yearly.spread.positions.q4_achievement')
        ],
        positionKeys: [
          'q1_career', 'q1_love', 'q1_health', 'q1_achievement',
          'q2_career', 'q2_love', 'q2_health', 'q2_achievement',
          'q3_career', 'q3_love', 'q3_health', 'q3_achievement',
          'q4_career', 'q4_love', 'q4_health', 'q4_achievement'
        ]
      };
      
      // 保存牌阵信息
      localStorage.setItem('selectedSpread', JSON.stringify(yearlySpread));
      
      // 设置问题
      const userQuestion = t('yearly.spread.question', '请为我解读未来一年的运势');
      localStorage.setItem('userQuestion', userQuestion);
      
      // 导航到选牌页面
      navigate('/reading/shuffle', { replace: true });
    } catch (error) {
      // console.error('Error starting yearly fortune:', error);
      setErrors({ ...errors, general: '启动年度运势预测时出错' });
      navigate('/reading/shuffle', { replace: true });
    }
  };

  // 渲染下拉选择框
  const renderSelect = (
    label: string,
    value: string,
    onChange: (value: string) => void,
    options: { value: string; label: string }[]
  ) => {
    return (
      <div className="space-y-2">
        <label className={`block ${theme === 'light' ? 'text-gray-800' : 'text-white'} text-base ${getFontClass()}`}>{label}</label>
        <div className="relative">
          <select
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className={`w-full p-3 ${theme === 'light' ? 'bg-white/80 text-gray-800 border-purple-300/30' : 'bg-black/20 text-white border-purple-500/20'} border rounded-xl text-base 
              focus:outline-none focus:${theme === 'light' ? 'border-purple-400/60' : 'border-purple-500/40'} focus:ring-0 focus:outline-0
              hover:${theme === 'light' ? 'border-purple-400/40' : 'border-purple-500/30'} transition-all duration-300 appearance-none`}
          >
            <option value="" disabled>
              {t('form.select_placeholder', { label })}
            </option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {t(`form.${option.value}`, option.label)}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg
              className={`h-5 w-5 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen flex flex-col relative">
      <SEO />
      <LandingBackground />
      
      <div className="container mx-auto px-4 py-6 sm:py-4 mt-8 sm:mt-10">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-4 sm:mb-6">
            <h1 className={`main-title mb-2 sm:mb-3 ${getFontClass()}`}>
              {t('yearly.title')}
            </h1>
          </div>
          
          <div className={`feature-card relative ${theme === 'light' ? 'bg-white/90' : 'bg-[#0D0C0F]/95'} backdrop-blur-xl p-6 md:p-8 rounded-2xl`}>
            <style>
              {`
                .feature-card {
                  position: relative;
                  border: 1px solid rgba(236, 72, 153, ${theme === 'light' ? '0.2' : '0.3'});
                  box-shadow: 
                    0 0 0 1px rgba(168, 85, 247, ${theme === 'light' ? '0.15' : '0.2'}),
                    0 0 15px rgba(168, 85, 247, ${theme === 'light' ? '0.1' : '0.15'}),
                    0 0 30px rgba(236, 72, 153, ${theme === 'light' ? '0.1' : '0.15'}),
                    inset 0 0 15px rgba(168, 85, 247, ${theme === 'light' ? '0.05' : '0.1'});
                }
                .feature-card::before {
                  content: '';
                  position: absolute;
                  inset: -1px;
                  padding: 1px;
                  background: linear-gradient(
                    135deg,
                    rgba(168, 85, 247, ${theme === 'light' ? '0.4' : '0.5'}),
                    rgba(236, 72, 153, ${theme === 'light' ? '0.4' : '0.5'})
                  );
                  -webkit-mask: 
                    linear-gradient(#fff 0 0) content-box, 
                    linear-gradient(#fff 0 0);
                  -webkit-mask-composite: xor;
                  mask-composite: exclude;
                  pointer-events: none;
                  border-radius: 1rem;
                }
                .feature-card::after {
                  content: '';
                  position: absolute;
                  inset: 0;
                  background: 
                    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, ${theme === 'light' ? '0.1' : '0.15'}), transparent 70%),
                    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, ${theme === 'light' ? '0.1' : '0.15'}), transparent 70%);
                  pointer-events: none;
                  border-radius: 1rem;
                }
              `}
            </style>
            <p className={`${theme === 'light' ? 'text-gray-700' : 'text-white/80'} mb-6 text-center ${getFontClass()}`}>
              {t('yearly.description')}
            </p>
            
            <form onSubmit={handleButtonClick} className="space-y-6">
              {/* 出生日期 */}
              <div className="space-y-2">
                <DateInput
                  label={t('form.birthdate')}
                  value={formData.birthDate}
                  onChange={(value) => setFormData({ ...formData, birthDate: value })}
                />
              </div>
              
              {/* 性别 */}
              <div className="space-y-2">
                <label className={`block ${theme === 'light' ? 'text-gray-800' : 'text-white'} text-base ${getFontClass()}`}>{t('form.gender')}</label>
                <div className="grid grid-cols-2 gap-3">
                  <label 
                    className={`
                      relative flex items-center justify-center p-3 rounded-xl cursor-pointer transition-all duration-300
                      ${formData.gender === 'male' 
                        ? `bg-gradient-to-br from-purple-500/90 to-pink-500/90 shadow-md shadow-purple-500/30 ${theme === 'light' ? 'text-white' : 'text-white'}` 
                        : `${theme === 'light' ? 'bg-white/80 text-gray-700' : 'bg-black/20 text-gray-200'} border ${theme === 'light' ? 'border-purple-400/30' : 'border-purple-500/20'} hover:border-purple-500/40`}
                    `}
                  >
                    <input
                      type="radio"
                      className="sr-only"
                      name="gender"
                      value="male"
                      checked={formData.gender === 'male'}
                      onChange={() => setFormData({ ...formData, gender: 'male' })}
                    />
                    <span className={`${getFontClass()} text-center`}>{t('form.gender_male')}</span>
                    {formData.gender === 'male' && (
                      <div className="absolute top-1 right-1">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    )}
                  </label>
                  
                  <label 
                    className={`
                      relative flex items-center justify-center p-3 rounded-xl cursor-pointer transition-all duration-300
                      ${formData.gender === 'female' 
                        ? `bg-gradient-to-br from-purple-500/90 to-pink-500/90 shadow-md shadow-purple-500/30 ${theme === 'light' ? 'text-white' : 'text-white'}` 
                        : `${theme === 'light' ? 'bg-white/80 text-gray-700' : 'bg-black/20 text-gray-200'} border ${theme === 'light' ? 'border-purple-400/30' : 'border-purple-500/20'} hover:border-purple-500/40`}
                    `}
                  >
                    <input
                      type="radio"
                      className="sr-only"
                      name="gender"
                      value="female"
                      checked={formData.gender === 'female'}
                      onChange={() => setFormData({ ...formData, gender: 'female' })}
                    />
                    <span className={`${getFontClass()} text-center`}>{t('form.gender_female')}</span>
                    {formData.gender === 'female' && (
                      <div className="absolute top-1 right-1">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    )}
                  </label>
                </div>
              </div>
              
              {/* 职业 */}
              {renderSelect(t('form.occupation'), formData.occupation, (value) => setFormData({ ...formData, occupation: value }), occupationOptions)}
              
              {/* 感情状态 */}
              {renderSelect(t('form.relationship'), formData.relationship, (value) => setFormData({ ...formData, relationship: value }), relationshipOptions)}
              
              {/* 统一错误提示 */}
              {Object.keys(errors).length > 0 && (
                <div className={`p-4 ${theme === 'light' ? 'bg-red-50' : 'bg-black/30'} border ${theme === 'light' ? 'border-red-400/50' : 'border-red-500/50'} rounded-xl`}>
                  <p className={`text-red-500 text-center ${getFontClass()}`}>{t('form.error_message')}</p>
                </div>
              )}
              
              {/* 提交按钮 */}
              <button
                type="button"
                onClick={handleButtonClick}
                className={`w-full py-3 px-4 font-medium rounded-xl shadow-lg transition-all duration-300 ${getButtonStyle()}`}
              >
                <span className={getFontClass()}>{getButtonText()}</span>
              </button>
            </form>
          </div>
        </div>
      </div>
      
      <Footer />

      {/* VIP提示弹窗 */}
      <VipPromptDialog
        isOpen={showVipPrompt}
        onCancel={() => setShowVipPrompt(false)}
      />
    </div>
  );
};

export default YearlyFortune; 