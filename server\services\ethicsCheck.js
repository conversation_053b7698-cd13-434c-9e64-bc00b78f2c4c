/**
 * 伦理问题检测服务
 * 用于检测用户提问是否涉及自杀、自残、伤害他人、健康咨询等伦理问题
 */

const { zhipuAPI, qwenAPI } = require('./apiClients');

/**
 * 获取适合伦理问题类别的干预模板
 * @param {string} category 伦理问题类别
 * @param {string} language 语言代码
 * @returns {string} 干预消息模板
 */
function getInterventionTemplate(category, language) {
  // 简体中文模板
  if (language === 'zh-CN') {
    // 暴力、色情等违法犯罪相关
    if (category === '赌博' || category === '毒品') {
      return `⚠️ 内容提示：
我们检测到您的问题可能涉及不适宜的内容。根据相关法律法规：  
1. AI塔罗无法回应涉及违法或不良内容的问题  
2. 根据《网络信息内容生态治理规定》，互联网信息服务提供者不得生成、复制、发布、传播法律法规禁止的信息  
3. 如您有法律相关问题，建议咨询专业律师或相关执法部门  
（本回复已根据《互联网信息服务管理办法》相关规定生成）`;
    }
    // 政治立场、敏感话题相关
    else if (category === '政治立场') {
      return `⚠️ 提示： 
我们检测到您的问题可能涉及政治敏感话题。根据相关法律法规：  
1. AI塔罗作为娱乐工具，无法对政治立场、国家政策、历史评价等敏感话题提供解读  
2. 根据《网络信息内容生态治理规定》，互联网信息服务应当坚持正确政治方向、舆论导向和价值取向  
3. 建议您重新调整问题，将焦点放在个人成长、日常困惑等方面  
（本回复已根据相关法律法规和平台规定生成）`;
    }
    // 默认响应
    else {
      return `⚠️ 提示：  
很抱歉，我们无法对您的问题提供塔罗解读。您的问题可能：  
1. 涉及法律法规禁止的内容  
2. 超出AI塔罗师的能力范围  
3. 需要专业人士的指导和建议  
建议您调整问题内容，或咨询相关专业人士获取帮助。
（本回复已根据相关法律法规要求生成）`;
    }
  }
  // 繁体中文模板
  else if (language === 'zh-TW') {
    // 暴力、色情等违法犯罪相关
    if (category === '賭博' || category === '毒品') {
      return `⚠️ 內容提示：  
我們檢測到您的問題可能涉及不適宜的內容。根據相關法律法規：  
1. AI塔羅無法回應涉及違法或不良內容的問題  
2. 根據網絡信息內容生態治理相關規定，互聯網信息服務提供者不得生成、複製、發布、傳播法律法規禁止的信息  
3. 如您有法律相關問題，建議諮詢專業律師或相關執法部門  
（本回覆已根據相關法規要求生成）`;
    }
    // 政治立场、敏感话题相关
    else if (category === '政治立場') {
      return `⚠️ 提示：  
我們檢測到您的問題可能涉及政治敏感話題。根據相關法律法規：  
1. AI塔羅作為娛樂工具，無法對政治立場、國家政策、歷史評價等敏感話題提供解讀  
2. 根據網絡信息內容生態治理相關規定，互聯網信息服務應當堅持正確政治方向、輿論導向和價值取向  
3. 建議您重新調整問題，將焦點放在個人成長、日常困惑等方面  
（本回覆已根據相關法律法規和平台規定生成）`;
    }
    // 默认响应
    else {
      return `⚠️ 提示：  
很抱歉，我們無法對您的問題提供塔羅解讀。您的問題可能：  
1. 涉及法律法規禁止的內容  
2. 超出AI塔羅師的能力範圍  
3. 需要專業人士的指導和建議  
建議您調整問題內容，或諮詢相關專業人士獲取幫助。
（本回覆已根據相關法律法規要求生成）`;
    }
  }
  // 英文模板
  else if (language === 'en' || language.startsWith('en-')) {
    // 暴力、色情等违法犯罪相关
    if (category === 'Gambling' || category === 'Drugs') {
      return `⚠️ Content Notice:  
We've detected that your question may involve inappropriate content. According to relevant laws and regulations:  
1. AI Tarot cannot respond to questions involving illegal or inappropriate content  
2. Internet information service providers are prohibited from generating, copying, publishing, or disseminating information that is prohibited by laws and regulations  
3. If you have legal concerns, we recommend consulting a professional lawyer or relevant law enforcement agencies  
(This response has been generated in accordance with relevant regulations)`;
    }
    // 政治立场、敏感话题相关
    else if (category === 'Political Stance') {
      return `⚠️ Notice:  
We've detected that your question may involve politically sensitive topics. According to relevant platform policies:  
1. AI Tarot, as an entertainment tool, cannot provide readings about political stances, national policies, historical evaluations, or other sensitive topics  
2. Our platform maintains neutrality on political matters and follows regulatory requirements for content moderation  
3. We suggest refocusing your question on personal growth, daily concerns, or other appropriate topics  
(This response has been generated in accordance with relevant regulations and platform policies)`;
    }
    // 默认响应
    else {
      return `⚠️ Notice:  
We apologize, but we cannot provide a tarot reading for your question. Your question may:  
1. Involve content prohibited by laws and regulations  
2. Be beyond the capabilities of AI Tarot readers  
3. Require guidance and advice from professionals  
We suggest adjusting your question content or consulting relevant professionals for assistance.
(This response has been generated in accordance with relevant laws and regulations)`;
    }
  }
  // 日文模板
  else if (language === 'ja' || language.startsWith('ja-')) {
    // 暴力、色情等违法犯罪相关
    if (category === 'ギャンブル' || category === '薬物') {
      return `⚠️ コンテンツに関するお知らせ：  
あなたの質問が不適切なコンテンツを含む可能性があることを検出しました。関連法規に従い：  
1. AIタロットは違法または不適切なコンテンツに関する質問に回答できません  
2. インターネット情報サービスプロバイダーは、法律や規制によって禁止されている情報を生成、複製、公開、または普及することが禁止されています  
3. 法的懸念がある場合は、専門の弁護士または関連法執行機関に相談することをお勧めします  
（この回答は関連規制に従って生成されています）`;
    }
    // 政治立场、敏感话题相关
    else if (category === '政治的立場') {
      return `⚠️ お知らせ：  
あなたの質問が政治的に敏感な話題を含む可能性があることを検出しました。関連プラットフォームポリシーに従い：  
1. エンターテイメントツールとしてのAIタロットは、政治的立場、国家政策、歴史的評価、またはその他の敏感な話題についての解読を提供することができません  
2. 当プラットフォームは政治的問題に対して中立性を維持し、コンテンツ管理に関する規制要件に従います  
3. 個人の成長、日常の懸念事項、またはその他の適切な話題に質問の焦点を当て直すことをお勧めします  
（この回答は関連規制およびプラットフォームポリシーに従って生成されています）`;
    }
    // 默认响应
    else {
      return `⚠️ お知らせ：  
申し訳ありませんが、ご質問に対するタロットリーディングを提供することができません。あなたの質問は：  
1. 法律や規制によって禁止されているコンテンツに関わる可能性があります  
2. AIタロットリーダーの能力範囲を超えている可能性があります  
3. 専門家のガイダンスとアドバイスが必要な可能性があります  
質問内容を調整するか、関連する専門家に相談して援助を求めることをお勧めします。
（この回答は関連法規に従って生成されています）`;
    }
  }
  // 默认使用简体中文模板
  else {
    return getInterventionTemplate(category, 'zh-CN');
  }
}

/**
 * 检测用户提问是否涉及伦理问题
 * @param {string} question 用户提问
 * @param {string} language 语言代码
 * @returns {Promise<Object>} 检测结果，包含hasSensitiveContent和interventionMessage字段
 */
async function checkEthicalIssues(question, language = 'zh-CN') {
  try {
    // console.log('\n正在检测问题是否涉及安全问题...');
    
    // 根据不同语言选择不同的提示词
    let systemPrompt;
    // console.log(language)
    // 简体中文提示词
    if (language === 'zh-CN') {
      systemPrompt = `你是一个内容安全审核助手，负责检测用户问题中是否涉及以下敏感内容：
1.赌博（不包括财运）
2.毒品
3.政治立场

你必须直接返回JSON格式数据，格式如下：
{
  "whetherOrNot": true/false,
  "category": "赌博/毒品/政治立场/无安全问题",
  "confidence": 0.0-1.0之间的数值,
  "reason": "简要分析原因"
}
注意：不包含任何其他代码、代码块标记或解释、markdown格式。`;
    }
    // 繁体中文提示词
    else if (language === 'zh-TW') {
      systemPrompt = `你是一個內容安全審核助手，負責檢測用戶問題中是否涉及以下敏感內容：
1.賭博（不包括財運）
2.毒品
3.政治立場

你必須直接返回JSON格式數據，格式如下：
{
  "whetherOrNot": true/false,
  "category": "賭博/毒品/政治立場/無安全問題",
  "confidence": 0.0-1.0之間的數值,
  "reason": "簡要分析原因"
}
注意：不包含任何其他代碼、代碼塊標記或解釋、markdown格式。`;
    }
    // 英文提示词
    else if (language === 'en') {
      systemPrompt = `You are a content safety review assistant, responsible for detecting whether user questions contain any of the following sensitive content:
1. Gambling (excluding financial fortune inquiries)
2. Drugs
3. Political stance

You must directly return data in JSON format as follows:
{
  "whetherOrNot": true/false,
  "category": "Gambling/Drugs/Political Stance/No Ethical Issue",
  "confidence": value between 0.0-1.0,
  "reason": "Brief analysis"
}
Note: Do not include any other code, code block markers, explanations, or markdown formatting.`;
    }
    // 日文提示词
    else if (language === 'ja') {
      systemPrompt = `あなたはコンテンツ安全性検査アシスタントであり、ユーザーの質問が次の敏感なコンテンツを含むかどうかを検出する責任があります：
1.ギャンブル（金運占いは除く）
2.薬物
3.政治的立場

以下のようにJSON形式でデータを直接返す必要があります：
{
  "whetherOrNot": true/false,
  "category": "ギャンブル/薬物/政治的立場/倫理的問題なし",
  "confidence": 0.0-1.0の間の値,
  "reason": "簡単な分析"
}
注意：他のコード、コードブロックマーカー、説明、またはmarkdown形式を含めないでください。`;
    }
    // 默认使用繁体中文
    else {
      systemPrompt = `你是一個內容安全審核助手，負責檢測用戶問題中是否涉及以下敏感內容：
1.賭博（不包括財運）
2.毒品
3.政治立場

你必須直接返回JSON格式數據，格式如下：
{
  "whetherOrNot": true/false,
  "category": "賭博/毒品/政治立場/無安全問題",
  "confidence": 0.0-1.0之間的數值,
  "reason": "簡要分析原因"
}
注意：不包含任何其他代碼、代碼塊標記或解釋、markdown格式。`;
    }

    const userPrompt = `用户问题: ${question}`;
    // console.log(language || user.language || 'zh-TW')
    const response = await qwenAPI.post('chat/completions', {
      model: "qwen-turbo-latest",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ],
      stream: false,
      response_format: { type: "json_object" },
      parameters: {
        response_language: language || user.language || 'zh-TW'
      }
    });


    if (!response.data || !response.data.choices || !response.data.choices[0]) {
      console.error('安全检测API响应格式错误:', response.data);
      return { whetherOrNot: false };
    }

    const content = response.data.choices[0].message.content;
    console.log('\n安全检测API响应:', content);
    
    // 记录输入和输出token数量
    const inputTokens = response.data.usage?.prompt_tokens || 0;
    const outputTokens = response.data.usage?.completion_tokens || 0;
    // console.log(`安全检测API使用: 输入tokens: ${inputTokens}, 输出tokens: ${outputTokens}`);

    try {
      // 清理各种可能的代码块标记和注释
      let cleanContent = content.replace(/```json\s*|\s*```|```python\s*|\s*```|```\s*|\s*```/g, '').trim();
      
      // 尝试提取JSON对象
      const jsonMatch = cleanContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanContent = jsonMatch[0];
      }
      
      // 移除JSON中的注释
      cleanContent = cleanContent.replace(/\/\/.*$/gm, '').trim();
      
      // console.log('处理后的JSON内容:', cleanContent);
      
      const result = JSON.parse(cleanContent);
      
      // 如果检测到伦理问题，添加干预消息
      if (result.whetherOrNot) {
        // 检查confidence是否大于0.9，否则将whetherOrNot设为false
        if (result.confidence > 0.9) {
          // 使用新的函数获取针对特定类别的干预模板
          result.interventionMessage = getInterventionTemplate(result.category, language);
        } else {
          // confidence不够高，将whetherOrNot设为false
          result.whetherOrNot = false;
          delete result.interventionMessage;
        }
        // 添加token统计信息
        result.input_tokens = inputTokens;
        result.output_tokens = outputTokens;
        return result;
      }
      
      // 没有伦理问题时也添加token统计信息
      result.input_tokens = inputTokens;
      result.output_tokens = outputTokens;
      return result;
    } catch (parseError) {
      console.error('解析安全检测API响应失败:', parseError);
      console.error('解析失败的内容:', content);
      
      // API调用成功但解析失败时，认为是潜在问题
      return {
        whetherOrNot: false,
        category: '安全检查',
        confidence: 0.85,
        reason: '安全检测结果解析失败，为保障安全，默认判定为有伦理问题',
        interventionMessage: getInterventionTemplate('默认', language),
        input_tokens: inputTokens,
        output_tokens: outputTokens
      };
    }
  } catch (error) {
    console.error('安全问题检测失败:', error);
    
    // API调用失败时，直接返回伦理问题及默认干预消息
    return {
      whetherOrNot: true,
      category: '安全检查',
      confidence: 1.0,
      reason: 'API调用失败，为保障安全，默认判定为有伦理问题',
      interventionMessage: getInterventionTemplate('默认', language),
      input_tokens: 0,
      output_tokens: 0
    };
  }
}

module.exports = {
  checkEthicalIssues
}; 