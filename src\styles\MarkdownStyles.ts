// 通用的Markdown样式（暗色主题）
export const markdownStyles = `
.markdown-content {
  color: inherit;
  line-height: 1.6;
}

.markdown-content h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: rgba(139, 92, 246, 0.9);
}

.markdown-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.4rem;
  margin-bottom: 0.8rem;
  color: rgba(139, 92, 246, 0.9);
}

.markdown-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.3rem;
  margin-bottom: 0.6rem;
  color: rgba(139, 92, 246, 0.85);
}

.markdown-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 1.2rem;
  margin-bottom: 0.5rem;
  color: rgba(139, 92, 246, 0.8);
}

.markdown-content p {
  margin-bottom: 1rem;
}

.markdown-content strong {
  font-weight: 600;
  color: rgba(139, 92, 246, 0.9);
}

.markdown-content em {
  font-style: italic;
}

.markdown-content ul {
  list-style-type: disc;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content ol {
  list-style-type: decimal;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content li {
  margin-bottom: 0.5rem;
}

.markdown-content a {
  color: rgba(139, 92, 246, 1);
  text-decoration: underline;
  transition: color 0.2s ease;
}

.markdown-content a:hover {
  color: rgba(124, 58, 237, 1);
}

.markdown-content blockquote {
  border-left: 3px solid rgba(139, 92, 246, 0.6);
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
}

.markdown-content code {
  font-family: monospace;
  background-color: rgba(30, 30, 30, 0.5);
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.9em;
}

.markdown-content pre {
  background-color: rgba(30, 30, 30, 0.5);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  font-size: 0.9em;
}

.markdown-content hr {
  border: 0;
  height: 1px;
  background-color: rgba(139, 92, 246, 0.3);
  margin: 2rem 0;
}

.markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.markdown-content th {
  background-color: rgba(139, 92, 246, 0.2);
  padding: 0.5rem;
  border: 1px solid rgba(139, 92, 246, 0.3);
  text-align: left;
}

.markdown-content td {
  padding: 0.5rem;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.markdown-content tr:nth-child(even) {
  background-color: rgba(139, 92, 246, 0.05);
}
`;

// 浅色主题的Markdown样式
export const lightModeMarkdownStyles = `
.markdown-content {
  color: #1f2937;
}

.markdown-content h1 {
  color: rgba(91, 33, 182, 0.9);
}

.markdown-content h2 {
  color: rgba(91, 33, 182, 0.9);
}

.markdown-content h3 {
  color: rgba(91, 33, 182, 0.85);
}

.markdown-content h4 {
  color: rgba(91, 33, 182, 0.8);
}

.markdown-content strong {
  color: rgba(91, 33, 182, 0.9);
}

.markdown-content blockquote {
  color: rgba(55, 55, 55, 0.8);
  border-left-color: rgba(91, 33, 182, 0.6);
}

.markdown-content code {
  background-color: rgba(240, 240, 240, 0.8);
  color: #3b3b3b;
}

.markdown-content pre {
  background-color: rgba(240, 240, 240, 0.8);
}

.markdown-content hr {
  background-color: rgba(91, 33, 182, 0.3);
}

.markdown-content th {
  background-color: rgba(91, 33, 182, 0.1);
  border-color: rgba(91, 33, 182, 0.2);
}

.markdown-content td {
  border-color: rgba(91, 33, 182, 0.1);
}

.markdown-content tr:nth-child(even) {
  background-color: rgba(91, 33, 182, 0.03);
}
`; 