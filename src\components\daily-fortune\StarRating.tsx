import React from 'react';

interface StarRatingProps {
  rating: number;  // 1-5之间的评分
  maxRating?: number;  // 最大评分，默认为5
  size?: 'sm' | 'md' | 'lg' | 'xl';  // 星星尺寸
  className?: string;  // 添加可选的className属性
}

/**
 * 星级评分组件
 * 根据评分值显示相应数量的星星
 */
const StarRating: React.FC<StarRatingProps> = ({
  rating,
  maxRating = 5,
  size = 'md',
  className = ''
}) => {
  // 确保评分在有效范围内
  const validRating = Math.max(0, Math.min(rating, maxRating));
  
  // 根据尺寸设置星星大小
  const starSizeClass = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8'
  }[size];
  
  // 创建星星数组
  const stars = Array.from({ length: maxRating }, (_, index) => {
    const starFilled = index < validRating;
    
    return (
      <svg
        key={index}
        className={`${starSizeClass} ${starFilled ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
        />
      </svg>
    );
  });

  return (
    <div className={`flex items-center ${className}`}>
      {stars}
    </div>
  );
};

export default StarRating; 