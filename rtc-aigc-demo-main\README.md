# 实时语音对话系统

这是一个基于火山引擎语音服务的实时语音对话系统，包含前端React应用和后端Flask API服务。本系统支持实时语音输入、AI语音对话和实时音频输出。

## 系统架构

系统由以下主要组件构成：

1. **前端应用**：基于React的Web应用，提供用户界面和音频捕获功能
2. **后端API服务**：基于Flask的RESTful API和WebSocket服务，处理会话管理和音频流转发
3. **语音对话服务**：基于火山引擎的实时语音对话服务，处理语音识别、AI对话和语音合成

通信流程：
1. 前端通过WebSocket实时发送用户音频流到后端
2. 后端转发音频流到火山引擎语音对话服务
3. 火山引擎处理语音识别、AI对话生成和语音合成
4. 后端接收合成的音频并实时转发回前端
5. 前端播放收到的音频流

## 目录结构

```
├── backend/                    # 后端服务目录
│   ├── app/                    # Flask应用
│   │   ├── __init__.py         # 应用初始化
│   │   ├── api.py              # API路由
│   │   ├── socket_handlers.py  # WebSocket处理程序
│   │   ├── config.py           # 配置
│   │   ├── error_handler.py    # 错误处理
│   │   ├── logger.py           # 日志管理
│   │   ├── session_manager.py  # 会话管理
│   │   └── realtime_dialog/    # 实时对话模块
│   │       ├── __init__.py     # 模块初始化
│   │       ├── client.py       # 对话客户端
│   │       ├── dialog_service.py # 对话服务
│   │       └── protocol.py     # 通信协议
│   ├── requirements.txt        # 依赖列表
│   └── run.py                  # 启动脚本
├── src/                        # 前端源代码
│   ├── app/                    # 应用核心
│   │   ├── api.ts              # API定义
│   │   ├── base.ts             # 基础功能
│   │   ├── index.ts            # 入口
│   │   ├── type.ts             # 类型定义
│   │   └── voiceApi.ts         # 语音API客户端
│   └── components/             # React组件
└── README.md                   # 主文档
```

## 安装与配置

### 后端服务

1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

2. 配置API密钥

编辑`backend/app/config.py`文件，填写火山引擎API密钥：

```python
VOLCANO_CONFIG = {
    "base_url": "wss://openspeech.bytedance.com/api/v3/realtime/dialogue",
    "headers": {
        "X-Api-App-ID": "填写您的App ID",
        "X-Api-Access-Key": "填写您的Access Key",
        "X-Api-Resource-Id": "volc.speech.dialog",  # 固定值
        "X-Api-App-Key": "PlgvMymc7f3tQnJ6",  # 固定值
    }
}
```
  
3. 启动后端服务

```bash
cd backend
python run.py
```

服务将在`http://localhost:5000`启动。

### 前端应用

1. 安装依赖

```bash
npm install
```

2. 配置后端API地址

创建`.env.local`文件，配置API地址：

```
REACT_APP_API_BASE_URL=http://localhost:5000/api
REACT_APP_WS_BASE_URL=http://localhost:5000
```

3. 启动开发服务器

```bash
npm start
```

## 使用方法

前端应用提供了`VoiceChat`接口，可以通过以下方式使用：

```typescript
import API from '@/app';

// 初始化语音聊天
const startChat = async () => {
  const result = await API.VoiceChat.startVoiceChat('user123');
  if (result.success) {
    console.log('语音会话已启动');
  }
};

// 结束语音聊天
const stopChat = async () => {
  const result = await API.VoiceChat.stopVoiceChat();
  if (result.success) {
    console.log('语音会话已结束');
  }
};

// 发送音频数据
const sendAudio = async (audioBase64) => {
  await API.VoiceChat.sendAudio(audioBase64);
};

// 监听音频返回
API.VoiceChat.addAudioListener((data) => {
  // 处理返回的音频数据
  const audioBase64 = data.audio;
  // 播放音频...
});

// 监听文本返回
API.VoiceChat.addTextListener((data) => {
  // 显示文本
  console.log('收到文本:', data.text);
});

// 监听事件
API.VoiceChat.addEventListener((data) => {
  console.log('收到事件:', data.event);
});

// 监听错误
API.VoiceChat.addErrorListener((data) => {
  console.error('发生错误:', data.message);
});
```

## API文档

完整的API文档请参见：
- [后端API文档](backend/README.md)

## 技术栈

- **前端**: React, TypeScript, Socket.io客户端
- **后端**: Python, Flask, Flask-SocketIO
- **第三方服务**: 火山引擎语音对话服务

## 常见问题

### 1. 无法连接到语音服务

- 检查火山引擎API密钥是否正确配置
- 确保您的网络可以访问火山引擎服务

### 2. 音频不工作

- 检查浏览器是否有麦克风权限
- 确认WebSocket连接是否正常建立
- 检查音频格式是否符合要求（PCM, 16kHz, 16bit）

### 3. 服务启动失败

- 检查端口5000是否被占用
- 确保所有依赖都已正确安装

## 许可证

本项目采用BSD-3-Clause许可证。详情请参阅LICENSE文件。