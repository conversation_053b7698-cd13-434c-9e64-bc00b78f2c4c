// import FingerprintJS from '@fingerprintjs/fingerprintjs';
import FingerprintJS from '@fingerprintjs/fingerprintjs';

// 创建指纹实例
let fpPromise: Promise<any> | null = null;

/**
 * 获取浏览器指纹 - 已启用，并优化长度
 * @returns 返回优化后的浏览器指纹
 */
export const getBrowserFingerprint = async (): Promise<string> => {
  try {
    if (!fpPromise) {
      // 初始化指纹实例
      fpPromise = FingerprintJS.load().catch(err => {
        throw err;
      });
    }
    
    // 添加超时处理，确保不会无限等待
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('获取指纹超时(5秒)')), 5000);
    });
    
    // 使用Promise.race确保请求不会无限等待
    const fp = await Promise.race([fpPromise, timeoutPromise]);
    
    // 获取指纹结果
    const result = await fp.get({ extendedResult: true });
    
    if (!result || !result.visitorId) {
      throw new Error('指纹生成失败: 没有获取到有效的visitorId');
    }
    
    // 从canvas值创建哈希值而不是使用完整字符串
    let canvasHash = '';
    try {
      if (result.components?.canvas?.value) {
        const canvasValue = result.components.canvas.value;
        // 简化canvas指纹：创建一个简单的哈希
        const canvasString = typeof canvasValue === 'object' ? 
          JSON.stringify(canvasValue).substring(0, 50) : // 仅取前50个字符
          String(canvasValue).substring(0, 50);
          
        // 简单的哈希函数
        let hash = 0;
        for (let i = 0; i < canvasString.length; i++) {
          const char = canvasString.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // 转换为32位整数
        }
        canvasHash = Math.abs(hash).toString(36); // 转为较短的base36字符串
      }
    } catch (e) {
      canvasHash = 'c-err';
    }
    
    // 简化屏幕分辨率格式
    let resolution = '';
    try {
      if (result.components?.screenResolution?.value) {
        if (Array.isArray(result.components.screenResolution.value)) {
          resolution = result.components.screenResolution.value.join('x');
        } else {
          resolution = String(result.components.screenResolution.value).replace(/\s+/g, '');
        }
      }
    } catch (e) {
      resolution = 'r-err';
    }
    
    // 用户代理哈希
    let uaHash = '';
    try {
      const ua = navigator.userAgent || '';
      let hash = 0;
      for (let i = 0; i < ua.length; i++) {
        const char = ua.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      uaHash = Math.abs(hash).toString(36);
    } catch (e) {
      uaHash = 'ua-err';
    }
    
    // 优化后的指纹：更短但仍保持唯一性
    const optimizedFingerprint = [
      result.visitorId,     // 保留原始visitorId（这是最关键的部分）
      canvasHash,           // Canvas哈希（大大缩短）
      uaHash,               // UserAgent哈希（缩短）
      resolution            // 屏幕分辨率（格式简化）
    ].filter(Boolean).join('-');
    
    // 保存到sessionStorage以便同一会话中重复使用
    try {
      sessionStorage.setItem('browserFingerprint', optimizedFingerprint);
    } catch (e) {
      // 处理无法保存到sessionStorage的情况
    }
    return optimizedFingerprint;
  } catch (error) {
    // 尝试从sessionStorage获取之前保存的指纹
    try {
      const savedFingerprint = sessionStorage.getItem('browserFingerprint');
      if (savedFingerprint) {
        return savedFingerprint;
      }
    } catch (e) {
      // 处理无法从sessionStorage获取指纹的情况
    }
    
    // 如果获取失败，返回一个基于当前时间的随机值
    const fallbackId = `fallback-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    return fallbackId;
  }
}; 