import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { StarRating } from './index';
import CdnLazyImage from '../../components/CdnLazyImage';

interface FortuneResults {
  intro?: string;    // 开场语
  overall?: string;  // 今日能量总览
  love?: string;     // 爱情运势
  career?: string;   // 事业运势
  wealth?: string;   // 财富运势
  health?: string;   // 健康运势
  mystery?: string;  // 今日神秘指引
}

interface FortuneRatings {
  overall?: number;  // 今日能量总览评分
  love?: number;     // 爱情运势评分
  career?: number;   // 事业运势评分
  wealth?: number;   // 财富运势评分
  health?: number;   // 健康运势评分
}

interface AnalysisSectionProps {
  getFontClass: () => string;
  fortuneResults?: FortuneResults;
  fortuneRatings?: FortuneRatings;
  isMainTitle?: boolean;
  hideTitle?: boolean;
}

const AnalysisSection: React.FC<AnalysisSectionProps> = ({ getFontClass, fortuneResults = {}, fortuneRatings = {}, isMainTitle = false, hideTitle = false }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  
  // 运势解析内容配置
  const analysisItems = [
    {
      id: 'overall',
      title: t('daily.analysis.overall.title', '整體運勢：今日能量總覽'),
      description: fortuneResults.overall || t('daily.analysis.overall.description', '想知道今天整體運勢的基調嗎？這裡是今日能量的總結，為你提供一個清晰的方向感，指引你今日的思考與行動。'),
      image: '/images-optimized/daily-fortune/overall-energy.webp',
      alt: 'Overall Energy Forecast'
    },
    {
      id: 'love',
      title: t('daily.analysis.love.title', '愛情運勢占卜'),
      description: fortuneResults.love || t('daily.analysis.love.description', '正在為愛情煩惱嗎？無論你是單身、戀愛中或關係複雜，這裡的愛情運勢占卜將揭示今日情感世界的微妙變化，為你的愛情生活提供一盞明燈。'),
      image: '/images-optimized/daily-fortune/love-tarot.webp',
      alt: 'Love Tarot Reading'
    },
    {
      id: 'career',
      title: t('daily.analysis.career.title', '事業運勢與工作指引'),
      description: fortuneResults.career || t('daily.analysis.career.description', '在職場的十字路口感到迷惘？從日常工作挑戰到長遠的求職運勢占卜，此處的分析將幫助你抓住機遇、避開陷阱，讓你的專業能力大放異彩。'),
      image: '/images-optimized/daily-fortune/career-guidance.webp',
      alt: 'Career Guidance'
    },
    {
      id: 'wealth',
      title: t('daily.analysis.wealth.title', '今日財富運勢'),
      description: fortuneResults.wealth || t('daily.analysis.wealth.description', '想測運勢中的財富走向？這裡不僅是關於金錢，更是關於價值與機會。洞察今日的財富運勢，學習如何做出更明智的財務決策，吸引豐盛能量。'),
      image: '/images-optimized/daily-fortune/money-wealth.webp',
      alt: 'Wealth Forecast'
    },
    {
      id: 'health',
      title: t('daily.analysis.health.title', '健康運勢提醒'),
      description: fortuneResults.health || t('daily.analysis.health.description', '你的身心靈是你最寶貴的資產。每日運勢中的健康提醒，關注你今日的能量水平與情緒狀態，提供實用的建議，助你保持最佳平衡。'),
      image: '/images-optimized/daily-fortune/health-wellness.webp',
      alt: 'Health and Wellness'
    },
    {
      id: 'mystery',
      title: t('daily.analysis.mystery.title', '今日神秘指引'),
      description: fortuneResults.mystery || t('daily.analysis.mystery.description', '每張塔羅牌都隱藏著一個能量密碼。這是專屬於你、今日限定的「神秘指引」。它可能是一個物品、一個顏色、或一個概念。解鎖它，讓它成為你創造幸運一天的秘密武器。'),
      image: '/images-optimized/daily-fortune/mystic-key2.webp',
      alt: 'Mystic Guidance'
    }
  ];

  return (
    <div className={`max-w-6xl mx-auto ${!isMainTitle ? 'mt-6 sm:mt-8' : ''} px-4 sm:px-6 relative z-10`} id="fortune-analysis">
      {/* 开场语部分 */}
      {fortuneResults.intro && (
        <div className={`${theme === 'light' ? 'bg-white/90' : 'bg-gray-800/80'} p-6 rounded-xl shadow-lg mb-10 text-left`}>
          <p className={`text-lg sm:text-xl ${getFontClass()} ${theme === 'light' ? 'text-gray-700' : 'text-gray-100'} whitespace-pre-line`}>
            {fortuneResults.intro}
          </p>
        </div>
      )}
      
      {!hideTitle && (
        <div className={`text-center ${isMainTitle ? 'mb-4 sm:mb-6' : 'mb-10'}`}>
          {isMainTitle ? (
            <h1 className={`main-title mb-2 sm:mb-3 ${getFontClass()} dark:text-white text-gray-900`}>
              {t('daily.analysis.title', '今日運勢深度解析')}
            </h1>
          ) : (
            <h2 className="text-2xl sm:text-3xl font-bold text-white">
              {t('daily.analysis.title', '今日運勢深度解析')}
            </h2>
          )}
          
          {isMainTitle ? (
            <p className={`text-base sm:text-lg dark:text-purple-300 text-purple-600 italic ${getFontClass()}`}>
              {t('daily.analysis.subtitle', '專業塔羅解讀結合星象能量，為您提供全方位今日運勢解析')}
            </p>
          ) : (
            <>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
              <p className={`${theme === 'light' ? 'text-gray-700' : 'text-gray-300'} text-lg max-w-3xl mx-auto`}>
                {t('daily.analysis.subtitle', '專業塔羅解讀結合星象能量，為您提供全方位今日運勢解析')}
              </p>
            </>
          )}
        </div>
      )}

      <div className="space-y-16 sm:space-y-24">
        {analysisItems.map((item, index) => (
          <div 
            key={item.id} 
            className="flex flex-col md:flex-row items-center gap-8 md:gap-12"
          >
            {/* 左侧图片区域 - 仅在偶数项显示 */}
            {index % 2 === 0 && (
              <div className="md:w-1/2 order-2 md:order-1 relative hidden md:block">
                <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                  <CdnLazyImage 
                    src={item.image}
                    alt={item.alt}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            )}
            
            {/* 中间文本区域 */}
            <div className="md:w-1/2 order-1 md:order-2 mt-0">
              <div className="flex flex-col md:flex-row md:items-center mb-4">
                <h3 className={`text-2xl sm:text-3xl font-bold ${
                  theme === 'light' ? 'text-gray-800' : 'text-white'
                }`}>
                  {item.title}
                </h3>
                {item.id !== 'intro' && item.id !== 'mystery' && fortuneRatings[item.id as keyof FortuneRatings] && (
                  <div className="md:ml-3 mt-2 md:mt-0">
                    <StarRating rating={fortuneRatings[item.id as keyof FortuneRatings] || 0} size="lg" className="md:hidden" />
                    <StarRating rating={fortuneRatings[item.id as keyof FortuneRatings] || 0} size="xl" className="hidden md:flex" />
                  </div>
                )}
              </div>
              <p className={`${
                theme === 'light' ? 'text-gray-600' : 'text-gray-300'
              } mb-6 text-base sm:text-lg whitespace-pre-line`}>
                {item.description}
              </p>
              
              {/* 移动端图片显示在文案下方 */}
              <div className="block md:hidden mb-6 mt-4">
                <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                  <CdnLazyImage 
                    src={item.image}
                    alt={item.alt}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
            
            {/* 右侧图片区域 - 仅在奇数项显示 */}
            {index % 2 === 1 && (
              <div className="md:w-1/2 order-2 md:order-3 relative hidden md:block">
                <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                  <CdnLazyImage 
                    src={item.image}
                    alt={item.alt}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default AnalysisSection; 