import React, { useEffect, useState } from 'react';
import { useUser } from '../contexts/UserContext';
import { 
  getPendingSubmissions,
  reviewSubmission,
  ShareSubmission
} from '../services/shareService';

// 扩展 ShareSubmission 类型以包含语言字段
interface ExtendedShareSubmission extends ShareSubmission {
  language?: string;
}
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import { toast } from 'react-hot-toast';
import { Eye, ChevronLeft, ChevronRight, Loader, CheckCircle, XCircle } from 'lucide-react';
import { Navigate, useNavigate } from 'react-router-dom';

const ShareSubmissionManagement: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const [submissions, setSubmissions] = useState<ExtendedShareSubmission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isReviewing, setIsReviewing] = useState<string | null>(null);
  const [reviewNote, setReviewNote] = useState('');
  const [selectedSubmission, setSelectedSubmission] = useState<ExtendedShareSubmission | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  
  // 检查用户是否为管理员
  const [isAdmin, setIsAdmin] = useState(false);
  const [isCheckingPermission, setIsCheckingPermission] = useState(true);

  // 检查用户是否有管理员权限
  useEffect(() => {
    if (!user) {
      setIsCheckingPermission(false);
      return;
    }

    // 硬编码特定邮箱为管理员
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    
    // 检查用户是否为管理员
    if (user.isAdmin || (user.email && adminEmails.includes(user.email))) {
      setIsAdmin(true);
    } else {
      toast.error('权限不足，需要管理员权限');
      // 3秒后重定向到首页
      setTimeout(() => {
        navigate('/');
      }, 3000);
    }
    setIsCheckingPermission(false);
  }, [user, navigate]);

  // 加载待审核的分享提交
  useEffect(() => {
    if (!user || !isAdmin) return;

    const fetchPendingSubmissions = async () => {
      setIsLoading(true);
      try {
        const offset = (currentPage - 1) * itemsPerPage;
        const result = await getPendingSubmissions(itemsPerPage, offset);
        // console.log('获取到的分享提交数据:', result); // 添加调试日志
        setSubmissions(result.submissions);
        // 假设API返回总数，如果没有，可以根据实际情况调整
        setTotalItems(result.submissions.length > 0 ? result.total || result.submissions.length : 0);
      } catch (error) {
        console.error('获取待审核分享失败:', error);
        toast.error('获取待审核分享失败');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPendingSubmissions();
  }, [user, isAdmin, currentPage, itemsPerPage]);

  // 处理审核操作
  const handleReview = async (id: string, status: 'approved' | 'rejected') => {
    if (!user) return;

    setIsReviewing(id);
    try {
      await reviewSubmission(id, status, reviewNote);

      // 更新本地状态，移除已审核的提交
      setSubmissions(prev => prev.filter(sub => sub.id !== id));
      
      toast.success(status === 'approved'
        ? '审核通过，已自动发放奖励，邮件通知已发送'
        : '已拒绝该分享提交，邮件通知已发送');
      
      // 关闭详情模态框
      if (isModalOpen) {
        setIsModalOpen(false);
        setSelectedSubmission(null);
      }
      
      // 重置审核备注
      setReviewNote('');
    } catch (error) {
      console.error('审核分享失败:', error);
      toast.error('审核操作失败，请重试');
    } finally {
      setIsReviewing(null);
    }
  };

  // 查看分享详情
  const handleViewDetails = (submission: ExtendedShareSubmission) => {
    setSelectedSubmission(submission);
    setIsModalOpen(true);
  };

  // 关闭详情模态框
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedSubmission(null);
    setReviewNote('');
  };

  // 格式化日期
  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '-';
      }
      return date.toLocaleString();
    } catch (error) {
      return '-';
    }
  };

  // 获取语言显示名称
  const getLanguageDisplayName = (language: string | undefined): string => {
    const languageMap: { [key: string]: string } = {
      'zh-CN': '简体中文',
      'zh-TW': '繁体中文',
      'en': 'English',
      'ja': '日本語'
    };
    return languageMap[language || ''] || '未知';
  };

  // 获取完整的图片URL - 统一使用/share-screenshots/路径
  const getFullImageUrl = (imageUrl: string): string => {
    if (!imageUrl) return '';

    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // 兼容旧的路径格式，转换为新格式
    if (imageUrl.startsWith('/uploads/share-screenshots/')) {
      const filename = imageUrl.replace('/uploads/share-screenshots/', '');
      return `${window.location.origin}/share-screenshots/${filename}`;
    }

    // 直接使用新的路径格式
    return `${window.location.origin}${imageUrl}`;
  };

  // 计算总页数
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // 页码变更处理
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // 每页显示数量变更处理
  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1); // 重置为第一页
  };

  // 权限检查中
  if (isCheckingPermission) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-black">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  // 无权限时重定向
  if (!isAdmin) {
    return <Navigate to="/" replace />;
  }

  return (
    <main className="relative min-h-screen">
      <LandingBackground />
      <div className="container mx-auto px-4 py-8 relative z-10">
        <h1 className="text-3xl font-bold text-center text-white mb-6">
          分享审核管理
        </h1>

        {/* 待审核分享列表 */}
        <div className="bg-gray-900/80 backdrop-blur-sm border border-purple-500/30 rounded-xl p-6 mb-8 shadow-lg">
          <h2 className="text-xl font-semibold text-white mb-4">待审核分享列表</h2>
          
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          ) : submissions.length === 0 ? (
            <div className="text-center py-12 text-gray-400">
              <p>暂无待审核的分享提交</p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left text-gray-300">
                  <thead className="text-xs uppercase bg-gray-800 text-gray-400">
                    <tr>
                      <th className="px-4 py-3 rounded-tl-lg">用户</th>
                      <th className="px-4 py-3">分享平台</th>
                      <th className="px-4 py-3">分享链接</th>
                      <th className="px-4 py-3">提交时间</th>
                      <th className="px-4 py-3">语言</th>
                      <th className="px-4 py-3">预览</th>
                      <th className="px-4 py-3 rounded-tr-lg">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {submissions.map((submission, index) => (
                      <tr 
                        key={submission.id} 
                        className={`border-b border-gray-700 ${index % 2 === 0 ? 'bg-gray-800/50' : 'bg-gray-900/50'}`}
                      >
                        <td className="px-4 py-3">
                          {submission.userId}
                          {submission.email && <div className="text-xs text-gray-400">{submission.email}</div>}
                          {submission.username && <div className="text-xs text-gray-400">{submission.username}</div>}
                        </td>
                        <td className="px-4 py-3 capitalize">{submission.platform}</td>
                        <td className="px-4 py-3">
                          <div className="truncate max-w-[200px]" title={submission.shareUrl}>
                            {submission.shareUrl}
                          </div>
                        </td>
                        <td className="px-4 py-3">{formatDate(submission.createdAt)}</td>
                        <td className="px-4 py-3">
                          {getLanguageDisplayName(submission.language)}
                        </td>
                        <td className="px-4 py-3">
                          {submission.imageUrl && (
                            <div 
                              className="w-10 h-10 bg-gray-700 rounded-md overflow-hidden cursor-pointer border border-gray-600"
                              onClick={() => handleViewDetails(submission)}
                              title="点击查看详情"
                            >
                              <img 
                                src={getFullImageUrl(submission.imageUrl)}
                                alt="预览" 
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  console.error('缩略图加载失败:', getFullImageUrl(submission.imageUrl));
                                  // 图片加载失败时显示占位符
                                  (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWltYWdlLW9mZiI+PHBhdGggZD0iTTE4IDExLjVhLjUuNSAwIDEgMSAxIDAgLjUuNSAwIDAgMS0xIDBaIi8+PHBhdGggZD0iTTQgNWMwLTEuMSAwLTEuNjEuMTUtMi4wNWE0IDQgMCAwIDEgMi44LTIuOEM3LjM5IDAgNy45IDAgOSAwaC4xYzEuMTUgMCAxLjcyIDAgMi4xNS4xM2E0IDQgMCAwIDAgMi44LTIuOEMyMCAxLjQgMjAgLjg5IDIwIDB2OC41IiBzdHJva2U9IiM4ODgiLz48cGF0aCBkPSJtMiAyIDIwIDIwIiBzdHJva2U9IiM4ODgiLz48cGF0aCBkPSJNNC4xMyAxMi42NSA2IDExbDQgNCA1LjUtNS41IiBzdHJva2U9IiM4ODgiLz48cGF0aCBkPSJNMTggMTR2NmMwIDEuMS0uOS0uMS0yIDBINGMtMS4xIDAtMi0uOS0yLTJWOGMwLTEuMS45LTIgMi0yaDZNMTYgMmg2YzEuMSAwIDIgLjkgMiAydjEyYzAgMS4xLS45IDItMiAyaC02IiBzdHJva2U9IiM4ODgiLz48L3N2Zz4=';
                                  (e.target as HTMLImageElement).classList.add('p-1');
                                  
                                  // 添加提示标记
                                  const parent = (e.target as HTMLImageElement).parentElement;
                                  if (parent) {
                                    const errorBadge = document.createElement('div');
                                    errorBadge.className = 'absolute top-0 right-0 bg-red-500 text-white text-xs px-1 rounded-bl';
                                    errorBadge.textContent = '!';
                                    errorBadge.title = `加载失败: ${getFullImageUrl(submission.imageUrl)}`;
                                    parent.appendChild(errorBadge);
                                  }
                                }}
                              />
                            </div>
                          )}
                        </td>
                        <td className="px-4 py-3">
                          <button
                            onClick={() => handleViewDetails(submission)}
                            className="p-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                            title="查看详情"
                          >
                            <Eye size={16} />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* 分页控制 */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-gray-400">
                    显示 {(currentPage - 1) * itemsPerPage + 1} - {Math.min(currentPage * itemsPerPage, totalItems)} 条，共 {totalItems} 条
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="p-1 rounded-md bg-gray-800 text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft size={20} />
                    </button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter(page => {
                        // 只显示当前页附近的页码
                        return page === 1 || page === totalPages || 
                               (page >= currentPage - 2 && page <= currentPage + 2);
                      })
                      .map((page, index, array) => {
                        // 添加省略号
                        if (index > 0 && page - array[index - 1] > 1) {
                          return (
                            <React.Fragment key={`ellipsis-${page}`}>
                              <span className="text-gray-500">...</span>
                              <button
                                onClick={() => handlePageChange(page)}
                                className={`w-8 h-8 rounded-md ${
                                  currentPage === page
                                    ? 'bg-purple-600 text-white'
                                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                                }`}
                              >
                                {page}
                              </button>
                            </React.Fragment>
                          );
                        }
                        return (
                          <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`w-8 h-8 rounded-md ${
                              currentPage === page
                                ? 'bg-purple-600 text-white'
                                : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      })}
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="p-1 rounded-md bg-gray-800 text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRight size={20} />
                    </button>
                    <select
                      value={itemsPerPage}
                      onChange={handleItemsPerPageChange}
                      className="ml-4 bg-gray-800 text-gray-300 rounded-md px-2 py-1 text-sm"
                    >
                      <option value={10}>10条/页</option>
                      <option value={20}>20条/页</option>
                      <option value={50}>50条/页</option>
                    </select>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* 详情模态框 */}
      {isModalOpen && selectedSubmission && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/70" onClick={closeModal}></div>
          <div className="relative bg-gray-900 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-xl font-bold text-white mb-4">分享详情</h3>
              
              {/* 将详情和图片改为横向排列 */}
              <div className="flex flex-col md:flex-row gap-6 mb-4">
                {/* 左侧详情信息 */}
                <div className="flex-1">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="!text-blue-400 text-sm font-medium" style={{color: '#60a5fa'}}>用户ID</p>
                      <p className="text-white">{selectedSubmission.userId}</p>
                      {selectedSubmission.username && (
                        <p className="text-gray-300 text-sm">{selectedSubmission.username}</p>
                      )}
                      {selectedSubmission.email && (
                        <p className="text-gray-300 text-sm">{selectedSubmission.email}</p>
                      )}
                    </div>
                    <div>
                    <p className="!text-blue-400 text-sm font-medium" style={{color: '#60a5fa'}}>分享平台</p>
                      <p className="text-white capitalize">{selectedSubmission.platform}</p>
                    </div>
                    <div>
                    <p className="!text-blue-400 text-sm font-medium" style={{color: '#60a5fa'}}>语言</p>
                      <p className="text-white">
                        {getLanguageDisplayName(selectedSubmission.language)}
                      </p>
                    </div>
                    <div>
                    <p className="!text-blue-400 text-sm font-medium" style={{color: '#60a5fa'}}>提交时间</p>
                      <p className="text-white">{formatDate(selectedSubmission.createdAt)}</p>
                    </div>
                    <div>
                    <p className="!text-blue-400 text-sm font-medium" style={{color: '#60a5fa'}}>会话ID</p>
                      <p className="text-white">{selectedSubmission.sessionId || '-'}</p>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                  <p className="!text-blue-400 text-sm font-medium" style={{color: '#60a5fa'}}>分享链接</p>
                    <p className="text-white break-all">{selectedSubmission.shareUrl}</p>
                  </div>
                </div>
                
                {/* 右侧图片 */}
                {selectedSubmission.imageUrl && (
                  <div className="md:w-1/2 flex-shrink-0">
                    <p className="!text-blue-400 text-sm font-medium" style={{color: '#60a5fa'}}>分享截图</p>
                    <div className="border border-gray-700 rounded-lg overflow-hidden h-full">
                      <div className="relative h-full flex items-center justify-center">
                        <img 
                          src={getFullImageUrl(selectedSubmission.imageUrl)}
                          alt="分享截图" 
                          className="max-w-full max-h-[400px] object-contain"
                          onError={(e) => {
                            console.error('图片加载失败:', getFullImageUrl(selectedSubmission.imageUrl));
                            // 图片加载失败时显示占位符
                            (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWltYWdlLW9mZiI+PHBhdGggZD0iTTE4IDExLjVhLjUuNSAwIDEgMSAxIDAgLjUuNSAwIDAgMS0xIDBaIi8+PHBhdGggZD0iTTQgNWMwLTEuMSAwLTEuNjEuMTUtMi4wNWE0IDQgMCAwIDEgMi44LTIuOEM3LjM5IDAgNy45IDAgOSAwaC4xYzEuMTUgMCAxLjcyIDAgMi4xNS4xM2E0IDQgMCAwIDAgMi44LTIuOEMyMCAxLjQgMjAgLjg5IDIwIDB2OC41IiBzdHJva2U9IiM4ODgiLz48cGF0aCBkPSJtMiAyIDIwIDIwIiBzdHJva2U9IiM4ODgiLz48cGF0aCBkPSJNNC4xMyAxMi42NSA2IDExbDQgNCA1LjUtNS41IiBzdHJva2U9IiM4ODgiLz48cGF0aCBkPSJNMTggMTR2NmMwIDEuMS0uOS0uMS0yIDBINGMtMS4xIDAtMi0uOS0yLTJWOGMwLTEuMS45LTIgMi0yaDZNMTYgMmg2YzEuMSAwIDIgLjkgMiAydjEyYzAgMS4xLS45IDItMiAyaC02IiBzdHJva2U9IiM4ODgiLz48L3N2Zz4=';
                            // 添加样式以便更好地显示占位符
                            (e.target as HTMLImageElement).style.width = '100px';
                            (e.target as HTMLImageElement).style.height = '100px';
                            (e.target as HTMLImageElement).style.margin = '0 auto';
                            (e.target as HTMLImageElement).style.padding = '20px';
                            // 添加错误提示
                            const errorDiv = document.createElement('div');
                            errorDiv.textContent = '图片加载失败';
                            errorDiv.className = 'text-red-500 text-center py-2';
                            (e.target as HTMLImageElement).parentNode?.appendChild(errorDiv);
                          }}
                        />
                        <a 
                          href={getFullImageUrl(selectedSubmission.imageUrl)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="absolute top-2 right-2 bg-gray-800/70 hover:bg-gray-700 p-2 rounded-full text-white"
                          title="在新标签页中打开图片"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                          </svg>
                        </a>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* 审核备注 */}
              <div className="mb-6">
                <label className="block text-gray-400 text-sm mb-2">审核备注</label>
                <textarea
                  value={reviewNote}
                  onChange={(e) => setReviewNote(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-800 text-white border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={3}
                  placeholder="可选，添加审核备注"
                ></textarea>
                <p className="text-gray-500 text-xs mt-2">
                  💡 审核结果将通过邮件自动发送给用户，备注内容会包含在邮件中
                </p>
              </div>
              
              {/* 审核操作按钮 */}
              <div className="flex justify-end space-x-3">
                <button
                  onClick={closeModal}
                  className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
                >
                  关闭
                </button>
                <button
                  onClick={() => handleReview(selectedSubmission.id, 'rejected')}
                  disabled={isReviewing === selectedSubmission.id}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isReviewing === selectedSubmission.id ? (
                    <>
                      <Loader size={16} className="animate-spin mr-2" />
                      处理中...
                    </>
                  ) : (
                    <>
                      <XCircle size={16} className="mr-2" />
                      拒绝
                    </>
                  )}
                </button>
                <button
                  onClick={() => handleReview(selectedSubmission.id, 'approved')}
                  disabled={isReviewing === selectedSubmission.id}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isReviewing === selectedSubmission.id ? (
                    <>
                      <Loader size={16} className="animate-spin mr-2" />
                      处理中...
                    </>
                  ) : (
                    <>
                      <CheckCircle size={16} className="mr-2" />
                      通过
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <Footer />
    </main>
  );
};

export default ShareSubmissionManagement; 