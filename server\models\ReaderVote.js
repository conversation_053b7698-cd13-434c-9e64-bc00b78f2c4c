const { getConnection } = require('../services/database');

class ReaderVote {
  static async addVote(userId, readerId) {
    try {
      const pool = await getConnection();
      const [result] = await pool.query(
        `INSERT INTO reader_votes (user_id, reader_id, created_at) VALUES (?, ?, NOW())`,
        [userId, readerId]
      );
      return result.insertId;
    } catch (error) {
      console.error('Error adding vote:', error);
      throw error;
    }
  }

  static async removeVote(userId, readerId) {
    try {
      const pool = await getConnection();
      const [result] = await pool.query(
        `DELETE FROM reader_votes WHERE user_id = ? AND reader_id = ?`,
        [userId, readerId]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error removing vote:', error);
      throw error;
    }
  }

  static async batchProcessVotes(userId, voteOperations) {
    const logPrefix = '[ReaderVoteBatch]';
    try {
      
      const pool = await getConnection();
      const connection = await pool.getConnection();
      
      try {
        await connection.beginTransaction();
        
        const results = {
          added: 0,
          removed: 0
        };
        
        for (const operation of voteOperations) {
          const { readerId, action } = operation;
          
          // 首先检查当前状态以便精确记录
          const [checkRows] = await connection.query(
            `SELECT COUNT(*) as voteExists FROM reader_votes WHERE user_id = ? AND reader_id = ?`,
            [userId, readerId]
          );
          const currentlyVoted = checkRows[0].voteExists > 0;
          
          if (action === 'add') {
            try {
              // 如果当前没有点赞且操作是添加，才添加
              if (!currentlyVoted) {
                const [result] = await connection.query(
                  `INSERT INTO reader_votes (user_id, reader_id, created_at) 
                   VALUES (?, ?, NOW())
                   ON DUPLICATE KEY UPDATE created_at = IF(reader_id = VALUES(reader_id), created_at, NOW())`,
                  [userId, readerId]
                );
                
                if (result.affectedRows > 0) {
                  results.added++;
                }
              } else {
              }
            } catch (err) {
              console.error(`${logPrefix} 添加点赞时出错: 用户 ${userId} 到占卜师 ${readerId}:`, err);
            }
          } else if (action === 'remove') {
            try {
              // 如果当前已点赞且操作是移除，才移除
              if (currentlyVoted) {
                const [result] = await connection.query(
                  `DELETE FROM reader_votes WHERE user_id = ? AND reader_id = ?`,
                  [userId, readerId]
                );
                
                if (result.affectedRows > 0) {
                  results.removed++;
                }
              } else {
              }
            } catch (err) {
              console.error(`${logPrefix} 移除点赞时出错: 用户 ${userId} 对占卜师 ${readerId}:`, err);
            }
          }
        }
        
        await connection.commit();
        return results;
      } catch (err) {
        await connection.rollback();
        console.error(`${logPrefix} 事务回滚，发生错误:`, err);
        throw err;
      } finally {
        connection.release();
      }
    } catch (error) {
      console.error(`${logPrefix} 批量处理投票时发生错误:`, error);
      throw error;
    }
  }

  static async getVotesByReaderId(readerId) {
    try {
      const pool = await getConnection();
      const [rows] = await pool.query(
        `SELECT COUNT(*) as voteCount FROM reader_votes WHERE reader_id = ?`,
        [readerId]
      );
      return rows[0].voteCount;
    } catch (error) {
      console.error('Error getting votes count:', error);
      throw error;
    }
  }

  static async getAllReadersVotes() {
    try {
      const pool = await getConnection();
      const [rows] = await pool.query(
        `SELECT reader_id, COUNT(*) as voteCount FROM reader_votes GROUP BY reader_id`
      );
      return rows;
    } catch (error) {
      console.error('Error getting all readers votes:', error);
      throw error;
    }
  }

  static async hasUserVotedReader(userId, readerId) {
    try {
      const pool = await getConnection();
      const [rows] = await pool.query(
        `SELECT COUNT(*) as voteExists FROM reader_votes WHERE user_id = ? AND reader_id = ?`,
        [userId, readerId]
      );
      return rows[0].voteExists > 0;
    } catch (error) {
      console.error('Error checking if user voted reader:', error);
      throw error;
    }
  }

  static async getUserVotedReader(userId) {
    try {
      const pool = await getConnection();
      const [rows] = await pool.query(
        `SELECT reader_id FROM reader_votes WHERE user_id = ?`,
        [userId]
      );
      return rows.map(row => row.reader_id);
    } catch (error) {
      console.error('Error getting user voted readers:', error);
      throw error;
    }
  }

  static async createTable() {
    try {
      const pool = await getConnection();
      await pool.query(`
        CREATE TABLE IF NOT EXISTS reader_votes (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id VARCHAR(36) NOT NULL,
          reader_id VARCHAR(36) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE KEY unique_user_reader (user_id, reader_id),
          INDEX idx_reader_id (reader_id)
        )
      `);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = { ReaderVote }; 