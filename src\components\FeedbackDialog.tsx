import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { SessionTypes, checkSessionType, submitReadingFeedback } from '../services/userService';

interface FeedbackDialogProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId?: string;
}

const FeedbackDialog: React.FC<FeedbackDialogProps> = ({ isOpen, onClose, sessionId }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  const [rating, setRating] = useState<number | null>(null);
  const [feedback, setFeedback] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  
  // 各字段独立的错误信息
  const [ratingError, setRatingError] = useState('');
  const [typeError, setTypeError] = useState('');
  const [feedbackError, setFeedbackError] = useState('');
  
  const [sessionTypes, setSessionTypes] = useState<SessionTypes>({
    basicReading: true,
    deepAnalysis: false,
    followup: false
  });
  const [loadingSessionTypes, setLoadingSessionTypes] = useState(false);
  const [selectedFeedbackTypes, setSelectedFeedbackTypes] = useState<SessionTypes>({
    basicReading: true,
    deepAnalysis: false,
    followup: false
  });
  const [showAdditionalOptions, setShowAdditionalOptions] = useState(false);

  // 在对话框打开时检查会话类型
  useEffect(() => {
    if (isOpen && sessionId) {
      setLoadingSessionTypes(true);
      checkSessionType(sessionId)
        .then(types => {
          setSessionTypes(types);
          setSelectedFeedbackTypes({
            basicReading: true,
            deepAnalysis: false,
            followup: false
          });
        })
        .finally(() => {
          setLoadingSessionTypes(false);
        });
    }
  }, [isOpen, sessionId]);

  // 当用户选择评分后，显示额外选项
  useEffect(() => {
    if (rating !== null && !showAdditionalOptions) {
      setShowAdditionalOptions(true);
      setRatingError(''); // 清除评分错误
    }
  }, [rating]);

  // 关闭对话框时重置状态
  useEffect(() => {
    if (!isOpen) {
      setRating(null);
      setFeedback('');
      setShowAdditionalOptions(false);
      setSelectedFeedbackTypes({
        basicReading: true,
        deepAnalysis: false,
        followup: false
      });
      setSubmitted(false);
      setRatingError('');
      setTypeError('');
      setFeedbackError('');
    }
  }, [isOpen]);

  // 切换选择状态
  const toggleFeedbackType = (type: keyof SessionTypes) => {
    const newTypes = {
      ...selectedFeedbackTypes,
      [type]: !selectedFeedbackTypes[type]
    };
    
    setSelectedFeedbackTypes(newTypes);
    
    // 如果有类型被选中，清除类型错误
    if (Object.values(newTypes).some(value => value)) {
      setTypeError('');
    }
  };
  
  const handleSubmit = async () => {
    // 重置所有错误
    setRatingError('');
    setTypeError('');
    setFeedbackError('');
    
    let hasError = false;
    
    if (!rating) {
      setRatingError(t('feedback.please_select_rating'));
      hasError = true;
    }

    // 检查是否至少选择了一种类型
    const hasSelectedType = Object.values(selectedFeedbackTypes).some(value => value);
    if (!hasSelectedType) {
      setTypeError(t('feedback.please_select_type'));
      hasError = true;
    }
    
    // 检查反馈内容不能为空
    if (!feedback.trim()) {
      setFeedbackError(t('feedback.error.content_required'));
      hasError = true;
    }
    
    if (hasError || !sessionId) return;
    
    try {
      setSubmitting(true);
      
      await submitReadingFeedback({
        sessionId,
        rating: rating as number,
        content: feedback,
        feedbackTypes: selectedFeedbackTypes
      });
      
      setSubmitted(true);
      
      // 3秒后自动关闭
      setTimeout(() => {
        onClose();
      }, 3000);
      
    } catch (err) {
      setFeedbackError(t('feedback.submit_error'));
    } finally {
      setSubmitting(false);
    }
  };
  
  const stars = [1, 2, 3, 4, 5];
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6"
        >
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.6 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className={`relative ${isDark ? 'bg-gray-900' : 'bg-white'} rounded-xl sm:rounded-2xl p-3 sm:p-6 md:p-8 shadow-xl max-w-lg w-full mx-auto overflow-hidden sm:max-h-[90vh] sm:overflow-y-auto`}
          >
            {/* 关闭按钮 */}
            <button 
              onClick={onClose}
              className={`absolute top-2 right-2 sm:top-4 sm:right-4 p-1 rounded-full ${
                isDark ? 'text-gray-400 hover:text-white hover:bg-gray-800' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
              } transition-colors focus:outline-none`}
              aria-label="关闭"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            
            {!submitted ? (
              <>
                {loadingSessionTypes ? (
                  <div className="flex justify-center items-center py-6 sm:py-10">
                    <div className="w-8 h-8 sm:w-12 sm:h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                ) : (
                  <>
                    <h2 className={`text-lg sm:text-2xl font-bold mb-3 sm:mb-8 text-center ${isDark ? 'text-white' : 'text-gray-800'} pt-1 sm:pt-0`}>
                      {t('feedback.reading_feedback')}
                    </h2>
                    
                    <div className="mb-4 sm:mb-6">
                      <p className={`text-xs sm:text-base mb-2 sm:mb-4 text-center ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                        {t('feedback.please_rate_experience')}
                      </p>
                      <div className="flex justify-center space-x-2 sm:space-x-5">
                        {stars.map((star) => (
                          <button
                            key={star}
                            onClick={() => setRating(star)}
                            className="focus:outline-none transition-transform transform hover:scale-110"
                          >
                            <svg 
                              xmlns="http://www.w3.org/2000/svg" 
                              viewBox="0 0 24 24" 
                              fill={rating !== null && star <= rating ? "#8B5CF6" : "none"}
                              stroke={rating !== null && star <= rating ? "#8B5CF6" : isDark ? "#6B7280" : "#9CA3AF"}
                              className="w-7 h-7 sm:w-10 sm:h-10 transition-colors duration-200"
                            >
                              <path 
                                strokeLinecap="round" 
                                strokeLinejoin="round" 
                                strokeWidth={1.5} 
                                d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" 
                              />
                            </svg>
                          </button>
                        ))}
                      </div>
                      {rating && (
                        <p className="text-center mt-1 sm:mt-3 text-sm sm:text-base text-purple-500">
                          {rating === 5 ? t('feedback.rating.excellent') :
                           rating === 4 ? t('feedback.rating.good') :
                           rating === 3 ? t('feedback.rating.average') :
                           rating === 2 ? t('feedback.rating.below_average') :
                                          t('feedback.rating.poor')}
                        </p>
                      )}
                      {ratingError && (
                        <p className="text-center mt-2 text-xs sm:text-sm italic text-gray-500">
                          {ratingError}
                        </p>
                      )}
                    </div>
                    
                    {showAdditionalOptions && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        transition={{ duration: 0.3 }}
                      >
                        {/* 内容类型选择（如果有多种类型） */}
                        {(sessionTypes.deepAnalysis || sessionTypes.followup) && (
                          <div className="mb-4 sm:mb-6">
                            <p className={`text-sm sm:text-base mb-2 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                              {t('feedback.select_content_type')}
                            </p>
                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
                              <button
                                onClick={() => toggleFeedbackType('basicReading')}
                                className={`p-2 sm:p-3 rounded-lg text-center text-sm sm:text-base transition-colors relative ${
                                  selectedFeedbackTypes.basicReading 
                                    ? isDark 
                                      ? 'bg-purple-700 text-white' 
                                      : 'bg-purple-100 text-purple-800 border border-purple-300' 
                                    : isDark 
                                      ? 'bg-gray-800 text-gray-300 hover:bg-gray-700' 
                                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                }`}
                              >
                                {selectedFeedbackTypes.basicReading && (
                                  <span className="absolute top-1 right-1 text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 sm:h-4 sm:w-4" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                  </span>
                                )}
                                {t('feedback.basic_reading')}
                              </button>
                              
                              {sessionTypes.deepAnalysis && (
                                <button
                                  onClick={() => toggleFeedbackType('deepAnalysis')}
                                  className={`p-2 sm:p-3 rounded-lg text-center text-sm sm:text-base transition-colors relative ${
                                    selectedFeedbackTypes.deepAnalysis 
                                      ? isDark 
                                        ? 'bg-purple-700 text-white' 
                                        : 'bg-purple-100 text-purple-800 border border-purple-300' 
                                      : isDark 
                                        ? 'bg-gray-800 text-gray-300 hover:bg-gray-700' 
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                  }`}
                                >
                                  {selectedFeedbackTypes.deepAnalysis && (
                                    <span className="absolute top-1 right-1 text-white">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 sm:h-4 sm:w-4" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                      </svg>
                                    </span>
                                  )}
                                  {t('feedback.deep_analysis')}
                                </button>
                              )}
                              
                              {sessionTypes.followup && (
                                <button
                                  onClick={() => toggleFeedbackType('followup')}
                                  className={`p-2 sm:p-3 rounded-lg text-center text-sm sm:text-base transition-colors relative ${
                                    selectedFeedbackTypes.followup 
                                      ? isDark 
                                        ? 'bg-purple-700 text-white' 
                                        : 'bg-purple-100 text-purple-800 border border-purple-300' 
                                      : isDark 
                                        ? 'bg-gray-800 text-gray-300 hover:bg-gray-700' 
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                  }`}
                                >
                                  {selectedFeedbackTypes.followup && (
                                    <span className="absolute top-1 right-1 text-white">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 sm:h-4 sm:w-4" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                      </svg>
                                    </span>
                                  )}
                                  {t('feedback.followup')}
                                </button>
                              )}
                            </div>
                            {typeError && (
                              <p className="mt-2 text-xs sm:text-sm italic text-gray-500">
                                {typeError}
                              </p>
                            )}
                          </div>
                        )}
                        
                        <div className="mb-4 sm:mb-6">
                          <label 
                            htmlFor="feedback" 
                            className={`block mb-1 sm:mb-3 text-sm sm:text-base font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}
                          >
                            {t('feedback.additional_comments')}
                          </label>
                          <textarea
                            id="feedback"
                            rows={3}
                            value={feedback}
                            onChange={(e) => {
                              setFeedback(e.target.value);
                              if (e.target.value.trim()) {
                                setFeedbackError('');
                              }
                            }}
                            placeholder={t('feedback.comments_placeholder')}
                            className={`w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base rounded-lg border ${
                              feedbackError 
                                ? isDark 
                                  ? 'border-gray-500 focus:border-purple-500' 
                                  : 'border-gray-400 focus:border-purple-500' 
                                : isDark 
                                  ? 'border-gray-700 focus:border-purple-500' 
                                  : 'border-gray-300 focus:border-purple-500'
                            } ${
                              isDark 
                                ? 'bg-gray-800 text-white placeholder-gray-400' 
                                : 'bg-white text-gray-700 placeholder-gray-400'
                            } focus:outline-none focus:ring-1 focus:ring-purple-500`}
                          />
                          {feedbackError && (
                            <p className="mt-2 text-xs sm:text-sm italic text-gray-500">
                              {feedbackError}
                            </p>
                          )}
                        </div>
                      </motion.div>
                    )}
                    
                    <div className="flex justify-center mt-4 sm:mt-8">
                      <button
                        onClick={handleSubmit}
                        disabled={submitting}
                        className={`w-full sm:w-auto px-4 sm:px-16 py-2 sm:py-4 rounded-full text-sm sm:text-base font-medium bg-purple-600 hover:bg-purple-700 text-white transition-colors ${
                          submitting ? 'opacity-70 cursor-not-allowed' : ''
                        }`}
                      >
                        {submitting ? (
                          <div className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 sm:h-5 sm:w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {t('common.submitting')}
                          </div>
                        ) : t('feedback.submit')}
                      </button>
                    </div>
                  </>
                )}
              </>
            ) : (
              <div className="text-center py-4 sm:py-10">
                <div className="mb-3 sm:mb-6 flex justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 sm:h-20 sm:w-20 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className={`text-xl sm:text-2xl font-bold mb-2 sm:mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t('feedback.thank_you')}
                </h3>
                <p className={`text-sm sm:text-base ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  {t('feedback.submitted_successfully')}
                </p>
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FeedbackDialog; 