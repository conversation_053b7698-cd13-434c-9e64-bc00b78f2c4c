import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '../contexts/UserContext';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import SEO from '../components/SEO';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import LoginPrompt from '../components/LoginPrompt';
import VipPromptDialog from '../components/VipPromptDialog';
import { getFontClass as getGlobalFontClass } from '../utils/fontUtils';

// 导入其他组件
import YesNoQuestionInput from '../components/yes-no-tarot/YesNoQuestionInput';
import SingleCardHowItWords from '../components/yes-no-tarot/SingleCardHowItWords';
import MoreTarotOptions from '../components/yes-no-tarot/MoreTarotOptions';
import SpotlightSection from '../components/yes-no-tarot/SingleCardSpotlightSection';
import SingleCardBenefits from '../components/yes-no-tarot/SingleCardBenefits';
import SingleCardBestQuestions from '../components/yes-no-tarot/SingleCardBestQuestions';
import SingleCardUnderstanding from '../components/yes-no-tarot/SingleCardUnderstanding';

const YesNoSingleCard: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { user } = useUser();
  const { navigate } = useLanguageNavigate();
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showVipPrompt, setShowVipPrompt] = useState(false);

  // 问题输入相关状态
  const [userQuestion, setUserQuestion] = useState(''); // 用户问题
  const [isSubmitting, setIsSubmitting] = useState(false); // 提交状态
  const [errorMessage, setErrorMessage] = useState(''); // 错误信息

  // 检查用户权限
  const checkUserPermission = () => {
    if (!user) {
      setShowLoginPrompt(true);
      return false;
    }
    if (user.vipStatus === 'active') return true;
    if (user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return false;
    }
    return true;
  };
  
  // 开始塔罗阅读 - 用于SpotlightCard组件
  const handleStartReading = () => {
    if (!checkUserPermission()) return;

    // 直接导航到当前页面，强制刷新
    navigate('/yes-no-tarot/single-card');
  };

  // 验证问题
  const validateQuestion = (question: string): { isValid: boolean; errorMessage: string } => {
    const trimmedQuestion = question.trim();
    const currentLang = i18n.language;
    
    // 检查问题长度
    if (trimmedQuestion.length < 5) {
      return { isValid: false, errorMessage: t('home.validation.too_short') };
    }
    if (trimmedQuestion.length > 200) {
      return { isValid: false, errorMessage: t('home.validation.too_long') };
    }

    // 检查是否包含表情符号
    const emojiRegex = /[\u{1F000}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/u;
    if (emojiRegex.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.contains_emoji') };
    }

    // 根据语言设置不同的验证规则
    if (currentLang === 'zh-CN' || currentLang === 'zh-TW') {
      // 中文验证规则
      if (!/[\u4e00-\u9fa5]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    } else if (currentLang === 'ja') {
      // 日文验证规则
      if (!/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    } else {
      // 英文验证规则
      if (!/[a-zA-Z]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    }

    // 检查是否为纯数字
    if (/^\d+$/.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    // 检查重复字符
    if (/(.)\1{4,}/.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    // 检查测试文本 - 根据语言设置不同的测试词
    const testPatterns = {
      'zh-CN': ['测试', 'test', '123', 'abc'],
      'zh-TW': ['測試', 'test', '123', 'abc'],
      'ja': ['テスト', 'test', '123', 'abc'],
      'en': ['test', '123', 'abc']
    };

    if (testPatterns[currentLang as keyof typeof testPatterns].some(pattern => 
      trimmedQuestion.toLowerCase().includes(pattern.toLowerCase())
    )) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    return { isValid: true, errorMessage: '' };
  };

  // 提交问题处理函数
  const handleSubmitQuestion = (e: React.FormEvent) => {
    e.preventDefault();

    const validation = validateQuestion(userQuestion);
    if (!validation.isValid) {
      setErrorMessage(validation.errorMessage);
      return;
    }

    if (!userQuestion.trim()) return;

    // 检查用户权限
    if (!checkUserPermission()) return;

    setIsSubmitting(true);

    // 清理之前的解读结果，确保重新开始
    localStorage.removeItem('singleCardReadingResult');
    localStorage.removeItem('singleCardSelectedCard');
    localStorage.removeItem('singleCardOrientation');
    localStorage.removeItem('singleCardSafetyIssue');
    localStorage.removeItem('singleCardSafetyMessage');
    localStorage.removeItem('sessionId');

    // 保存用户问题到localStorage，供解读结果页面使用
    localStorage.setItem('userQuestion', userQuestion.trim());

    // 模拟提交处理，然后跳转到解读结果页面
    setTimeout(() => {
      setIsSubmitting(false);
      // 跳转到单卡解读结果页面
      navigate('/yes-no-tarot/single-card/result');
    }, 1000);
  };

  return (
    <div className="main-container min-h-screen flex flex-col relative">
      <SEO 
      />
      <LandingBackground />
      
      {/* 主要内容 */}
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
            <h1 className={`main-title mb-2 sm:mb-3 ${getGlobalFontClass(i18n.language)} dark:text-white text-gray-900`}>
              {t('yes_no_tarot.single_card_subtitle')}
            </h1>
            <p className={`text-base sm:text-lg dark:text-purple-300 text-purple-600 italic ${getGlobalFontClass(i18n.language)}`}>
              {t('yes_no_tarot.single_card_subtitle_description')}
            </p>
          </div>

          {/* 主页面内容 */}
          <div className="max-w-[95%] lg:max-w-5xl mx-auto mt-0 sm:mt-8">
            {/* 问题输入组件 */}
            <YesNoQuestionInput
              userQuestion={userQuestion}
              setUserQuestion={setUserQuestion}
              errorMessage={errorMessage}
              isSubmitting={isSubmitting}
              onSubmitQuestion={handleSubmitQuestion}
            />

            {/* Best Questions for One Card Tarot Yes/No 板块 */}
            <SingleCardBestQuestions />
            
            {/* How One Card Tarot Yes No Reading Works 板块 */}
            <SingleCardHowItWords />

            {/* Benefits of Single Card Yes No Tarot 板块 */}
            <SingleCardBenefits />

            {/* Understanding Your One Card Reading 板块 */}
            <SingleCardUnderstanding />

            {/* 更多塔罗占卜区域 */}
            <MoreTarotOptions onNavigate={navigate} pageType="single" />

            {/* SpotlightCard组件 */}
            <SpotlightSection onStartReading={handleStartReading} />
          </div>
        </div>
      </div>

      <Footer />

      {/* VIP提示弹窗 */}
      <VipPromptDialog isOpen={showVipPrompt} onCancel={() => setShowVipPrompt(false)} />

      {/* 登录提示弹窗 */}
      <LoginPrompt isOpen={showLoginPrompt} onClose={() => setShowLoginPrompt(false)} />
    </div>
  );
};

export default YesNoSingleCard; 