import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { motion } from 'framer-motion';

interface ThemeToggleProps {
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <button
      onClick={toggleTheme}
      className={`relative flex items-center justify-center w-8 h-8 rounded-full 
                dark:text-gray-100 text-gray-800 dark:hover:text-white hover:text-black
                transition-colors duration-300 
                hover:bg-purple-500/10
                focus:outline-none ${className}`}
      aria-label={theme === 'dark' ? '切换到浅色模式' : '切换到深色模式'}
    >
      <div className="relative w-5 h-5">
        {/* 太阳图标 (浅色主题) */}
        <motion.svg
          xmlns="http://www.w3.org/2000/svg"
          className="absolute inset-0 w-full h-full dark:text-white text-gray-800"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          initial={{ opacity: 0, rotate: -45 }}
          animate={{ 
            opacity: theme === 'light' ? 1 : 0,
            rotate: theme === 'light' ? 0 : -45,
          }}
          transition={{ duration: 0.3 }}
        >
          <circle cx="12" cy="12" r="5" />
          <line x1="12" y1="1" x2="12" y2="3" />
          <line x1="12" y1="21" x2="12" y2="23" />
          <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" />
          <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" />
          <line x1="1" y1="12" x2="3" y2="12" />
          <line x1="21" y1="12" x2="23" y2="12" />
          <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" />
          <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" />
        </motion.svg>
        
        {/* 月亮图标 (深色主题) */}
        <motion.svg
          xmlns="http://www.w3.org/2000/svg"
          className="absolute inset-0 w-full h-full dark:text-white text-gray-800"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          initial={{ opacity: 1, rotate: 0 }}
          animate={{ 
            opacity: theme === 'dark' ? 1 : 0,
            rotate: theme === 'dark' ? 0 : 45,
          }}
          transition={{ duration: 0.3 }}
        >
          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
        </motion.svg>
      </div>
      
      {/* 背景发光效果 */}
      <div className={`absolute inset-0 rounded-full opacity-0 transition-opacity duration-300
                     ${theme === 'dark' ? 'bg-purple-500/10' : 'bg-yellow-500/10'}
                     group-hover:opacity-100`} />
    </button>
  );
};

export default ThemeToggle; 