const express = require('express');
const router = express.Router();
const ReadingFeedback = require('../models/ReadingFeedback');
const { optionalAuthenticateToken } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

// 提交塔罗牌解读反馈
router.post(
  '/',
  optionalAuthenticateToken, // 可选认证，支持匿名反馈
  [
    body('sessionId').notEmpty().withMessage('会话ID不能为空'),
    body('rating').isInt({ min: 1, max: 5 }).withMessage('评分必须是1-5之间的整数'),
    body('content').optional().isString().isLength({ max: 2000 }).withMessage('反馈内容不能超过2000字符'),
    body('feedbackTypes').isObject().withMessage('反馈类型必须是对象')
  ],
  async (req, res) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    try {
      const { sessionId, rating, content, feedbackTypes } = req.body;
      
      // // 添加调试信息
      // console.log('Authorization Header:', req.headers['authorization']);
      // console.log('User from JWT:', req.user);
      
      // 检查至少选择了一种反馈类型
      if (!feedbackTypes.basicReading && !feedbackTypes.deepAnalysis && !feedbackTypes.followup) {
        return res.status(400).json({ 
          success: false, 
          message: '请至少选择一种反馈类型' 
        });
      }
      
      // 准备反馈数据
      const feedbackData = {
        sessionId,
        userId: req.user?.userId || null,
        rating,
        content: content || '',
        feedbackTypes,
        metadata: {
          userAgent: req.headers['user-agent'],
          platform: req.headers['sec-ch-ua-platform'],
          language: req.headers['accept-language'],
          ipAddress: req.ip
        }
      };
      
      // console.log('准备提交的反馈数据:', {
      //   sessionId: feedbackData.sessionId,
      //   userId: feedbackData.userId,
      //   rating: feedbackData.rating,
      //   contentLength: feedbackData.content?.length,
      //   feedbackTypes: feedbackData.feedbackTypes
      // });
      
      // 首先检查是否已存在该会话的反馈记录
      const existingFeedback = await ReadingFeedback.findBySessionIdAndUserId(sessionId, feedbackData.userId);
      // console.log('检查是否已存在反馈:', existingFeedback?.length > 0 ? '已存在' : '不存在');
      
      // 创建或更新反馈
      const feedback = await ReadingFeedback.create(feedbackData);
      // console.log('反馈处理完成，ID:', feedback.id);
      
      res.status(201).json({
        success: true,
        message: existingFeedback?.length > 0 ? '反馈更新成功，感谢您的评价' : '反馈提交成功，感谢您的评价',
        data: {
          id: feedback.id
        }
      });
    } catch (error) {
      console.error('提交塔罗解读反馈失败:', error);
      res.status(500).json({ success: false, message: '提交反馈失败，请稍后重试' });
    }
  }
);

// 获取会话的所有反馈
router.get(
  '/session/:sessionId',
  optionalAuthenticateToken,
  async (req, res) => {
    try {
      const { sessionId } = req.params;
      const feedbacks = await ReadingFeedback.findBySessionId(sessionId);
      
      res.json({
        success: true,
        data: feedbacks
      });
    } catch (error) {
      console.error('获取会话反馈失败:', error);
      res.status(500).json({ success: false, message: '获取反馈失败，请稍后重试' });
    }
  }
);

// 获取反馈统计数据
router.get(
  '/stats',
  optionalAuthenticateToken,
  async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      const options = {};
      
      if (startDate && endDate) {
        options.startDate = new Date(startDate);
        options.endDate = new Date(endDate);
      }
      
      const stats = await ReadingFeedback.getStats(options);
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('获取反馈统计失败:', error);
      res.status(500).json({ success: false, message: '获取统计数据失败，请稍后重试' });
    }
  }
);

module.exports = router; 