import axiosInstance from '../../utils/axios';
import { audioManager } from './audioManager';

/**
 * 将文本分割成段落
 * @param text 要分割的文本
 * @returns 分割后的段落数组
 */
export const splitIntoParagraphs = (text: string): string[] => {
  if (!text) return [];
  
  // 使用双换行作为段落分隔符
  const paragraphs = text.split(/\n\n+/)
    .map(p => p.trim())
    .filter(p => p.length > 0);
  
  // 如果没有段落分隔符，按照句子分隔
  if (paragraphs.length <= 1 && text.length > 300) {
    // 按句号、问号、感叹号分隔（中英文标点都支持）
    return text.split(/(?<=[。？！.?!])\s*/)
      .map(p => p.trim())
      .filter(p => p.length > 0);
  }
  
  return paragraphs;
};

/**
 * 获取单个段落的音频
 * @param paragraphText 段落文本
 * @param index 段落索引
 * @param readerId 朗读者ID
 * @param sessionId 会话ID
 * @param cacheKeyPrefix 缓存键前缀或传入的messageId
 * @param blockType 板块类型，例如base或followup
 * @param componentId 可选的组件ID，用于音频管理器
 * @param language 语言代码，如'zh'、'en'、'ja'，默认为'zh'
 * @returns 返回一个Promise，包含创建的Audio对象
 */
export const fetchParagraphAudio = async (
  paragraphText: string, 
  index: number, 
  readerId?: string,
  sessionId?: string,
  cacheKeyPrefix?: string,
  blockType: string = 'base',
  componentId?: string,
  language: string = 'zh'
): Promise<HTMLAudioElement> => {
  // 确定板块类型 - 强制统一为base或followup
  let actualBlockType = blockType === 'basic' ? 'base' : blockType; 
  
  // 如果传入的cacheKeyPrefix中包含板块类型信息
  if (cacheKeyPrefix) {
    // 检测新格式: blockType-para-X
    if (cacheKeyPrefix.includes('-para-')) {
      // 从新格式中提取板块类型和索引
      const blockTypeMatch = cacheKeyPrefix.match(/^([a-z_]+)-para-(\d+)$/);
      if (blockTypeMatch && blockTypeMatch[1] && blockTypeMatch[2]) {
        const extractedBlockType = blockTypeMatch[1];
        const extractedIndex = parseInt(blockTypeMatch[2], 10);
        
        // 更新板块类型和索引
        actualBlockType = extractedBlockType;
        if (extractedIndex !== index) {
          index = extractedIndex;
        }
      }
    }
    // 原有格式处理: para-X
    else if (cacheKeyPrefix.includes('followup')) {
      actualBlockType = 'followup';
    } else if (cacheKeyPrefix.includes('base') || cacheKeyPrefix.includes('basic')) {
      actualBlockType = 'base';
    }
    
    // 原来的para-X格式处理
    if (cacheKeyPrefix.startsWith('para-')) {
      // 从para-X格式中提取索引，并更新index参数
      const matches = cacheKeyPrefix.match(/para-(\d+)$/);
      if (matches && matches[1]) {
        const extractedIndex = parseInt(matches[1], 10);
        // 如果提取出的索引与传入的不同，使用提取的索引
        if (extractedIndex !== index) {
          index = extractedIndex;
        }
      }
    }
  }
  
  // 构造后端可识别的缓存键格式
  let actualCacheKey: string;
  
  // 构建messageId，根据语言参数添加语言标识
  const languageSuffix = language !== 'zh' ? `_${language}` : '';
  const messageId = `${actualBlockType}${languageSuffix}_para_${index}`;
  
  if (sessionId) {
    // 使用更清晰的格式，确保后端可以识别板块类型
    actualCacheKey = `${sessionId}_${messageId}`;
  } else {
    // 没有会话ID时的fallback
    actualCacheKey = messageId;
  }
  
  // 发送API请求获取音频文件
  const response = await axiosInstance.post('/tts', {
    text: paragraphText,
    readerId,
    sessionId,
    messageId: messageId, // 使用带有语言标识的messageId
    cacheKey: actualCacheKey, // 使用实际的缓存键
    language // 添加语言参数
  }, {
    responseType: 'arraybuffer' // 接收二进制数据
  });
  
  // 处理服务器返回的语音二进制数据，创建一个本地Blob URL
  const blob = new Blob([response.data], { type: 'audio/mpeg' });
  const url = URL.createObjectURL(blob);
  
  // 创建新的Audio对象
  const audio = new Audio(url);
  
  // 如果提供了组件ID，则注册到音频管理器
  if (componentId) {
    audioManager.registerAudio(audio, componentId);
  }
  
  return audio;
}; 