/* 字体加载策略说明：
 * 使用系统内置字体，无需额外加载
 * 1. 英文：系统默认 -apple-system 等优质字体
 * 2. 中文：系统默认中文字体
 * 3. 日文：系统默认日文字体
 */

:root {
  /* 字体系统 */
  /* 英文字体 */
  --font-en: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  
  /* 简体中文字体 */
  --font-zh-CN: -apple-system, 'PingFang SC', 'Microsoft YaHei', 'Source Han Sans SC', 'Noto Sans CJK SC', 
                'WenQuanYi Micro Hei', sans-serif;
  
  /* 繁体中文字体 */              
  --font-zh-TW: -apple-system, 'PingFang TC', 'Microsoft JhengHei', 'Source Han Sans TC', 'Noto Sans CJK TC', 
                'Hiragino Sans TC', 'Apple LiGothic', sans-serif;
  
  /* 日文字体 */
  --font-ja: -apple-system, 'Hiragino Kaku Gothic Pro', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', 
             'Source Han Sans JP', 'Noto Sans CJK JP', sans-serif;

  /* 默认字体组合 */
  --font-sans: var(--font-en), var(--font-zh-CN), var(--font-zh-TW), var(--font-ja);
  
  /* 其他字体设置保持不变 */
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --font-numeric: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* 字重系统 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* 字号系统 (rem) */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */

  /* 行高系统 */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* 字间距 */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;

  /* 间距系统 */
  --section-spacing: 10rem;  /* 64px */
  --section-spacing-sm: 6rem;  /* 48px */
}

/* 通用字体类 */
.font-sans {
  font-family: var(--font-sans);
}

.font-mono {
  font-family: var(--font-mono);
}

.font-numeric {
  font-family: var(--font-numeric);
}

/* 字重类 */
.font-light {
  font-weight: var(--font-light);
}

.font-normal {
  font-weight: var(--font-normal);
}

.font-medium {
  font-weight: var(--font-medium);
}

.font-semibold {
  font-weight: var(--font-semibold);
}

.font-bold {
  font-weight: var(--font-bold);
}

/* 数字字体优化 */
.tabular-nums {
  font-variant-numeric: tabular-nums;
}

.ordinal {
  font-variant-numeric: ordinal;
}

/* 中文标点优化 */
:lang(zh-CN), :lang(zh-TW) {
  hanging-punctuation: first;
}

/* 繁体中文文本优化 */
.chinese-trad {
  font-family: var(--font-zh-TW);
}

/* 简体中文文本优化 */
.chinese {
  font-family: var(--font-zh-CN);
}

/* 日文文本优化 - 保留原有的.japanese类，但推荐使用:lang(ja) */
.japanese {
  font-family: var(--font-ja);
  font-kerning: normal;
}

/* 文本抗锯齿渲染 */
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 默认文本样式 */
body {
  font-family: var(--font-sans);
  font-weight: var(--font-normal);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 主标题样式 */
.main-title {
  font-family: var(--font-sans);
  font-size: var(--text-3xl);  /* 30px */
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-wide);
  text-align: center;
  background: linear-gradient(to right, #A78BFA, #EC4899);  /* purple-400 to pink-400 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 副标题样式 */
.sub-title {
  font-family: var(--font-sans);
  font-size: var(--text-lg);  /* 18px */
  font-style: italic;
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-wide);
  text-align: center;
  background: linear-gradient(to right, #A78BFA, #EC4899);  /* purple-400 to pink-400 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  max-width: 36rem;  /* 576px */
  margin-left: auto;
  margin-right: auto;
}

/* 二级标题样式 */
.section-title {
  font-family: var(--font-sans);
  font-size: var(--text-lg);  /* 18px */
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-wide);
  color: white;
}

/* 正文文本样式 */
.body-text {
  font-family: var(--font-sans);
  font-size: var(--text-base);  /* 16px */
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  color: #D1D5DB;  /* text-gray-300 */
}

/* 板块间距 */
.section-spacing {
  margin-top: var(--section-spacing-sm);
}

@media (min-width: 640px) {
  .main-title {
    font-size: var(--text-4xl);  /* 36px */
  }
  
  .section-spacing {
    margin-top: var(--section-spacing);
  }
}

/* 语言特定字体类 */
:lang(en) {
  font-family: var(--font-en);
}

/* 英文文本优化 */
.text-en {
  font-family: var(--font-en);
  letter-spacing: 0.01em;
  font-feature-settings: "kern", "liga", "calt";
}

:lang(zh-CN) {
  font-family: var(--font-zh-CN);
}

:lang(zh-TW) {
  font-family: var(--font-zh-TW);
}

:lang(ja) {
  font-family: var(--font-ja);
  font-kerning: normal;
}
/* 确保在移动端繁体中文字体与简体中文一致 */
@media (max-width: 768px) {
  :lang(zh-TW) {
    font-family: var(--font-zh-TW) !important;
  }
  
  .chinese-trad {
    font-family: var(--font-zh-TW) !important;
  }
}

