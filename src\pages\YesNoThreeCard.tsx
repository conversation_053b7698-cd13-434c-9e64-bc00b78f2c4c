import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '../contexts/UserContext';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import SEO from '../components/SEO';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import LoginPrompt from '../components/LoginPrompt';
import VipPromptDialog from '../components/VipPromptDialog';
import YesNoQuestionForm from '../components/yes-no-tarot/YesNoQuestionForm';
import { getFontClass as getGlobalFontClass } from '../utils/fontUtils';

// 导入其他组件
import MoreTarotOptions from '../components/yes-no-tarot/MoreTarotOptions';
import ThreeCardQuestions from '../components/yes-no-tarot/ThreeCardQuestions';
import ThreeCardCalculation from '../components/yes-no-tarot/ThreeCardCalculation';
import ThreeCardBenefits from '../components/yes-no-tarot/ThreeCardBenefits';
import ThreeCardUnderstanding from '../components/yes-no-tarot/ThreeCardUnderstanding';
import ThreeCardSpotlightSection from '../components/yes-no-tarot/ThreeCardSpotlightSection';

// 导入CdnLazyImage组件
import CdnLazyImage from '../components/CdnLazyImage';

import axiosInstance from '../utils/axios';
import { SPREAD_OPTIONS } from '../data/spreads';

const YesNoThreeCard: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { user } = useUser();
  const { navigate } = useLanguageNavigate();
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  
  // 问题相关状态
  const [userQuestion, setUserQuestion] = useState(''); 
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // 检查用户权限
  const checkUserPermission = () => {
    if (!user) {
      setShowLoginPrompt(true);
      return false;
    }
    if (user.vipStatus === 'active') return true;
    if (user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return false;
    }
    return true;
  };
  
  // 开始塔罗阅读 - 用于SpotlightCard组件
  const handleStartReading = () => {
    if (!checkUserPermission()) return;
    
    // 直接导航到当前页面，强制刷新
    navigate('/yes-no-tarot/three-cards');
  };

  // 验证问题
  const validateQuestion = (question: string): { isValid: boolean; errorMessage: string } => {
    const trimmedQuestion = question.trim();
    const currentLang = i18n.language;
    
    // 检查问题长度
    if (trimmedQuestion.length < 5) {
      return { isValid: false, errorMessage: t('home.validation.too_short') };
    }
    if (trimmedQuestion.length > 200) {
      return { isValid: false, errorMessage: t('home.validation.too_long') };
    }

    // 检查是否包含表情符号
    const emojiRegex = /[\u{1F000}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/u;
    if (emojiRegex.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.contains_emoji') };
    }

    // 根据语言设置不同的验证规则
    if (currentLang === 'zh-CN' || currentLang === 'zh-TW') {
      // 中文验证规则
      if (!/[\u4e00-\u9fa5]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    } else if (currentLang === 'ja') {
      // 日文验证规则
      if (!/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    } else {
      // 英文验证规则
      if (!/[a-zA-Z]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    }

    // 检查是否为纯数字
    if (/^\d+$/.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    // 检查重复字符
    if (/(.)\1{4,}/.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    // 检查测试文本 - 根据语言设置不同的测试词
    const testPatterns = {
      'zh-CN': ['测试', 'test', '123', 'abc'],
      'zh-TW': ['測試', 'test', '123', 'abc'],
      'ja': ['テスト', 'test', '123', 'abc'],
      'en': ['test', '123', 'abc']
    };

    if (testPatterns[currentLang as keyof typeof testPatterns].some(pattern => 
      trimmedQuestion.toLowerCase().includes(pattern.toLowerCase())
    )) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    return { isValid: true, errorMessage: '' };
  };

  // 提交问题处理函数
  const handleSubmitQuestion = (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validateQuestion(userQuestion);
    if (!validation.isValid) {
      setErrorMessage(validation.errorMessage);
      return;
    }
    
    if (!userQuestion.trim()) return;
    if (!checkUserPermission()) return;
    
    setIsSubmitting(true);
    
    // 保存问题到本地存储 - 使用正确的key，与普通塔罗解读兼容
    localStorage.setItem('userQuestion', userQuestion.trim());
    
    // 从预定义的牌阵配置中获取"是否牌阵"
    const yesNoSpread = SPREAD_OPTIONS.find(spread => spread.id === 'yes-no');
    
    // 设置默认读者为basic的Molly
    const defaultReader = {
      id: 'basic',
      nameEn: 'Molly',
      name: i18n.language.startsWith('zh') ? '茉伊' : 'Molly',
      price: 'free',
      voteCount: 1500
    };
    
    // 保存读者信息到localStorage
    localStorage.setItem('selectedReader', JSON.stringify(defaultReader));
    
    if (yesNoSpread) {
      // 保存牌阵配置到本地存储
      localStorage.setItem('selectedSpread', JSON.stringify(yesNoSpread));
      
      // 创建session以便API调用
      try {
        const createSession = async () => {
          const response = await axiosInstance.post('/api/session', {
            question: userQuestion.trim(),
            spreadId: 'yes-no',
            readerId: 'basic', // 设置读者ID为basic
            status: 'question_asked'
          });
          
          if (response.data && response.data.success) {
            localStorage.setItem('sessionId', response.data.session.id);
          }
          
          // 直接跳转到抽牌页面
          navigate('/reading/shuffle');
        };
        
        createSession();
      } catch (error) {
        console.error('创建会话失败:', error);
        // 即使创建session失败也跳转到抽牌页面
        navigate('/reading/shuffle');
      }
    } else {
      console.error('未找到是否牌阵配置');
      // 跳转到抽牌页面
      navigate('/reading/shuffle');
    }
  };

  return (
    <div className="main-container min-h-screen flex flex-col relative">
      <SEO 
      />
      <LandingBackground />
      
      {/* 主要内容 */}
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
            <h1 className={`main-title mb-2 sm:mb-3 ${getGlobalFontClass(i18n.language)} dark:text-white text-gray-900`}>
              {t('yes_no_tarot.three_card_subtitle')}
            </h1>
            <p className={`text-base sm:text-lg dark:text-purple-300 text-purple-600 italic ${getGlobalFontClass(i18n.language)}`}>
              {t('yes_no_tarot.three_card_subtitle_description')}
            </p>
          </div>

          {/* 主页面内容 */}
          <div className="max-w-[95%] lg:max-w-5xl mx-auto mt-0 sm:mt-8">
            {/* 三卡占卜输入卡片 */}
            <div className="max-w-3xl mx-auto">
              <div className="relative backdrop-blur-xl rounded-xl overflow-hidden dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 shadow-lg">
                <div className="relative h-auto min-h-[200px] aspect-video overflow-hidden">
                  <CdnLazyImage
                    src="/images-optimized/yes-no-tarot/Yes-No-Three-Card-Spread.webp"
                    alt="Three Card Tarot Reading"
                    className="w-full h-auto object-contain"
                  />
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/60"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6">
                    <h3 className="text-xl font-bold text-white">{t('yes_no_tarot.reading_options.three_card.title')}</h3>
                    <p className="text-gray-300 mt-2 hidden sm:block text-sm">{t('yes_no_tarot.reading_options.three_card.description')}</p>
                  </div>
                </div>
                
                {/* 内容区域 */}
                <div className="p-6">
                  <YesNoQuestionForm
                    userQuestion={userQuestion}
                    setUserQuestion={setUserQuestion}
                    errorMessage={errorMessage}
                    isSubmitting={isSubmitting}
                    onSubmit={handleSubmitQuestion}
                    buttonType="three"
                  />
                </div>
              </div>
            </div>
            
            {/* Perfect Questions for Three Card Yes No Spreads */}
            <ThreeCardQuestions />

            {/* How We Calculate Your Three Card Answer 板块 */}
            <ThreeCardCalculation />
            
            {/* Benefits of Three Card Tarot Reading Yes No 板块 */}
            <ThreeCardBenefits />
            
            {/* Understanding the Three Card Yes No Tarot Spread 板块 */}
            <ThreeCardUnderstanding />

            {/* 更多塔罗占卜区域 */}
            <MoreTarotOptions onNavigate={navigate} pageType="three" />

            {/* SpotlightCard组件 */}
            <ThreeCardSpotlightSection onStartReading={handleStartReading} />
          </div>
        </div>
      </div>

      <Footer />
      
      {/* VIP提示弹窗 */}
      <VipPromptDialog isOpen={showVipPrompt} onCancel={() => setShowVipPrompt(false)} />

      {/* 登录提示弹窗 */}
      <LoginPrompt isOpen={showLoginPrompt} onClose={() => setShowLoginPrompt(false)} />
    </div>
  );
};

export default YesNoThreeCard; 