import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { PAGE_TYPES, useHoroscopeData } from '../../data/horoscopes';
import { useTheme } from '../../contexts/ThemeContext';
import { getFontClass } from '../../utils/fontUtils';
import LandingBackground from '../../components/LandingBackground';
import Footer from '../../components/Footer';
import SEO from '../../components/SEO';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';
import ZodiacSignsGrid from '../../components/horoscope/ZodiacSignsGrid';
import Breadcrumb, { BreadcrumbItem } from '../../components/Breadcrumb';
import HoroscopeNavigation from '../../components/horoscope/HoroscopeNavigation';
import HoroscopeContent from '../../components/horoscope/HoroscopeContent';
import { HOROSCOPE_DETAIL_SEO_TEMPLATES, SIGN_TRAITS } from '../../lib/SEOConfig';
import CdnLazyImage from '../../components/CdnLazyImage';
import { motion } from 'framer-motion';
import HoverableText from '../../components/horoscope/HoverableText';
import SpotlightCard from '../../blocks/Components/SpotlightCard/SpotlightCard';

// 定义多语言标题和副标题的接口
interface MultiLangText {
  en: string;
  zh_CN: string;
  zh_TW: string;
  ja: string;
}

interface HoroscopeTitleTemplate {
  title: MultiLangText;
  subtitle: MultiLangText;
}

// 添加主标题和副标题模板
const HOROSCOPE_TITLE_TEMPLATES: Record<string, HoroscopeTitleTemplate> = {
  // 每日运势模板
  yesterday: {
    title: {
      en: '{SIGN} Yesterday Horoscope - {SIGN_TRAIT} Insights',
      zh_CN: '{SIGN}昨日运势 - {SIGN_TRAIT}星座解读',
      zh_TW: '{SIGN}昨日運勢 - {SIGN_TRAIT}星座解讀',
      ja: '{SIGN}の昨日の運勢 - {SIGN_TRAIT}星座占い'
    },
    subtitle: {
      en: 'Review your {SIGN} horoscope for yesterday with {SIGN_TRAIT} insights on what happened in love, career, and personal growth.',
      zh_CN: '回顾您的{SIGN}昨日星座运势，获取关于爱情、事业和个人成长方面的{SIGN_TRAIT}洞察。',
      zh_TW: '回顧您的{SIGN}昨日星座運勢，獲取關於愛情、事業和個人成長方面的{SIGN_TRAIT}洞察。',
      ja: '昨日の{SIGN}の星座占いを振り返り、愛、キャリア、個人の成長に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  daily: {
    title: {
      en: '{SIGN} Today Horoscope - {SIGN_TRAIT} Daily Reading',
      zh_CN: '{SIGN}今日运势 - {SIGN_TRAIT}每日解读',
      zh_TW: '{SIGN}今日運勢 - {SIGN_TRAIT}每日解讀',
      ja: '{SIGN}の今日の運勢 - {SIGN_TRAIT}デイリーリーディング'
    },
    subtitle: {
      en: 'Your {SIGN} daily horoscope for today with {SIGN_TRAIT} insights on love, career, and personal opportunities.',
      zh_CN: '您今天的{SIGN}每日星座运势，提供关于爱情、事业和个人机遇的{SIGN_TRAIT}洞察。',
      zh_TW: '您今天的{SIGN}每日星座運勢，提供關於愛情、事業和個人機遇的{SIGN_TRAIT}洞察。',
      ja: '今日の{SIGN}の星座占いで、愛、キャリア、個人的な機会に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  tomorrow: {
    title: {
      en: '{SIGN} Tomorrow Horoscope - {SIGN_TRAIT} Predictions',
      zh_CN: '{SIGN}明日运势 - {SIGN_TRAIT}星座预测',
      zh_TW: '{SIGN}明日運勢 - {SIGN_TRAIT}星座預測',
      ja: '{SIGN}の明日の運勢 - {SIGN_TRAIT}星座予測'
    },
    subtitle: {
      en: 'Prepare for tomorrow with your {SIGN} horoscope featuring {SIGN_TRAIT} insights for love, career, and upcoming opportunities.',
      zh_CN: '为明天做好准备，通过{SIGN}星座运势获取关于爱情、事业和即将到来的机遇的{SIGN_TRAIT}洞察。',
      zh_TW: '為明天做好準備，通過{SIGN}星座運勢獲取關於愛情、事業和即將到來的機遇的{SIGN_TRAIT}洞察。',
      ja: '{SIGN}の星座占いで明日に備え、愛、キャリア、今後の機会に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  
  // 每周运势模板
  lastweek: {
    title: {
      en: '{SIGN} Last Week Horoscope - {SIGN_TRAIT} Weekly Review',
      zh_CN: '{SIGN}上周运势 - {SIGN_TRAIT}每周回顾',
      zh_TW: '{SIGN}上週運勢 - {SIGN_TRAIT}每週回顧',
      ja: '{SIGN}の先週の運勢 - {SIGN_TRAIT}週間レビュー'
    },
    subtitle: {
      en: 'Review your {SIGN} horoscope for last week with {SIGN_TRAIT} insights on what happened in love, career, and personal development.',
      zh_CN: '回顾您的{SIGN}上周星座运势，获取关于爱情、事业和个人发展方面的{SIGN_TRAIT}洞察。',
      zh_TW: '回顧您的{SIGN}上週星座運勢，獲取關於愛情、事業和個人發展方面的{SIGN_TRAIT}洞察。',
      ja: '先週の{SIGN}の星座占いを振り返り、愛、キャリア、個人の発展に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  weekly: {
    title: {
      en: '{SIGN} This Week Horoscope - {SIGN_TRAIT} Weekly Forecast',
      zh_CN: '{SIGN}本周运势 - {SIGN_TRAIT}每周预测',
      zh_TW: '{SIGN}本週運勢 - {SIGN_TRAIT}每週預測',
      ja: '{SIGN}の今週の運勢 - {SIGN_TRAIT}週間予測'
    },
    subtitle: {
      en: 'Your {SIGN} weekly horoscope for this week with {SIGN_TRAIT} insights for love, career, and personal opportunities.',
      zh_CN: '您本周的{SIGN}每周星座运势，提供关于爱情、事业和个人机遇的{SIGN_TRAIT}洞察。',
      zh_TW: '您本週的{SIGN}每週星座運勢，提供關於愛情、事業和個人機遇的{SIGN_TRAIT}洞察。',
      ja: '今週の{SIGN}の星座占いで、愛、キャリア、個人的な機会に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  nextweek: {
    title: {
      en: '{SIGN} Next Week Horoscope - {SIGN_TRAIT} Weekly Predictions',
      zh_CN: '{SIGN}下周运势 - {SIGN_TRAIT}每周预测',
      zh_TW: '{SIGN}下週運勢 - {SIGN_TRAIT}每週預測',
      ja: '{SIGN}の来週の運勢 - {SIGN_TRAIT}週間予測'
    },
    subtitle: {
      en: 'Plan ahead with your {SIGN} horoscope for next week featuring {SIGN_TRAIT} insights for love, career, and upcoming opportunities.',
      zh_CN: '提前规划，通过{SIGN}星座运势获取关于下周爱情、事业和即将到来的机遇的{SIGN_TRAIT}洞察。',
      zh_TW: '提前規劃，通過{SIGN}星座運勢獲取關於下週愛情、事業和即將到來的機遇的{SIGN_TRAIT}洞察。',
      ja: '{SIGN}の星座占いで来週に備え、愛、キャリア、今後の機会に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  
  // 每月运势模板
  lastmonth: {
    title: {
      en: '{SIGN} Last Month Horoscope - {SIGN_TRAIT} Monthly Review',
      zh_CN: '{SIGN}上月运势 - {SIGN_TRAIT}每月回顾',
      zh_TW: '{SIGN}上月運勢 - {SIGN_TRAIT}每月回顧',
      ja: '{SIGN}の先月の運勢 - {SIGN_TRAIT}月間レビュー'
    },
    subtitle: {
      en: 'Review your {SIGN} horoscope for last month with {SIGN_TRAIT} insights on what happened in love, career, and personal transformation.',
      zh_CN: '回顾您的{SIGN}上月星座运势，获取关于爱情、事业和个人转变方面的{SIGN_TRAIT}洞察。',
      zh_TW: '回顧您的{SIGN}上月星座運勢，獲取關於愛情、事業和個人轉變方面的{SIGN_TRAIT}洞察。',
      ja: '先月の{SIGN}の星座占いを振り返り、愛、キャリア、個人の変容に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  monthly: {
    title: {
      en: '{SIGN} This Month Horoscope - {SIGN_TRAIT} Monthly Forecast',
      zh_CN: '{SIGN}本月运势 - {SIGN_TRAIT}每月预测',
      zh_TW: '{SIGN}本月運勢 - {SIGN_TRAIT}每月預測',
      ja: '{SIGN}の今月の運勢 - {SIGN_TRAIT}月間予測'
    },
    subtitle: {
      en: 'Your {SIGN} monthly horoscope for this month with {SIGN_TRAIT} insights for love, career, and personal growth opportunities.',
      zh_CN: '您本月的{SIGN}每月星座运势，提供关于爱情、事业和个人成长机遇的{SIGN_TRAIT}洞察。',
      zh_TW: '您本月的{SIGN}每月星座運勢，提供關於愛情、事業和個人成長機遇的{SIGN_TRAIT}洞察。',
      ja: '今月の{SIGN}の星座占いで、愛、キャリア、個人的な成長の機会に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  nextmonth: {
    title: {
      en: '{SIGN} Next Month Horoscope - {SIGN_TRAIT} Monthly Predictions',
      zh_CN: '{SIGN}下月运势 - {SIGN_TRAIT}每月预测',
      zh_TW: '{SIGN}下月運勢 - {SIGN_TRAIT}每月預測',
      ja: '{SIGN}の来月の運勢 - {SIGN_TRAIT}月間予測'
    },
    subtitle: {
      en: 'Plan ahead with your {SIGN} horoscope for next month featuring {SIGN_TRAIT} insights for love, career, and upcoming opportunities.',
      zh_CN: '提前规划，通过{SIGN}星座运势获取关于下月爱情、事业和即将到来的机遇的{SIGN_TRAIT}洞察。',
      zh_TW: '提前規劃，通過{SIGN}星座運勢獲取關於下月愛情、事業和即將到來的機遇的{SIGN_TRAIT}洞察。',
      ja: '{SIGN}の星座占いで来月に備え、愛、キャリア、今後の機会に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  
  // 年度运势模板
  lastyear: {
    title: {
      en: '{SIGN} 2024 Horoscope Review - {SIGN_TRAIT} Annual Summary',
      zh_CN: '{SIGN} 2024年运势回顾 - {SIGN_TRAIT}年度总结',
      zh_TW: '{SIGN} 2024年運勢回顧 - {SIGN_TRAIT}年度總結',
      ja: '{SIGN}の2024年運勢レビュー - {SIGN_TRAIT}年間サマリー'
    },
    subtitle: {
      en: 'Review your {SIGN} horoscope for 2024 with {SIGN_TRAIT} insights on major life events, relationships, and career developments.',
      zh_CN: '回顾您的{SIGN} 2024年星座运势，获取关于重大生活事件、人际关系和职业发展的{SIGN_TRAIT}洞察。',
      zh_TW: '回顧您的{SIGN} 2024年星座運勢，獲取關於重大生活事件、人際關係和職業發展的{SIGN_TRAIT}洞察。',
      ja: '2024年の{SIGN}の星座占いを振り返り、人生の重要なイベント、人間関係、キャリア発展に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  yearly: {
    title: {
      en: '{SIGN} 2025 Horoscope - {SIGN_TRAIT} Annual Forecast',
      zh_CN: '{SIGN} 2025年运势 - {SIGN_TRAIT}年度预测',
      zh_TW: '{SIGN} 2025年運勢 - {SIGN_TRAIT}年度預測',
      ja: '{SIGN}の2025年運勢 - {SIGN_TRAIT}年間予測'
    },
    subtitle: {
      en: 'Your complete {SIGN} horoscope for 2025 with {SIGN_TRAIT} predictions for love, career, health, and major life changes.',
      zh_CN: '您的完整{SIGN} 2025年星座运势，提供关于爱情、事业、健康和重大生活变化的{SIGN_TRAIT}预测。',
      zh_TW: '您的完整{SIGN} 2025年星座運勢，提供關於愛情、事業、健康和重大生活變化的{SIGN_TRAIT}預測。',
      ja: '2025年の{SIGN}の完全な星座占いで、愛、キャリア、健康、人生の重要な変化に関する{SIGN_TRAIT}の予測を得ましょう。'
    }
  },
  nextyear: {
    title: {
      en: '{SIGN} 2026 Horoscope - {SIGN_TRAIT} Annual Predictions',
      zh_CN: '{SIGN} 2026年运势 - {SIGN_TRAIT}年度预测',
      zh_TW: '{SIGN} 2026年運勢 - {SIGN_TRAIT}年度預測',
      ja: '{SIGN}の2026年運勢 - {SIGN_TRAIT}年間予測'
    },
    subtitle: {
      en: 'Plan ahead with your {SIGN} horoscope for 2026 featuring {SIGN_TRAIT} insights for love, career, and long-term opportunities.',
      zh_CN: '提前规划，通过{SIGN} 2026年星座运势获取关于爱情、事业和长期机遇的{SIGN_TRAIT}洞察。',
      zh_TW: '提前規劃，通過{SIGN} 2026年星座運勢獲取關於愛情、事業和長期機遇的{SIGN_TRAIT}洞察。',
      ja: '2026年の{SIGN}の星座占いで先に計画し、愛、キャリア、長期的な機会に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  
  // 爱情运势模板
  lastlove: {
    title: {
      en: '{SIGN} Last Month Love Horoscope - {SIGN_TRAIT} Romance Review',
      zh_CN: '{SIGN}上月爱情运势 - {SIGN_TRAIT}情感回顾',
      zh_TW: '{SIGN}上月愛情運勢 - {SIGN_TRAIT}情感回顧',
      ja: '{SIGN}の先月の恋愛運勢 - {SIGN_TRAIT}ロマンスレビュー'
    },
    subtitle: {
      en: 'Review your {SIGN} love horoscope for last month with {SIGN_TRAIT} insights on relationships, compatibility, and romantic experiences.',
      zh_CN: '回顾您的{SIGN}上月爱情星座运势，获取关于人际关系、匹配度和浪漫体验的{SIGN_TRAIT}洞察。',
      zh_TW: '回顧您的{SIGN}上月愛情星座運勢，獲取關於人際關係、匹配度和浪漫體驗的{SIGN_TRAIT}洞察。',
      ja: '先月の{SIGN}の恋愛星座占いを振り返り、人間関係、相性、ロマンチックな経験に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  love: {
    title: {
      en: '{SIGN} Love Horoscope - {SIGN_TRAIT} Romance Reading',
      zh_CN: '{SIGN}爱情运势 - {SIGN_TRAIT}情感解读',
      zh_TW: '{SIGN}愛情運勢 - {SIGN_TRAIT}情感解讀',
      ja: '{SIGN}の恋愛運勢 - {SIGN_TRAIT}ロマンスリーディング'
    },
    subtitle: {
      en: 'Discover your {SIGN} love horoscope with {SIGN_TRAIT} insights for romance, relationships, and compatibility connections.',
      zh_CN: '探索您的{SIGN}爱情星座运势，获取关于浪漫、人际关系和匹配度的{SIGN_TRAIT}洞察。',
      zh_TW: '探索您的{SIGN}愛情星座運勢，獲取關於浪漫、人際關係和匹配度的{SIGN_TRAIT}洞察。',
      ja: '{SIGN}の恋愛星座占いを発見し、ロマンス、人間関係、相性に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  },
  nextlove: {
    title: {
      en: '{SIGN} Next Month Love Horoscope - {SIGN_TRAIT} Romance Forecast',
      zh_CN: '{SIGN}下月爱情运势 - {SIGN_TRAIT}情感预测',
      zh_TW: '{SIGN}下月愛情運勢 - {SIGN_TRAIT}情感預測',
      ja: '{SIGN}の来月の恋愛運勢 - {SIGN_TRAIT}ロマンス予測'
    },
    subtitle: {
      en: 'Plan your love life with your {SIGN} romance horoscope for next month featuring {SIGN_TRAIT} insights on relationships and compatibility.',
      zh_CN: '规划您的爱情生活，通过{SIGN}下月情感星座运势获取关于人际关系和匹配度的{SIGN_TRAIT}洞察。',
      zh_TW: '規劃您的愛情生活，通過{SIGN}下月情感星座運勢獲取關於人際關係和匹配度的{SIGN_TRAIT}洞察。',
      ja: '{SIGN}の来月のロマンス星座占いで恋愛生活を計画し、人間関係と相性に関する{SIGN_TRAIT}の洞察を得ましょう。'
    }
  }
};

const UnifiedHoroscopeDetail: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const { getSignName } = useHoroscopeData();
  
  // 获取URL参数 - 格式为 /:signId-:pageType-horoscope
  const { horoscopeParam } = useParams<{ horoscopeParam: string }>();
  
  // 解析URL参数获取页面类型，用于SEO
  const [pageType, setPageType] = useState<string>('');
  const [signId, setSignId] = useState<string>('');
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  
  // 添加一个状态标志，用于控制HoroscopeNavigation组件的渲染时机
  const [isPageReady, setIsPageReady] = useState<boolean>(false);
  
  // 获取一周的开始日期（周一）
  const getStartOfWeek = (date: Date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一为一周的第一天
    return new Date(d.setDate(diff));
  };
  
  // 获取月初日期（1号）
  const getStartOfMonth = (date: Date) => {
    const d = new Date(date);
    d.setDate(1); // 设置为当月1号
    return d;
  };
  
  // 获取年初日期（1月1日）
  const getStartOfYear = (date: Date) => {
    const d = new Date(date);
    d.setMonth(0); // 设置为1月
    d.setDate(1); // 设置为1号
    return d;
  };
  
  // 根据页面类型获取适当的日期
  const getDateForPageType = (type: string) => {
    const today = new Date();
    
    switch (type) {
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        return yesterday;
      case 'tomorrow':
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        return tomorrow;
      case PAGE_TYPES.WEEKLY:
        // 对于周运势，返回本周的开始日期（周一）
        // 确保使用周一日期，与生成文件命名规则一致
        return getStartOfWeek(today);
      case 'lastweek':
        // 上周
        // 确保使用上周一日期，与生成文件命名规则一致
        const lastWeek = getStartOfWeek(today);
        lastWeek.setDate(lastWeek.getDate() - 7);
        return lastWeek;
      case 'nextweek':
        // 下周
        // 确保使用下周一日期，与生成文件命名规则一致
        const nextWeek = getStartOfWeek(today);
        nextWeek.setDate(nextWeek.getDate() + 7);
        return nextWeek;
      case PAGE_TYPES.MONTHLY:
        // 本月 - 返回当月1号，与生成文件命名规则一致
        return getStartOfMonth(today);
      case 'lastmonth':
        // 上个月 - 返回上月1号，与生成文件命名规则一致
        const lastMonth = new Date(today);
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        return getStartOfMonth(lastMonth);
      case 'nextmonth':
        // 下个月 - 返回下月1号，与生成文件命名规则一致
        const nextMonth = new Date(today);
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        return getStartOfMonth(nextMonth);
      case PAGE_TYPES.YEARLY:
        // 本年 - 返回当年1月1号，与生成文件命名规则一致
        return getStartOfYear(today);
      case 'lastyear':
        // 去年 - 返回去年1月1号，与生成文件命名规则一致
        const lastYear = new Date(today);
        lastYear.setFullYear(lastYear.getFullYear() - 1);
        return getStartOfYear(lastYear);
      case 'nextyear':
        // 明年 - 返回明年1月1号，与生成文件命名规则一致
        const nextYear = new Date(today);
        nextYear.setFullYear(nextYear.getFullYear() + 1);
        return getStartOfYear(nextYear);
      case PAGE_TYPES.LOVE:
        // 爱情运势 - 返回当月1号，与生成文件命名规则一致
        return getStartOfMonth(today);
      case 'lastlove':
        // 上个月爱情 - 返回上月1号，与生成文件命名规则一致
        const lastLove = new Date(today);
        lastLove.setMonth(lastLove.getMonth() - 1);
        return getStartOfMonth(lastLove);
      case 'nextlove':
        // 下个月爱情 - 返回下月1号，与生成文件命名规则一致
        const nextLove = new Date(today);
        nextLove.setMonth(nextLove.getMonth() + 1);
        return getStartOfMonth(nextLove);
      default:
        return today;
    }
  };
  
  // 解析URL参数
  useEffect(() => {
    // 立即设置页面未准备好，显示占位符
    setIsPageReady(false);
    
    if (horoscopeParam) {
      // 预期格式: signId-pageType-horoscope
      const parts = horoscopeParam.split('-');
      if (parts.length >= 3) {
        const newSignId = parts[0];
        const newPageType = parts[1];
        
        setSignId(newSignId);
        setPageType(newPageType);
        
        // 根据页面类型设置正确的日期
        setCurrentDate(getDateForPageType(newPageType));
        
        // 使用requestAnimationFrame确保DOM更新后再设置isPageReady为true
        // 这比setTimeout更可靠，因为它会在下一帧渲染前执行
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            setIsPageReady(true);
          });
        });
      }
    }
  }, [horoscopeParam]);
  
  // 获取当前年月信息
  const getCurrentYearMonth = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.toLocaleString('en-US', { month: 'long' });
    return { year, month };
  };
  
  // 获取页面标题
  const getPageTitle = () => {
    // 如果没有星座ID，返回默认标题
    if (!signId) {
      return t('horoscope.title', '免费星座运势预测 | 12星座占星分析');
    }
    
    // 获取星座名称和特征词
    const signName = t(`zodiac.${signId}`, getSignName(signId));
    const signTrait = SIGN_TRAITS[signId]?.[i18n.language === 'zh-CN' ? 'zh_CN' : 
                                          i18n.language === 'zh-TW' ? 'zh_TW' : 
                                          i18n.language === 'ja' ? 'ja' : 'en'] || '';
    
    // 获取当前年份（用于年度运势）
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;
    
    // 确定当前语言
    const langKey = i18n.language === 'zh-CN' ? 'zh_CN' : 
                    i18n.language === 'zh-TW' ? 'zh_TW' : 
                    i18n.language === 'ja' ? 'ja' : 'en';
    
    // 根据页面类型获取标题模板
    const titleTemplate = HOROSCOPE_TITLE_TEMPLATES[pageType as keyof typeof HOROSCOPE_TITLE_TEMPLATES]?.title?.[langKey];
    
    if (titleTemplate) {
      // 替换占位符
      return titleTemplate.replace('{SIGN}', signName)
                          .replace('{SIGN_TRAIT}', signTrait)
                          .replace('{YEAR}', pageType.includes('next') ? String(nextYear) : String(currentYear));
    }
    
    // 如果没有找到模板，使用原有的逻辑
    const { year, month } = getCurrentYearMonth();
    
    switch (pageType) {
      case PAGE_TYPES.DAILY:
        return `${signName} Daily Horoscope for Today`;
      case 'yesterday':
        return `${signName} Daily Horoscope for Yesterday`;
      case 'tomorrow':
        return `${signName} Daily Horoscope for Tomorrow`;
      case PAGE_TYPES.WEEKLY:
        return `${signName} Weekly Horoscope`;
      case 'lastweek':
        return `${signName} Last Week Horoscope`;
      case 'nextweek':
        return `${signName} Next Week Horoscope`;
      case PAGE_TYPES.MONTHLY:
        return `${signName} ${month} ${year} Monthly Horoscope`;
      case 'lastmonth':
        // 获取上个月的月份名称和年份
        const lastMonth = new Date();
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        const lastMonthName = lastMonth.toLocaleString('en-US', { month: 'long' });
        const lastMonthYear = lastMonth.getFullYear();
        return `${signName} ${lastMonthName} ${lastMonthYear} Monthly Horoscope`;
      case 'nextmonth':
        // 获取下个月的月份名称和年份
        const nextMonth = new Date();
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        const nextMonthName = nextMonth.toLocaleString('en-US', { month: 'long' });
        const nextMonthYear = nextMonth.getFullYear();
        return `${signName} ${nextMonthName} ${nextMonthYear} Monthly Horoscope`;
      case PAGE_TYPES.YEARLY:
        return `${signName} Yearly Horoscope ${year}`;
      case 'lastyear':
        // 获取去年的年份
        const lastYear = new Date();
        lastYear.setFullYear(lastYear.getFullYear() - 1);
        const lastYearNum = lastYear.getFullYear();
        return `${signName} Yearly Horoscope ${lastYearNum}`;
      case 'nextyear':
        // 获取明年的年份
        const nextYear = new Date();
        nextYear.setFullYear(nextYear.getFullYear() + 1);
        const nextYearNum = nextYear.getFullYear();
        return `${signName} Yearly Horoscope ${nextYearNum}`;
      case PAGE_TYPES.LOVE:
        return `${signName} Love Horoscope ${month} ${year}`;
      case 'lastlove':
        // 获取上个月的月份名称和年份
        const lastLoveMonth = new Date();
        lastLoveMonth.setMonth(lastLoveMonth.getMonth() - 1);
        const lastLoveMonthName = lastLoveMonth.toLocaleString('en-US', { month: 'long' });
        const lastLoveYear = lastLoveMonth.getFullYear();
        return `${signName} Love Horoscope ${lastLoveMonthName} ${lastLoveYear}`;
      case 'nextlove':
        // 获取下个月的月份名称和年份
        const nextLoveMonth = new Date();
        nextLoveMonth.setMonth(nextLoveMonth.getMonth() + 1);
        const nextLoveMonthName = nextLoveMonth.toLocaleString('en-US', { month: 'long' });
        const nextLoveYear = nextLoveMonth.getFullYear();
        return `${signName} Love Horoscope ${nextLoveMonthName} ${nextLoveYear}`;
      default:
        return t('horoscope.title', '免费星座运势预测 | 12星座占星分析');
    }
  };
  
  // 获取页面副标题
  const getPageSubtitle = () => {
    // 如果没有星座ID，返回默认副标题
    if (!signId) {
      return t('horoscope.subheading', 'Navigate your life with hyper-personalized horoscopes, powered by real astrological data and AI-driven insights');
    }
    
    // 获取星座名称和特征词
    const signName = t(`zodiac.${signId}`, getSignName(signId));
    const signTrait = SIGN_TRAITS[signId]?.[i18n.language === 'zh-CN' ? 'zh_CN' : 
                                          i18n.language === 'zh-TW' ? 'zh_TW' : 
                                          i18n.language === 'ja' ? 'ja' : 'en'] || '';
    
    // 获取当前年份（用于年度运势）
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;
    
    // 确定当前语言
    const langKey = i18n.language === 'zh-CN' ? 'zh_CN' : 
                    i18n.language === 'zh-TW' ? 'zh_TW' : 
                    i18n.language === 'ja' ? 'ja' : 'en';
    
    // 根据页面类型获取副标题模板
    const subtitleTemplate = HOROSCOPE_TITLE_TEMPLATES[pageType as keyof typeof HOROSCOPE_TITLE_TEMPLATES]?.subtitle?.[langKey];
    
    if (subtitleTemplate) {
      // 替换占位符
      return subtitleTemplate.replace('{SIGN}', signName)
                             .replace('{SIGN_TRAIT}', signTrait)
                             .replace('{YEAR}', pageType.includes('next') ? String(nextYear) : String(currentYear));
    }
    
    // 如果没有找到模板，使用原有的逻辑
    switch (pageType) {
      case PAGE_TYPES.DAILY:
      case 'yesterday':
      case 'tomorrow':
        return t('horoscope.daily_subheading', '每日星座运势，助你把握今日机遇与挑战');
      case PAGE_TYPES.WEEKLY:
      case 'lastweek':
      case 'nextweek':
        return t('horoscope.weekly_subheading', '每周星座运势，助你规划未来七天的行动方向');
      case PAGE_TYPES.MONTHLY:
        return t('horoscope.monthly_subheading', '每月星座运势，助你了解本月整体走向与关键时刻');
      case PAGE_TYPES.YEARLY:
        return t('horoscope.yearly_subheading', '年度星座运势，助你把握全年关键机遇与挑战');
      case PAGE_TYPES.LOVE:
        return t('horoscope.love_subheading', '星座爱情运势，解读你的情感世界与桃花运势');
      default:
        return t('horoscope.subheading', 'Navigate your life with hyper-personalized horoscopes, powered by real astrological data and AI-driven insights');
    }
  };
  
  // 生成面包屑导航项
  const getBreadcrumbItems = (): BreadcrumbItem[] => {
    const items: BreadcrumbItem[] = [
      {
        label: t('horoscope.title_short', '星座运势'),
        path: '/horoscope',
      }
    ];
    
    // 如果有页面类型，添加页面类型导航
    if (pageType) {
      let pageTypeLabel = '';
      let pageTypePath = '';
      
      switch (pageType) {
        case PAGE_TYPES.DAILY:
        case 'yesterday':
        case 'tomorrow':
          pageTypeLabel = t('horoscope.daily_title', '每日运势');
          pageTypePath = `${PAGE_TYPES.DAILY}-horoscope`;
          break;
        case PAGE_TYPES.WEEKLY:
        case 'lastweek':
        case 'nextweek':
          pageTypeLabel = t('horoscope.weekly_title', '每周运势');
          pageTypePath = `${PAGE_TYPES.WEEKLY}-horoscope`;
          break;
        case PAGE_TYPES.MONTHLY:
        case 'lastmonth':
        case 'nextmonth':
          pageTypeLabel = t('horoscope.monthly_title', '每月运势');
          pageTypePath = `${pageType === 'lastmonth' || pageType === 'nextmonth' ? PAGE_TYPES.MONTHLY : pageType}-horoscope`;
          break;
        case PAGE_TYPES.YEARLY:
        case 'lastyear':
        case 'nextyear':
          pageTypeLabel = t('horoscope.yearly_title', '年度运势');
          pageTypePath = `${pageType === 'lastyear' || pageType === 'nextyear' ? PAGE_TYPES.YEARLY : pageType}-horoscope`;
          break;
        case PAGE_TYPES.LOVE:
        case 'lastlove':
        case 'nextlove':
          pageTypeLabel = t('horoscope.love_title', '爱情运势');
          pageTypePath = `${pageType === 'lastlove' || pageType === 'nextlove' ? PAGE_TYPES.LOVE : pageType}-horoscope`;
          break;
      }
      
      if (pageTypeLabel) {
        items.push({
          label: pageTypeLabel,
          path: `/horoscope/${pageTypePath}`,
        });
      }
    }
    
    // 如果有星座ID，添加星座详情页面（放在最后）
    if (signId) {
      const signName = getSignName(signId);
      items.push({
        label: signName,
        path: `/horoscope/${signId}`,
        isActive: true
      });
    }
    
    return items;
  };
  
  // 处理星座点击事件
  const handleSignClick = (clickedSignId: string) => {
    // 如果点击的是当前星座，不做任何操作
    if (clickedSignId === signId) {
      return;
    }
    
    // 直接导航到对应星座的当前页面类型
    const currentPageType = pageType || 'daily';
    
    // 设置页面未准备好，以便在导航后重新初始化
    setIsPageReady(false);
    
    // 导航到新页面
    navigate(`/horoscope/${clickedSignId}-${currentPageType}-horoscope`);
  };

  // 处理类型变更
  const handleTypeChange = (type: string) => {
    // 如果类型相同，不做任何操作
    if (type === pageType) {
      return;
    }
    
    // 设置页面未准备好
    setIsPageReady(false);
    
    if (signId) {
      navigate(`/horoscope/${signId}-${type}-horoscope`);
    } else {
      navigate(`/horoscope/${type}-horoscope`);
    }
  };
  
  // 处理日期变更
  const handleDateChange = (direction: 'prev' | 'current' | 'next') => {
    if (!signId) return;
    
    // 设置页面未准备好
    setIsPageReady(false);
    
    if (pageType === PAGE_TYPES.DAILY || pageType === 'yesterday' || pageType === 'tomorrow') {
      if (direction === 'prev') {
        navigate(`/horoscope/${signId}-yesterday-horoscope`);
      } else if (direction === 'next') {
        navigate(`/horoscope/${signId}-tomorrow-horoscope`);
      } else {
        navigate(`/horoscope/${signId}-daily-horoscope`);
      }
      return;
    } else if (pageType === PAGE_TYPES.WEEKLY || pageType === 'lastweek' || pageType === 'nextweek') {
      if (direction === 'prev') {
        navigate(`/horoscope/${signId}-lastweek-horoscope`);
      } else if (direction === 'next') {
        navigate(`/horoscope/${signId}-nextweek-horoscope`);
      } else {
        navigate(`/horoscope/${signId}-weekly-horoscope`);
      }
      return;
    } else if (pageType === PAGE_TYPES.MONTHLY || pageType === 'lastmonth' || pageType === 'nextmonth') {
      if (direction === 'prev') {
        navigate(`/horoscope/${signId}-lastmonth-horoscope`);
      } else if (direction === 'next') {
        navigate(`/horoscope/${signId}-nextmonth-horoscope`);
      } else {
        navigate(`/horoscope/${signId}-monthly-horoscope`);
      }
      return;
    } else if (pageType === PAGE_TYPES.YEARLY || pageType === 'lastyear' || pageType === 'nextyear') {
      if (direction === 'prev') {
        navigate(`/horoscope/${signId}-lastyear-horoscope`);
      } else if (direction === 'next') {
        navigate(`/horoscope/${signId}-nextyear-horoscope`);
      } else {
        navigate(`/horoscope/${signId}-yearly-horoscope`);
      }
      return;
    } else if (pageType === PAGE_TYPES.LOVE || pageType === 'lastlove' || pageType === 'nextlove') {
      if (direction === 'prev') {
        navigate(`/horoscope/${signId}-lastlove-horoscope`);
      } else if (direction === 'next') {
        navigate(`/horoscope/${signId}-nextlove-horoscope`);
      } else {
        navigate(`/horoscope/${signId}-love-horoscope`);
      }
      return;
    }
    
    // 其他类型的处理逻辑
    const newDate = new Date(currentDate);
    
    switch (pageType) {
      case PAGE_TYPES.YEARLY:
        // 年运势：去年、今年、明年
        if (direction === 'prev') {
          newDate.setFullYear(newDate.getFullYear() - 1);
        } else if (direction === 'next') {
          newDate.setFullYear(newDate.getFullYear() + 1);
        } else {
          // 重置为今年
          setCurrentDate(new Date());
          return;
        }
        break;
      
      default:
        // 默认行为
        if (direction === 'current') {
          setCurrentDate(new Date());
          return;
        }
        break;
    }
    
    setCurrentDate(newDate);
  };

  // 只有在signId存在时才传递defaultSelectedSign，避免默认选择白羊座导致闪烁
  const defaultSelectedSign = signId ? signId : undefined;

  // 获取SEO配置
  const getSeoConfig = () => {
    // 如果没有星座ID或页面类型，返回默认SEO
    if (!signId || !pageType) {
      return {
        title: t('horoscope.title', '免费星座运势预测 | 12星座占星分析'),
        description: t('horoscope.description', '专业占星师解读十二星座运势指南，包括每日、每周和年度运势分析')
      };
    }

    // 获取星座名称和特征词
    const signName = t(`zodiac.${signId}`, getSignName(signId));
    const signTrait = SIGN_TRAITS[signId]?.[i18n.language === 'zh-CN' ? 'zh_CN' : 
                                          i18n.language === 'zh-TW' ? 'zh_TW' : 
                                          i18n.language === 'ja' ? 'ja' : 'en'] || '';

    // 获取当前年份（用于年度运势）
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;

    // 根据页面类型获取SEO模板
    const seoTemplate = HOROSCOPE_DETAIL_SEO_TEMPLATES[pageType as keyof typeof HOROSCOPE_DETAIL_SEO_TEMPLATES];
    if (!seoTemplate) {
      return {
        title: getPageTitle(),
        description: t('horoscope.description', '专业占星师解读十二星座运势指南，包括每日、每周和年度运势分析')
      };
    }

    // 获取当前语言的标题和描述
    const langKey = i18n.language === 'zh-CN' ? 'zh_CN' : 
                    i18n.language === 'zh-TW' ? 'zh_TW' : 
                    i18n.language === 'ja' ? 'ja' : 'en';
    
    let title = seoTemplate.title[langKey] || '';
    let description = seoTemplate.description[langKey] || '';

    // 替换占位符
    title = title.replace(/{SIGN}/g, signName)
                 .replace(/{SIGN_TRAIT}/g, signTrait)
                 .replace(/{YEAR}/g, pageType.includes('next') ? String(nextYear) : String(currentYear));
    
    description = description.replace(/{SIGN}/g, signName)
                           .replace(/{SIGN_TRAIT}/g, signTrait)
                           .replace(/{YEAR}/g, pageType.includes('next') ? String(nextYear) : String(currentYear));

    return { 
      title, 
      description,
      ogType: (seoTemplate.ogType || 'article') as 'article' | 'website'
    };
  };

  // 获取SEO配置
  const seoConfig = getSeoConfig();

  // 处理开始阅读按钮点击
  const handleStartReading = () => {
    navigate('/');
  };

  return (
    <div className={`min-h-screen flex flex-col relative antialiased ${isDark ? 'text-white' : 'text-gray-800'}`}>
      <SEO 
        title={seoConfig.title}
        description={seoConfig.description}
        ogType={seoConfig.ogType}
      />
      <LandingBackground />
      
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-16">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
            <h1 className={`main-title mb-1 sm:mb-2 ${getFontClass(i18n.language)} dark:text-white text-gray-900`}>
              <span className="block">{getPageTitle()}</span>
            </h1>
            <h2 className={`text-base sm:text-lg ${isDark ? 'text-purple-300' : 'text-purple-600'} italic ${getFontClass(i18n.language)} font-normal`}>
              {getPageSubtitle()}
            </h2>
          </div>

          {/* 星座运势网格 - 使用ZodiacSignsGrid组件 */}
          <ZodiacSignsGrid 
            containerClassName="max-w-4xl mx-auto" 
            horoscopeType={
              pageType === PAGE_TYPES.DAILY || pageType === 'yesterday' || pageType === 'tomorrow' ? 'daily' :
              pageType === PAGE_TYPES.WEEKLY || pageType === 'lastweek' || pageType === 'nextweek' ? 'weekly' :
              pageType === PAGE_TYPES.MONTHLY || pageType === 'lastmonth' || pageType === 'nextmonth' ? 'monthly' :
              pageType === PAGE_TYPES.YEARLY || pageType === 'lastyear' || pageType === 'nextyear' ? 'yearly' :
              pageType === PAGE_TYPES.LOVE || pageType === 'lastlove' || pageType === 'nextlove' ? 'love' : 'daily'
            }
            fullPageType={pageType}
            defaultSelectedSign={defaultSelectedSign}
            onSignClick={handleSignClick}
            showPreview={false}
          />
          
          {/* 面包屑导航 */}
          <div className="max-w-6xl mx-auto px-4 sm:px-6 mt-8">
            <Breadcrumb items={getBreadcrumbItems()} className="text-base" />
          </div>
          
          {/* HoroscopeNavigation组件 - 使用占位符保持高度稳定 */}
          <div className="max-w-md mx-auto mt-12 mb-12 min-h-[180px] relative">
            <div className={`absolute top-0 left-0 w-full transition-opacity duration-300 ${isPageReady ? 'opacity-100 z-10' : 'opacity-0 z-0'}`}>
              <HoroscopeNavigation
                key={`${signId}-${pageType}`}
                currentType={pageType}
                currentDate={currentDate}
                signId={signId}
                onTypeChange={handleTypeChange}
                onDateChange={handleDateChange}
              />
            </div>
            
            {/* 占位符结构，与实际组件结构相同，但透明 */}
            <div className={`absolute top-0 left-0 w-full transition-opacity duration-300 pointer-events-none ${isPageReady ? 'opacity-0' : 'opacity-40'}`}>
              <div className="w-full flex flex-col items-center space-y-6">
                {/* 模拟下拉菜单 */}
                <div className="relative w-full max-w-sm mx-auto">
                  <div className="w-full h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                </div>
                
                {/* 模拟时间周期切换 */}
                <div className="flex items-center justify-center space-x-6">
                  <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <span>|</span>
                  <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <span>|</span>
                  <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
                
                {/* 模拟日期显示 */}
                <div className="w-40 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          </div>
          
          {/* 显示星座运势内容 */}
          <div className="max-w-4xl mx-auto mt-12">
            {signId && pageType && (
              <HoroscopeContent 
                signId={signId} 
                pageType={pageType} 
                date={currentDate} 
              />
            )}
          </div>

          {/* Your Horoscope is Just the Beginning 板块 */}
          <div className="mt-36 max-w-6xl mx-auto px-4 sm:px-6">
            <div className="text-center mb-16">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                isDark ? 'text-white' : 'text-purple-800'
              }`}>
                {t('horoscope.beginning.title', 'Your Free Horoscope is Just the Beginning')}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
            </div>
            
            <div className="flex flex-col md:flex-row gap-12 md:gap-16 items-center">
              {/* 左侧图片 */}
              <div className="md:w-1/2">
                <div className="rounded-xl overflow-hidden">
                  <CdnLazyImage 
                    src="/images-optimized/daily-fortune/step3-select-tarot.webp" 
                    alt={t('horoscope.beginning.image_alt', 'Multi-dimensional Astrology and Tarot Reading')} 
                    className="w-full h-auto object-cover"
                  />
                </div>
              </div>
              
              {/* 右侧文案 */}
              <div className="md:w-1/2">
                <h3 className={`text-xl font-semibold mb-6 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t('horoscope.beginning.subtitle', 'Free Tarot Reading & Astrology: The Ultimate Cosmic Partnership')}
                </h3>
                <p className="body-text dark:text-gray-300 text-gray-600 mb-6">
                  {t('horoscope.beginning.description', 'Free tarot card readings and zodiac energies unite for powerful insights. Our AI merges ancient tarot symbolism with precise astrology for personalized guidance across all life dimensions.')}
                </p>
                <ul className="space-y-8">
                  <li className="flex items-start">
                    <div className="mr-3 mt-1">
                      <div className="w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900/60 flex items-center justify-center">
                        <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M12 2v10l4.24 4.24"></path>
                          <circle cx="12" cy="12" r="10"></circle>
                        </svg>
                      </div>
                    </div>
                    <div>
                      <HoverableText 
                        as="h4" 
                        onClick={() => navigate('/daily-fortune')}
                        className="font-medium dark:text-purple-300 text-purple-700 text-lg mb-2"
                      >
                        {t('horoscope.beginning.daily.title', 'Daily Horoscope & Free Tarot Reading')}
                      </HoverableText>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('horoscope.beginning.daily.description', 'Precise daily guidance blending free tarot wisdom with your zodiac\'s planetary influences for actionable insights and today\'s horoscope predictions.')}
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-3 mt-1">
                      <div className="w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900/60 flex items-center justify-center">
                        <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                        </svg>
                      </div>
                    </div>
                    <div>
                      <HoverableText 
                        as="h4" 
                        onClick={() => navigate('/home')}
                        className="font-medium dark:text-purple-300 text-purple-700 text-lg mb-2"
                      >
                        {t('home.tarot_types.ai_tarot.title', 'AI塔罗牌占卜')}
                      </HoverableText>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('home.tarot_types.ai_tarot.description', '结合人工智能与传统塔罗智慧，我们的AI塔罗占卜为您提供个性化、深度的牌阵解读，帮助您洞察生活难题和未来走向。通过高级算法分析牌面关系，提供比传统占卜更全面的视角。')}
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-3 mt-1">
                      <div className="w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900/60 flex items-center justify-center">
                        <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"></circle>
                          <line x1="12" y1="8" x2="12" y2="16"></line>
                          <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>
                      </div>
                    </div>
                    <div>
                      <HoverableText 
                        as="h4" 
                        onClick={() => navigate('/yes-no-tarot')}
                        className="font-medium dark:text-purple-300 text-purple-700 text-lg mb-2"
                      >
                        {t('home.tarot_types.yes_no_tarot.title', '是否塔罗占卜')}
                      </HoverableText>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('home.tarot_types.yes_no_tarot.description', '面对人生关键决策时，我们的是非塔罗能提供清晰指引。只需问一个具体问题，抽一张牌，立即获得宇宙的回应。每张牌都有明确的是与否指向，并辅以深入解释，帮您在人生十字路口找到方向。')}
                      </p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* 添加探索区域 */}
          <div className="spotlight-section py-24 md:py-32 mt-28">
            <div className="max-w-3xl mx-auto px-2 sm:px-4">
              <SpotlightCard
                className="custom-spotlight-card"
                spotlightColor="rgba(0, 229, 255, 0.2)"
              >
                <div className="p-6 sm:p-10 text-center">
                  <h3
                    className="text-2xl md:text-3xl font-semibold mb-4"
                    style={{
                      background: theme === 'light' 
                        ? "none" 
                        : "linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)",
                      WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                      WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                      color: theme === 'light' ? "#000" : "inherit"
                    }}
                  >
                    {t("home.explore_section.title", "探索塔罗牌阅读")}
                  </h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                  } text-lg md:text-xl mb-6 px-1`}>
                    {t("home.explore_section.description", "开始您的塔罗之旅，获取专属于您的塔罗牌阅读")}
                  </p>
                  <div className="flex justify-center">
                    <motion.button
                      onClick={handleStartReading}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-6 py-3 rounded-full"
                      style={{
                        background:
                          theme === 'light'
                            ? "linear-gradient(135deg, rgba(124, 58, 237, 0.7), rgba(168, 85, 247, 0.7))"
                            : "linear-gradient(135deg, rgba(124, 58, 237, 0.8), rgba(168, 85, 247, 0.8))",
                        boxShadow: theme === 'light' 
                          ? "0 0 20px rgba(168, 85, 247, 0.4)"
                          : "0 0 20px rgba(168, 85, 247, 0.5)",
                        color: 'white',
                      }}
                    >
                      {t("home.explore_section.button", "开始阅读")}
                    </motion.button>
                  </div>
                </div>
              </SpotlightCard>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default UnifiedHoroscopeDetail; 