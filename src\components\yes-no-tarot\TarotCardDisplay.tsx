import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import TiltImage from "../../blocks/Components/TiltImage/TiltImage";
import { TAROT_CARDS } from '../../data/tarot-cards';

interface TarotCardDisplayProps {
  flipped: boolean;
  cardBackImage: string;
  selectedCard: number | null;
  cardImage: string | null;
  cardOrientation: boolean;
  processingCard: boolean;
  handleCardFlip: () => void;
  getFontClass: () => string;
}

const TarotCardDisplay: React.FC<TarotCardDisplayProps> = ({
  flipped,
  cardBackImage,
  selectedCard,
  cardImage,
  cardOrientation,
  processingCard,
  handleCardFlip,
  getFontClass
}) => {
  const { t } = useTranslation();

  return (
    <motion.div 
      className="relative w-full max-w-md mx-auto mt-8 mb-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex justify-center">
        <div className="relative">
          <div className="w-[160px] h-[285px] sm:w-[240px] sm:h-[428px]">
            {!flipped ? (
              <motion.div
                initial={{ rotateY: 0 }}
                exit={{ rotateY: 90 }}
                transition={{ duration: 0.3 }}
                className=""
              >
                <TiltImage
                  imageUrl={cardBackImage}
                  alt={t('daily.card_back', '塔罗牌背面')}
                  className="cursor-pointer"
                  enableTilt={true}
                  onClick={handleCardFlip}
                />
              </motion.div>
            ) : (
              <motion.div
                initial={{ rotateY: -90 }}
                animate={{ rotateY: 0 }}
                transition={{ duration: 0.3 }}
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'relative',
                }}
              >
                <motion.div 
                  className="h-full w-full flex items-center justify-center"
                  style={{ 
                    transform: cardOrientation ? 'rotate(180deg)' : 'rotate(0deg)'
                  }}
                >
                  <TiltImage
                    imageUrl={cardImage || (selectedCard !== null ? `/images-optimized/tarot/${TAROT_CARDS[selectedCard].nameEn}.webp` : '')}
                    alt={selectedCard !== null ? (() => {
                      const card = TAROT_CARDS[selectedCard];
                      const nameEn = card.nameEn?.toLowerCase();
                      
                      if (selectedCard <= 21) {
                        // Major Arcana
                        const translationKey = `reading.cards.major.${selectedCard}`;
                        return String(t(translationKey));
                      } else {
                        // Minor Arcana
                        const suit = nameEn.includes('wands') ? 'wands' :
                                 nameEn.includes('cups') ? 'cups' :
                                 nameEn.includes('swords') ? 'swords' :
                                 'pentacles';
                        
                        const rankMap: { [key: string]: string } = {
                          'ace': 'ace',
                          'two': '2',
                          'three': '3',
                          'four': '4',
                          'five': '5',
                          'six': '6',
                          'seven': '7',
                          'eight': '8',
                          'nine': '9',
                          'ten': '10',
                          'page': 'page',
                          'knight': 'knight',
                          'queen': 'queen',
                          'king': 'king'
                        };
                        
                        const rank = Object.keys(rankMap).find(r => nameEn.includes(r));
                        return rank ? String(t(`reading.cards.${suit}.${rankMap[rank]}`)) : card.name;
                      }
                    })() : t('daily.card_back', '塔罗牌')}
                    className="cursor-pointer"
                    enableTilt={true}
                  />
                </motion.div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
      
      {/* 显示卡牌名称和正逆位 */}
      {flipped && selectedCard !== null && (
        <motion.div 
          className={`text-center ${getFontClass()} mt-2`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <div className="flex items-center justify-center space-x-2">
            <span className="text-purple-200 font-medium text-lg">
              {(() => {
                const card = TAROT_CARDS[selectedCard];
                const nameEn = card.nameEn?.toLowerCase();
                
                if (selectedCard <= 21) {
                  // Major Arcana
                  const translationKey = `reading.cards.major.${selectedCard}`;
                  const translatedName = String(t(translationKey));
                  return translatedName;
                } else {
                  // Minor Arcana
                  const suit = nameEn.includes('wands') ? 'wands' :
                           nameEn.includes('cups') ? 'cups' :
                           nameEn.includes('swords') ? 'swords' :
                           'pentacles';
                  
                  const rankMap: { [key: string]: string } = {
                    'ace': 'ace',
                    'two': '2',
                    'three': '3',
                    'four': '4',
                    'five': '5',
                    'six': '6',
                    'seven': '7',
                    'eight': '8',
                    'nine': '9',
                    'ten': '10',
                    'page': 'page',
                    'knight': 'knight',
                    'queen': 'queen',
                    'king': 'king'
                  };
                  
                  const rank = Object.keys(rankMap).find(r => nameEn.includes(r));
                  return rank ? String(t(`reading.cards.${suit}.${rankMap[rank]}`)) : card.name;
                }
              })()}
            </span>
            <span className="text-gray-300 text-sm">
              ({cardOrientation ? t('reading.result.reversed', '逆位') : t('reading.result.upright', '正位')})
            </span>
          </div>
        </motion.div>
      )}
      
      {/* 翻牌按钮 - 只在卡牌未翻开时显示 */}
      {!flipped && (
        <div className="text-center mt-4">
          <motion.button
            onClick={handleCardFlip}
            disabled={processingCard}
            className={`relative px-12 py-3 min-w-[180px] rounded-full font-medium text-lg ${getFontClass()}
              ${!processingCard
                ? 'bg-purple-600 hover:bg-purple-500 transition-colors duration-200' 
                : 'bg-purple-600 opacity-70 cursor-not-allowed'
              }`}
            whileHover={!processingCard ? { scale: 1.02 } : {}}
            whileTap={!processingCard ? { scale: 0.98 } : {}}
            style={{
              boxShadow: "0 0 20px rgba(168, 85, 247, 0.3)",
            }}
          >
            {processingCard ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span style={{color: 'white'}}>{t('common.loading')}</span>
              </div>
            ) : <span style={{color: 'white'}}>{t('yes_no_tarot.flip_card_button')}</span>}
          </motion.button>
        </div>
      )}
    </motion.div>
  );
};

export default TarotCardDisplay; 