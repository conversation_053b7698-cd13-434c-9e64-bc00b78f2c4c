import { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { fetchParagraphAudio } from './audioUtils';

/**
 * TarotSpeech组件的自定义Hook
 * @param options 配置项
 * @returns 返回状态和操作函数
 */
export const useTarotSpeech = ({
  text,
  sessionId,
  readerId,
  messageId,
  cacheKey,
  blockType = 'base', // 默认为base板块
  language = 'zh'     // 默认为中文
}: {
  text: string;
  sessionId?: string;
  readerId?: string;
  messageId?: string;
  cacheKey?: string;
  blockType?: string;
  language?: string;
}) => {
  const { t } = useTranslation();
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);
  
  // 保留这些状态以实现接口兼容性，但实际上我们不再需要流式播放多段文本
  const [paragraphQueue] = useState<string[]>([text]);
  const [currentParagraphIndex] = useState(0);
  const [isStreamPlaying, setIsStreamPlaying] = useState(false);
  
  const isMountedRef = useRef(true);
  
  // 标准化板块类型，确保统一使用base
  const normalizedBlockType = blockType === 'basic' ? 'base' : blockType;
  
  // 组件清理
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
      
      // 清理音频资源
      if (audioElement) {
        audioElement.pause();
        audioElement.src = '';
        audioElement.load();
        
        // 清理事件监听器
        audioElement.onended = null;
        audioElement.oncanplaythrough = null;
        audioElement.onerror = null;
      }
      
      // 清理URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, []);
  
  // 在audioElement变化时设置事件处理
  useEffect(() => {
    if (audioElement) {
      // 播放完成
      audioElement.onended = () => {
        if (isMountedRef.current) {
          setIsPlaying(false);
          setIsStreamPlaying(false);
        }
      };
      
      // 错误处理
      audioElement.onerror = () => {
        if (isMountedRef.current) {
          setIsPlaying(false);
          setIsStreamPlaying(false);
          setError(t('speech.playback_error', '播放错误，请重试'));
        }
      };
    }
  }, [audioElement, t]);
  
  // 获取音频数据（单个段落）
  const fetchAudio = async () => {
    if (isLoading) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      // 从messageId中提取段落索引
      let paragraphIndex = currentParagraphIndex; // 默认使用0
      
      // 检测新的消息ID格式：blockType-para-X
      if (messageId && messageId.includes('-para-')) {
        // 从新格式中提取板块类型和索引
        const blockTypeMatch = messageId.match(/^([a-z_]+)-para-(\d+)$/);
        if (blockTypeMatch && blockTypeMatch[1] && blockTypeMatch[2]) {
          // 获取索引，blockType会在audioUtils.ts中处理
          paragraphIndex = parseInt(blockTypeMatch[2], 10);
        }
      }
      // 处理老的消息ID格式：para-X
      else if (messageId && messageId.startsWith('para-')) {
        // 从para-X格式中提取索引
        const matches = messageId.match(/para-(\d+)$/);
        if (matches && matches[1]) {
          paragraphIndex = parseInt(matches[1], 10);
        }
      }
      
      // 释放之前的URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
        setAudioUrl(null);
      }
      
      // 停止之前的音频
      if (audioElement) {
        audioElement.pause();
        audioElement.src = '';
        audioElement.load();
      }
      
      // 创建唯一组件ID
      const componentId = `tarot-speech-${messageId || `${normalizedBlockType}-para-${paragraphIndex}`}`;
      
      // 获取音频，确保使用normalizedBlockType
      const audio = await fetchParagraphAudio(
        text,
        paragraphIndex, // 使用从messageId中提取的索引
        readerId,
        sessionId,
        messageId || cacheKey, // 优先使用messageId
        normalizedBlockType, // 使用标准化的板块类型
        componentId, // 传递组件ID用于音频管理
        language // 传递语言参数
      );
      
      if (!isMountedRef.current) return;
      
      // 保存音频元素
      setAudioElement(audio);
      
      // 设置音频加载完成的回调
      audio.oncanplaythrough = () => {
        if (isMountedRef.current) {
          setIsLoading(false);
          setIsPlaying(true);
          
          // 尝试自动播放
          const playPromise = audio.play();
          if (playPromise !== undefined) {
            playPromise.catch(() => {
              if (isMountedRef.current) {
                setIsPlaying(false);
                setIsLoading(false);
                setError(t('speech.playback_error', '播放错误，请重试'));
              }
            });
          }
        }
      };
      
      // 预加载音频
      audio.load();
    } catch (err: any) {
      if (isMountedRef.current) {
        setError(t('speech.generation_error', '语音生成失败，请重试'));
        setIsLoading(false);
        setIsPlaying(false);
      }
    }
  };
  
  // 暂停音频播放
  const pauseAudio = () => {
    if (audioElement) {
      audioElement.pause();
      setIsPlaying(false);
    }
  };
  
  // 切换播放/暂停状态
  const togglePlay = () => {
    if (isPlaying) {
      pauseAudio();
    } else if (audioElement && audioElement.readyState >= 2) {
      // 音频已加载但暂停了，继续播放
      try {
        const playPromise = audioElement.play();
        if (playPromise !== undefined) {
          playPromise.catch(() => {
            // console.error("[TarotSpeech] 播放错误:", error);
            setError(t('speech.playback_error', '播放错误，请重试'));
            setIsPlaying(false);
          });
          
          // 立即更新播放状态，不等待播放成功回调
          setIsPlaying(true);
        }
      } catch (e) {
        // console.error("[TarotSpeech] 播放异常:", e);
        setError(t('speech.playback_error', '播放错误，请重试'));
      }
    } else {
      // 尚未加载音频，开始加载并播放
      fetchAudio();
    }
  };
  
  return {
    isPlaying,
    isLoading,
    error,
    togglePlay,
    isStreamPlaying,
    paragraphQueue,
    currentParagraphIndex,
    audioElement, // 暴露音频元素
    setIsPlaying // 暴露setIsPlaying函数，用于外部状态控制
  };
}; 