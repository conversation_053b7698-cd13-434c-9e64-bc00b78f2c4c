/**
 * 百度搜索资源平台URL提交服务
 * 统一管理百度URL提交相关功能
 */
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const logger = require('../utils/logger');

// 百度提交API的配置
const BAIDU_PUSH_CONFIG = {
  // API接口地址（完整URL，直接从百度站长平台复制）
  apiUrl: 'http://data.zz.baidu.com/urls?site=https://tarotqa.com&token=gXW20EqkPLSoyYrz',
  defaultSitemapUrl: 'https://tarotqa.com/sitemap.xml',
  baiduSitemapUrl: 'https://tarotqa.com/baidusitemap.xml'
};

// 临时文件和日志目录
const TEMP_DIR = path.join(__dirname, '../temp');
const URLS_FILE = path.join(TEMP_DIR, 'urls.txt');
const PUSH_LOG_DIR = process.env.NODE_ENV !== 'development'
  ? '/var/www/tarot/baidu_push' 
  : path.join(__dirname, '../logs/baidu-push');
const PUSH_HISTORY_FILE = path.join(PUSH_LOG_DIR, 'push-history.json');
const PUSH_LOG_FILE = () => {
  // 获取北京时间日期
  const date = new Date();
  // 调整为北京时间 (UTC+8)
  date.setHours(date.getHours() + 8);
  const today = date.toISOString().split('T')[0];
  return path.join(PUSH_LOG_DIR, `push-log-${today}.json`);
};

/**
 * 向百度推送URL列表 - 使用API方式
 * @param {string[]} urls 要推送的URL列表
 * @returns {Promise<Object>} 推送结果
 */
async function pushUrlsToBaidu(urls) {
  if (!urls || !Array.isArray(urls) || urls.length === 0) {
    return {
      success: false,
      message: '没有提供有效的URL列表'
    };
  }

  try {
    // 将URL数组转换为文本格式（每行一个URL）
    const urlsText = urls.join('\n');
    
    logger.info(`推送到百度API: ${BAIDU_PUSH_CONFIG.apiUrl}`);
    logger.info(`推送URL数量: ${urls.length}`);
    
    // 发送POST请求到百度API
    const response = await axios.post(BAIDU_PUSH_CONFIG.apiUrl, urlsText, {
      headers: {
        'Content-Type': 'text/plain'
      }
    });

    logger.info(`百度URL提交响应: ${JSON.stringify(response.data)}`);
    
    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    logger.error(`百度URL提交失败: ${error.message}`);
    if (error.response) {
      logger.error(`响应状态: ${error.response.status}, 数据: ${JSON.stringify(error.response.data)}`);
    }
    
    return {
      success: false,
      message: `推送失败: ${error.message}`,
      error: error.response ? error.response.data : null
    };
  }
}

/**
 * 从sitemap XML中提取URL
 * @param {string} sitemapContent sitemap XML内容
 * @param {boolean} chineseOnly 是否只提取中文URL
 * @returns {string[]} 提取的URL列表
 */
function extractUrlsFromSitemap(sitemapContent, chineseOnly = false) {
  const urlRegex = /<loc>(.*?)<\/loc>/g;
  const urls = [];
  let match;
  
  while ((match = urlRegex.exec(sitemapContent)) !== null) {
    const url = match[1];
    // 如果设置了只选择中文URL，则进行过滤
    if (!chineseOnly || url.includes('zh-CN')) {
      urls.push(url);
    }
  }
  
  return urls;
}

/**
 * 获取站点地图内容并提取URL
 * @param {string} sitemapUrl 站点地图URL
 * @param {boolean} chineseOnly 是否只提取中文URL
 * @returns {Promise<string[]>} 站点地图中的URL列表
 */
async function getUrlsFromSitemap(sitemapUrl, chineseOnly = false) {
  try {
    logger.info(`获取站点地图: ${sitemapUrl}`);
    const response = await axios.get(sitemapUrl);
    const urls = extractUrlsFromSitemap(response.data, chineseOnly);
    logger.info(`从 ${sitemapUrl} 中提取到 ${urls.length} 个URL`);
    return urls;
  } catch (error) {
    logger.error(`获取站点地图失败: ${sitemapUrl} - ${error.message}`);
    return [];
  }
}

/**
 * 从站点地图获取URL并推送到百度
 * @param {string} sitemapUrl sitemap的URL
 * @param {boolean} chineseOnly 是否只提取中文URL
 * @returns {Promise<Object>} 推送结果
 */
async function pushUrlsFromSitemap(sitemapUrl, chineseOnly = false) {
  try {
    // 获取sitemap中的URL列表
    const urls = await getUrlsFromSitemap(sitemapUrl, chineseOnly);
    
    if (urls.length === 0) {
      logger.error(`站点地图中没有找到URL: ${sitemapUrl}`);
      return {
        success: false,
        message: '站点地图中没有找到URL'
      };
    }
    
    // 推送提取的URL（分批处理，每次最多推送100个URL）
    const batchSize = 100;
    const results = [];
    
    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      logger.info(`推送第 ${Math.floor(i/batchSize) + 1} 批，包含 ${batch.length} 个URL`);
      
      const result = await pushUrlsToBaidu(batch);
      results.push(result);
      
      // 如果批量推送失败，停止后续推送
      if (!result.success) {
        logger.error(`批量推送失败，停止后续处理`);
        break;
      }
      
      // 为避免过快请求导致API限制，批量推送间隔1秒
      if (i + batchSize < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // 汇总所有批次的结果
    const successCount = results.reduce((total, r) => 
      total + (r.success && r.data && r.data.success ? r.data.success : 0), 0);
    
    return {
      success: true,
      data: {
        batchCount: results.length,
        totalUrls: urls.length,
        successCount,
        details: results.map(r => r.data)
      }
    };
  } catch (error) {
    logger.error(`从站点地图提取URL失败: ${error.message}`);
    return {
      success: false,
      message: `获取站点地图失败: ${error.message}`
    };
  }
}

/**
 * 自动提交默认站点地图到百度
 * 同时提交主站点地图和百度专用站点地图
 */
async function pushDefaultSitemaps() {
  // 提交主站点地图
  const mainResult = await pushUrlsFromSitemap(BAIDU_PUSH_CONFIG.defaultSitemapUrl, false);
  
  // 提交百度专用站点地图
  const baiduResult = await pushUrlsFromSitemap(BAIDU_PUSH_CONFIG.baiduSitemapUrl, false);
  
  return {
    success: true,
    mainSitemap: mainResult.success ? mainResult.data : { error: mainResult.message },
    baiduSitemap: baiduResult.success ? baiduResult.data : { error: baiduResult.message }
  };
}

/**
 * 将URL列表写入文件
 * @param {string[]} urls URL列表
 * @param {string} filePath 文件路径
 */
function writeUrlsToFile(urls, filePath) {
  // 确保目录存在
  if (!fs.existsSync(path.dirname(filePath))) {
    fs.mkdirSync(path.dirname(filePath), { recursive: true });
  }
  
  // 写入文件，每行一个URL
  fs.writeFileSync(filePath, urls.join('\n'), 'utf8');
  logger.info(`已将 ${urls.length} 个URL写入文件: ${filePath}`);
}

/**
 * 使用curl命令推送URL
 * @param {string} urlsFile URL文件路径
 * @returns {string} 推送结果
 */
function pushUrlsWithCurl(urlsFile) {
  try {
    // 构建curl命令，与百度官方示例完全一致，添加-s参数使其静默执行
    const curlCommand = `curl -s -H 'Content-Type:text/plain' --data-binary @${urlsFile} "${BAIDU_PUSH_CONFIG.apiUrl}"`;
    logger.info(`执行命令: ${curlCommand}`);
    
    // 执行curl命令
    const result = execSync(curlCommand, { encoding: 'utf8' });
    return result;
  } catch (error) {
    logger.error(`执行curl命令失败: ${error.message}`);
    throw error;
  }
}

/**
 * 检查是否超出配额
 * @param {string} result API返回结果
 * @returns {boolean} 是否超出配额
 */
function isOverQuota(result) {
  try {
    const resultData = JSON.parse(result);
    return resultData.error === 400 && resultData.message === "over quota";
  } catch (e) {
    return false;
  }
}

/**
 * 保存推送记录
 * @param {string[]} pushedUrls 已推送的URL列表
 * @param {Object} result 推送结果
 */
function savePushLog(pushedUrls, result) {
  try {
    const logFilePath = PUSH_LOG_FILE();
    
    // 确保日志目录存在
    if (!fs.existsSync(PUSH_LOG_DIR)) {
      logger.warn(`推送记录目录不存在: ${PUSH_LOG_DIR}`);
      return;
    }
    
    // 读取当天已有的记录
    let logData = { 
      date: (() => {
        // 获取北京时间日期时间
        const date = new Date();
        date.setHours(date.getHours() + 8);
        return date.toISOString();
      })(),
      total: 0,
      success: 0,
      remain: 0,
      urls: []
    };
    
    if (fs.existsSync(logFilePath)) {
      try {
        logData = JSON.parse(fs.readFileSync(logFilePath, 'utf8'));
      } catch (e) {
        logger.error(`读取推送记录文件失败: ${e.message}`);
      }
    }
    
    // 更新推送数据
    try {
      const resultData = JSON.parse(result);
      logData.success += resultData.success || 0;
      logData.remain = resultData.remain || 0;
    } catch (e) {
      logger.error(`解析推送结果失败: ${e.message}`);
    }
    
    // 添加新推送的URL
    logData.urls = [...new Set([...logData.urls, ...pushedUrls])];
    logData.total = logData.urls.length;
    
    // 写入日志文件
    fs.writeFileSync(logFilePath, JSON.stringify(logData, null, 2), 'utf8');
    logger.info(`已更新推送记录: ${logFilePath}`);
    
    // 同时更新历史推送记录
    saveHistoryPushedUrls(pushedUrls);
  } catch (error) {
    logger.error(`保存推送记录失败: ${error.message}`);
  }
}

/**
 * 获取当天推送记录
 * @returns {Object|null} 当天推送记录
 */
function getTodayPushLog() {
  try {
    const logFilePath = PUSH_LOG_FILE();
    if (fs.existsSync(logFilePath)) {
      return JSON.parse(fs.readFileSync(logFilePath, 'utf8'));
    }
    return null;
  } catch (error) {
    logger.error(`获取当天推送记录失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取最新的百度推送报告
 * @returns {Object|null} 推送报告数据
 */
function getLatestPushReport() {
  try {
    // 获取北京时间的今天日期
    const date = new Date();
    // 调整为北京时间 (UTC+8)
    date.setHours(date.getHours() + 8);
    const today = date.toISOString().split('T')[0];
    const reportFilePath = path.join(PUSH_LOG_DIR, `push-log-${today}.json`);
    
    // 先尝试读取当天的推送记录
    if (fs.existsSync(reportFilePath)) {
      const reportData = JSON.parse(fs.readFileSync(reportFilePath, 'utf8'));
      return {
        date: today,
        total: reportData.total || 0,
        success: reportData.success || 0,
        remain: reportData.remain || 0,
        urls: reportData.urls || []
      };
    }
    
    // 如果当天记录不存在，尝试获取前一天的记录
    const yesterday = new Date(date);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];
    const yesterdayFilePath = path.join(PUSH_LOG_DIR, `push-log-${yesterdayStr}.json`);
    
    if (fs.existsSync(yesterdayFilePath)) {
      const reportData = JSON.parse(fs.readFileSync(yesterdayFilePath, 'utf8'));
      return {
        date: yesterdayStr,
        total: reportData.total || 0,
        success: reportData.success || 0,
        remain: reportData.remain || 0,
        urls: reportData.urls || [],
        isYesterday: true  // 标记这是前一天的数据
      };
    }
    
    return null;
  } catch (error) {
    logger.error(`获取推送报告失败: ${error.message}`);
    return null;
  }
}

/**
 * 执行完整的百度URL推送流程
 * 从站点地图获取URL并推送到百度
 */
async function executeFullPushProcess() {
  try {
    logger.info('===== 开始百度URL推送 =====');
    
    // 获取当天已推送记录
    const todayLog = getTodayPushLog();
    logger.info(`当天已推送记录: ${todayLog ? `${todayLog.success}个URL，剩余配额${todayLog.remain}` : '无'}`);
    
    // 检查是否已经超出配额
    if (todayLog && todayLog.remain <= 0) {
      const message = '今日推送配额已用完，无法继续推送';
      logger.info(message);
      return { success: 0, totalSubmitted: 0, overQuota: true, message };
    }
    
    // 收集所有站点地图中的URL
    let allUrls = [];
    const sitemapUrls = [BAIDU_PUSH_CONFIG.defaultSitemapUrl, BAIDU_PUSH_CONFIG.baiduSitemapUrl];
    
    for (const sitemapUrl of sitemapUrls) {
      const urls = await getUrlsFromSitemap(sitemapUrl, false); // 获取所有URL，不限中文
      allUrls = [...allUrls, ...urls];
    }
    
    // 去重
    allUrls = [...new Set(allUrls)];
    logger.info(`总共收集到 ${allUrls.length} 个不重复URL`);
    
    if (allUrls.length === 0) {
      logger.error('没有找到任何URL，推送取消');
      return { success: 0, totalSubmitted: 0, message: '没有找到任何URL' };
    }
    
    // 获取历史推送记录
    const historyPushedUrls = getHistoryPushedUrls();
    logger.info(`历史记录中已推送过 ${historyPushedUrls.length} 个URL`);
    
    // 优先推送从未推送过的URL
    let neverPushedUrls = allUrls.filter(url => !historyPushedUrls.includes(url));
    logger.info(`从未推送过的新URL: ${neverPushedUrls.length} 个`);
    
    // 如果没有从未推送过的URL，直接返回，不再推送已经推送过的URL
    if (neverPushedUrls.length === 0) {
      const message = '没有找到新URL，所有URL已被历史推送，跳过本次推送';
      logger.info(message);
      return { success: 0, totalSubmitted: 0, message };
    }
    
    // 使用未推送过的URL
    let urlsToSubmit = neverPushedUrls;
    
    // 过滤掉当天已推送的URL
    if (todayLog && todayLog.urls && todayLog.urls.length > 0) {
      urlsToSubmit = urlsToSubmit.filter(url => !todayLog.urls.includes(url));
      logger.info(`过滤掉 ${(neverPushedUrls.length > 0 ? neverPushedUrls.length : allUrls.length) - urlsToSubmit.length} 个当天已推送URL，待推送 ${urlsToSubmit.length} 个URL`);
    }
    
    if (urlsToSubmit.length === 0) {
      const message = neverPushedUrls.length === 0 
        ? '没有找到新URL，所有URL已被历史推送' 
        : '所有新URL今天已推送过，无需再次推送';
      logger.info(message);
      return { success: 0, totalSubmitted: 0, message };
    }
    
    // 限制推送数量，避免浪费请求
    const maxUrls = todayLog && todayLog.remain ? todayLog.remain : 10;
    if (urlsToSubmit.length > maxUrls) {
      logger.info(`待推送URL数(${urlsToSubmit.length})超过剩余配额(${maxUrls})，将只推送前${maxUrls}个URL`);
      urlsToSubmit = urlsToSubmit.slice(0, maxUrls);
    }
    
    // 写入文件
    writeUrlsToFile(urlsToSubmit, URLS_FILE);
    
    // 推送URL（分批次处理，每次最多推送10个URL）
    const batchSize = 10;
    let successCount = 0;
    let pushedBatches = [];
    let isQuotaExceeded = false;
    
    for (let i = 0; i < urlsToSubmit.length; i += batchSize) {
      // 如果已经超出配额，停止推送
      if (isQuotaExceeded) {
        logger.info('检测到配额已用完，停止后续推送');
        break;
      }
      
      const batchUrls = urlsToSubmit.slice(i, i + batchSize);
      const batchFile = path.join(TEMP_DIR, `urls_batch_${Math.floor(i/batchSize)}.txt`);
      
      writeUrlsToFile(batchUrls, batchFile);
      logger.info(`推送第 ${Math.floor(i/batchSize) + 1} 批，包含 ${batchUrls.length} 个URL`);
      
      // 推送当前批次
      const result = pushUrlsWithCurl(batchFile);
      logger.info(`推送结果: ${result}`);
      
      // 检查是否超出配额
      if (isOverQuota(result)) {
        logger.info('检测到配额已用完 (over quota)，停止后续推送');
        isQuotaExceeded = true;
      }
      
      // 记录本批次信息
      pushedBatches.push({
        urls: batchUrls,
        result: result
      });
      
      // 记录推送结果
      savePushLog(batchUrls, result);
      
      try {
        const resultData = JSON.parse(result);
        if (resultData.success) {
          successCount += resultData.success;
        }
        
        // 显示剩余配额
        if (resultData.remain !== undefined) {
          logger.info(`剩余推送配额: ${resultData.remain}`);
          
          // 如果配额为0，停止推送
          if (resultData.remain <= 0) {
            logger.info('今日推送配额已用完，停止推送');
            isQuotaExceeded = true;
          }
        }
      } catch (e) {
        logger.error(`解析推送结果失败: ${e.message}`);
      }
      
      // 清理批次文件
      fs.unlinkSync(batchFile);
      
      // 批次间隔2秒，避免频率限制
      if (!isQuotaExceeded && i + batchSize < urlsToSubmit.length) {
        logger.info('等待2秒后推送下一批...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    logger.info(`推送完成，成功推送 ${successCount} 个URL`);
    logger.info('===== 百度URL推送结束 =====');
    
    // 清理主URL文件
    if (fs.existsSync(URLS_FILE)) {
      fs.unlinkSync(URLS_FILE);
      logger.info(`已清理临时文件: ${URLS_FILE}`);
    }
    
    // 返回推送结果摘要
    return {
      success: successCount,
      totalSubmitted: urlsToSubmit.length,
      batches: pushedBatches,
      overQuota: isQuotaExceeded
    };
  } catch (error) {
    logger.error(`百度URL推送出错: ${error.message}`);
    return { error: error.message };
  }
}

/**
 * 获取历史推送记录
 * @returns {string[]} 历史推送过的URL列表
 */
function getHistoryPushedUrls() {
  try {
    // 确保日志目录存在
    if (!fs.existsSync(PUSH_LOG_DIR)) {
      logger.warn(`推送记录目录不存在: ${PUSH_LOG_DIR}`);
      return [];
    }
    
    // 读取历史推送记录
    if (fs.existsSync(PUSH_HISTORY_FILE)) {
      const historyData = JSON.parse(fs.readFileSync(PUSH_HISTORY_FILE, 'utf8'));
      return historyData.urls || [];
    }
    
    return [];
  } catch (error) {
    logger.error(`获取历史推送记录失败: ${error.message}`);
    return [];
  }
}

/**
 * 保存URL到历史推送记录
 * @param {string[]} urls 要保存的URL列表
 */
function saveHistoryPushedUrls(urls) {
  try {
    // 确保日志目录存在
    if (!fs.existsSync(PUSH_LOG_DIR)) {
      logger.warn(`推送记录目录不存在: ${PUSH_LOG_DIR}`);
      return;
    }
    
    // 读取现有历史记录
    let historyData = { urls: [] };
    if (fs.existsSync(PUSH_HISTORY_FILE)) {
      try {
        historyData = JSON.parse(fs.readFileSync(PUSH_HISTORY_FILE, 'utf8'));
      } catch (e) {
        logger.error(`读取历史推送记录失败: ${e.message}`);
      }
    }
    
    // 合并并去重
    historyData.urls = [...new Set([...historyData.urls, ...urls])];
    historyData.lastUpdated = new Date().toISOString();
    historyData.totalCount = historyData.urls.length;
    
    // 写入历史记录文件
    fs.writeFileSync(PUSH_HISTORY_FILE, JSON.stringify(historyData, null, 2), 'utf8');
    logger.info(`已更新历史推送记录，总计 ${historyData.totalCount} 个URL`);
  } catch (error) {
    logger.error(`保存历史推送记录失败: ${error.message}`);
  }
}

module.exports = {
  // 基础推送API
  pushUrlsToBaidu,
  pushUrlsFromSitemap,
  pushDefaultSitemaps,
  
  // 完整推送流程
  executeFullPushProcess,
  
  // 日志和报告
  getTodayPushLog,
  getLatestPushReport,
  
  // 历史推送记录
  getHistoryPushedUrls,
  saveHistoryPushedUrls,
  
  // 工具函数
  getUrlsFromSitemap,
  writeUrlsToFile,
  pushUrlsWithCurl
}; 