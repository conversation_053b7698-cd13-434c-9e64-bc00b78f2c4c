const express = require('express');
const router = express.Router();
const axios = require('axios');
const { authenticateToken } = require('../middleware/auth');
const { User } = require('../models/User');
const { getConnection } = require('../services/database');
const readerAnalysis = require('../config/reader_analysis');

// 创建方舟 API 客户端实例
const arkAPI = axios.create({
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  timeout: 1800000, // 30分钟超时
  headers: {
    'Authorization': `Bearer ${process.env.ARK_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

// // 创建用于调用 Deepseek API 的 axios 实例
// const deepseekAPI = axios.create({
//   baseURL: 'https://api.deepseek.com',
//   timeout: 1800000,
//   headers: {
//     'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
//     'Content-Type': 'application/json'
//   }
// });

// 添加清理 markdown 格式的函数
const cleanMarkdownFormat = (text) => {
  return text
    .replace(/\*\*(.*?)\*\*/g, '$1')  // 移除粗体标记 **text**
    .replace(/\*(.*?)\*/g, '$1')      // 移除斜体标记 *text*
    .replace(/\[(.*?)\]\((.*?)\)/g, '$1')  // 移除链接标记 [text](url)
    .replace(/#{1,6}\s/g, '')         // 移除标题标记 # text
    .replace(/`(.*?)`/g, '$1')        // 移除代码标记 `text`
    .replace(/~~(.*?)~~/g, '$1')      // 移除删除线标记 ~~text~~
    .replace(/\n\s*[-*+]\s/g, '\n• ') // 将列表标记统一转换为 •
    .replace(/\n\s*\d+\.\s/g, '\n• '); // 将有序列表标记转换为 •
};

// 添加清理特殊引号符号的函数
const cleanSpecialQuotes = (text) => {
  return text
    .replace(/[「」]/g, '"')       // 将中文角引号替换为英文双引号
    .replace(/[『』]/g, "'")       // 将中文书名号替换为英文单引号
    .replace(/[〝〞]/g, '"')       // 将其他中文引号替换为英文双引号
    .replace(/['']/g, "'")        // 统一英文单引号样式
    .replace(/[""]/g, '"');       // 统一英文双引号样式
};

// 添加多语言文本映射
const textTranslations = {
  'zh-CN': {
    prologue: "让我们从更深层的维度来解读这个牌阵...\n",
    summary: "以上就是对这个牌阵的深入解读。"
  },
  'en': {
    prologue: "Let's explore this spread from a deeper dimension...\n",
    summary: "This concludes the in-depth interpretation of your spread."
  },
  'ja': {
    prologue: "より深い次元からこのタロットスプレッドを解読していきましょう...\n",
    summary: "以上がこのスプレッドの深い解釈となります。"
  },
  'zh-TW': {
    prologue: "讓我們從更深層的維度來解讀這個牌陣...\n",
    summary: "以上就是對這個牌陣的深入解讀。"
  }
};

// 获取翻译文本的函数
const getTranslatedText = (key, language) => {
  const defaultLang = 'zh-CN';
  const translations = textTranslations[language] || textTranslations[defaultLang];
  return translations[key];
};

router.post('/', authenticateToken, async (req, res) => {
  try {
    const { sessionId, language, initialAnalysis } = req.body;
    const userId = req.user.userId;


    if (!sessionId) {
      return res.status(400).json({ error: '缺少必要的参数' });
    }

    // 获取用户信息
    const user = await User.findById(userId);

    // 从数据库获取用户详细信息
    const pool = await getConnection();
    const [userDetails] = await pool.query(
      'SELECT gender, location, birthday, user_profile FROM users WHERE id = ?',
      [userId]
    );

    // 获取当前年月
    const now = new Date();
    const currentDate = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月`;
    const currentFullDate = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月${String(now.getDate()).padStart(2, '0')}日`;

    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 检查用户权限：VIP用户或首次使用深度解析的普通用户
    let hasPermission = false;
    
    // 检查是否是VIP用户
    if (user.vip_status && ['monthly', 'yearly', 'active'].includes(user.vip_status)) {
      hasPermission = true;
    } else {
      // 如果不是VIP用户，检查是否已经使用过深度解析
      const [usedDeepAnalysis] = await pool.query(`
        SELECT COUNT(*) as count
        FROM sessions
        WHERE user_id = ? AND deep_analysis IS NOT NULL AND deep_analysis != ''
      `, [userId]);
      
      // 如果从未使用过深度解析，允许使用一次
      if (usedDeepAnalysis[0].count === 0) {
        hasPermission = true;
      }
    }
    
    // 验证用户权限
    if (!hasPermission) {
      return res.status(403).json({ error: '您已使用过一次免费深度解析，该功能仅对VIP用户开放' });
    }

    // 获取会话信息
    const [sessions] = await pool.query(`
      SELECT 
        s.*,
        s.selected_cards as cards_json,
        s.reading_result,
        s.reader_id
      FROM sessions s
      WHERE s.id = ? AND s.user_id = ?
    `, [sessionId, userId]);

    if (!sessions || sessions.length === 0) {
      return res.status(404).json({ error: '找不到相关会话' });
    }

    const session = sessions[0];
    let cards;
    let readingResult;
    
    try {
      // 检查cards_json是否已经是对象
      cards = typeof session.cards_json === 'string' 
        ? JSON.parse(session.cards_json)
        : session.cards_json;
        
      readingResult = session.reading_result 
        ? (typeof session.reading_result === 'string' 
          ? JSON.parse(session.reading_result) 
          : session.reading_result)
        : null;
        
    } catch (error) {
      console.error('Error parsing session data:', error);
      return res.status(500).json({ error: '解析会话数据时出现错误' });
    }

    // 根据reader_id获取对应的分析prompt
    const selectedReader = readerAnalysis.find(reader => reader.id.toLowerCase() === session.reader_id?.toLowerCase());
    const analysisPrompt = selectedReader?.prompt || readerAnalysis[0].prompt; // 如果找不到对应的reader，使用默认的第一个reader的prompt

    // 构建卡牌描述
    const cardsDescription = cards
      .map(card => {
        return `${card.position}：${card.name}${card.isReversed ? '（逆位）' : ''}`;
      })
      .join('\n');

    const userPrompt = 
`
请使用${language || user.language || 'zh-CN'}语言回答
直接输出纯文本内容，不要使用任何markdown格式，不要输出表情符号
不要使用任何引号、双引号
当前时间：${currentFullDate}

问卜者信息：
姓名：${user.username}
性别：${userDetails[0]?.gender || '未知'}
生日：${userDetails[0]?.birthday || '未知'}
问题：${session.question}

原始问题抽出的卡牌：
${cardsDescription}

历史总结:
${session.summary || '无历史记录'}

`;

    console.log('Sending request to API with prompts:', {
      systemPrompt: analysisPrompt,
      userPrompt
    });

    // 设置响应为流式传输
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no'); // 防止Nginx缓冲响应

    // reasoning_effort: 'Low',
    try {
      const response = await arkAPI.post('/chat/completions', {
        model: "doubao-1-5-thinking-pro-250415",
        // model: "deepseek-r1-250120",
        messages: [
          { role: "system", content: analysisPrompt },
          { role: "user", content: userPrompt }
        ],

        stream: true,
        parameters: {
          response_language: language || user.language || 'zh-TW'
        }
      }, {
        responseType: 'stream'
      });

      // 收集完整响应以保存到数据库
      let fullContent = '';
      let contentChunks = [];

      // 处理流式响应
      response.data.on('data', (chunk) => {
        try {
          const dataStr = chunk.toString();
          // 处理可能包含多个JSON对象的数据块
          const lines = dataStr.split('\n').filter(line => line.trim().startsWith('data: '));
          
          for (const line of lines) {
            // 提取JSON数据部分
            const jsonData = line.substring(6); // 移除 "data: " 前缀
            
            if (jsonData.trim() === '[DONE]') {
              // 流结束标记
              res.write('data: [DONE]\n\n');
              continue;
            }
            
            try {
              const data = JSON.parse(jsonData);
              
              // 提取delta内容
              const deltaContent = data.choices[0]?.delta?.content || '';
              
              if (deltaContent) {
                // 清理特殊引号符号
                const cleanedContent = cleanSpecialQuotes(deltaContent);
                fullContent += cleanedContent;
                contentChunks.push(cleanedContent);
                
                // 发送到客户端
                res.write(`data: ${JSON.stringify({ content: cleanedContent })}\n\n`);
              }
            } catch (jsonError) {
              console.error('Error parsing JSON in stream:', jsonError);
            }
          }
        } catch (chunkError) {
          console.error('Error processing chunk:', chunkError);
        }
      });

      // 流结束处理
      response.data.on('end', async () => {
        try {
          // 使用翻译后的文本
          const userLang = language || user.language || 'zh-TW';
          const analysisResult = {
            prologue: getTranslatedText('prologue', userLang),
            analysis1: fullContent.trim(),
            summary: getTranslatedText('summary', userLang)
          };

          // 将结果转换为字符串
          const resultJson = JSON.stringify(analysisResult);

          // 更新会话中的深度解析结果
          const pool = await getConnection();
          await pool.query(
            'UPDATE sessions SET deep_analysis = ? WHERE id = ?',
            [resultJson, sessionId]
          );

          // 完成响应
          res.end();
        } catch (error) {
          console.error('Error saving analysis result:', error);
          res.end();
        }
      });

      // 错误处理
      response.data.on('error', (error) => {
        console.error('Stream error:', error);
        res.write(`data: ${JSON.stringify({ error: '流处理错误' })}\n\n`);
        res.end();
      });
    } catch (error) {
      console.error('Error in API request:', error);
      
      // 发送错误信息给客户端
      res.write(`data: ${JSON.stringify({ error: '请求API错误' })}\n\n`);
      res.end();
      
      // 继续向后抛出错误，让外部错误处理来处理
      throw error;
    }
  } catch (error) {
    console.error('Error in deep analysis:', error);
    let errorMessage = '生成深度解析时出现错误';
    
    if (error.response?.data?.error) {
      errorMessage = error.response.data.error;
    } else if (error.message === 'API 返回格式错误') {
      errorMessage = 'API 返回数据格式不正确';
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请稍后重试';
    }
    
    res.status(500).json({ error: errorMessage });
  }
});

module.exports = router; 