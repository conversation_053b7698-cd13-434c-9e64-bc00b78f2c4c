const crypto = require('crypto');

class AesUtil {
  constructor(apiV3Key) {
    if (Buffer.from(apiV3Key).length !== 32) {
      throw new Error('无效的ApiV3Key，长度必须为32个字节');
    }
    this.aesKey = Buffer.from(apiV3Key);
  }

  decryptToString(ciphertext, key, nonce, associatedData) {
    try {
      // 使用 AEAD_AES_256_GCM 算法
      const decipher = crypto.createDecipheriv(
        'aes-256-gcm',
        this.aesKey,
        Buffer.from(nonce)
      );

      // 设置 AAD
      if (associatedData) {
        decipher.setAAD(Buffer.from(associatedData));
      }

      // 设置 auth tag (从 ciphertext 末尾提取)
      const ciphertextBuffer = Buffer.from(ciphertext, 'base64');
      const authTag = ciphertextBuffer.slice(ciphertextBuffer.length - 16);
      decipher.setAuthTag(authTag);

      // 解密
      let decrypted = decipher.update(
        ciphertextBuffer.slice(0, ciphertextBuffer.length - 16),
        null,
        'utf8'
      );
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      console.error('解密失败:', error);
      throw error;
    }
  }
}

module.exports = AesUtil;
