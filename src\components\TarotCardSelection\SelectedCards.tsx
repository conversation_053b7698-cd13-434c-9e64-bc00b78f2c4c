import React from 'react';
import { motion } from 'framer-motion';
import { TAROT_CARDS } from '../../data/tarot-cards';
import CdnLazyImage from '../CdnLazyImage';

interface CardPosition {
  card: number | undefined;
  position: string;
  orientation: boolean;
}

interface SelectedCardsProps {
  positions: CardPosition[];
  cardImages: Map<number, string>;
  currentLanguage: string;
  isDark: boolean;
  selectionMode: 'slide' | 'number' | 'custom';
  onSelectPosition?: (index: number) => void;
  onToggleOrientation?: (cardId: number) => void;
  t: (key: string) => string;
}

const SelectedCards: React.FC<SelectedCardsProps> = ({
  positions,
  cardImages,
  currentLanguage,
  isDark,
  selectionMode,
  onSelectPosition,
  onToggleOrientation,
  t
}) => {
  return (
    <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 md:gap-6">
      {positions.map((item, index) => {
        const { card: selectedCard, position, orientation } = item;
        
        return (
          <motion.div 
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.5 }}
            className="flex flex-col items-center w-[110px] sm:w-[140px] md:w-[160px] lg:w-[180px]"
          >
            {selectedCard !== undefined ? (
              <>
                <div className="relative mb-1 w-full">
                  <div className={`w-full aspect-[2/3] rounded-lg overflow-hidden border-0 transition-all duration-300 
                    ${selectionMode === 'custom' ? 'cursor-pointer hover:shadow-lg' : ''} 
                    group-hover:shadow-purple-500/30 flex items-center justify-center bg-transparent`}
                    style={{ 
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                    }}
                    onClick={() => {
                      if (selectionMode === 'custom' && onSelectPosition) {
                        onSelectPosition(index);
                      }
                    }}
                  >
                    <motion.div 
                      className="h-full w-full flex items-center justify-center"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                      style={{ 
                        transform: orientation ? 'rotate(180deg)' : 'rotate(0deg)'
                      }}
                    >
                      <CdnLazyImage 
                        src={cardImages.get(selectedCard) || ''}
                        alt={TAROT_CARDS[selectedCard]?.nameEn.replace(/_/g, ' ') || t('reading.shuffle.tarot_card')}
                        className="h-full w-full object-contain"
                        style={{ imageRendering: 'crisp-edges' }}
                        draggable="false"
                      />
                    </motion.div>
                  </div>
                </div>
                
                <div className="text-center mt-2 space-y-1">
                  <h3 className="text-sm font-medium text-white font-sans japanese">
                    {currentLanguage === 'en' ? (TAROT_CARDS[selectedCard]?.displayNameEn || TAROT_CARDS[selectedCard]?.nameEn.replace(/_/g, ' ')) : TAROT_CARDS[selectedCard]?.name || t('reading.shuffle.tarot_card')}
                  </h3>
                  <p className="text-sm text-purple-500 font-sans japanese">
                    {t(orientation ? 'reading.result.reversed' : 'reading.result.upright')}
                  </p>
                  <p className="text-sm text-gray-400 font-sans japanese">
                    {position}
                  </p>
                  {selectionMode === 'custom' && onToggleOrientation && (
                    <button
                      onClick={() => onToggleOrientation(selectedCard)}
                      className={`mt-2 px-3 py-1.5 text-xs rounded-full ${isDark ? 'bg-gray-800/60 text-gray-300 hover:bg-gray-700/80 hover:text-white' : 'bg-gray-200/70 text-gray-600 hover:bg-gray-300/80 hover:text-gray-800'} transition-colors`}
                    >
                      {t('reading.shuffle.toggle_orientation')}
                    </button>
                  )}
                </div>
              </>
            ) : (
              <>
                <div className="relative mb-1 w-full">
                  <div className={`w-full aspect-[2/3] flex items-center justify-center border-0 rounded-lg 
                    ${selectionMode === 'custom' ? 'cursor-pointer hover:shadow-lg' : ''} 
                    ${isDark ? 'group-hover:border-purple-500/30' : 'group-hover:border-purple-400/50'} transition-all duration-300 bg-transparent`}
                    style={{ 
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                    }}
                    onClick={() => {
                      if (selectionMode === 'custom' && onSelectPosition) {
                        onSelectPosition(index);
                      }
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-8 w-8 ${isDark ? 'text-gray-600/80' : 'text-gray-400/80'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4v16m8-8H4" />
                    </svg>
                  </div>
                </div>
                
                <div className="text-center mt-2 space-y-1">
                  <h3 className={`text-sm font-medium ${isDark ? 'text-white/60' : 'text-gray-600/80'} font-sans japanese`}>{t('reading.shuffle.pending_card')}</h3>
                  <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'} font-sans japanese`}>{position}</p>
                </div>
              </>
            )}
          </motion.div>
        );
      })}
    </div>
  );
};

export default SelectedCards; 