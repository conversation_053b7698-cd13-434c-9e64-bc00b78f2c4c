import { useMemo } from 'react';
import { getImageUrl, toCdnUrl } from '../utils/cdnImageUrl';

// CDN基础URL
const CDN_BASE_URL = 'https://cdn.tarotqa.com/public';
const LOCAL_IMAGE_PATH = '/images-optimized';

/**
 * 自定义Hook，用于处理CDN图片URL
 * 
 * @param url 原始URL，可以是CDN URL或本地资源路径
 * @returns 处理后的URL和相关工具函数
 */
const useCdnImage = (url: string) => {
  // 处理后的URL
  const processedUrl = useMemo(() => getImageUrl(url), [url]);
  
  // 转换为CDN URL的函数
  const convertToCdnUrl = useMemo(() => toCdnUrl(url), [url]);
  
  // 判断是否为CDN URL
  const isCdnUrl = useMemo(() => url.includes(CDN_BASE_URL), [url]);
  
  // 判断是否为本地资源路径
  const isLocalUrl = useMemo(() => url.startsWith(LOCAL_IMAGE_PATH), [url]);
  
  return {
    url: processedUrl,
    cdnUrl: convertToCdnUrl,
    isCdnUrl,
    isLocalUrl,
  };
};

/**
 * 自定义Hook，用于处理多个CDN图片URL
 * 
 * @param urls 原始URL数组
 * @returns 处理后的URL数组
 */
export const useCdnImages = (urls: string[]) => {
  return useMemo(() => urls.map(url => getImageUrl(url)), [urls]);
};

export default useCdnImage; 