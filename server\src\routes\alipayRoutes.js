const express = require('express');
const router = express.Router();
const alipayService = require('../services/alipayService');
const { authenticateToken } = require('../../middleware/auth');
const { getConnection } = require('../../services/database');

// 生成16位订单号：年月日(8位) + 随机数(8位)
function generateOrderId() {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const dateStr = `${year}${month}${day}`;
  const random = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
  return `${dateStr}${random}`;
}

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 创建支付宝支付订单
router.post('/create-order', authenticateToken, async (req, res) => {
  try {
    console.log('Received create order request:', req.body);
    const { amount, productId, productName } = req.body;
    const userId = req.user.userId;

    // 参数验证
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '无效的支付金额'
      });
    }

    if (!productId || !productName) {
      return res.status(400).json({
        success: false,
        message: '商品信息不完整'
      });
    }

    // 生成订单号和UUID
    const orderId = generateOrderId();
    const uuid = generateUUID();

    // 创建订单记录
    const pool = await getConnection();
    const sql = `
      INSERT INTO payment_orders (
        id, order_id, user_id, product_id, product_name, 
        amount, payment_method, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW(), NOW())
    `;

    const [result] = await pool.query(sql, [
      uuid,
      orderId,
      userId,
      productId,
      productName,
      amount,
      'alipay'
    ]);

    console.log('Created order:', {
      orderId,
      uuid,
      userId,
      amount,
      paymentMethod: 'alipay'
    });

    const payResult = await alipayService.createPageOrder({
      amount,
      description: productName,
      orderId,
      userId,
      productId,
      productName
    });

    res.json({
      success: true,
      data: {
        orderId: orderId,
        payUrl: payResult.payUrl,
        paymentMethod: 'alipay'
      }
    });
  } catch (error) {
    console.error('Create payment error:', error.response?.data || error);
    res.status(500).json({
      success: false,
      message: error.response?.data?.message || error.message || '创建支付订单失败'
    });
  }
});

// 创建支付宝移动端支付订单
router.post('/create-wap-order', authenticateToken, async (req, res) => {
  try {
    console.log('Received create wap order request:', req.body);
    const { amount, productId, productName } = req.body;
    const userId = req.user.userId;

    // 参数验证
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '无效的支付金额'
      });
    }

    if (!productId || !productName) {
      return res.status(400).json({
        success: false,
        message: '商品信息不完整'
      });
    }

    // 生成订单号和UUID
    const orderId = generateOrderId();
    const uuid = generateUUID();

    // 创建订单记录
    const pool = await getConnection();
    const sql = `
      INSERT INTO payment_orders (
        id, order_id, user_id, product_id, product_name, 
        amount, payment_method, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW(), NOW())
    `;

    const [result] = await pool.query(sql, [
      uuid,
      orderId,
      userId,
      productId,
      productName,
      amount,
      'alipay'
    ]);

    console.log('Created wap order:', {
      orderId,
      uuid,
      userId,
      amount,
      paymentMethod: 'alipay'
    });

    const payResult = await alipayService.createWapOrder({
      amount,
      description: productName,
      orderId,
      userId,
      productId,
      productName
    });

    res.json({
      success: true,
      data: {
        orderId: orderId,
        payUrl: payResult.payUrl,
        paymentMethod: 'alipay'
      }
    });
  } catch (error) {
    console.error('Create wap payment error:', error.response?.data || error);
    res.status(500).json({
      success: false,
      message: error.response?.data?.message || error.message || '创建移动端支付订单失败'
    });
  }
});

// 查询支付宝订单状态
router.get('/order-status/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    // 参数验证
    if (!orderId || orderId.length !== 16) {
      return res.status(400).json({
        success: false,
        message: '无效的订单号'
      });
    }

    console.log('Querying order status:', orderId);
    const result = await alipayService.queryOrder(orderId);
    
    res.json({
      success: true,
      data: {
        orderId,
        tradeState: result.tradeState,
        tradeStateDesc: result.tradeStateDesc
      }
    });
  } catch (error) {
    console.error('Query order status error:', error.response?.data || error);
    res.status(500).json({
      success: false,
      message: error.response?.data?.message || error.message || '查询订单状态失败'
    });
  }
});

// 关闭支付宝订单
router.post('/close-order', async (req, res) => {
  const startTime = new Date();
  console.log('=== 收到关闭订单请求 ===');
  console.log('请求时间:', startTime.toISOString());
  console.log('请求参数:', req.body);
  
  try {
    const { orderId } = req.body;
    
    if (!orderId) {
      console.log('请求参数错误: 缺少订单ID');
      return res.status(400).json({ error: 'Order ID is required' });
    }

    const result = await alipayService.closeOrder(orderId);
    
    const endTime = new Date();
    const duration = endTime - startTime;
    console.log('订单关闭完成:', {
      orderId,
      success: result.success,
      duration: `${duration}ms`
    });
    
    res.json(result);
  } catch (error) {
    console.error('关闭订单失败:', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({ error: 'Failed to close order' });
  }
});

// 支付宝支付异步通知
router.post('/notify', async (req, res) => {
  try {
    const notifyData = req.body;
    const { out_trade_no, trade_status, total_amount } = notifyData;

    console.log(`[支付宝通知] 订单 ${out_trade_no}: 状态=${trade_status}, 金额=${total_amount}`);

    // 验证是否成功解析
    if (!notifyData || typeof notifyData !== 'object') {
      console.error(`[支付宝通知] ${out_trade_no}: 请求体解析失败`);
      return res.send('fail');
    }

    // 验证通知签名
    const signVerified = await alipayService.verifyCallback(notifyData);
    if (!signVerified) {
      console.error(`[支付宝通知] ${out_trade_no}: 签名验证失败`);
      return res.send('fail');
    }

    // 验证 app_id 是否正确
    const { app_id } = notifyData;
    if (app_id !== process.env.ALIPAY_APP_ID) {
      console.error(`[支付宝通知] ${out_trade_no}: 应用ID不匹配`);
      return res.send('fail');
    }

    // 处理支付结果
    if (trade_status === 'TRADE_SUCCESS' || trade_status === 'TRADE_FINISHED') {
      const result = await alipayService.handlePaymentNotification(notifyData);
      console.log(`[支付宝通知] ${out_trade_no}: 处理完成, 结果=${result.success ? '成功' : '失败'}`);
      return res.send('success');
    }

    return res.send('success');
  } catch (error) {
    console.error('处理支付宝异步通知时发生错误:', error);
    console.error('错误堆栈:', error.stack);
    return res.send('fail');
  }
});

// 支付宝支付同步回调
router.get('/return', async (req, res) => {
  try {
    console.log('=== 支付宝支付完成，用户返回 ===');
    console.log('返回参数:', req.query);

    // 重定向到前端的支付成功页面
    res.redirect(process.env.VITE_CLIENT_URL + '/payment/success');
  } catch (error) {
    console.error('处理支付宝返回时发生错误:', error);
    res.redirect(process.env.VITE_CLIENT_URL + '/payment/error');
  }
});

module.exports = router; 