import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import SEO from '../components/SEO';
import { useUser } from '../contexts/UserContext';
import { useTheme } from '../contexts/ThemeContext';
import toast from 'react-hot-toast';
import axios from 'axios';

// 反馈类型选项
const feedbackTypes = [
  { id: 'problem', label: '问题反馈' },
  { id: 'suggestion', label: '功能建议' },
  { id: 'other', label: '其他' }
];

const Feedback: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { navigate } = useLanguageNavigate();
  const { user } = useUser();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // 表单状态
  const [formData, setFormData] = useState({
    type: 'problem',
    content: '',
    email: user?.email || ''
  });
  
  // 提交状态
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  
  // 字数统计
  const contentLength = formData.content.length;
  const maxContentLength = 2000;
  
  // 处理表单字段变更
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;
    
    // 基本表单验证
    if (formData.content.trim().length < 5) {
      toast.error(t('feedback.error.content_too_short', '请提供更详细的反馈信息（至少5个字符）'));
      return;
    }
    
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      toast.error(t('feedback.error.invalid_email', '请输入有效的邮箱地址'));
      return;
    }
    
    // 提交反馈
    try {
      setIsSubmitting(true);
      
      const response = await axios.post('/api/feedback', {
        type: formData.type,
        content: formData.content,
        email: formData.email || undefined
      });
      
      if (response.data.success) {
        setIsSuccess(true);
        toast.success(t('feedback.success', '感谢您的反馈！'));
        
        // 3秒后返回首页
        setTimeout(() => {
          navigate('/');
        }, 3000);
      } else {
        throw new Error(response.data.message || '提交失败');
      }
    } catch (error) {
      toast.error(t('feedback.error.submit_failed', '提交失败，请稍后重试'));
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // 获取字体类名
  const getFontClass = () => {
    // 根据语言设置不同的字体
    switch(i18n.language) {
      case 'ja':
        return 'font-["Noto_Sans_JP"]';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-["Noto_Sans_SC"]';
      default:
        return 'font-["Inter"]';
    }
  };
  
  // 获取按钮样式
  const getButtonStyle = () => {
    if (isSubmitting) {
      return isDark ? 'bg-purple-400 cursor-not-allowed' : 'bg-purple-300 cursor-not-allowed';
    }
    if (isSuccess) {
      return isDark ? 'bg-green-500 hover:bg-green-600' : 'bg-green-500 hover:bg-green-600';
    }
    return isDark 
      ? 'bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-500 hover:to-purple-700'
      : 'bg-gradient-to-r from-purple-500 to-purple-700 hover:from-purple-400 hover:to-purple-600';
  };
  
  // 获取按钮文本
  const getButtonText = () => {
    if (isSubmitting) {
      return t('feedback.submitting', '提交中...');
    }
    if (isSuccess) {
      return t('feedback.submitted', '提交成功');
    }
    return t('feedback.submit', '提交反馈');
  };
  
  return (
    <>
      <SEO 
      />
      <div className="min-h-screen flex flex-col relative font-['Inter']">
        <LandingBackground />
        <div className="flex-grow relative z-10">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8">
            <div className="text-center mt-8 sm:mt-10 mb-6">
              <h1 className={`main-title mb-1 ${getFontClass()}`}>{t('feedback.title')}</h1>
              <p className={`sub-title mb-4 sm:mb-6 ${getFontClass()}`}>{t('feedback.description')}</p>
            </div>
            
            <div className="max-w-2xl mx-auto">
              <div className={`${isDark 
                ? 'bg-black/30 backdrop-blur-sm border border-purple-500/20' 
                : 'bg-white backdrop-blur-sm border border-gray-200 shadow-sm'
              } rounded-xl p-6 shadow-xl`}>
                {isSuccess ? (
                  <div className="text-center py-10">
                    <div className="text-green-400 text-5xl mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <h2 className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-gray-800'} mb-2`}>{t('feedback.thank_you', '感谢您的反馈！')}</h2>
                    <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'} mb-6`}>{t('feedback.thank_you_description', '我们将认真考虑您的意见和建议。')}</p>
                    <p className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`}>{t('feedback.redirecting', '正在返回上一页...')}</p>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* 反馈类型 */}
                    <div>
                      <label htmlFor="type" className={`block mb-2 text-sm font-medium ${isDark ? 'text-white' : 'text-gray-700'} ${getFontClass()}`}>
                        {t('feedback.form.type', '反馈类型')}
                      </label>
                      <select
                        id="type"
                        name="type"
                        value={formData.type}
                        onChange={handleChange}
                        className={`w-full px-4 py-2 ${isDark 
                          ? 'bg-black/40 border-purple-500/30 text-white' 
                          : 'bg-white border-gray-300 text-gray-800'
                        } border rounded-lg focus:outline-none ${isDark 
                          ? 'focus:border-purple-500/60' 
                          : 'focus:border-purple-500'
                        } ${getFontClass()}`}
                      >
                        {feedbackTypes.map(type => (
                          <option key={type.id} value={type.id}>
                            {t(`feedback.form.types.${type.id}`, type.label)}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    {/* 反馈内容 */}
                    <div>
                      <label htmlFor="content" className={`block mb-2 text-sm font-medium ${isDark ? 'text-white' : 'text-gray-700'} ${getFontClass()}`}>
                        {t('feedback.form.content', '反馈内容')}
                        <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <textarea
                          id="content"
                          name="content"
                          value={formData.content}
                          onChange={handleChange}
                          rows={6}
                          maxLength={maxContentLength}
                          placeholder={t('feedback.form.content_placeholder', '请详细描述您遇到的问题或建议...')}
                          className={`w-full px-4 py-3 ${isDark 
                            ? 'bg-black/40 border-purple-500/30 text-white placeholder-gray-500' 
                            : 'bg-white border-gray-300 text-gray-800 placeholder-gray-400'
                          } border rounded-lg focus:outline-none ${isDark 
                            ? 'focus:border-purple-500/60' 
                            : 'focus:border-purple-500'
                          } ${getFontClass()}`}
                          required
                        ></textarea>
                        <div className={`absolute bottom-2 right-3 text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                          {contentLength}/{maxContentLength}
                        </div>
                      </div>
                    </div>
                    
                    {/* 联系邮箱 */}
                    <div>
                      <label htmlFor="email" className={`block mb-2 text-sm font-medium ${isDark ? 'text-white' : 'text-gray-700'} ${getFontClass()}`}>
                        {t('feedback.form.email', '联系邮箱')}
                        <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} text-xs ml-2`}>{t('feedback.form.optional', '(可选)')}</span>
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder={t('feedback.form.email_placeholder', '您的邮箱地址，便于我们联系您')}
                        className={`w-full px-4 py-2 ${isDark 
                          ? 'bg-black/40 border-purple-500/30 text-white placeholder-gray-500' 
                          : 'bg-white border-gray-300 text-gray-800 placeholder-gray-400'
                        } border rounded-lg focus:outline-none ${isDark 
                          ? 'focus:border-purple-500/60' 
                          : 'focus:border-purple-500'
                        } ${getFontClass()}`}
                      />
                    </div>
                    
                    {/* 提交按钮 */}
                    <div className="pt-4">
                      <button
                        type="submit"
                        disabled={isSubmitting || isSuccess}
                        className={`w-full py-3 px-4 rounded-lg text-white font-medium
                                 transition-all duration-300 ${getButtonStyle()} ${getFontClass()}`}
                      >
                        {getButtonText()}
                      </button>
                    </div>
                  </form>
                )}
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
};

export default Feedback; 