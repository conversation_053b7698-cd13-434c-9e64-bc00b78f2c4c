import React, { useState, useEffect } from 'react';
import { useUser } from '../contexts/UserContext';
import { login } from '../services/userService';
import { motion } from 'framer-motion';
import LandingBackground from '../components/LandingBackground';
import { GoogleLogin } from '@react-oauth/google';
import axios from '../utils/axios';
import { toast } from 'react-hot-toast';
import Footer from '../components/Footer';
import { useTranslation } from 'react-i18next';
import SEO from '../components/SEO';
import LanguageLink from '../components/LanguageLink';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

// 添加 CheckmarkAnimation 组件
const CheckmarkAnimation: React.FC<{
  size?: number;
  color?: string;
  strokeWidth?: number;
  animationDuration?: number;
}> = ({
  size = 32,
  color = '#FFFFFF',
  strokeWidth = 4,
  // animationDuration = 2,
}) => {
  const circleVariants = {
    hidden: { opacity: 0, scale: 0.5 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  const checkVariants = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { duration: 0.5, ease: "easeOut" },
        opacity: { duration: 0.01 },
      },
    },
  };

  return (
    <div style={{ width: size, height: size }}>
      <motion.svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        initial="hidden"
        animate="visible"
      >
        <defs>
          <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgb(168, 85, 247)" />
            <stop offset="100%" stopColor="rgb(236, 72, 153)" />
          </linearGradient>
        </defs>
        <motion.circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          variants={circleVariants}
        />
        <motion.path
          d="M30,50 L45,65 L70,40"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
          variants={checkVariants}
        />
      </motion.svg>
    </div>
  );
};

const Login: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { setUser } = useUser();
  const { t, i18n } = useTranslation();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isDarkTheme, setIsDarkTheme] = useState(() => 
    document.documentElement.classList.contains('dark')
  );

  // 添加监听主题变化的逻辑
  useEffect(() => {
    const themeObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          setIsDarkTheme(document.documentElement.classList.contains('dark'));
        }
      });
    });
    
    themeObserver.observe(document.documentElement, { attributes: true });
    
    return () => {
      themeObserver.disconnect();
    };
  }, []);

  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  // 简单的加密函数
  const encryptPassword = (password: string): string => {
    return btoa(password); // 使用 base64 编码，实际项目中应使用更安全的加密方法
  };

  // 简单的解密函数
  const decryptPassword = (encryptedPassword: string): string => {
    try {
      return atob(encryptedPassword);
    } catch {
      return '';
    }
  };

  // 组件加载时检查是否有保存的登录信息
  useEffect(() => {
    const savedEmail = localStorage.getItem('rememberedEmail');
    const savedPassword = localStorage.getItem('rememberedPassword');
    
    if (savedEmail && savedPassword) {
      setFormData(prev => ({
        ...prev,
        email: savedEmail,
        password: decryptPassword(savedPassword),
        rememberMe: true
      }));
    }
  }, []);

  useEffect(() => {
    // 禁用 COOP 错误日志
    const originalError = console.error;
    console.error = (...args) => {
      if (args[0]?.includes?.('Cross-Origin-Opener-Policy')) {
        return;
      }
      originalError.apply(console, args);
    };

    return () => {
      console.error = originalError;
    };
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    // 当取消"记住我"时，清除保存的登录信息
    if (name === 'rememberMe' && !checked) {
      localStorage.removeItem('rememberedEmail');
      localStorage.removeItem('rememberedPassword');
    }
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setIsSuccess(false);

    try {
      const response = await login(formData.email, formData.password);
      if (response.token && response.user) {
        // 始终将 token 和 userId 存储在 localStorage 中
        localStorage.setItem('token', response.token);
        localStorage.setItem('userId', response.user.id);
        
        // 只有在选择"记住我"时才保存登录信息
        if (formData.rememberMe) {
          localStorage.setItem('rememberedEmail', formData.email);
          localStorage.setItem('rememberedPassword', encryptPassword(formData.password));
        } else {
          // 如果没有选择"记住我"，清除保存的登录信息
          localStorage.removeItem('rememberedEmail');
          localStorage.removeItem('rememberedPassword');
        }
        
        setTimeout(() => {
          setIsLoading(false);
          setIsSuccess(true);
          setTimeout(() => {
            setUser(response.user);
            navigate('/home');
          }, 800);
        }, 500);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message;

      
      if (errorMessage && typeof errorMessage === 'string') {
        // 检查是否是后端返回的翻译键
        if (errorMessage.startsWith('auth.')) {

          setError(t(errorMessage));
        } else {

          setError(errorMessage || t('login.errors.login_failed'));
        }
      } else {

        setError(t('login.errors.login_failed'));
      }
      setIsLoading(false);
    }
  };

  const handleGoogleSuccess = async (credentialResponse: any) => {
    setIsLoading(true);
    setError('');
    setIsSuccess(false);
    
    try {
      // 获取浏览器指纹
      let fingerprint = '';
      const { getBrowserFingerprint } = await import('../utils/fingerprint');
      fingerprint = await getBrowserFingerprint();


      const response = await axios.post('/api/auth/google', {
        credential: credentialResponse.credential,
        fingerprint
      });

      if (response.data.token) {
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('userId', response.data.user.id);
        
        // 移除"记住我"保存的信息，因为使用Google登录
        localStorage.removeItem('rememberedEmail');
        localStorage.removeItem('rememberedPassword');
        
        setIsLoading(false);
        setIsSuccess(true);
        
        setTimeout(() => {
        setUser(response.data.user);
        navigate('/home');
        }, 800);
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(
        error.response?.data?.message || 
        t('login.errors.google_login_failed')
      );
    }
  };

  // 切换密码显示/隐藏状态
  const togglePasswordVisibility = () => {
    setShowPassword(prevState => !prevState);
  };

  // 在这里添加一个处理忘记密码的函数
  const handleForgotPassword = () => {
    // 保存邮箱到sessionStorage，这样在忘记密码页面可以获取
    if (formData.email) {
      sessionStorage.setItem('forgotPasswordEmail', formData.email);
    }
    navigate('/forgot-password');
  };

  return (
    <div className="min-h-screen flex flex-col relative font-['Inter']">
      <SEO 
      />
      <style>
        {`
          .input-field {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.2s ease-in-out;
            z-index: 1;
            position: relative;
          }
          
          /* CSS变量 - 主题相关颜色 */
          :root, html, body {
            --input-bg-dark: rgba(255, 255, 255, 0.05);
            --input-border-dark: rgba(255, 255, 255, 0.1);
            --input-text-dark: rgba(255, 255, 255, 0.9);
            --input-bg-light: #F4F4F5;
            --input-border-light: rgba(168, 85, 247, 0.2);
            --input-text-light: #1F2937;
          }
          
          /* 彻底覆盖输入框样式 */
          body:not(.dark) .login-card input, 
          body:not(.dark) .login-card textarea,
          html:not(.dark) .login-card input, 
          html:not(.dark) .login-card textarea,
          div:not(.dark) .login-card input,
          div:not(.dark) .login-card textarea,
          :not(.dark) .input-field[type="email"],
          :not(.dark) .input-field[type="password"],
          :not(.dark) .input-field[type="text"] {
            background-color: #F4F4F5 !important;
            color: #1F2937 !important;
            border: 1px solid rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 确保登录按钮在浅色主题下文字为白色 */
          body:not(.dark) .login-card button[type="submit"],
          html:not(.dark) .login-card button[type="submit"],
          :not(.dark) .login-card button[type="submit"] {
            color: white !important;
          }
          
          /* 确保登录按钮内的所有文字元素在浅色主题下都是白色 */
          body:not(.dark) .login-card button[type="submit"] *,
          html:not(.dark) .login-card button[type="submit"] *,
          :not(.dark) .login-card button[type="submit"] span,
          :not(.dark) .login-card button[type="submit"] div {
            color: white !important;
          }
          
          /* 输入框聚焦状态 */
          body:not(.dark) .login-card input:focus, 
          html:not(.dark) .login-card input:focus,
          :not(.dark) .input-field[type="email"]:focus,
          :not(.dark) .input-field[type="password"]:focus,
          :not(.dark) .input-field[type="text"]:focus {
            background-color: #FFFFFF !important;
            color: #1F2937 !important;
            border: 1px solid rgba(168, 85, 247, 0.5) !important;
            box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 占位符文本样式 */
          body:not(.dark) .login-card input::placeholder,
          html:not(.dark) .login-card input::placeholder,
          :not(.dark) .input-field::placeholder {
            color: #6B7280 !important;
          }
          
          /* 深色主题输入框样式 */
          body.dark .login-card input, 
          html.dark .login-card input,
          .dark .input-field[type="email"],
          .dark .input-field[type="password"],
          .dark .input-field[type="text"] {
            background-color: #13111C !important;
            color: #FFFFFF !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
          }
          
          /* 深色主题输入框聚焦状态 */
          body.dark .login-card input:focus, 
          html.dark .login-card input:focus,
          .dark .input-field[type="email"]:focus,
          .dark .input-field[type="password"]:focus,
          .dark .input-field[type="text"]:focus {
            background-color: #13111C !important;
            color: #FFFFFF !important;
            border: 1px solid rgba(168, 85, 247, 0.5) !important;
            box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 深色主题下的登录输入框样式 */
          .dark .login-input {
            background-color: #13111C !important;
            color: rgba(255, 255, 255, 0.9) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
          }

          /* 修复自动填充状态下的输入框样式 */
          body:not(.dark) .login-card input:-webkit-autofill,
          body:not(.dark) .login-card input:-webkit-autofill:hover,
          body:not(.dark) .login-card input:-webkit-autofill:focus,
          body:not(.dark) .login-card input:-webkit-autofill:active,
          html:not(.dark) .login-card input:-webkit-autofill,
          html:not(.dark) .login-card input:-webkit-autofill:hover,
          html:not(.dark) .login-card input:-webkit-autofill:focus,
          html:not(.dark) .login-card input:-webkit-autofill:active,
          :not(.dark) .login-card input:-webkit-autofill,
          :not(.dark) .login-card input:-webkit-autofill:hover,
          :not(.dark) .login-card input:-webkit-autofill:focus,
          :not(.dark) .login-card input:-webkit-autofill:active {
            -webkit-text-fill-color: #1F2937 !important;
            -webkit-box-shadow: 0 0 0px 1000px #F4F4F5 inset !important;
            box-shadow: 0 0 0px 1000px #F4F4F5 inset !important;
            background-color: #F4F4F5 !important;
            border: 1px solid rgba(168, 85, 247, 0.2) !important;
            caret-color: #1F2937 !important;
            transition: background-color 5000s ease-in-out 0s !important;
          }
          
          /* 深色主题下自动填充状态的输入框样式 */
          body.dark .login-card input:-webkit-autofill,
          body.dark .login-card input:-webkit-autofill:hover,
          body.dark .login-card input:-webkit-autofill:focus,
          body.dark .login-card input:-webkit-autofill:active,
          html.dark .login-card input:-webkit-autofill,
          html.dark .login-card input:-webkit-autofill:hover,
          html.dark .login-card input:-webkit-autofill:focus,
          html.dark .login-card input:-webkit-autofill:active,
          .dark .login-card input:-webkit-autofill,
          .dark .login-card input:-webkit-autofill:hover,
          .dark .login-card input:-webkit-autofill:focus,
          .dark .login-card input:-webkit-autofill:active {
            -webkit-text-fill-color: #FFFFFF !important;
            -webkit-box-shadow: 0 0 0px 1000px #13111C inset !important;
            box-shadow: 0 0 0px 1000px #13111C inset !important;
            background-color: #13111C !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            caret-color: #FFFFFF !important;
            transition: background-color 5000s ease-in-out 0s !important;
          }
          
          .login-card {
            position: relative;
            background: rgba(13, 12, 15, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(236, 72, 153, 0.3);
            box-shadow: 
              0 0 0 1px rgba(168, 85, 247, 0.2),
              0 0 15px rgba(168, 85, 247, 0.15),
              0 0 30px rgba(236, 72, 153, 0.15),
              inset 0 0 15px rgba(168, 85, 247, 0.1);
          }
          /* 深色主题下的样式保持不变 */
          :root.dark .login-card, 
          html.dark .login-card, 
          .dark .login-card {
            background: rgba(13, 12, 15, 0.95);
          }
          /* 浅色主题下的卡片背景颜色 */
          :root:not(.dark) .login-card, 
          html:not(.dark) .login-card, 
          :not(.dark) .login-card {
            background: #F4F4F5;
          }
          .login-card::before {
            content: '';
            position: absolute;
            inset: -1px;
            padding: 1px;
            background: linear-gradient(
              135deg,
              rgba(168, 85, 247, 0.5),
              rgba(236, 72, 153, 0.5)
            );
            -webkit-mask: 
              linear-gradient(#fff 0 0) content-box, 
              linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
          }
          @media (max-width: 640px) {
            .login-card {
              margin: 1rem;
            }
          }
          
          /* 浅色主题下图标颜色 */
          body:not(.dark) .relative .absolute.left-3 svg path {
            stroke: #6B7280 !important;
          }
          
          /* 深色主题下图标颜色 */
          .dark .relative .absolute.left-3 svg path {
            stroke: rgba(255, 255, 255, 0.7) !important;
          }
          
          /* 图标位置调整 */
          .relative .absolute.left-3 {
            z-index: 20 !important; 
            pointer-events: none !important;
          }
          
          /* 确保左侧有足够空间放置图标 */
          .input-field {
            padding-left: 40px !important;
          }
          
          /* 登录输入框特殊样式 */
          .login-input {
            background-color: var(--input-bg-light) !important;
            color: var(--input-text-light) !important;
            border-color: var(--input-border-light) !important;
          }
          
          .dark .login-input {
            background-color: var(--input-bg-dark) !important;
            color: var(--input-text-dark) !important;
            border-color: var(--input-border-dark) !important;
          }
          
          /* 浅色主题下确保文字和图标不重叠 */
          body:not(.dark) .login-input {
            color: #1F2937 !important;
            background-color: #F4F4F5 !important;
          }
          
          body:not(.dark) .login-input::placeholder {
            color: #6B7280 !important;
          }
        `}
      </style>
      <LandingBackground />
      <div className="flex-grow flex items-center justify-center relative z-10 mt-8 sm:mt-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-2 sm:mb-8">
            <h1 className={`main-title mb-1 ${getFontClass()}`}>{t('login.title')}</h1>
            <p className="sub-title"></p>
          </div>
          <div className="flex items-center justify-center">
            <div className="w-full max-w-md mx-auto">
              <div className="login-card p-5 sm:p-8 rounded-2xl shadow-2xl relative overflow-hidden">
                {/* 装饰性光效 */}
                <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/20 rounded-full blur-3xl"></div>
                
                <div className="relative">
                  {error && (
                    <div className="mb-3 sm:mb-4 p-2.5 sm:p-3 rounded-lg bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
                      <p className="text-red-400 text-xs sm:text-sm font-['Inter']">{error}</p>
                    </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-5 sm:space-y-6">
                    <div>
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 8L10.8906 13.2604C11.5624 13.7083 12.4376 13.7083 13.1094 13.2604L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          className={`input-field login-input block w-full px-3 py-2 sm:py-2.5 rounded-xl
                                  dark:text-white text-gray-800 text-[15px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                  placeholder-gray-400 backdrop-blur-sm pl-10 font-['Inter']`}
                          placeholder={t('login.email.placeholder')}
                          required
                          autoComplete="email"
                        />
                      </div>
                    </div>

                    <div>
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 15V17M6 21H18C19.1046 21 20 20.1046 20 19V13C20 11.8954 19.1046 11 18 11H6C4.89543 11 4 11.8954 4 13V19C4 20.1046 4.89543 21 6 21ZM16 11V7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7V11H16Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <input
                          type={showPassword ? "text" : "password"}
                          name="password"
                          value={formData.password}
                          onChange={handleChange}
                          className={`input-field login-input block w-full px-3 py-2 sm:py-2.5 rounded-xl
                                  dark:text-white text-gray-800 text-[15px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                  placeholder-gray-400 backdrop-blur-sm pl-10 pr-12 font-['Inter']`}
                          placeholder={t('login.password.placeholder')}
                          required
                          autoComplete="current-password"
                        />
                        <button
                          type="button"
                          onClick={togglePasswordVisibility}
                          className="absolute right-2 top-1/2 -translate-y-1/2
                                  text-gray-300 hover:text-purple-400 transition-colors
                                  focus:outline-none focus:text-purple-400 z-20
                                  w-8 h-8 flex items-center justify-center"
                          aria-label={showPassword ? t('login.hide_password') : t('login.show_password')}
                        >
                          {showPassword ? (
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                            </svg>
                          ) : (
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <label className="relative flex items-center gap-3 group cursor-pointer select-none font-['Inter']">
                        <div className="relative">
                          <input
                            type="checkbox"
                            name="rememberMe"
                            checked={formData.rememberMe}
                            onChange={handleChange}
                            className="peer sr-only"
                          />
                          <div className="h-5 w-5 rounded-md border border-purple-500/30 bg-white/5 backdrop-blur-sm
                                        peer-checked:border-purple-500/50
                                        transition-all duration-200"></div>
                          <div className="absolute inset-0 rounded-md opacity-0
                                        peer-checked:opacity-100 transition-opacity duration-200
                                        bg-gradient-to-r from-purple-500/80 to-pink-500/80
                                        flex items-center justify-center">
                            <svg 
                              className="w-3.5 h-3.5 text-white" 
                              viewBox="0 0 16 16"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M3 8l3 3 7-7" />
                            </svg>
                          </div>
                        </div>
                        <span className="text-sm text-gray-300 group-hover:text-purple-300 transition-colors">{t('login.remember_me')}</span>
                      </label>
                      <button 
                        type="button"
                        onClick={handleForgotPassword}
                        className="text-sm text-gray-400 hover:text-purple-300 transition-colors font-['Inter']"
                      >
                        {t('login.forgot_password')}
                      </button>
                    </div>

                    <div className="pt-2 sm:pt-3">
                      <button
                        type="submit"
                        disabled={isLoading || isSuccess}
                        className="w-full relative bg-purple-600 rounded-xl 
                                 hover:bg-purple-500 active:bg-purple-700 
                                 transition-colors duration-200 disabled:opacity-50
                                 text-white"
                      >
                        <div className="relative px-3 sm:px-4 h-[34px] sm:h-[40px] flex items-center justify-center">
                          <div className="flex items-center justify-center w-[24px] sm:w-[28px] h-[24px] sm:h-[28px]">
                            {isLoading ? (
                              <div className="w-4 sm:w-5 h-4 sm:h-5 border-2 border-white/20 border-t-white rounded-full animate-spin"></div>
                            ) : isSuccess ? (
                              <CheckmarkAnimation size={24} strokeWidth={4} />
                            ) : (
                              <span className="text-white text-sm sm:text-base font-medium whitespace-nowrap font-['Inter']">{t('login.submit')}</span>
                            )}
                          </div>
                        </div>
                      </button>
                    </div>
                  </form>

                  <div className="mt-2.5 sm:mt-4 text-center">
                    <span className="text-gray-400 text-sm font-['Inter']">{t('login.no_account')}</span>
                    <LanguageLink 
                      to="/register"
                      className="text-blue-500 hover:text-blue-600 ml-1 text-sm font-medium
                               transition-all duration-200 font-['Inter'] !important"
                      style={{ color: '#3b82f6' }}
                    >
                      {t('login.register')}
                    </LanguageLink>
                  </div>

                  <div className="mt-2.5 sm:mt-4">
                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t dark:border-gray-600 border-gray-300"></div>
                      </div>
                      <div className="relative flex justify-center text-sm">
                        <span className="px-2 dark:bg-[#0D0C0F] bg-[#F4F4F5] dark:text-gray-400 text-gray-500 text-sm font-['Inter']">{t('login.or')}</span>
                      </div>
                    </div>

                    <div className="mt-2.5 sm:mt-4 flex justify-center">
                      <GoogleLogin
                        onSuccess={handleGoogleSuccess}
                        onError={() => {
                          toast.error(t('login.errors.google_login_failed'));
                        }}
                        theme={isDarkTheme ? "filled_black" : "outline"}
                        shape="pill"
                        text="signin_with"
                        locale={i18n.language}
                        useOneTap={true}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* 服务条款和隐私政策文本 - 卡片外部，紧贴下方边框 */}
          <div className="text-center text-xs text-gray-400 mt-2 sm:mt-8 mb-10 sm:mb-4">
            <span>{t('auth.login_terms_agreement')} </span>
            <LanguageLink 
              to="/terms" 
              className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 
                     hover:from-purple-500 hover:to-pink-500 transition-all duration-200"
            >
              {t('auth.terms_of_service')}
            </LanguageLink>
            <span> {t('auth.and')} </span>
            <LanguageLink 
              to="/privacy"
              className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 
                     hover:from-purple-500 hover:to-pink-500 transition-all duration-200"
            >
              {t('auth.privacy_policy')}
            </LanguageLink>
            {i18n.language === 'ja' && <span>{t('auth.terms_agreement_2')}</span>}
          </div>
        </div>
      </div>
      <div className="relative z-10">
        <Footer />
      </div>
    </div>
  );
};

export default Login;
