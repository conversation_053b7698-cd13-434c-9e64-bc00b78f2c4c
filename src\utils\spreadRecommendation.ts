import axiosInstance from './axios';

// 从API获取牌阵推荐
export async function getSpreadRecommendation(question: string): Promise<string> {
  
  const token = localStorage.getItem('token');
  const language = localStorage.getItem('i18nextLng') || 'zh-CN';
  const sessionId = localStorage.getItem('sessionId'); // 获取sessionId

  // 确保language是有效的语言代码
  const validLanguages = ['zh-CN', 'zh-TW', 'en', 'ja'];
  const currentLanguage = validLanguages.includes(language) ? language : 'zh-CN';


  if (!token) {
    return 'celtic-cross'; // 默认牌阵
  }

  try {
    const requestBody = {
      question,
      language: currentLanguage,
      sessionId // 添加sessionId到请求
    };

    const response = await axiosInstance.post('/api/spread-recommendation/', requestBody);
    if (response.data.recommendation?.recommendedSpreadId) {
      return response.data.recommendation.recommendedSpreadId;
    }

    return 'celtic-cross'; // 默认牌阵
  } catch (error) {
    // console.error('获取推荐时出错:', error);
    return 'celtic-cross'; // 默认牌阵
  }
}
