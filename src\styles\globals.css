/* 覆盖浏览器自动填充样式 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
}

/* 暗色主题下自动填充样式 */
.dark input:-webkit-autofill,
.dark input:-webkit-autofill:hover,
.dark input:-webkit-autofill:focus,
.dark input:-webkit-autofill:active {
    -webkit-background-clip: text;
    -webkit-text-fill-color: #fff !important;
    box-shadow: inset 0 0 20px 20px #13111C;
}

/* 浅色主题下自动填充样式 */
body:not(.dark) input:-webkit-autofill,
body:not(.dark) input:-webkit-autofill:hover,
body:not(.dark) input:-webkit-autofill:focus,
body:not(.dark) input:-webkit-autofill:active {
    -webkit-background-clip: text;
    -webkit-text-fill-color: #000 !important;
    box-shadow: inset 0 0 20px 20px #ffffff;
}

/* 输入框基础样式 - 暗色主题 */
.dark input {
    background: #13111C !important;
    color: #fff !important;
    border: 1px solid #2D2D2D;
    border-radius: 0.75rem;
    padding: 8px 12px;
    width: 100%;
    outline: none;
    transition: border-color 0.3s ease;
}

/* 输入框基础样式 - 浅色主题 */
body:not(.dark) input {
    background: #ffffff !important;
    color: #000 !important;
    border: 1px solid #d1d5db;
    border-radius: 0.75rem;
    padding: 8px 12px;
    width: 100%;
    outline: none;
    transition: border-color 0.3s ease;
}

.dark input:focus {
    border-color: #8B5CF6;
}

body:not(.dark) input:focus {
    border-color: #8B5CF6;
}

/* 下拉框样式 - 暗色主题 */
.dark select {
    background: #13111C !important;
    color: #fff !important;
    border: 1px solid #2D2D2D;
    border-radius: 4px;
    padding: 8px 12px;
    width: 100%;
    outline: none;
    transition: border-color 0.3s ease;
    appearance: none;
    cursor: pointer;
}

/* 下拉框样式 - 浅色主题 */
body:not(.dark) select {
    background: #ffffff !important;
    color: #000 !important;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 8px 12px;
    width: 100%;
    outline: none;
    transition: border-color 0.3s ease;
    appearance: none;
    cursor: pointer;
}

.dark select:focus {
    border-color: #8B5CF6;
}

body:not(.dark) select:focus {
    border-color: #8B5CF6;
}

/* 占位符文本颜色 - 暗色主题 */
.dark ::placeholder {
    color: #6B7280;
    opacity: 1;
}

/* 占位符文本颜色 - 浅色主题 */
body:not(.dark) ::placeholder {
    color: #9CA3AF;
    opacity: 1;
}

/* DatePicker 深色主题样式 */
.react-datepicker {
  background-color: #1a1a1a !important;
  border-color: #2D2D2D !important;
  font-family: inherit;
}

.react-datepicker__header {
  background-color: #13111C !important;
  border-bottom-color: #2D2D2D !important;
}

.react-datepicker__current-month,
.react-datepicker__day-name,
.react-datepicker-time__header {
  color: #fff !important;
}

.react-datepicker__day {
  color: #fff !important;
}

.react-datepicker__day:hover {
  background-color: #8B5CF6 !important;
}

.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  background-color: #8B5CF6 !important;
  color: #fff !important;
}

.react-datepicker__day--disabled {
  color: #666 !important;
}

.react-datepicker__navigation-icon::before {
  border-color: #fff !important;
}

.react-datepicker__year-dropdown,
.react-datepicker__month-dropdown {
  background-color: #1a1a1a !important;
  border-color: #2D2D2D !important;
}

.react-datepicker__year-option:hover,
.react-datepicker__month-option:hover {
  background-color: #8B5CF6 !important;
}

.react-datepicker__year-option,
.react-datepicker__month-option {
  color: #fff !important;
}

.react-datepicker__input-container input {
  background-color: #13111C !important;
  color: #fff !important;
  border: 1px solid #2D2D2D !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  width: 100% !important;
  outline: none !important;
}

.react-datepicker__input-container input:focus {
  border-color: #8B5CF6 !important;
}

/* Landing page background meteor animation */
@keyframes meteor-anim {
  0% {
    transform: rotate(-12deg) translate(419px, 10px);
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: rotate(-12deg) translate(-419px, -10px);
    opacity: 0;
  }
}

/* 日期选择器样式 */
input[type="date"] {
    position: relative;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    background: transparent;
    color: transparent;
    cursor: pointer;
    height: 100%;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
}

input[type="date"]::-webkit-datetime-edit-text,
input[type="date"]::-webkit-datetime-edit-month-field,
input[type="date"]::-webkit-datetime-edit-day-field,
input[type="date"]::-webkit-datetime-edit-year-field {
    color: transparent;
}

input[type="date"]:not(:valid)::-webkit-datetime-edit {
    color: transparent;
}

input[type="date"]:focus::-webkit-datetime-edit {
    color: #fff;
}

input[type="date"]:valid::-webkit-datetime-edit {
    color: #fff;
}

/* 特殊处理带有内部图标的输入框（如登录页面） */
.dark input.input-field,
.dark .input-field[type="email"],
.dark .input-field[type="password"],
.dark .input-field[type="text"] {
    padding-left: 40px !important; /* 为左侧图标留出空间 */
    background: #13111C !important;
    color: #FFFFFF !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body:not(.dark) input.input-field,
body:not(.dark) .input-field[type="email"],
body:not(.dark) .input-field[type="password"],
body:not(.dark) .input-field[type="text"] {
    padding-left: 40px !important; /* 为左侧图标留出空间 */
    background: #F4F4F5 !important;
    color: #1F2937 !important;
    border: 1px solid rgba(168, 85, 247, 0.2) !important;
}

/* 输入框里的图标颜色 */
body:not(.dark) .relative .absolute.left-3 svg path {
    stroke: #6B7280 !important;
}

.dark .relative .absolute.left-3 svg path {
    stroke: rgba(255, 255, 255, 0.7) !important;
}

/* 确保图标位置正确 */
.relative .absolute.left-3 {
    z-index: 10 !important;
    pointer-events: none !important;
}

/* 覆盖一般输入框样式，保留特定区域的内部样式 */
body:not(.dark) .login-card input, 
body:not(.dark) .login-card textarea {
    background-color: #F4F4F5 !important;
    color: #1F2937 !important;
    border: 1px solid rgba(168, 85, 247, 0.2) !important;
}

/* 深色主题输入框样式 */
body.dark .login-card input, 
html.dark .login-card input,
.dark .input-field[type="email"],
.dark .input-field[type="password"],
.dark .input-field[type="text"] {
    background-color: #13111C !important;
    color: #FFFFFF !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* 深色主题下的登录输入框样式 */
.dark .login-input {
    background-color: #13111C !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

/* 追问错误消息样式 */
.error-message {
    color: #ef4444 !important;
    background-color: rgba(239, 68, 68, 0.1);
    padding: 8px 12px;
    border-radius: 8px;
    border-left: 3px solid #ef4444;
    margin: 8px 0;
    font-weight: 500;
}

/* 深色主题下的错误消息样式 */
.dark .error-message {
    background-color: rgba(239, 68, 68, 0.15);
    border-left: 3px solid #ef4444;
}
