import React, { createContext, useContext, useState, useEffect, useRef } from 'react';

type DropdownType = 'language' | 'user' | 'mobile' | 'catalog' | 'fortune' | 'blog' | 'blog-mobile' | 'mobile-blog' | null;

interface DropdownContextType {
  openDropdown: DropdownType;
  setOpenDropdown: (type: DropdownType) => void;
  registerDropdownRef: (type: DropdownType, buttonRef: React.RefObject<HTMLElement>, dropdownRef: React.RefObject<HTMLElement>) => void;
}

interface RegisteredRefs {
  buttonRef: React.RefObject<HTMLElement>;
  dropdownRef: React.RefObject<HTMLElement>;
}

const DropdownContext = createContext<DropdownContextType | undefined>(undefined);

export const DropdownProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [openDropdown, setOpenDropdown] = useState<DropdownType>(null);
  const refsMapRef = useRef<Map<DropdownType, RegisteredRefs>>(new Map());

  const registerDropdownRef = (
    type: DropdownType,
    buttonRef: React.RefObject<HTMLElement>,
    dropdownRef: React.RefObject<HTMLElement>
  ) => {
    refsMapRef.current.set(type, { buttonRef, dropdownRef });
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!openDropdown) return;

      // 特殊处理移动端菜单，防止点击内部元素导致菜单关闭
      if (openDropdown === 'mobile') {
        const currentRefs = refsMapRef.current.get(openDropdown);
        if (!currentRefs) return;
        
        const { buttonRef, dropdownRef } = currentRefs;
        
        // 只有当点击的是按钮本身或者完全在菜单外部时才关闭菜单
        // 这样可以确保点击菜单内部的子菜单项不会关闭整个菜单
        if (
          buttonRef.current && 
          !buttonRef.current.contains(event.target as Node) &&
          dropdownRef.current && 
          !dropdownRef.current.contains(event.target as Node)
        ) {
          setOpenDropdown(null);
        }
        return;
      }
      
      // 其他下拉菜单的常规处理
      const currentRefs = refsMapRef.current.get(openDropdown);
      if (!currentRefs) return;

      const { buttonRef, dropdownRef } = currentRefs;

      if (
        buttonRef.current &&
        dropdownRef.current &&
        !buttonRef.current.contains(event.target as Node) &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('click', handleClickOutside);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [openDropdown]);

  return (
    <DropdownContext.Provider value={{ openDropdown, setOpenDropdown, registerDropdownRef }}>
      {children}
    </DropdownContext.Provider>
  );
};

export const useDropdown = () => {
  const context = useContext(DropdownContext);
  if (context === undefined) {
    throw new Error('useDropdown must be used within a DropdownProvider');
  }
  return context;
}; 