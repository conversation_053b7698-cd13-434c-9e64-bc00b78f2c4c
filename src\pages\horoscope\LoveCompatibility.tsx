import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { horoscopeSigns } from '../../data/horoscopes';
import { useTheme } from '../../contexts/ThemeContext';
import { getFontClass } from '../../utils/fontUtils';
import LandingBackground from '../../components/LandingBackground';
import Footer from '../../components/Footer';
import SEO from '../../components/SEO';
import CdnLazyImage from '../../components/CdnLazyImage';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';
import SpotlightCard from '../../blocks/Components/SpotlightCard/SpotlightCard';
import ZodiacSignsGrid from '../../components/horoscope/ZodiacSignsGrid';
import HoroscopeFeatureCard from '../../components/horoscope/HoroscopeFeatureCard';
import HoverableText from '../../components/horoscope/HoverableText';
import Breadcrumb from '../../components/Breadcrumb';

const LoveCompatibility: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // 默认选择第一个星座
  const defaultSelectedSign = horoscopeSigns[0].id;

  const handleStartReading = () => {
    navigate('/');
  };

  // 定义面包屑导航项 - 与UnifiedHoroscopeDetail.tsx保持一致，不包含"首页"节点
  const breadcrumbItems = [
    { label: t('horoscope.title_short', '星座运势'), path: '/horoscope' },
    { label: t('horoscope.love_title', '爱情运势'), path: '/horoscope/love-horoscope', isActive: true }
  ];

  return (
    <div className={`min-h-screen flex flex-col relative antialiased ${isDark ? 'text-white' : 'text-gray-800'}`}>
      <SEO 
      />
      <LandingBackground />
      
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-16">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
            <h1 className={`main-title mb-1 sm:mb-2 ${getFontClass(i18n.language)} dark:text-white text-gray-900`}>
              <span className="block">{t('loveHoroscope.heading.line1', 'Love Horoscope - Daily Romance & Relationship Astrology')}</span>
            </h1>
            <h2 className={`text-base sm:text-lg ${isDark ? 'text-purple-300' : 'text-purple-600'} italic ${getFontClass(i18n.language)} font-normal`}>
              {t('loveHoroscope.subheading', 'Find your perfect match and navigate relationships with personalized love horoscopes and compatibility insights.')}
            </h2>
          </div>

          {/* 星座运势网格 - 使用ZodiacSignsGrid组件 */}
          <ZodiacSignsGrid containerClassName="" horoscopeType="love" defaultSelectedSign={defaultSelectedSign} date={new Date()} />
          
          {/* 面包屑导航 - 与UnifiedHoroscopeDetail.tsx保持一致 */}
          <div className="max-w-6xl mx-auto px-4 sm:px-6 mt-8">
            <Breadcrumb items={breadcrumbItems} className="text-base" />
          </div>
            
          {/* 星座运势介绍区域 - SEO优化版本 */}
          <div className="mt-12 max-w-6xl mx-auto px-4 sm:px-6">
            
            <style>
              {`
                .feature-card {
                  position: relative;
                  border: 1px solid rgba(236, 72, 153, 0.3);
                  box-shadow: 
                    0 0 0 1px rgba(168, 85, 247, 0.2),
                    0 0 15px rgba(168, 85, 247, 0.15),
                    0 0 30px rgba(236, 72, 153, 0.15),
                    inset 0 0 15px rgba(168, 85, 247, 0.1);
                }
                
                /* 深色主题特定样式 */
                .dark .feature-card::before {
                  content: '';
                  position: absolute;
                  inset: -1px;
                  padding: 1px;
                  background: linear-gradient(
                    135deg,
                    rgba(168, 85, 247, 0.5),
                    rgba(236, 72, 153, 0.5)
                  );
                  -webkit-mask: 
                    linear-gradient(#fff 0 0) content-box, 
                    linear-gradient(#fff 0 0);
                  -webkit-mask-composite: xor;
                  mask-composite: exclude;
                  pointer-events: none;
                  border-radius: 1rem;
                }
                
                /* 浅色主题特定样式 */
                :not(.dark) .feature-card::before {
                  content: '';
                  position: absolute;
                  inset: -1px;
                  padding: 1px;
                  background: linear-gradient(
                    135deg,
                    rgba(168, 85, 247, 0.3),
                    rgba(236, 72, 153, 0.3)
                  );
                  -webkit-mask: 
                    linear-gradient(#fff 0 0) content-box, 
                    linear-gradient(#fff 0 0);
                  -webkit-mask-composite: xor;
                  mask-composite: exclude;
                  pointer-events: none;
                  border-radius: 1rem;
                }
                
                .dark .feature-card::after {
                  content: '';
                  position: absolute;
                  inset: 0;
                  background: 
                    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, 0.15), transparent 70%),
                    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, 0.15), transparent 70%);
                  pointer-events: none;
                  border-radius: 1rem;
                }
                
                :not(.dark) .feature-card::after {
                  content: '';
                  position: absolute;
                  inset: 0;
                  background: 
                    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, 0.05), transparent 50%),
                    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, 0.05), transparent 50%);
                  pointer-events: none;
                  border-radius: 1rem;
                }
              `}
            </style>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-10 md:gap-10 lg:gap-12">
              {/* 每日运势 - 使用HoroscopeFeatureCard组件 */}
              <HoroscopeFeatureCard
                title={
                  i18n.language === 'zh-CN' 
                    ? "每日星座运势：今日宇宙洞察"
                    : i18n.language === 'zh-TW'
                    ? "每日星座運勢：今日宇宙洞察"
                    : i18n.language === 'ja'
                    ? "デイリー星占い：今日の宇宙の洞察"
                    : "Daily Horoscope: Cosmic Insight for Today"
                }
                description={
                  i18n.language === 'zh-CN'
                    ? "每24小时更新一次的日常见解。根据实时行星位置获取爱情、事业和财务方面的可行指导。"
                    : i18n.language === 'zh-TW'
                    ? "每24小時更新一次的日常見解。根據實時行星位置獲取愛情、事業和財務方面的可行指導。"
                    : i18n.language === 'ja'
                    ? "24時間ごとに更新される日々のインサイト。リアルタイムの惑星位置に基づいて、愛、キャリア、財政に関する実用的なガイダンスを得ましょう。"
                    : "Daily insights updated every 24 hours. Get actionable guidance for love, career, and finances based on real-time planetary positions."
                }
                icon={
                  <svg className={`w-10 h-10 ${isDark ? 'text-amber-500' : 'text-amber-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="5"></circle>
                    <line x1="12" y1="1" x2="12" y2="3"></line>
                    <line x1="12" y1="21" x2="12" y2="23"></line>
                    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                    <line x1="1" y1="12" x2="3" y2="12"></line>
                    <line x1="21" y1="12" x2="23" y2="12"></line>
                    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                  </svg>
                }
                onClick={() => navigate('/horoscope/daily-horoscope')}
              />
              
              {/* 每周运势 - 使用HoroscopeFeatureCard组件 */}
              <HoroscopeFeatureCard
                title={
                  i18n.language === 'zh-CN' 
                    ? "每周星座运势：您的7天战略预测"
                    : i18n.language === 'zh-TW'
                    ? "每週星座運勢：您的7天戰略預測"
                    : i18n.language === 'ja'
                    ? "ウィークリー星占い：あなたの7日間の戦略的予測"
                    : "Weekly Horoscope: Your 7-Day Strategic Forecast"
                }
                description={
                  i18n.language === 'zh-CN'
                    ? "用我们的7天预测来规划您的一周。我们追踪关键的行星变化，帮助您自信地应对重要时刻。"
                    : i18n.language === 'zh-TW'
                    ? "用我們的7天預測來規劃您的一週。我們追蹤關鍵的行星變化，幫助您自信地應對重要時刻。"
                    : i18n.language === 'ja'
                    ? "7日間の予測で週間計画を立てましょう。重要な惑星の動きを追跡し、重要な瞬間を自信を持って乗り切るためのガイダンスを提供します。"
                    : "Plan your week with our 7-day forecast. We track key planetary shifts to help you navigate important moments with confidence."
                }
                icon={
                  <svg className={`w-10 h-10 ${isDark ? 'text-amber-500' : 'text-amber-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                }
                onClick={() => navigate('/horoscope/weekly-horoscope')}
              />
              
              {/* 每月运势 - 使用HoroscopeFeatureCard组件 */}
              <HoroscopeFeatureCard
                title={
                  i18n.language === 'zh-CN' 
                    ? "每月星座运势：更深入的月度指南"
                    : i18n.language === 'zh-TW'
                    ? "每月星座運勢：更深入的月度指南"
                    : i18n.language === 'ja'
                    ? "マンスリー星占い：来月へのより深いガイド"
                    : "Monthly Horoscope: A Deeper Guide for the Month Ahead"
                }
                description={
                  i18n.language === 'zh-CN'
                    ? "全面的月度指导，分析主要的星象趋势。将您的行动与宇宙能量保持一致，实现最佳个人成长。"
                    : i18n.language === 'zh-TW'
                    ? "全面的月度指導，分析主要的星象趨勢。將您的行動與宇宙能量保持一致，實現最佳個人成長。"
                    : i18n.language === 'ja'
                    ? "主要な占星術のトレンドを分析する包括的な月間ガイダンス。宇宙のエネルギーと行動を調和させて、最適な個人的成長を実現しましょう。"
                    : "Comprehensive monthly guidance analyzing major astrological trends. Align your actions with cosmic energies for optimal personal growth."
                }
                icon={
                  <svg className={`w-10 h-10 ${isDark ? 'text-amber-500' : 'text-amber-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                    <path d="M8 14h.01"></path>
                    <path d="M12 14h.01"></path>
                    <path d="M16 14h.01"></path>
                    <path d="M8 18h.01"></path>
                    <path d="M12 18h.01"></path>
                    <path d="M16 18h.01"></path>
                  </svg>
                }
                onClick={() => navigate('/horoscope/monthly-horoscope')}
              />
              
              {/* 年度运势 - 使用HoroscopeFeatureCard组件 */}
              <HoroscopeFeatureCard
                title={
                  i18n.language === 'zh-CN' 
                    ? "2025年度星座运势：您的成功年度蓝图"
                    : i18n.language === 'zh-TW'
                    ? "2025年度星座運勢：您的成功年度藍圖"
                    : i18n.language === 'ja'
                    ? "2025年の年間星占い：成功への年間ブループリント"
                    : "2025 Yearly Horoscope: Your Annual Blueprint for Success"
                }
                description={
                  i18n.language === 'zh-CN'
                    ? "基于主要行星轨道和日食月食的2025年路线图。为长期规划和有目的的生活提供必要的指导。"
                    : i18n.language === 'zh-TW'
                    ? "基於主要行星軌道和日食月食的2025年路線圖。為長期規劃和有目的的生活提供必要的指導。"
                    : i18n.language === 'ja'
                    ? "主要な惑星の軌道と日食に基づく2025年のロードマップ。長期的な計画と目的のある生活のための重要なガイダンスを提供します。"
                    : "Your 2025 roadmap based on major planetary orbits and eclipses. Essential guidance for long-term planning and purposeful living."
                }
                icon={
                  <div className={`text-center ${isDark ? 'text-amber-500' : 'text-amber-600'} font-bold text-lg`}>
                    365
                  </div>
                }
                onClick={() => navigate('/horoscope/yearly-horoscope')}
              />
            </div>
          </div>

          {/* The Celestial Compass Difference 板块 */}
          <div className="mt-36 max-w-6xl mx-auto px-4 sm:px-6">
            <div className="text-center mb-16">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                isDark ? 'text-white' : 'text-purple-800'
              }`}>
                {t('horoscope.difference.title', 'The Celestial Compass Difference')}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
              <div className="flex items-center justify-center">
                <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t('horoscope.difference.tag1', 'Precision')}</span>
                <span className="mx-2 text-purple-500">✦</span>
                <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t('horoscope.difference.tag2', 'Personalization')}</span>
                <span className="mx-2 text-purple-500">✦</span>
                <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t('horoscope.difference.tag3', 'Purpose')}</span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-10 md:gap-12 lg:gap-12">
              {/* Block 1: Real Astrology, Not Just Sun Signs */}
              <div className="relative backdrop-blur-xl p-8 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div className="flex justify-center mb-6">
                  {/* Astrological chart icon */}
                  <div className={`w-20 h-20 rounded-full flex items-center justify-center ${isDark ? 'bg-indigo-900/50' : 'bg-indigo-100'}`}>
                    <svg className={`w-12 h-12 ${isDark ? 'text-indigo-300' : 'text-indigo-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                  </div>
                </div>
                <h3 className={`text-xl font-semibold text-center mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t('horoscope.difference.block1.title', 'Real Astrology, Not Just Sun Signs')}
                </h3>
                <p className="body-text dark:text-gray-300 text-gray-600">
                  {t('horoscope.difference.block1.content', "Generic magazine horoscopes only look at your sun sign—one piece of a vast cosmic puzzle. We synthesize data from your complete celestial profile, including your Moon (your emotional world) and your Ascendant (your interface with life). We analyze the critical angles and planetary aspects in the sky and how they interact with your unique chart. This is the difference between a map of the world and a real-time GPS route to your front door.")}
                </p>
              </div>
              
              {/* Block 2: AI-Enhanced Insights */}
              <div className="relative backdrop-blur-xl p-8 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div className="flex justify-center mb-6">
                  {/* AI + Astrology icon */}
                  <div className={`w-20 h-20 rounded-full flex items-center justify-center ${isDark ? 'bg-purple-900/50' : 'bg-purple-100'}`}>
                    <svg className={`w-12 h-12 ${isDark ? 'text-purple-300' : 'text-purple-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="16 18 22 12 16 6"></polyline>
                      <polyline points="8 6 2 12 8 18"></polyline>
                    </svg>
                  </div>
                </div>
                <h3 className={`text-xl font-semibold text-center mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t('horoscope.difference.block2.title', 'AI-Enhanced Insights')}
                </h3>
                <p className="body-text dark:text-gray-300 text-gray-600">
                  {t('horoscope.difference.block2.content', "Our proprietary AI, trained on millennia of astrological texts and cross-referenced with real-time planetary data from NASA's Jet Propulsion Laboratory, identifies subtle patterns and complex transit connections that human analysis can miss. It's the ancient art of the cosmos, amplified by the processing power of 21st-century technology, delivering insights with unparalleled speed and accuracy.")}
                </p>
              </div>
              
              {/* Block 3: Crafted by Expert Astrologers */}
              <div className="relative backdrop-blur-xl p-8 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div className="flex justify-center mb-6">
                  {/* Expert astrologer icon */}
                  <div className={`w-20 h-20 rounded-full flex items-center justify-center ${isDark ? 'bg-pink-900/50' : 'bg-pink-100'}`}>
                    <svg className={`w-12 h-12 ${isDark ? 'text-pink-300' : 'text-pink-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M17 11l-5-5-5 5"></path>
                      <path d="M17 18l-5-5-5 5"></path>
                    </svg>
                  </div>
                </div>
                <h3 className={`text-xl font-semibold text-center mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t('horoscope.difference.block3.title', 'Crafted by Expert Astrologers')}
                </h3>
                <p className="body-text dark:text-gray-300 text-gray-600">
                  {t('horoscope.difference.block3.content', "Technology provides the data, but wisdom provides the meaning. Our AI-generated insights are reviewed, refined, and interpreted by a team of real, human astrologers. We combine the precision of computational power with the nuance, empathy, and inspiring wisdom that only a human expert can provide. You get guidance that is not only accurate but also deeply resonant and actionable.")}
                </p>
              </div>
            </div>
          </div>

          {/* Your Horoscope is Just the Beginning 板块 */}
          <div className="mt-36 max-w-6xl mx-auto px-4 sm:px-6">
            <div className="text-center mb-16">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                isDark ? 'text-white' : 'text-purple-800'
              }`}>
                {t('horoscope.beginning.title', 'Your Horoscope is Just the Beginning')}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
            </div>
            
            <div className="flex flex-col md:flex-row gap-12 md:gap-16 items-center">
              {/* 左侧图片 */}
              <div className="md:w-1/2">
                <div className="rounded-xl overflow-hidden">
                  <CdnLazyImage 
                    src="/images-optimized/daily-fortune/step3-select-tarot.webp" 
                    alt={t('horoscope.beginning.image_alt', 'Multi-dimensional Astrology and Tarot Reading')} 
                    className="w-full h-auto object-cover"
                  />
                </div>
              </div>
              
              {/* 右侧文案 */}
              <div className="md:w-1/2">
                <h3 className={`text-xl font-semibold mb-6 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t('horoscope.beginning.subtitle', 'Tarot & Astrology: The Ultimate Cosmic Partnership')}
                </h3>
                <p className="body-text dark:text-gray-300 text-gray-600 mb-6">
                  {t('horoscope.beginning.description', 'Tarot cards and zodiac energies unite for powerful insights. Our AI merges ancient symbolism with precise astrology for personalized guidance across all life dimensions.')}
                </p>
                <ul className="space-y-8">
                  <li className="flex items-start">
                    <div className="mr-3 mt-1">
                      <div className="w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900/60 flex items-center justify-center">
                        <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M12 2v10l4.24 4.24"></path>
                          <circle cx="12" cy="12" r="10"></circle>
                        </svg>
                      </div>
                    </div>
                    <div>
                      <HoverableText 
                        as="h4" 
                        onClick={() => navigate('/daily-fortune')}
                        className="font-medium dark:text-purple-300 text-purple-700 text-lg mb-2"
                      >
                        {t('horoscope.beginning.daily.title', 'Daily Horoscope & Tarot')}
                      </HoverableText>
                      <p className="body-text dark:text-gray-300 text-gray-600">{t('horoscope.beginning.daily.description', 'Precise daily guidance blending tarot wisdom with your zodiac\'s planetary influences for actionable insights.')}</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-3 mt-1">
                      <div className="w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900/60 flex items-center justify-center">
                        <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                          <line x1="16" y1="2" x2="16" y2="6"></line>
                          <line x1="8" y1="2" x2="8" y2="6"></line>
                          <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                      </div>
                    </div>
                    <div>
                      <HoverableText 
                        as="h4" 
                        onClick={() => navigate('/spreads/monthly-fortune')}
                        className="font-medium dark:text-purple-300 text-purple-700 text-lg mb-2"
                      >
                        {t('horoscope.beginning.monthly.title', 'Monthly Cosmic Forecast')}
                      </HoverableText>
                      <p className="body-text dark:text-gray-300 text-gray-600">{t('horoscope.beginning.monthly.description', 'Monthly roadmap combining tarot revelations with lunar cycles and planetary transits to illuminate opportunities and challenges ahead.')}</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-3 mt-1">
                      <div className="w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900/60 flex items-center justify-center">
                        <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"></circle>
                          <path d="M12 6v6l4 2"></path>
                        </svg>
                      </div>
                    </div>
                    <div>
                      <HoverableText 
                        as="h4" 
                        onClick={() => navigate('/yearly-fortune')}
                        className="font-medium dark:text-purple-300 text-purple-700 text-lg mb-2"
                      >
                        {t('horoscope.beginning.yearly.title', 'Yearly Astrological Blueprint')}
                      </HoverableText>
                      <p className="body-text dark:text-gray-300 text-gray-600">{t('horoscope.beginning.yearly.description', 'Strategic yearly blueprint where major arcana cards reveal themes and planetary movements time key events for aligned decision-making.')}</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* 添加探索区域 */}
          <div className="spotlight-section py-24 md:py-32 mt-28">
            <div className="max-w-3xl mx-auto px-2 sm:px-4">
              <SpotlightCard
                className="custom-spotlight-card"
                spotlightColor="rgba(0, 229, 255, 0.2)"
              >
                <div className="p-6 sm:p-10 text-center">
                  <h3
                    className="text-2xl md:text-3xl font-semibold mb-4"
                    style={{
                      background: theme === 'light' 
                        ? "none" 
                        : "linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)",
                      WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                      WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                      color: theme === 'light' ? "#000" : "inherit"
                    }}
                  >
                    {t("home.explore_section.title", "探索塔罗牌阅读")}
                  </h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                  } text-lg md:text-xl mb-6 px-1`}>
                    {t("home.explore_section.description", "开始您的塔罗之旅，获取专属于您的塔罗牌阅读")}
                  </p>
                  <div className="flex justify-center">
                    <motion.button
                      onClick={handleStartReading}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-6 py-3 rounded-full"
                      style={{
                        background:
                          theme === 'light'
                            ? "linear-gradient(135deg, rgba(124, 58, 237, 0.7), rgba(168, 85, 247, 0.7))"
                            : "linear-gradient(135deg, rgba(124, 58, 237, 0.8), rgba(168, 85, 247, 0.8))",
                        boxShadow: theme === 'light' 
                          ? "0 0 20px rgba(168, 85, 247, 0.4)"
                          : "0 0 20px rgba(168, 85, 247, 0.5)",
                        color: 'white',
                      }}
                    >
                      {t("home.explore_section.button", "开始阅读")}
                    </motion.button>
                  </div>
                </div>
              </SpotlightCard>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default LoveCompatibility; 