import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { getFontClass } from '../utils/fontUtils';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  isInReadingFlow: boolean;
  onConfirmNavigation: (path: string) => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({ 
  isOpen, 
  onClose, 
  isInReadingFlow,
  onConfirmNavigation
}) => {
  const { t, i18n } = useTranslation();
  const { navigate } = useLanguageNavigate();
  
  // 子菜单展开状态
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({});
  
  // 博客分类
  const blogCategories = [
    { path: `/${i18n.language}/horoscope`, label: t('blog.categories.horoscope', '星座运势') },
    { path: `/${i18n.language}/zodiac-traits`, label: t('blog.categories.zodiac_traits', '星座特质') },
    { path: `/${i18n.language}/tarot-guide`, label: t('blog.tarot_guide.heading', '塔罗指南') },
    { path: `/${i18n.language}/general-divination`, label: t('blog.general_divination.heading', '大众占卜') }
  ];

  // 主菜单项
  const menuItems = [
    { path: '/home', label: t('nav.home'), hasSubmenu: false },
    { path: '/daily-fortune', label: t('nav.daily_fortune', '每日运势'), hasSubmenu: false },
    { path: '/yes-no-tarot', label: t('nav.yes_no_tarot', 'Yes/No 塔罗'), hasSubmenu: false },
    { 
      path: '/blog', 
      label: t('nav.blog', '博客'), 
      hasSubmenu: true,
      submenuKey: 'blog',
      submenuItems: blogCategories
    },
    { path: '/membership', label: t('nav.membership', '会员订阅'), hasSubmenu: false }
  ];
  
  // 切换子菜单展开状态
  const toggleSubmenu = (key: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };
  
  // 处理导航
  const handleNavigation = (path: string) => {
    if (isInReadingFlow) {
      onConfirmNavigation(path);
    } else {
      navigate(path);
      onClose();
    }
  };
  
  // 处理子菜单项点击
  const handleSubmenuItemClick = (path: string) => {
    if (isInReadingFlow) {
      onConfirmNavigation(path);
    } else {
      navigate(path);
      onClose();
    }
  };
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed left-0 right-0 top-14 bottom-0 bg-white/70 dark:bg-black/70 backdrop-blur-md shadow-lg z-[999]
                   border-t border-gray-200/30 dark:border-gray-800/30 overflow-y-auto overflow-x-hidden lg:hidden"
        >
          <div className="relative min-h-full">
            <div className="absolute inset-0 pointer-events-none" />
            
            <div className="relative py-1 divide-y divide-gray-200/30 dark:divide-gray-800/30">
              {menuItems.map((item) => (
                <div key={item.path} className="relative">
                  {item.hasSubmenu ? (
                    <>
                      {/* 带子菜单的菜单项 */}
                      <button
                        type="button"
                        className="flex items-center justify-between w-full px-8 py-5 text-lg font-medium text-left
                                 text-gray-800 dark:text-gray-200 transition-all duration-200"
                        onClick={() => toggleSubmenu(item.submenuKey!)}
                      >
                        <span className={`${i18n.language === 'en' ? 'tracking-wide' : ''} ${getFontClass(i18n.language)}`}>
                          {item.label}
                        </span>
                        <span className="ml-2 text-lg">
                          {expandedMenus[item.submenuKey!] ? '▴' : '▾'}
                        </span>
                      </button>
                      
                      {/* 子菜单内容 */}
                      <AnimatePresence>
                        {expandedMenus[item.submenuKey!] && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                            className="bg-gray-50/50 dark:bg-gray-900/50"
                          >
                            {item.submenuItems!.map((subItem) => (
                              <button
                                key={subItem.path}
                                type="button"
                                className="block w-full px-12 py-4 text-base text-left
                                       text-gray-700 dark:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50
                                       transition-all duration-200"
                                onClick={() => handleSubmenuItemClick(subItem.path)}
                              >
                                <span className={`${i18n.language === 'en' ? 'tracking-wide' : ''} ${getFontClass(i18n.language)}`}>
                                  {subItem.label}
                                </span>
                              </button>
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </>
                  ) : (
                    <button
                      type="button"
                      onClick={() => handleNavigation(item.path)}
                      className="block w-full px-8 py-5 text-lg font-medium text-left
                               transition-all duration-200
                               text-gray-800 dark:text-gray-200 hover:bg-gray-50/40 dark:hover:bg-gray-900/40"
                    >
                      <span className={`${i18n.language === 'en' ? 'tracking-wide' : ''} ${getFontClass(i18n.language)}`}>
                        {item.label}
                      </span>
                    </button>
                  )}
                </div>
              ))}
            </div>

            <div className="absolute -bottom-6 left-0 right-0 h-6 bg-gradient-to-b dark:from-black/10 from-white/10 to-transparent pointer-events-none" />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MobileMenu; 