import { useState, useEffect } from 'react';
import { User, getCurrentUser } from '../services/userService';
import axios from 'axios';

export const useUser = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const token = localStorage.getItem('token');
        // 如果没有token，直接返回null，不发送请求
        if (!token) {
          setUser(null);
          setLoading(false);
          return;
        }

        const userData = await getCurrentUser();
        setUser(userData);
      } catch (err) {
        // 如果是401错误，说明未登录或token过期，直接设置user为null
        if (axios.isAxiosError(err) && err.response?.status === 401) {
          localStorage.removeItem('token'); // 清除无效token
          setUser(null);
        } else {
          setError(err instanceof Error ? err.message : 'An error occurred');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, []);

  return { user, loading, error };
}; 