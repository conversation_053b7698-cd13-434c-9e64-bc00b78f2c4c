import React from 'react';
import { useTranslation } from 'react-i18next';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import SEO from '../components/SEO';

const PrivacyPolicy: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <>
      <SEO />

      <div className="min-h-screen flex flex-col relative">
        <LandingBackground />
        <div className="flex-grow relative z-10">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
            <div className="text-center mt-8 sm:mt-10">
              <h1 className="text-4xl font-bold dark:text-white text-gray-900 mb-8">
                {t('privacy_policy.title')}
              </h1>
            </div>

            <div className="space-y-6 dark:text-gray-300 text-gray-700">
              <p>
                {t('privacy_policy.intro')}
              </p>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.information_collection.title')}</h2>
                <p>
                  {t('privacy_policy.information_collection.content')}
                </p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.data_deletion.title')}</h2>
                <p>{t('privacy_policy.data_deletion.intro')}</p>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>{t('privacy_policy.data_deletion.steps.step1')}</li>
                  <li>{t('privacy_policy.data_deletion.steps.step2')}</li>
                  <li>{t('privacy_policy.data_deletion.steps.step3')}
                    <ul className="list-disc pl-6 mt-2">
                      <li>{t('privacy_policy.data_deletion.details.username')}</li>
                      <li>{t('privacy_policy.data_deletion.details.email')}</li>
                      <li>{t('privacy_policy.data_deletion.details.scope')}</li>
                    </ul>
                  </li>
                </ol>
                <div className="bg-gray-900/50 dark:bg-gray-900/50 bg-gray-100/80 p-4 rounded-lg mt-4">
                  <h3 className="font-semibold dark:text-white text-gray-900 mb-2">{t('privacy_policy.data_deletion.notes.title')}</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>{t('privacy_policy.data_deletion.notes.note1')}</li>
                    <li>{t('privacy_policy.data_deletion.notes.note2')}</li>
                    <li>{t('privacy_policy.data_deletion.notes.note3')}</li>
                  </ul>
                </div>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.information_use.title')}</h2>
                <p>
                  {t('privacy_policy.information_use.content')}
                </p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.information_protection.title')}</h2>
                <p>
                  {t('privacy_policy.information_protection.content')}
                </p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.cookies.title')}</h2>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>
                    <span className="dark:text-white text-gray-700">{t('privacy_policy.cookies.purpose')}</span>
                  </li>
                  <li>
                    <span className="dark:text-white text-gray-700">{t('privacy_policy.cookies.management')}</span>
                  </li>
                </ol>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.third_party_links.title')}</h2>
                <p>
                  {t('privacy_policy.third_party_links.content1')}
                </p>
                <p>
                  {t('privacy_policy.third_party_links.content2')}
                </p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.minors_privacy.title')}</h2>
                <p>
                  {t('privacy_policy.minors_privacy.content')}
                </p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.user_rights.title')}</h2>
                <p>
                  {t('privacy_policy.user_rights.content')}
                </p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.international_transfers.title')}</h2>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>
                    <span className="dark:text-white text-gray-700">{t('privacy_policy.international_transfers.scenarios')}</span>
                  </li>
                  <li>
                    <span className="dark:text-white text-gray-700">{t('privacy_policy.international_transfers.compliance')}</span>
                  </li>
                </ol>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.policy_updates.title')}</h2>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>
                    <span className="dark:text-white text-gray-700">{t('privacy_policy.policy_updates.reasons')}</span>
                  </li>
                  <li>
                    <span className="dark:text-white text-gray-700">{t('privacy_policy.policy_updates.notification')}</span>
                  </li>
                </ol>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('privacy_policy.contact_us.title')}</h2>
                <p>
                  {t('privacy_policy.contact_us.content')}
                </p>
              </section>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
};

export default PrivacyPolicy; 