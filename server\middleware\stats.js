const { Session } = require('../models/tarot');

const trackApiUsage = async (req, res, next) => {
  const startTime = Date.now();
  let llmUsage = null;
  let statsRecorded = false;
  
  // 保存原始的方法
  const originalEnd = res.end;
  const originalJson = res.json;
  const originalSend = res.send;

  // 创建一个统一的记录统计数据的函数
  const recordStats = async () => {
    if (statsRecorded) return;  // 如果已经记录过，直接返回
    statsRecorded = true;  // 设置标志为已记录
    
    try {
      const responseTime = Date.now() - startTime;
      const isLlmCall = req.originalUrl.includes('/api/reading') && req.method === 'POST';

      // 只处理 LLM 调用
      if (isLlmCall && llmUsage) {
        // 从请求体中获取会话 ID
        const sessionId = req.body.sessionId;
        if (!sessionId) {
          console.error('No session ID found in request body');
          return;
        }

        // 更新会话的 LLM 统计信息
        await Session.update(sessionId, {
          llmStats: {
            inputTokens: llmUsage.input_tokens,
            outputTokens: llmUsage.output_tokens,
            promptCacheHitTokens: llmUsage.prompt_cache_hit_tokens,
            promptCacheMissTokens: llmUsage.prompt_cache_miss_tokens,
            model: 'deepseek',
            responseTime
          }
        });
      }
    } catch (error) {
      console.error('Error recording stats:', error);
    }
  };

  // 重写 res.json 方法来捕获 LLM API 响应
  res.json = function(data) {
    // 如果是 LLM API 响应，保存 token 使用量
    if (data && data.usage) {
      llmUsage = {
        input_tokens: data.usage.prompt_tokens,
        output_tokens: data.usage.completion_tokens,
        prompt_cache_hit_tokens: data.usage.prompt_cache_hit_tokens || 0,
        prompt_cache_miss_tokens: data.usage.prompt_cache_miss_tokens || 0
      };
    }
    
    // 如果是 LLM 调用，记录统计数据
    const isLlmCall = req.originalUrl.includes('/api/reading') && req.method === 'POST';
    if (isLlmCall && llmUsage) {
      recordStats();
    }
    
    return originalJson.apply(this, arguments);
  };

  // 重写 res.send 方法来捕获响应
  res.send = function(data) {
    if (typeof data === 'string') {
      try {
        // 只有当字符串看起来像JSON时才尝试解析
        if (data.trim().startsWith('{') && data.trim().endsWith('}')) {
          const parsedData = JSON.parse(data);
          if (parsedData && parsedData.usage) {
            llmUsage = {
              input_tokens: parsedData.usage.prompt_tokens,
              output_tokens: parsedData.usage.completion_tokens,
              prompt_cache_hit_tokens: parsedData.usage.prompt_cache_hit_tokens || 0,
              prompt_cache_miss_tokens: parsedData.usage.prompt_cache_miss_tokens || 0
            };
          }
        }
      } catch (e) {
        // Ignore parsing errors for non-JSON strings
      }
    } else if (data && data.usage) {
      llmUsage = {
        input_tokens: data.usage.prompt_tokens,
        output_tokens: data.usage.completion_tokens,
        prompt_cache_hit_tokens: data.usage.prompt_cache_hit_tokens || 0,
        prompt_cache_miss_tokens: data.usage.prompt_cache_miss_tokens || 0
      };
    }
    
    // 如果是 LLM 调用，记录统计数据
    const isLlmCall = req.originalUrl.includes('/api/reading') && req.method === 'POST';
    if (isLlmCall && llmUsage) {
      recordStats();
    }
    
    return originalSend.apply(this, arguments);
  };

  // 重写 res.end 方法来捕获响应状态码
  res.end = async function(chunk, encoding) {
    try {
      // 检查是否是大模型 API 调用
      const isLlmCall = req.originalUrl.includes('/api/reading') && req.method === 'POST';
      
      // 如果是 LLM 调用，尝试从 chunk 中解析 token 使用量
      if (isLlmCall && !llmUsage && chunk) {
        try {
          // 首先检查chunk是否是有效的JSON字符串
          const chunkStr = chunk.toString();
          if (chunkStr && 
              typeof chunkStr === 'string' && 
              chunkStr.trim().startsWith('{') && 
              chunkStr.trim().endsWith('}')) {
            const data = JSON.parse(chunkStr);
            if (data && data.usage) {
              llmUsage = {
                input_tokens: data.usage.prompt_tokens,
                output_tokens: data.usage.completion_tokens,
                prompt_cache_hit_tokens: data.usage.prompt_cache_hit_tokens || 0,
                prompt_cache_miss_tokens: data.usage.prompt_cache_miss_tokens || 0
              };
            }
          }
        } catch (e) {
          console.error('Error parsing chunk:', e);
        }
      }

      // 记录 API 统计数据（如果还没有记录过）
      await recordStats();
    } catch (error) {
      console.error('Error tracking API usage:', error);
    }
    
    // 调用原始的 end 方法
    originalEnd.apply(res, arguments);
  };
  
  next();
};

module.exports = { trackApiUsage }; 