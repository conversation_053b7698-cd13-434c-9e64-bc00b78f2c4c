.feature-card {
  position: relative;
  border: 1px solid rgba(236, 72, 153, 0.3);
  box-shadow: 
    0 0 0 1px rgba(168, 85, 247, 0.2),
    0 0 15px rgba(168, 85, 247, 0.15),
    0 0 30px rgba(236, 72, 153, 0.15),
    inset 0 0 15px rgba(168, 85, 247, 0.1);
}

/* 深色主题特定样式 */
.dark .feature-card::before {
  content: '';
  position: absolute;
  inset: -1px;
  padding: 1px;
  background: linear-gradient(
    135deg,
    rgba(168, 85, 247, 0.5),
    rgba(236, 72, 153, 0.5)
  );
  -webkit-mask: 
    linear-gradient(#fff 0 0) content-box, 
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  border-radius: 1rem;
}

/* 浅色主题特定样式 */
:not(.dark) .feature-card::before {
  content: '';
  position: absolute;
  inset: -1px;
  padding: 1px;
  background: linear-gradient(
    135deg,
    rgba(168, 85, 247, 0.3),
    rgba(236, 72, 153, 0.3)
  );
  -webkit-mask: 
    linear-gradient(#fff 0 0) content-box, 
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  border-radius: 1rem;
}

.dark .feature-card::after {
  content: '';
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, 0.15), transparent 70%),
    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, 0.15), transparent 70%);
  pointer-events: none;
  border-radius: 1rem;
}

:not(.dark) .feature-card::after {
  content: '';
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, 0.05), transparent 50%),
    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, 0.05), transparent 50%);
  pointer-events: none;
  border-radius: 1rem;
}

/* 文本悬停效果 */
.hoverable-text {
  cursor: pointer;
  transition: all 0.3s ease;
}

.hoverable-text:hover {
  text-decoration: underline;
}

.dark .hoverable-text:hover {
  color: theme('colors.purple.400');
}

:not(.dark) .hoverable-text:hover {
  color: theme('colors.purple.600');
} 