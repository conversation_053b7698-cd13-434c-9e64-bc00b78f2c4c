import { motion, AnimatePresence } from 'framer-motion';
import { Message } from '../../types/history';
import { getReaderNameEn } from '../../utils/historyDetailUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import VoiceExpiredDialog from '../speech/VoiceExpiredDialog';
import { useUser } from '../../contexts/UserContext';
import CdnLazyImage from '../../components/CdnLazyImage';

// Toast组件用于显示页面内弹窗
const Toast = ({ message, onClose, getFontClass }: { 
  message: string; 
  onClose: () => void;
  getFontClass: () => string;
}) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const { t } = useTranslation();
  
  // 自动关闭计时器
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, 4000);
    
    return () => clearTimeout(timer);
  }, [onClose]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className={`z-50 px-10 py-7 rounded-2xl ${
        isDark 
          ? 'bg-gray-900/95 text-white border border-gray-700/50' 
          : 'bg-gray-50/95 text-gray-800 border border-gray-200/70'
      } backdrop-blur-lg shadow-lg min-w-[320px] max-w-sm`}
    >
      <div className="flex flex-col items-center text-center">
        <div className={`text-lg leading-relaxed font-normal mb-6 ${getFontClass()}`}>
          {message}
        </div>
        <button 
          onClick={onClose}
          className={`px-8 py-2.5 rounded-full ${
            isDark 
              ? 'bg-purple-500 hover:bg-purple-400 text-white' 
              : 'bg-purple-600 hover:bg-purple-700 text-white'
          } transition-all duration-300 text-sm font-medium ${getFontClass()}`}
        >
          {t('speech.toast.close', '我知道了')}
        </button>
      </div>
    </motion.div>
  );
};

interface MessageDisplayProps {
  messages: Message[];
  readerInfo: any;
  getFontClass: () => string;
  sessionId?: string;
  session?: any;
}

const MessageDisplay: React.FC<MessageDisplayProps> = ({ messages, readerInfo, getFontClass, sessionId, session }) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const { t, i18n } = useTranslation();
  const { user } = useUser(); // 获取用户信息
  const [playingBlockIndex, setPlayingBlockIndex] = useState<number | null>(null);
  const [loadingBlockIndex, setLoadingBlockIndex] = useState<number | null>(null);
  const [toast, setToast] = useState<{ message: string } | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const paragraphRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const [isVoiceExpiredDialogOpen, setIsVoiceExpiredDialogOpen] = useState(false);

  // 组件卸载时停止音频播放
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      setPlayingBlockIndex(null);
      setIsPaused(false);
      clearHighlights();
    };
  }, []);

  // 将消息分组为不同的板块
  const messageBlocks = (() => {
    const blocks: Message[][] = [];
    let currentBlock: Message[] = [];
    let lastMessageType = '';

    messages.forEach((message) => {
      // 如果当前消息是用户消息，并且上一条不是用户消息，则开始一个新板块
      if (message.type === 'user' && lastMessageType !== 'user') {
        if (currentBlock.length > 0) {
          blocks.push([...currentBlock]);
        }
        currentBlock = [message];
      } else {
        currentBlock.push(message);
      }
      lastMessageType = message.type;
    });

    // 添加最后一个板块
    if (currentBlock.length > 0) {
      blocks.push(currentBlock);
    }

    return blocks;
  })();

  // 获取板块类型，根据用户消息内容判断
  const getBlockType = (block: Message[]): string => {
    // 查找用户消息
    const userMessage = block.find(msg => msg.type === 'user');
    if (!userMessage) return 'base'; // 默认为基础解读
    
    const content = userMessage.content || '';
    
    // 获取当前语言下的深度解析关键词
    const deepAnalysisKeywords = [
      '深度解析', '深度分析', // 中文
      t('reading.deep_analysis.request'), // 翻译函数获取当前语言的深度解析请求文本
      t('reading.deep_analysis.waiting'), // 翻译函数获取当前语言的深度解析等待文本
      'deep analysis', 'in-depth analysis', // 英文
      '深度解析', '深入分析', // 繁体中文
      '詳細な分析', '深層分析' // 日文
    ];
    
    // 检查内容是否包含任意一个深度解析关键词
    if (deepAnalysisKeywords.some(keyword => content.includes(keyword))) {
      return 'deep_analysis';
    } else if (block === messageBlocks[0]) {
      // 第一个板块总是base
      return 'base';
    } else {
      // 默认为追问
      return 'followup';
    }
  };

  // 显示Toast消息
  const showToast = (message: string) => {
    setToast({ message });
  };

  // 关闭Toast
  const closeToast = () => {
    setToast(null);
  };

  // 依次播放音频序列
  const playAudioSequence = async (blockIndex: number, readerMessages: Message[]) => {
    try {
      // 确保播放状态被正确设置
      setPlayingBlockIndex(blockIndex);
      setIsPaused(false);
      
      // 使用本地变量跟踪播放状态，避免异步状态更新问题
      let isPlaying = true;
      
      // 清除所有高亮
      clearHighlights();
      
      // 获取当前语言
      const currentLanguage = i18n.language.startsWith('zh') ? 'zh' : i18n.language;
      
      // 播放逻辑 - 依次播放每个段落
      for (let i = 0; i < readerMessages.length && isPlaying; i++) {
        // 不再检查playingBlockIndex，而是使用本地变量
        const blockType = getBlockType(messageBlocks[blockIndex]);
        const languageSuffix = currentLanguage !== 'zh' ? `_${currentLanguage}` : '';
        const messageId = `${blockType}${languageSuffix}_para_${i}`;
        
        // 高亮当前播放的段落
        highlightParagraph(blockIndex, i);
        
        try {
          // 确保sessionId存在
          if (!sessionId) {
            throw new Error('会话ID缺失，无法播放语音');
          }
          
          const audioResponse = await axios.get(
            `${import.meta.env.VITE_API_URL}/api/tts/audio/${sessionId}/${messageId}`,
            {
              responseType: 'arraybuffer',
              headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
            }
          );
          
          
          // 创建音频元素并播放
          const blob = new Blob([audioResponse.data], { type: 'audio/mpeg' });
          const url = URL.createObjectURL(blob);
          const audio = new Audio(url);
          audioRef.current = audio;
          
          // 手动跟踪当前正在播放的段落索引
          
          // 等待音频播放完成
          await new Promise<void>((resolve) => {
            audio.onended = () => {
              URL.revokeObjectURL(url);
              audioRef.current = null;
              resolve();
            };
            audio.onerror = () => {
              URL.revokeObjectURL(url);
              audioRef.current = null;
              resolve();
            };
            
            // 添加播放按钮点击事件，手动播放音频
            audio.play().then(() => {
            }).catch(() => {
              URL.revokeObjectURL(url);
              audioRef.current = null;
              resolve();
            });
          });
        } catch (error) {          // 错误时继续尝试播放下一段
          continue;
        }
      }
      
      // 播放完成
      setPlayingBlockIndex(null);
      setIsPaused(false);
      audioRef.current = null;
      
      // 清除所有高亮
      clearHighlights();
    } catch (error) {
      setPlayingBlockIndex(null);
      setIsPaused(false);
      audioRef.current = null;
      
      // 清除所有高亮
      clearHighlights();
      
      showToast(t('speech.playback_error', '播放错误，请重试'));
    }
  };

  // 高亮段落
  const highlightParagraph = (blockIndex: number, messageIndex: number) => {
    // 先清除所有高亮
    clearHighlights();
    
    // 构建唯一的段落ID
    const paragraphId = `paragraph-${blockIndex}-${messageIndex}`;
    const paragraphElement = paragraphRefs.current.get(paragraphId);
    
    if (paragraphElement) {
      // 添加高亮效果
      paragraphElement.style.transition = 'box-shadow 0.3s ease, border 0.3s ease';
      paragraphElement.style.boxShadow = '0 0 0 2px rgba(139, 92, 246, 0.5)';
      paragraphElement.style.border = '1px solid rgba(139, 92, 246, 0.8)';
      
      // 滚动到可见区域
      paragraphElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };
  
  // 清除所有高亮
  const clearHighlights = () => {
    paragraphRefs.current.forEach((element) => {
      if (element) {
        element.style.boxShadow = '';
        element.style.border = '';
      }
    });
  };

  // 停止当前音频播放
  const stopPlayback = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
    setPlayingBlockIndex(null);
    setIsPaused(false);
    clearHighlights();
  };

  // 处理播放按钮点击
  const handlePlayBlock = async (blockIndex: number) => {
    // 如果正在播放，则暂停
    if (blockIndex === playingBlockIndex) {
      if (isPaused) {
        // 如果当前是暂停状态，则继续播放
        if (audioRef.current) {
          audioRef.current.play();
          setIsPaused(false);
        }
      } else {
        // 如果当前正在播放，则暂停
        if (audioRef.current) {
          audioRef.current.pause();
          setIsPaused(true);
        }
      }
      return;
    }

    // 如果有其他块在播放，先停止它
    if (playingBlockIndex !== null && playingBlockIndex !== blockIndex) {
      stopPlayback();
    }

    try {
      setLoadingBlockIndex(blockIndex);
      
      // 获取当前板块中所有reader消息的ID
      const block = messageBlocks[blockIndex];
      const readerMessages = block.filter(msg => msg.type === 'reader');
      
      // 获取当前语言
      const currentLanguage = i18n.language.startsWith('zh') ? 'zh' : i18n.language;

      // 确定当前板块类型
      const blockType = getBlockType(block);
      
      // 为每个消息生成唯一的messageId
      const messageIds = readerMessages.map((_, index) => {
        // 使用板块类型、语言和消息索引生成唯一的messageId
        const languageSuffix = currentLanguage !== 'zh' ? `_${currentLanguage}` : '';
        return `${blockType}${languageSuffix}_para_${index}`;
      });
      
      // 只有当sessionId存在时才发送请求
      if (sessionId) {
        // 检查所有音频文件是否存在
        const response = await axios.post(
          `${import.meta.env.VITE_API_URL}/api/tts/check-block`,
          {
            sessionId,
            messageIds
          },
          {
            headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
          }
        );

        setLoadingBlockIndex(null);
        
        if (response.data.success && response.data.allExist) {
          // 所有音频文件存在，开始播放
          // 注意：不要在这里设置playingBlockIndex，让playAudioSequence函数来设置
          await playAudioSequence(blockIndex, readerMessages);
        } else {
          // 音频文件不存在或已过期，显示VIP提示弹窗
          setIsVoiceExpiredDialogOpen(true);
        }
      } else {
        setLoadingBlockIndex(null);
        showToast(t('speech.session_missing', '会话ID缺失，无法播放语音'));
      }
    } catch (error) {
      setLoadingBlockIndex(null);
      setPlayingBlockIndex(null);
      setIsPaused(false);
      clearHighlights();
      showToast(t('speech.playback_error', '播放错误，请重试'));
    }
  };

  return (
    <>
      <AnimatePresence>
        {toast && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 flex items-center justify-center"
              onClick={closeToast}
            >
              {/* Toast容器 - 停止事件冒泡防止点击Toast自身时关闭 */}
              <div onClick={(e) => e.stopPropagation()}>
                <Toast message={toast.message} onClose={closeToast} getFontClass={getFontClass} />
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
      
      {/* 语音解析过期弹窗 */}
      <VoiceExpiredDialog 
        isOpen={isVoiceExpiredDialogOpen} 
        onCancel={() => setIsVoiceExpiredDialogOpen(false)}
        isVip={user?.vipStatus === 'active'} // 根据用户VIP状态设置弹窗类型
      />
      
      <div className="space-y-6">
        {messageBlocks.map((block, blockIndex) => (
          <div key={blockIndex} className="space-y-4">
            {block.map((message, index) => (
              <motion.div
                key={`${blockIndex}-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className={`max-w-4xl ${
                  message.type === 'user'
                    ? 'ml-auto'
                    : ''
                }`}
              >
                <div className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start items-start'}`}>
                  {message.type === 'reader' && (
                    <>
                      <CdnLazyImage
                        src={`/images-optimized/readers/${getReaderNameEn(readerInfo)}.webp`}
                        alt={getReaderNameEn(readerInfo)}
                        className={`w-10 h-10 rounded-lg ${isDark ? 'border border-purple-500/30' : 'border border-purple-500/50'} object-cover mr-4 mt-1`}
                        onError={(e) => {
                          if (readerInfo) {
                            (e.target as HTMLImageElement).src = '/images-optimized/readers/Molly.webp';
                          }
                        }}
                      />
                      <div 
                        className={`rounded-2xl px-5 py-4 ${isDark ? 'bg-gray-800/50 border border-gray-700/30' : 'bg-gray-300/50 border border-gray-400/30'} rounded-tl-none transition-all duration-300`}
                        ref={el => {
                          if (el) {
                            const paragraphKey = `paragraph-${blockIndex}-${block.filter(msg => msg.type === 'reader').findIndex((_, i) => i === index - block.findIndex(m => m.type === 'reader'))}`;
                            paragraphRefs.current.set(paragraphKey, el);
                          }
                        }}
                      >
                        <div className={`${isDark ? 'text-gray-200' : 'text-gray-800'} leading-relaxed whitespace-pre-wrap ${getFontClass()}`}>
                          {message.content}
                        </div>
                      </div>
                    </>
                  )}
                  {message.type === 'user' && (
                    <div className={`rounded-2xl px-5 py-4 ${
                      message.type === 'user'
                        ? 'bg-blue-600 border border-blue-600 rounded-tr-none'
                        : isDark ? 'bg-gray-800/50 border border-gray-700/30 rounded-tl-none' : 'bg-gray-300/50 border border-gray-400/30 rounded-tl-none'
                    }`}>
                      <div 
                        className={`${isDark ? 'text-gray-200' : 'text-gray-800'} leading-relaxed whitespace-pre-wrap ${getFontClass()}`}
                        style={{color: 'white'}}
                      >
                        {message.content}
                      </div>
                    </div>
                  )}
                  {message.type === 'user' && (
                    <div className="w-10 h-10 rounded-lg border border-blue-600 bg-blue-600 flex items-center justify-center ml-4 mt-1">
                      <span className="text-sm font-medium text-white" style={{color: 'white'}}>
                        {user?.username?.charAt(0).toUpperCase() || 'U'}
                      </span>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
            
            {/* 添加提示文本，显示在板块末尾 */}
            {block.some(msg => msg.type === 'reader') && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="w-full"
              >
                <div className="max-w-4xl flex items-start">
                  {readerInfo && (
                    <div className="w-10 h-10 flex-shrink-0 mr-4 mt-1"></div>
                  )}
                  <div className={`${
                    isDark 
                      ? 'text-red-500 font-semibold' 
                      : 'text-red-600 font-semibold'
                    } flex items-start`}>
                    <svg className={`h-5 w-5 mr-2 flex-shrink-0 mt-0.5 ${
                      isDark ? 'text-red-500' : 'text-red-600'
                    }`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path fillRule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 01.67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 11-.671-1.34l.041-.022zM12 9a.75.75 0 100-********* 0 000 1.5z" clipRule="evenodd" />
                    </svg>
                    <div className={`${getFontClass()}`}>
                      {t('reading.disclaimer', '本回答由 AI 生成，内容仅供参考，请仔细甄别')}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
            
            {/* 板块中包含reader消息时，添加"聆听上述解读"按钮 */}
            {block.some(msg => msg.type === 'reader') && sessionId && getBlockType(block) !== 'deep_analysis' && 
              !(session?.ethical_status === 'ethical_intervention' || session?.ethical_status === 'ethical_intervention_follow' || 
                session?.status === 'ethical_intervention' || session?.status === 'ethical_intervention_follow') && (
              <div className="flex justify-center mt-5 mb-3">
                {/* PC端显示分割线 */}
                <div className="hidden sm:flex w-full md:w-4/5 lg:w-3/4 items-center px-4">
                  <div className="flex-grow h-[1.5px] bg-gradient-to-r from-transparent via-blue-600/70 to-transparent"></div>
                  <motion.button
                    onClick={() => handlePlayBlock(blockIndex)}
                    disabled={loadingBlockIndex !== null || (playingBlockIndex !== null && playingBlockIndex !== blockIndex && !isPaused)}
                    className={`px-4 py-2 rounded-full ${isDark ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-600 hover:bg-blue-700'} text-white flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-blue-500/30 ${(loadingBlockIndex !== null || (playingBlockIndex !== null && playingBlockIndex !== blockIndex && !isPaused)) && 'opacity-70 cursor-not-allowed'}`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {blockIndex === loadingBlockIndex ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span className="text-xs sm:text-sm font-normal text-white" style={{color: 'white'}}>{t('speech.loading', '加载中...')}</span>
                      </>
                    ) : blockIndex === playingBlockIndex && !isPaused ? (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-xs sm:text-sm font-normal text-white" style={{color: 'white'}}>{t('speech.pause', '暂停播放')}</span>
                      </>
                    ) : blockIndex === playingBlockIndex && isPaused ? (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5V19L19 12L8 5Z" />
                        </svg>
                        <span className="text-xs sm:text-sm font-normal text-white" style={{color: 'white'}}>{t('speech.resume', '继续播放')}</span>
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                        </svg>
                        <span className="text-xs sm:text-sm font-normal text-white" style={{color: 'white'}}>{t('speech.listen', '聆听上述解读')}</span>
                      </>
                    )}
                  </motion.button>
                  <div className="flex-grow h-[1.5px] bg-gradient-to-l from-transparent via-blue-600/70 to-transparent"></div>
                </div>

                {/* 移动端显示iOS风格长按钮 */}
                <div className="sm:hidden w-[90%] px-4">
                  <motion.button
                    onClick={() => handlePlayBlock(blockIndex)}
                    disabled={loadingBlockIndex !== null || (playingBlockIndex !== null && playingBlockIndex !== blockIndex && !isPaused)}
                    className={`px-4 py-2 rounded-full ${isDark ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-600 hover:bg-blue-700'} text-white flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-blue-500/30 w-full ${(loadingBlockIndex !== null || (playingBlockIndex !== null && playingBlockIndex !== blockIndex && !isPaused)) && 'opacity-70 cursor-not-allowed'}`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {blockIndex === loadingBlockIndex ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span className="text-xs sm:text-sm font-normal text-white" style={{color: 'white'}}>{t('speech.loading', '加载中...')}</span>
                      </>
                    ) : blockIndex === playingBlockIndex && !isPaused ? (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-xs sm:text-sm font-normal text-white" style={{color: 'white'}}>{t('speech.pause', '暂停播放')}</span>
                      </>
                    ) : blockIndex === playingBlockIndex && isPaused ? (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5V19L19 12L8 5Z" />
                        </svg>
                        <span className="text-xs sm:text-sm font-normal text-white" style={{color: 'white'}}>{t('speech.resume', '继续播放')}</span>
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                        </svg>
                        <span className="text-xs sm:text-sm font-normal text-white" style={{color: 'white'}}>{t('speech.listen', '聆听上述解读')}</span>
                      </>
                    )}
                  </motion.button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </>
  );
};

export default MessageDisplay; 