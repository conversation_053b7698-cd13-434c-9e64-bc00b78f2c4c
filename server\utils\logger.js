/**
 * 日志工具模块
 */

/**
 * 获取当前格式化的时间戳
 * @returns {string} 格式化的时间戳
 */
function getTimestamp() {
  const now = new Date();
  return now.toISOString();
}

/**
 * 信息日志
 * @param {string} message 日志消息
 */
function info(message) {
  console.log(`[${getTimestamp()}] [INFO] ${message}`);
}

/**
 * 错误日志
 * @param {string} message 错误消息
 */
function error(message) {
  console.error(`[${getTimestamp()}] [ERROR] ${message}`);
}

/**
 * 警告日志
 * @param {string} message 警告消息
 */
function warn(message) {
  console.warn(`[${getTimestamp()}] [WARN] ${message}`);
}

/**
 * 调试日志
 * @param {string} message 调试消息
 */
function debug(message) {
  if (process.env.NODE_ENV === 'development') {
    console.debug(`[${getTimestamp()}] [DEBUG] ${message}`);
  }
}

module.exports = {
  info,
  error,
  warn,
  debug
}; 