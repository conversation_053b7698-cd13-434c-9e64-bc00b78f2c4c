import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { useTranslation } from 'react-i18next';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import axiosInstance from '../utils/axios';
import { useUser } from '../contexts/UserContext';
import { useTheme } from '../contexts/ThemeContext';
import { useTarotProgress } from '../hooks/useTarotProgress';
import TarotProgressCircle from '../components/TarotProgressCircle';
import SEO from '../components/SEO';
import VipPromptDialog from '../components/VipPromptDialog';
import axios from 'axios'; // 引入原始axios以使用isAxiosError
import { TAROT_CARDS } from '../data/tarot-cards';
import CdnLazyImage from '../components/CdnLazyImage';

// 根据中文名称获取卡牌的英文名称
const getCardEnglishName = (chineseName: string): string => {
  const card = TAROT_CARDS.find(c => c.name === chineseName);
  return card ? card.nameEn : chineseName;
};

// 获取翻译后的卡牌名称
const getTranslatedCardName = (cardName: string, t: (key: string, options?: any) => any) => {
  const majorArcanaMap: { [key: string]: number } = {
    '愚者': 0, '魔术师': 1, '女祭司': 2, '女皇': 3, '皇帝': 4,
    '教皇': 5, '恋人': 6, '战车': 7, '力量': 8, '隐士': 9,
    '命运之轮': 10, '正义': 11, '倒吊人': 12, '死神': 13,
    '节制': 14, '恶魔': 15, '塔': 16, '星星': 17, '月亮': 18,
    '太阳': 19, '审判': 20, '世界': 21
  };

  if (majorArcanaMap[cardName] !== undefined) {
    return t(`reading.cards.major.${majorArcanaMap[cardName]}`);
  }

  const suits = {
    '权杖': 'wands',
    '圣杯': 'cups',
    '宝剑': 'swords',
    '钱币': 'pentacles'
  };

  const ranks = {
    '王牌': 'ace',
    '二': '2',
    '三': '3',
    '四': '4',
    '五': '5',
    '六': '6',
    '七': '7',
    '八': '8',
    '九': '9',
    '十': '10',
    '侍者': 'page',
    '骑士': 'knight',
    '皇后': 'queen',
    '国王': 'king'
  };

  for (const [suitCh, suitEn] of Object.entries(suits)) {
    if (cardName.includes(suitCh)) {
      for (const [rankCh, rankEn] of Object.entries(ranks)) {
        if (cardName.includes(rankCh)) {
          return t(`reading.cards.${suitEn}.${rankEn}`);
        }
      }
    }
  }

  return cardName;
};

// 获取翻译后的牌阵位置
const getTranslatedPosition = (index: number, t: (key: string, options?: any) => any) => {
  const positionKeys = [
    'q1_career', 'q1_love', 'q1_health', 'q1_achievement',
    'q2_career', 'q2_love', 'q2_health', 'q2_achievement',
    'q3_career', 'q3_love', 'q3_health', 'q3_achievement',
    'q4_career', 'q4_love', 'q4_health', 'q4_achievement'
  ];
  return t(`yearly.spread.positions.${positionKeys[index]}`);
};

const YearlyFortuneResult: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { navigate } = useLanguageNavigate();
  const { user } = useUser();
  const { theme } = useTheme();
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  
  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };
  
  // 占卜数据
  const [loading, setLoading] = useState<boolean>(true);
  // 添加选中的塔罗牌状态
  const [selectedCards, setSelectedCards] = useState<any[]>([]);
  // 添加解读结果状态
  const [readingContent, setReadingContent] = useState<string>('');
  const [isGeneratingReading, setIsGeneratingReading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState<number>(0);
  const [isRetrying, setIsRetrying] = useState<boolean>(false);
  const maxRetries = 3; // 最大重试次数
  
  const {
    isGenerating,
    startProgress,
    markApiReceived,
    completeProgress,
    progress
  } = useTarotProgress({
    duration: 60000,
    onComplete: () => {
      if (!error) {
        setLoading(false);
      }
    }
  });
  
  // 检查用户权限
  const checkUserPermission = () => {
    // console.log('检查用户权限:', user);
    if (!user) {
      // console.log('用户未登录，跳转到登录页面');
      navigate('/login');
      return false;
    }
    
    // 严格检查VIP状态
    if (user.vipStatus !== 'active') {
      // console.log('用户不是VIP会员，显示VIP提示:', user);   
      setShowVipPrompt(true);
      
      // 延迟一小段时间后跳转到会员页面
      setTimeout(() => {
        navigate('/membership');
      }, 3000);
      
      return false;
    }
    
    // console.log('用户是VIP会员，允许访问年运占卜结果');
    return true;
  };
  
  // 从本地存储获取数据
  useEffect(() => {
    // console.log('组件加载，检查VIP权限');
    
    // 先检查VIP权限，这是必须的检查
    if (!checkUserPermission()) {
      return;
    }
    
    // 权限验证通过后，再获取数据
    const storedDataStr = localStorage.getItem('yearlyFortuneData');
    const storedCardsStr = localStorage.getItem('selectedCards');
    const storedSpreadStr = localStorage.getItem('selectedSpread');
    const storedResult = localStorage.getItem('yearlyFortuneResult');
    
    if (!storedDataStr || !storedCardsStr || !storedSpreadStr) {
      // 如果没有数据，返回到表单页面
      // console.log('缺少必要数据，返回表单页面');
      navigate('/yearly-fortune');
      return;
    }
    
    try {
      const data = JSON.parse(storedDataStr);
      // 验证用户数据的完整性
      if (!data || !data.birthDate || !data.gender || !data.occupation || !data.relationship) {
        // console.error('用户数据不完整:', data);
        throw new Error('用户数据不完整');
      }
      
      // 获取选中的塔罗牌数据和牌阵数据
      const cardsData = JSON.parse(storedCardsStr);
      const spreadData = JSON.parse(storedSpreadStr);
      
      // 设置状态
      setSelectedCards(cardsData);
      
      // 如果本地已有解读结果，直接使用
      if (storedResult) {
        setReadingContent(storedResult);
        setLoading(false);
        return;
      }
      
      // 只有在没有本地结果时才调用生成函数
      generateYearlyFortune(cardsData, spreadData, data);
      
    } catch (error) {
      // console.error('解析占卜数据失败:', error);
      navigate('/yearly-fortune');
    }
  }, [user, i18n.language]);
  
  // 修改生成年运占卜结果函数的签名
  const generateYearlyFortune = async (cardsData: any[], spread: any, userData: any) => {
    if (isGeneratingReading) return;
    
    setIsGeneratingReading(true);
    setError(null);
    startProgress();
    
    try {
      const userQuestion = t('yearly.spread.question', '请为我解读未来一年的运势');
      
      // 准备卡牌数据
      const formattedCards = cardsData.map(card => ({
        name: card.name,
        isReversed: card.orientation === 'reversed'
      }));

      // 计算年龄
      const userAge = new Date().getFullYear() - new Date(userData.birthDate).getFullYear();
      
      // 准备上下文信息
      const context = {
        userAge,
        userGender: userData.gender === 'male' ? '男' : '女',
        userOccupation: occupationMap[userData.occupation] || userData.occupation,
        userRelationship: relationshipMap[userData.relationship] || userData.relationship,
        readingType: 'yearly',
        readingPurpose: '年度运势预测',
        spreadName: spread.name,
        spreadDescription: spread.description,
        cardPositions: spread.positions,
        selectedCards: formattedCards.map((card, index) => ({
          ...card,
          position: spread.positions[index],
          positionKey: spread.positionKeys[index]
        }))
      };
      
      // 准备请求数据
      const requestData = {
        question: userQuestion,
        status: 'cards_selected',
        selectedCards: formattedCards,
        selectedSpread: spread,
        userData: userData,
        context
      };

      // 获取已存在的会话ID
      let sessionId = localStorage.getItem('sessionId') as string | null;
      
      // 只有在确实没有会话ID的情况下才尝试创建新会话
      if (!sessionId) {
        try {
          // 尝试获取现有会话
          const checkSessionResponse = await axiosInstance.get('/api/session/check', {
            params: { spreadId: 'yearly-fortune', status: 'cards_selected' }
          });
          
          if (checkSessionResponse.data.success && checkSessionResponse.data.session) {
            // 如果找到现有会话，使用它
            sessionId = checkSessionResponse.data.session.id;
            if (sessionId) {
              localStorage.setItem('sessionId', sessionId);
            }
          } else {
            // 如果没有找到现有会话，创建新会话
            const sessionResponse = await axiosInstance.post('/api/session', requestData);
            
            if (!sessionResponse.data.success) {
              throw new Error('创建会话失败');
            }
            
            sessionId = sessionResponse.data.session.id;
            if (sessionId) {
              localStorage.setItem('sessionId', sessionId);
            }
          }
        } catch (error) {
          // console.error('会话管理失败:', error);
          throw new Error('会话管理失败');
        }
      }

      // 此时如果仍然没有sessionId，抛出错误
      if (!sessionId) {
        throw new Error('无法获取会话ID');
      }
      
      // 构建API请求参数
      const fortuneRequestParams = {
        question: userQuestion,
        selectedCards: formattedCards,
        selectedSpread: spread,
        sessionId: sessionId!,
        language: i18n.language,
        userData: userData,
        context,
        // 添加调试信息到请求体
        debug: {
          requestInfo: {
            userBasicInfo: {
              age: userAge,
              gender: context.userGender,
              occupation: context.userOccupation,
              relationship: context.userRelationship
            },
            readingType: context.readingType,
            readingPurpose: context.readingPurpose,
            spreadInfo: {
              name: context.spreadName,
              description: context.spreadDescription
            },
            selectedCardsInfo: context.selectedCards.map((card: any) => ({
              cardName: card.name,
              isReversed: card.isReversed,
              position: card.position,
              positionKey: card.positionKey
            }))
          },
          timestamp: new Date().toISOString(),
          clientLanguage: i18n.language,
          sessionId: sessionId
        }
      };
      
      // 增加重试逻辑
      let fortuneResponse: any = null;
      let retryAttempts = 0;
      const maxRetryAttempts = 2; // 最大重试次数
      
      while (retryAttempts <= maxRetryAttempts) {
        try {
          // 设置重试状态
          if (retryAttempts > 0) {
            setIsRetrying(true);
            setRetryCount(retryAttempts);
          }
          
          // 调用运势预测API
          fortuneResponse = await axiosInstance.post('/api/fortune', fortuneRequestParams);
          
          // 成功获取响应，跳出循环
          break;
        } catch (err) {
          // 检查是否为超时或网关错误
          const isTimeoutError = axios.isAxiosError(err) && 
            (err.code === 'ECONNABORTED' || err.response?.status === 504 || err.response?.status === 502);
          
          retryAttempts++;
          
          if (retryAttempts > maxRetryAttempts || !isTimeoutError) {
            // 达到最大重试次数或非超时错误，向上抛出
            throw err;
          }
          
          // 等待一段时间后重试
          const retryDelay = 5000 * retryAttempts; // 随着重试次数增加等待时间
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          
          // console.log(`正在重试第 ${retryAttempts} 次...`);
        }
      }
      
      // 重置重试状态
      setIsRetrying(false);
      setRetryCount(0);
      
      // 检查是否成功获取响应
      if (!fortuneResponse) {
        throw new Error('获取运势预测失败，请稍后再试');
      }
      
      markApiReceived();
      
      // 解析API返回的结果
      let content = '';
      try {
        // 检查 reading 是否已经是对象
        const readingData = typeof fortuneResponse.data.reading === 'string' 
          ? JSON.parse(fortuneResponse.data.reading)
          : fortuneResponse.data.reading;
          
        // 检查结构并合并所有解读内容，不添加额外文本
        if (readingData.prologue && readingData.reading && readingData.summary) {
          content = readingData.reading;
        } else if (readingData.reading) {
          content = readingData.reading;
        } else {
          // 如果没有预期的结构，尝试使用整个对象
          content = JSON.stringify(readingData);
        }
        
        // 保存结果到本地存储
        localStorage.setItem('yearlyFortuneResult', content);
        
        // 设置解读内容
        setReadingContent(content);
        setLoading(false);
      } catch (error) {
        // console.error('解析API返回结果失败:', error);
        const errorMessage = '抱歉，解析年度运势时出现了错误。请稍后再试。';
        setError(errorMessage);
        localStorage.setItem('yearlyFortuneResult', errorMessage);
      }
    } catch (error) {
      // console.error('生成年度运势失败:', error);
      
      // 根据错误类型定制错误消息
      let errorMessage = '抱歉，生成年度运势时出现了错误。请稍后再试。';
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED' || error.response?.status === 504) {
          errorMessage = '抱歉，服务器处理请求超时。年度运势需要较长时间生成，请稍后再试。';
        } else if (error.response?.status === 502) {
          errorMessage = '抱歉，服务器暂时不可用。请稍后再试。';
        }
      }
      
      setError(errorMessage);
      localStorage.setItem('yearlyFortuneResult', errorMessage);
    } finally {
      setIsGeneratingReading(false);
      completeProgress();
    }
  };

  // 重试生成年度运势
  const retryGenerateYearlyFortune = () => {
    if (retryCount >= maxRetries) {
      setError('已达到最大重试次数，请稍后再试');
      return;
    }
    
    // 获取需要的数据
    const storedDataStr = localStorage.getItem('yearlyFortuneData');
    const storedCardsStr = localStorage.getItem('selectedCards');
    const storedSpreadStr = localStorage.getItem('selectedSpread');
    
    if (!storedDataStr || !storedCardsStr || !storedSpreadStr) {
      navigate('/yearly-fortune');
      return;
    }
    
    try {
      const data = JSON.parse(storedDataStr);
      const cardsData = JSON.parse(storedCardsStr);
      const spreadData = JSON.parse(storedSpreadStr);
      
      // 重置状态并重新生成
      setLoading(true);
      setError(null);
      generateYearlyFortune(cardsData, spreadData, data);
    } catch (error) {
      // console.error('重试时解析数据失败:', error);
      navigate('/yearly-fortune');
    }
  };

  // 渲染加载状态
  if (loading || isGenerating) {
    return (
      <div className="min-h-screen">
        <LandingBackground />
        <SEO />
        <div className="container mx-auto px-2 md:px-4 pt-0 sm:pt-1">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-4xl mx-auto mt-6 sm:mt-8 md:mt-10"
          >
            <h1 className={`text-center text-3xl sm:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500 pb-1 mb-2 ${getFontClass()}`}>
              {t('yearly.result_title', '年度运势解读')}
            </h1>
            
            <div className={`${theme === 'light' ? 'bg-[#F4F4F5] border-purple-300/20' : 'bg-black/30 border-white/10'} backdrop-blur-md rounded-2xl p-3 md:p-8 border`}>
              {/* 加载状态显示 */}
              <div className="mb-12">
                <div className="p-8 rounded-2xl">
                  <div className="flex flex-col items-center justify-center py-8">
                    <TarotProgressCircle progress={progress} isGenerating={isGenerating} />
                    {isRetrying && (
                      <div className="mt-4 text-center">
                        <p className={`${theme === 'light' ? 'text-purple-700' : 'text-purple-300'} text-sm`}>
                          服务器响应较慢，正在重试中 ({retryCount}/{maxRetries})...
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 分割线 */}
              <div className={`w-full h-px ${theme === 'light' ? 'bg-gray-300/30' : 'bg-white/10'} my-8`}></div>

              {/* 塔罗牌阵展示 */}
              {selectedCards.length > 0 && (
                <div className="p-1 md:p-4 rounded-2xl mb-8 md:mb-12">
                  <div className="grid grid-cols-3 md:grid-cols-4 gap-2 md:gap-8">
                    {selectedCards.map((card, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div className="relative w-full pb-[180%]">
                          <CdnLazyImage
                            src={`/images-optimized/tarot/${getCardEnglishName(card.name)}.webp`}
                            alt={getCardEnglishName(card.name)}
                            className={`absolute inset-0 w-full h-full object-contain ${card.orientation === 'reversed' ? 'rotate-180' : ''}`}
                          />
                        </div>
                        <div className="text-center mt-2 md:mt-4">
                          <div className={`${theme === 'light' ? 'text-gray-800' : 'text-white/90'} font-medium mb-0.5 md:mb-1 text-sm md:text-base ${getFontClass()}`}>{getTranslatedPosition(index, t)}</div>
                          <div className={`${theme === 'light' ? 'text-gray-600' : 'text-white/60'} text-xs md:text-sm ${getFontClass()}`}>
                            {getTranslatedCardName(card.name, t)} ({card.orientation === 'reversed' ? t('reading.result.reversed', '逆位') : t('reading.result.upright', '正位')})
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* 返回按钮 */}
              <div className="flex justify-center">
                              <button
                onClick={() => navigate('/yearly-fortune')}
                className="px-8 py-3 bg-purple-600 text-white font-medium rounded-full hover:bg-purple-700 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20"
                >
                  {t('yearly.continue_exploring', '继续探索塔罗奥秘')}
                </button>
              </div>
            </div>
          </motion.div>
        </div>
        <Footer />
      </div>
    );
  }

  // 渲染结果页面
  return (
    <div className="min-h-screen">
      <LandingBackground />
      <SEO />
      <div className="container mx-auto px-2 md:px-4 pt-0 sm:pt-1">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto mt-6 sm:mt-8 md:mt-10"
        >
          <h1 className={`text-center text-3xl sm:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500 pb-1 mb-2 ${getFontClass()}`}>
            {t('yearly.result_title', '年度运势解读')}
          </h1>
          
          <div className={`${theme === 'light' ? 'bg-[#F4F4F5] border-purple-300/20' : 'bg-black/30 border-white/10'} backdrop-blur-md rounded-2xl p-3 md:p-8 border`}>
            {/* API返回的解读内容 */}
            <div className="mb-8 md:mb-12">
              <div className="p-2 md:p-8 rounded-2xl">
                <div className={`max-w-3xl mx-auto text-base leading-relaxed whitespace-pre-line ${getFontClass()} ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'} max-h-[60vh] md:max-h-none overflow-y-auto scrollbar-thin scrollbar-thumb-purple-500/50 scrollbar-track-white/10`}>
                  {error || readingContent}
                </div>
                
                {/* 添加错误重试按钮 */}
                {error && (
                  <div className="flex justify-center mt-8">
                    <button
                      onClick={retryGenerateYearlyFortune}
                      className="px-6 py-2 bg-purple-600 text-white font-medium rounded-full hover:bg-purple-700 transition-all duration-300"
                    >
                      重新尝试生成
                    </button>
                  </div>
                )}
              </div>
            </div>
            
            {/* 分割线 */}
            <div className={`w-full h-px ${theme === 'light' ? 'bg-gray-300/30' : 'bg-white/10'} my-4 md:my-8`}></div>
            
            {/* 塔罗牌阵展示 */}
            {selectedCards.length > 0 && (
              <div className="p-1 md:p-4 rounded-2xl mb-8 md:mb-12">
                <div className="grid grid-cols-3 md:grid-cols-4 gap-2 md:gap-8">
                  {selectedCards.map((card, index) => (
                    <div key={index} className="flex flex-col items-center">
                      <div className="relative w-full pb-[180%]">
                        <CdnLazyImage
                          src={`/images-optimized/tarot/${getCardEnglishName(card.name)}.webp`}
                          alt={getCardEnglishName(card.name)}
                          className={`absolute inset-0 w-full h-full object-contain ${card.orientation === 'reversed' ? 'rotate-180' : ''}`}
                        />
                      </div>
                      <div className="text-center mt-2 md:mt-4">
                        <div className={`${theme === 'light' ? 'text-gray-800' : 'text-white/90'} font-medium mb-0.5 md:mb-1 text-sm md:text-base ${getFontClass()}`}>{getTranslatedPosition(index, t)}</div>
                        <div className={`${theme === 'light' ? 'text-gray-600' : 'text-white/60'} text-xs md:text-sm ${getFontClass()}`}>
                          {getTranslatedCardName(card.name, t)} ({card.orientation === 'reversed' ? t('reading.result.reversed', '逆位') : t('reading.result.upright', '正位')})
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* 返回按钮 */}
            <div className="flex justify-center">
              <button
                onClick={() => navigate('/yearly-fortune')}
                className="px-8 py-3 bg-purple-600 text-white font-medium rounded-full hover:bg-purple-700 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20"
              >
                {t('yearly.continue_exploring', '继续探索塔罗奥秘')}
              </button>
            </div>
          </div>
        </motion.div>
      </div>
      <Footer />
      
      {/* VIP提示弹窗 */}
      <VipPromptDialog
        isOpen={showVipPrompt}
        onCancel={() => setShowVipPrompt(false)}
      />
    </div>
  );
};

// 职业映射
const occupationMap: { [key: string]: string } = {
  student: '学生',
  employee: '上班族',
  freelancer: '自由职业者',
  entrepreneur: '创业者',
  other: '其他'
};

// 感情状态映射
const relationshipMap: { [key: string]: string } = {
  single: '单身',
  dating: '恋爱中',
  married: '已婚',
  divorced: '离异',
  other: '其他'
};

export default YearlyFortuneResult; 