// 测试是非塔罗牌解读结果解析逻辑
import { createInterface } from 'readline';

// 解析是非塔罗牌API返回内容的函数
const parseYesNoTarotReading = (content) => {
  if (!content) return { success: false, error: '输入内容为空' };

  try {
    // 尝试解析JSON响应
    let reading;
    try {
      reading = JSON.parse(content);
    } catch (parseError) {
      return {
        success: false,
        error: '解析JSON失败',
        format: 'text',
        content
      };
    }

    // 验证必要字段是否存在
    const requiredFields = ['answer', 'answer_color', 'confidence', 'card_meaning', 
                           'interpretation', 'advice', 'conclusion'];
    
    const missingFields = requiredFields.filter(field => !reading[field]);
    
    if (missingFields.length > 0) {
      return {
        success: false,
        error: `缺少必要字段: ${missingFields.join(', ')}`,
        format: 'partial',
        content: reading
      };
    }

    // 验证answer字段的值
    const validAnswers = ['是', '否', '也许', 'Yes', 'No', 'Maybe', 'はい', 'いいえ', 'たぶん'];
    if (!validAnswers.includes(reading.answer)) {
      return {
        success: false,
        error: `无效的answer值: ${reading.answer}`,
        format: 'invalid',
        content: reading
      };
    }

    // 验证颜色格式
    const colorRegex = /^#[0-9A-F]{6}$/i;
    if (!colorRegex.test(reading.answer_color)) {
      return {
        success: false,
        error: `无效的颜色格式: ${reading.answer_color}`,
        format: 'invalid',
        content: reading
      };
    }

    // 验证confidence字段
    const validConfidence = ['高', '中', '低', 'High', 'Medium', 'Low', '高い', '中程度', '低い'];
    if (!validConfidence.includes(reading.confidence)) {
      return {
        success: false,
        error: `无效的confidence值: ${reading.confidence}`,
        format: 'invalid',
        content: reading
      };
    }

    // 验证card_meaning字段
    if (!reading.card_meaning.general || !reading.card_meaning.position_influence) {
      return {
        success: false,
        error: 'card_meaning字段不完整',
        format: 'incomplete',
        content: reading
      };
    }

    // 验证interpretation字段
    if (!reading.interpretation.summary || 
        !reading.interpretation.detailed || 
        !reading.interpretation.symbolism) {
      return {
        success: false,
        error: 'interpretation字段不完整',
        format: 'incomplete',
        content: reading
      };
    }

    // 验证advice字段
    if (!reading.advice.practical || !reading.advice.spiritual) {
      return {
        success: false,
        error: 'advice字段不完整',
        format: 'incomplete',
        content: reading
      };
    }

    // 所有验证通过
    return {
      success: true,
      format: 'json',
      content: reading
    };
  } catch (error) {
    return {
      success: false,
      error: `解析过程出错: ${error.message}`,
      format: 'error'
    };
  }
};

// 创建命令行交互界面
const rl = createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('====== 是非塔罗牌解读结果解析测试工具 ======');
console.log('请输入API返回的JSON内容，输入完成后请输入 "END" 单独一行表示结束：');

let input = '';
rl.on('line', (line) => {
  if (line.trim() === 'END') {
    // 用户输入完成，开始解析
    console.log('\n开始解析内容...\n');
    
    // 解析内容
    const result = parseYesNoTarotReading(input);
    
    // 输出解析结果
    console.log('====== 解析结果 ======');
    console.log(`解析状态: ${result.success ? '成功' : '失败'}`);
    
    if (!result.success) {
      console.log(`错误信息: ${result.error}`);
      console.log(`格式类型: ${result.format}`);
    } else {
      console.log(`格式类型: ${result.format}`);
    }
    
    // 输出解析后的内容结构
    console.log('\n====== 内容结构 ======');
    if (result.content) {
      if (typeof result.content === 'object') {
        // 输出主要字段
        console.log('\n--- 主要字段 ---');
        console.log(`答案: ${result.content.answer}`);
        console.log(`答案颜色: ${result.content.answer_color}`);
        console.log(`确信度: ${result.content.confidence}`);
        
        // 输出卡牌含义
        console.log('\n--- 卡牌含义 ---');
        if (result.content.card_meaning) {
          console.log(`一般含义: ${result.content.card_meaning.general || '(无内容)'}`);
          console.log(`位置影响: ${result.content.card_meaning.position_influence || '(无内容)'}`);
        } else {
          console.log('(无卡牌含义数据)');
        }
        
        // 输出解读
        console.log('\n--- 解读 ---');
        if (result.content.interpretation) {
          console.log(`摘要: ${result.content.interpretation.summary || '(无内容)'}`);
          console.log(`详细: ${result.content.interpretation.detailed ? '(内容过长，已省略)' : '(无内容)'}`);
          console.log(`象征意义: ${result.content.interpretation.symbolism || '(无内容)'}`);
        } else {
          console.log('(无解读数据)');
        }
        
        // 输出建议
        console.log('\n--- 建议 ---');
        if (result.content.advice) {
          console.log(`实用建议: ${result.content.advice.practical || '(无内容)'}`);
          console.log(`精神建议: ${result.content.advice.spiritual || '(无内容)'}`);
        } else {
          console.log('(无建议数据)');
        }
        
        // 输出结论
        console.log('\n--- 结论 ---');
        console.log(result.content.conclusion || '(无结论)');
      } else {
        // 如果不是对象，直接输出内容
        console.log(result.content);
      }
    } else {
      console.log('(无内容)');
    }
    
    // 结束程序
    rl.close();
  } else {
    // 继续收集输入
    input += line + '\n';
  }
});

rl.on('close', () => {
  console.log('\n====== 解析测试完成 ======');
  process.exit(0);
}); 