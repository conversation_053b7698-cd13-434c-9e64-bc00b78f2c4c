#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础版TTS脚本(basic-tts)，使用免费的edge-tts库实现
"""

import os
import sys
import asyncio
import traceback

# 检查edge-tts是否安装
try:
    import edge_tts
except ImportError:
    print("错误: 未安装edge-tts库。请运行: pip install edge-tts")
    sys.exit(1)

async def generate_speech(text, voice, output_path):
    """
    使用Edge TTS生成语音文件
    
    Args:
        text (str): 需要转换为语音的文本
        voice (str): 语音角色
        output_path (str): 输出文件路径
    
    Returns:
        bool: 是否成功
    """
    try:
        # 创建输出目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        print(f"使用basic-tts(edge-tts)生成语音，声音: {voice}")
        print(f"文本片段: {text[:50]}...")
        
        # 使用edge_tts直接生成
        communicate = edge_tts.Communicate(text, voice=voice)
        
        # 流式处理并写入文件
        print(f"开始生成basic-tts音频，保存到: {output_path}")
        
        with open(output_path, "wb") as file:
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    file.write(chunk["data"])
        
        # 检查文件是否创建成功
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"basic-tts音频文件生成成功: {output_path}, 大小: {file_size} 字节")
            return True
        else:
            print(f"basic-tts音频文件生成失败，未找到文件: {output_path}")
            return False
            
    except Exception as e:
        print(f"生成basic-tts语音时出错: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主函数入口点"""
    if len(sys.argv) < 4:
        print("使用方法: python basic_tts.py <文本文件路径> <声音> <输出路径>", file=sys.stderr)
        sys.exit(1)
    
    text_file_path = sys.argv[1]
    voice = sys.argv[2]
    output_path = sys.argv[3]
    
    print(f"basic-tts参数: 文本文件={text_file_path}, 声音={voice}, 输出路径={output_path}")
    
    # 从文件中读取文本
    try:
        with open(text_file_path, 'r', encoding='utf-8') as f:
            text = f.read().strip()
        print(f"成功从文件读取文本，长度: {len(text)}")
        if len(text) > 50:
            print(f"文本内容片段: {text[:50]}...")
    except Exception as e:
        print(f"读取文本文件时出错: {e}")
        sys.exit(1)
    
    # 运行异步函数
    try:
        success = asyncio.run(generate_speech(text, voice, output_path))
        # 根据结果设置退出代码
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 