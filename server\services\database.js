const mysql = require('mysql2/promise');

const getDbConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  const host = isProduction ? process.env.MYSQL_HOST_INTERNAL : process.env.MYSQL_HOST_PUBLIC;
  
  return {
    host,
    port: process.env.MYSQL_PORT,
    user: process.env.MYSQL_USERNAME,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE,
    // 连接池配置
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    // 连接配置
    connectTimeout: 60000, // 增加连接超时时间到60秒
    // 字符集配置
    charset: 'utf8mb4',
    collation: 'utf8mb4_unicode_ci', // 确保使用支持表情符号的排序规则
    // 其他配置
    multipleStatements: true, // 允许多语句查询
    dateStrings: true // 将日期返回为字符串
  };
};

let pool;

const initializePool = async () => {
  if (!pool) {
    const config = getDbConfig();
    // console.log('Initializing connection pool with config:', {
    //   ...config,
    //   password: '********' // 隐藏密码
    // });

    try {
      pool = mysql.createPool(config);
      
      // 测试连接
      const connection = await pool.getConnection();
      // console.log('Database connection test successful');
      
      // 测试查询
      const [rows] = await connection.query('SELECT 1 as test');
      // console.log('Test query result:', rows);
      
      connection.release();
      
      // 设置错误处理
      pool.on('error', (err) => {
        console.error('Unexpected error on idle client', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
          // console.log('Database connection was closed. Attempting to reconnect...');
          pool = null;  // 清空连接池，允许重新创建
        }
      });

      return pool;
    } catch (error) {
      // console.error('Failed to create connection pool:', error);
      throw error;
    }
  }
  return pool;
};

const getConnection = async () => {
  try {
    if (!pool) {
      await initializePool();
    }
    return pool;
  } catch (error) {
    console.error('Failed to get database connection:', error);
    throw error;
  }
};

// 添加一个关闭连接池的方法
const closePool = async () => {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('Connection pool closed');
  }
};

module.exports = {
  getConnection,
  initializePool,
  closePool
}; 