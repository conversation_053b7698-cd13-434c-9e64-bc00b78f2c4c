import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { getFontClass } from '../../utils/tarotUtils';
import CdnLazyImage from '../../components/CdnLazyImage';

interface TarotCardDisplayProps {
  selectedCards: any[];
  translatedSpreadInfo: {
    name: string;
    description: string;
    positions: string[];
  } | null;
}

const TarotCardDisplay: React.FC<TarotCardDisplayProps> = ({ selectedCards, translatedSpreadInfo }) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <motion.div 
      className={`${
        isDark 
          ? 'bg-gradient-to-b from-gray-800/40 to-gray-900/40 border border-purple-500/10' 
          : 'bg-[#F4F4F5] border border-purple-300/30'
      } backdrop-blur-md rounded-xl sm:rounded-2xl overflow-hidden shadow-2xl my-8`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.3 }}
    >
      {/* Header with spread name */}
      <div className={`py-4 sm:py-6 px-4 sm:px-8 ${
        isDark ? 'border-b border-gray-700/30' : 'border-b border-gray-300/50'
      }`}>
        <h2 className={`main-title text-center ${getFontClass(i18n.language)} ${isDark ? '' : 'text-gray-800'}`}>
          {translatedSpreadInfo?.name || t('reading.spread.default_name')}
        </h2>
      </div>
      
      {/* Cards container */}
      <div className="py-8 sm:py-10 md:py-12 px-4 sm:px-6 md:px-8">
        <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 sm:gap-8 md:gap-10 justify-center place-items-center">
          {selectedCards.map((card, index) => {
            const position = translatedSpreadInfo?.positions?.[index] || t('reading.spread.position_number', { number: index + 1 });
            
            return (
              <motion.div 
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                className="flex flex-col items-center w-[110px] sm:w-[140px] md:w-[160px] lg:w-[180px] mb-4"
              >
                <div className="relative mb-3 w-full">
                  <div className="w-full aspect-[2/3] rounded-lg overflow-hidden border-0 transition-all duration-300 flex items-center justify-center bg-transparent"
                    style={{ 
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                    }}
                  >
                    <motion.div 
                      className="h-full w-full flex items-center justify-center"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                      style={{ 
                        transform: card.isReversed ? 'rotate(180deg)' : 'rotate(0deg)'
                      }}
                    >
                      <CdnLazyImage 
                        src={`/images-optimized/tarot/${card.nameEn.replace(/ /g, '_')}.webp`}
                        alt={card.nameEn.replace(/_/g, ' ')}
                        className="h-full w-full object-contain"
                        style={{ imageRendering: 'crisp-edges' }}
                        draggable="false"
                      />
                    </motion.div>
                  </div>
                </div>
                
                <div className="text-center mt-4 space-y-2">
                  <h3 className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-800'} ${getFontClass(i18n.language)}`}>
                    {(() => {
                      if (card.id <= 21) {
                        // Major Arcana
                        return t(`reading.cards.major.${card.id}`);
                      } else {
                        // Minor Arcana
                        const nameEn = card.nameEn.toLowerCase();
                        const suit = nameEn.includes('wands') ? 'wands' :
                                   nameEn.includes('cups') ? 'cups' :
                                   nameEn.includes('swords') ? 'swords' :
                                   'pentacles';
                        
                        const rankMap: { [key: string]: string } = {
                          'ace': 'ace',
                          'two': '2',
                          'three': '3',
                          'four': '4',
                          'five': '5',
                          'six': '6',
                          'seven': '7',
                          'eight': '8',
                          'nine': '9',
                          'ten': '10',
                          'page': 'page',
                          'knight': 'knight',
                          'queen': 'queen',
                          'king': 'king'
                        };
                        
                        const rank = Object.keys(rankMap).find(r => nameEn.startsWith(r));
                        return rank ? t(`reading.cards.${suit}.${rankMap[rank]}`) : card.name;
                      }
                    })()}
                  </h3>
                  <p className={`text-sm text-purple-500 ${getFontClass(i18n.language)}`}>
                    {card.isReversed ? t('reading.result.reversed') : t('reading.result.upright')}
                  </p>
                  <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'} h-10 flex items-start justify-center pt-1 ${getFontClass(i18n.language)}`}>{position}</p>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </motion.div>
  );
};

export default TarotCardDisplay; 