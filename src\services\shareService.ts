import axiosInstance from '../utils/axios';

export interface ShareSubmission {
  id: string;
  userId: string;
  shareUrl: string;
  imageUrl: string;
  platform: string;
  sessionId?: string;
  language?: string; // 添加language字段，用于显示用户使用的语言
  status: 'pending' | 'approved' | 'rejected';
  reviewerId?: string;
  reviewNote?: string;
  reviewedAt?: string;
  rewardGranted: boolean;
  rewardGrantedAt?: string;
  createdAt: string;
  updatedAt: string;
  email?: string; // 添加email字段，用于显示用户邮箱
  username?: string; // 添加username字段，用于显示用户名
}

/**
 * 提交分享信息
 * @param formData 包含分享截图和信息的表单数据
 * @returns 分享提交结果
 */
export const submitShare = async (formData: FormData): Promise<{
  message: string;
  submission: ShareSubmission;
  hasReceivedReward: boolean;
}> => {
  const response = await axiosInstance.post('/api/share-submission', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
  return response.data;
};

/**
 * 获取当前用户的分享提交列表
 * @returns 分享提交列表和奖励状态
 */
export const getUserShareSubmissions = async (): Promise<{
  submissions: ShareSubmission[];
  hasReceivedReward: boolean;
}> => {
  const response = await axiosInstance.get('/api/share-submission/user');
  
  // 处理后端返回的数据，将snake_case转换为camelCase
  const submissions = response.data.submissions.map((sub: any) => ({
    id: sub.id,
    userId: sub.user_id,
    shareUrl: sub.share_url,
    imageUrl: sub.image_url,
    platform: sub.platform,
    sessionId: sub.session_id,
    language: sub.language,
    status: sub.status,
    reviewerId: sub.reviewer_id,
    reviewNote: sub.review_note,
    reviewedAt: sub.reviewed_at,
    rewardGranted: sub.reward_granted === 1,
    rewardGrantedAt: sub.reward_granted_at,
    createdAt: sub.created_at,
    updatedAt: sub.updated_at
  }));
  
  return {
    submissions,
    hasReceivedReward: response.data.hasReceivedReward
  };
};

/**
 * 检查用户是否已获得分享奖励
 * @returns 是否已获得奖励
 */
export const checkRewardStatus = async (): Promise<boolean> => {
  try {
    const response = await axiosInstance.get('/api/share-submission/reward-status');
    return response.data.hasReceivedReward;
  } catch (error) {
    console.error('检查分享奖励状态失败:', error);
    return false;
  }
};

/**
 * 获取待审核的分享提交列表（管理员功能）
 * @param limit 限制数量
 * @param offset 偏移量
 * @returns 待审核的分享提交列表
 */
export const getPendingSubmissions = async (limit = 10, offset = 0): Promise<{
  submissions: ShareSubmission[];
  total: number; // 添加total字段，表示总记录数
}> => {
  const response = await axiosInstance.get(`/api/share-submission/pending?limit=${limit}&offset=${offset}`);
  
  // 处理后端返回的数据，将snake_case转换为camelCase
  const submissions = response.data.submissions.map((sub: any) => ({
    id: sub.id,
    userId: sub.user_id,
    shareUrl: sub.share_url,
    imageUrl: sub.image_url,
    platform: sub.platform,
    sessionId: sub.session_id,
    language: sub.language,
    status: sub.status,
    reviewerId: sub.reviewer_id,
    reviewNote: sub.review_note,
    reviewedAt: sub.reviewed_at,
    rewardGranted: sub.reward_granted === 1,
    rewardGrantedAt: sub.reward_granted_at,
    createdAt: sub.created_at,
    updatedAt: sub.updated_at,
    email: sub.email,
    username: sub.username
  }));
  
  return {
    submissions,
    total: response.data.total
  };
};

/**
 * 审核分享提交（管理员功能）
 * @param id 分享提交ID
 * @param status 审核状态：'approved' 或 'rejected'
 * @param reviewNote 审核备注
 * @returns 审核结果
 */
export const reviewSubmission = async (
  id: string,
  status: 'approved' | 'rejected',
  reviewNote: string = ''
): Promise<{ message: string; status: string }> => {
  const response = await axiosInstance.put(`/api/share-submission/${id}/review`, {
    status,
    reviewNote
  });
  return response.data;
}; 