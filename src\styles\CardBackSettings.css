.card-back-settings {
  padding: 2rem;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.preview-section {
  max-width: 1200px;
  margin: 0 auto 4rem;
  padding: 2rem;
  background: rgba(30, 30, 30, 0.5);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-card-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-card {
  width: 300px;
  height: 500px;
  position: relative;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.card-item {
  background: rgba(30, 30, 30, 0.5);
  border-radius: 0.75rem;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  aspect-ratio: 1/1.7;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.card-item:hover {
  border-color: rgba(168, 85, 247, 0.5);
}

.card-item.selected {
  border-color: #a855f7;
  box-shadow: 0 0 30px rgba(168, 85, 247, 0.3);
}

.card-item.vip {
  border-color: rgba(234, 179, 8, 0.3);
}

.card-item.vip:hover {
  border-color: rgba(234, 179, 8, 0.5);
}

.card-item.vip.selected {
  border-color: #eab308;
  box-shadow: 0 0 30px rgba(234, 179, 8, 0.3);
}

.card-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.8) 50%, transparent);
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 1rem;
}

.card-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 0;
}

.card-name {
  color: #fff;
  font-weight: 600;
  font-size: 1.125rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-description {
  color: #9ca3af;
  font-size: 0.875rem;
  line-height: 1.25;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.vip-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  background: linear-gradient(45deg, #eab308, #fbbf24);
  border-radius: 0.5rem;
  color: #000;
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(234, 179, 8, 0.3);
}

.free-badge {
  padding: 0.25rem 0.75rem;
  background: linear-gradient(45deg, #a855f7, #a855f7);
  border-radius: 0.5rem;
  color: #fff;
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.3);
}

.card-image {
  flex: 1;
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 0.75rem;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  border-radius: 0.5rem;
}

.card-image.loaded {
  opacity: 1;
}

.card-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(110deg, #2a2a2a 8%, #383838 18%, #2a2a2a 33%);
  background-size: 200% 100%;
  animation: shimmer 1.5s linear infinite;
  border-radius: 0.5rem;
  margin: 0.75rem;
  width: calc(100% - 1.5rem);
  height: calc(100% - 1.5rem);
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.selected-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 2rem;
  height: 2rem;
  background: #a855f7;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 0 10px rgba(168, 85, 247, 0.5);
}

.check-icon {
  width: 1.2rem;
  height: 1.2rem;
}

@media (max-width: 768px) {
  .card-back-settings {
    padding: 0.75rem;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.75rem;
    padding: 0 0.5rem;
  }

  .card-item {
    aspect-ratio: 1/1.7;
    min-height: 0;
    border-radius: 0.5rem;
  }

  .card-image {
    padding: 0.5rem;
    border-radius: 0.375rem;
  }

  .card-info {
    padding: 0.5rem;
  }

  .card-name {
    font-size: 0.875rem;
  }

  .card-description {
    font-size: 0.75rem;
  }

  .vip-badge,
  .free-badge {
    padding: 0.2rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
  }

  .selected-indicator {
    width: 1.5rem;
    height: 1.5rem;
    top: 0.5rem;
    right: 0.5rem;
  }

  .check-icon {
    width: 1rem;
    height: 1rem;
  }

  .card-placeholder {
    margin: 0.5rem;
    width: calc(100% - 1rem);
    height: calc(100% - 1rem);
    border-radius: 0.375rem;
  }
}

@media (max-width: 480px) {
  .card-back-settings {
    padding: 0.5rem;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.5rem;
    padding: 0 0.25rem;
  }

  .card-item {
    aspect-ratio: 1/1.7;
    border-radius: 0.375rem;
  }

  .card-image {
    padding: 0.375rem;
    border-radius: 0.25rem;
  }

  .card-info {
    padding: 0.375rem;
  }

  .card-placeholder {
    margin: 0.375rem;
    width: calc(100% - 0.75rem);
    height: calc(100% - 0.75rem);
    border-radius: 0.25rem;
  }
}

/* 浅色主题样式 */
:not(.dark) .card-item {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

:not(.dark) .card-item:hover {
  border-color: rgba(168, 85, 247, 0.5) !important;
  box-shadow: 0 4px 20px rgba(168, 85, 247, 0.15);
}

:not(.dark) .card-item.selected {
  border-color: #a855f7 !important;
  box-shadow: 0 0 30px rgba(168, 85, 247, 0.2);
}

:not(.dark) .card-item.vip {
  border-color: rgba(234, 179, 8, 0.3) !important;
}

:not(.dark) .card-item.vip:hover {
  border-color: rgba(234, 179, 8, 0.5) !important;
  box-shadow: 0 4px 20px rgba(234, 179, 8, 0.15);
}

:not(.dark) .card-item.vip.selected {
  border-color: #eab308 !important;
  box-shadow: 0 0 30px rgba(234, 179, 8, 0.2);
}

:not(.dark) .card-name {
  color: #333;
}

:not(.dark) .card-description {
  color: #666;
}

:not(.dark) .selected-indicator {
  background: #a855f7;
  color: white;
  box-shadow: 0 0 10px rgba(168, 85, 247, 0.3);
}

:not(.dark) .free-badge {
  background: linear-gradient(45deg, #a855f7, #c4b5fd);
  color: white;
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.2);
}

@media (max-width: 480px) {
  .card-back-settings {
    padding: 0.5rem;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.5rem;
  }

  .card-item {
    min-height: 120px;
  }

  .card-image {
    padding: 0.25rem;
  }

  .card-info {
    padding: 0.25rem 0.5rem;
  }

  .card-placeholder {
    margin: 0.25rem;
    width: calc(100% - 0.5rem);
    height: calc(100% - 0.5rem);
  }
}

/* 确保在浅色主题下VIP徽章的文本也是黑色 */
:not(.dark) .vip-badge {
  color: #000; /* 确保文本为黑色 */
  font-weight: 600;
}
