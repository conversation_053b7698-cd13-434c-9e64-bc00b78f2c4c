/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

const reset = '\x1b[0m';
const bright = '\x1b[1m';
const green = '\x1b[32m';

console.log(`${bright}${bright}===================================================`);
console.log(`${bright}${green}| 请查看目录下的 README.md 内容, 否则启动可能失败 |`);
console.log(`${bright}${reset}===================================================${reset}`);