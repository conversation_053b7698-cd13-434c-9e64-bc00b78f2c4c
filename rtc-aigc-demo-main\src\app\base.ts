/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

import { Message } from '@arco-design/web-react';
// 移除后端API依赖
import type { RequestResponse, ApiConfig, ApiNames, Apis } from './type';

type Headers = Record<string, string>;

// 移除后端API实现，改为模拟响应

/**
 * @brief 模拟API响应
 */
export const resultHandler = (res: RequestResponse) => {
  const { Result, ResponseMetadata } = res || {};
  return Result;
};

/**
 * @brief 生成模拟API
 */
export const generateAPIs = <T extends readonly ApiConfig[]>(apiConfigs: T) =>
  apiConfigs.reduce<Apis<T>>((store, cur) => {
    const { action } = cur;
    const actionKey = action as ApiNames<T>;
    
    // 所有API调用返回空结果
    store[actionKey] = async () => {
      return {};
    };
    
    return store;
  }, {} as Apis<T>);
