const express = require('express');
const router = express.Router();
const wechatPayRoutes = require('./wechatPayRoutes');
const alipayRoutes = require('./alipayRoutes');
const paypalRoutes = require('./paypalRoutes');
const { authenticateToken } = require('../../middleware/auth');

// 添加原始请求体解析中间件，用于支付回调
// 支付宝使用 urlencoded 格式，微信和PayPal使用 raw 格式
router.use('/alipay/notify', express.urlencoded({ extended: true }));
router.use('/wechat/notify', express.raw({ type: '*/*' }));
router.use('/paypal/notify', express.raw({ type: '*/*' }));

// 使用支付宝路由
router.use('/alipay', alipayRoutes);

// 使用微信支付路由
router.use('/wechat', wechatPayRoutes);

// 使用PayPal路由
router.use('/paypal', paypalRoutes);

// 通用创建订单路由（保持与旧API兼容）
router.post('/create-order', authenticateToken, async (req, res) => {
  const { paymentMethod = 'alipay', isMobile = false } = req.body;
  
  // 根据支付方式转发到对应的路由
  if (paymentMethod === 'wechat') {
    req.url = '/wechat/create-order';
  } else if (paymentMethod === 'paypal') {
    req.url = '/paypal/create-order';
  } else {
    // 支付宝支付，根据设备类型选择合适的接口
    req.url = isMobile ? '/alipay/create-wap-order' : '/alipay/create-order';
  }
  
  // 继续处理请求
  router.handle(req, res);
});

// 通用查询订单状态路由（保持与旧API兼容）
router.get('/order-status/:orderId', async (req, res) => {
  const { paymentMethod = 'alipay' } = req.query;
  
  // 根据支付方式转发到对应的路由
  if (paymentMethod === 'wechat') {
    req.url = `/wechat/order-status/${req.params.orderId}`;
  } else if (paymentMethod === 'paypal') {
    req.url = `/paypal/order-status/${req.params.orderId}`;
  } else {
    req.url = `/alipay/order-status/${req.params.orderId}`;
  }
  
  // 继续处理请求
  router.handle(req, res);
});

// 通用关闭订单路由（保持与旧API兼容）
router.post('/close-order', async (req, res) => {
  const { paymentMethod = 'alipay' } = req.body;
  
  // 根据支付方式转发到对应的路由
  if (paymentMethod === 'wechat') {
    req.url = '/wechat/close-order';
  } else if (paymentMethod === 'paypal') {
    req.url = '/paypal/capture-payment';
  } else {
    req.url = '/alipay/close-order';
  }
  
  // 继续处理请求
  router.handle(req, res);
});

module.exports = router;