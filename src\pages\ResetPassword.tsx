import React, { useState, useEffect } from 'react';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { motion } from 'framer-motion';
import { resetPasswordWithCode } from '../services/userService';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import { useTranslation } from 'react-i18next';
import SEO from '../components/SEO';

const CheckmarkAnimation: React.FC<{
  size?: number;
  color?: string;
  strokeWidth?: number;
  animationDuration?: number;
}> = ({
  size = 32,
  color = '#FFFFFF',
  strokeWidth = 4,
}) => {
  const circleVariants = {
    hidden: { opacity: 0, scale: 0.5 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  const checkVariants = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { duration: 0.5, ease: "easeOut" },
        opacity: { duration: 0.01 },
      },
    },
  };

  return (
    <div style={{ width: size, height: size }}>
      <motion.svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        initial="hidden"
        animate="visible"
      >
        <motion.circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          variants={circleVariants}
        />
        <motion.path
          d="M30,50 L45,65 L70,40"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
          variants={checkVariants}
        />
      </motion.svg>
    </div>
  );
};

const ResetPassword: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { navigate } = useLanguageNavigate();
  const email = localStorage.getItem('resetEmail') || '';
  const code = localStorage.getItem('resetCode') || '';

  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);

  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  useEffect(() => {
    if (!email || !code) {
      navigate('/forgot-password');
    }
  }, [email, code, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.password || !formData.confirmPassword) {
      setError(t('reset_password.errors.required_fields'));
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError(t('reset_password.errors.passwords_not_match'));
      return;
    }

    setIsLoading(true);
    setError('');
    setIsSuccess(false);

    try {
      await resetPasswordWithCode(email, code, formData.password, localStorage.getItem('i18nextLng') || 'zh-CN');
      // 延长加载圈的显示时间
      setTimeout(() => {
        setIsLoading(false);
        setIsSuccess(true);
        // 延长延迟时间，等待动画完成后再跳转
        setTimeout(() => {
          // 清除localStorage中的数据
          localStorage.removeItem('resetEmail');
          localStorage.removeItem('resetCode');
          // 由于useLanguageNavigate不支持传递state，使用localStorage传递消息
          localStorage.setItem('loginMessage', t('reset_password.success_message'));
          navigate('/login');
        }, 800);
      }, 500);
    } catch (error: any) {
      const errorMessage = error.response?.data?.message;
      if (errorMessage && typeof errorMessage === 'string' && errorMessage.startsWith('auth.')) {
        setError(t(errorMessage));
      } else {
        setError(errorMessage || t('reset_password.errors.reset_failed'));
      }
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col relative font-['Inter']">
      <SEO 
        title={t('reset_password.meta.title', '重置密码 - TarotQA')}
        description={t('reset_password.meta.description', '安全重置您的TarotQA账户密码，设置一个新的安全密码来保护您的账户。')}
        keywords={t('reset_password.meta.keywords', '重置密码,新密码,密码修改,账户安全,密码重置')}
      />
      <LandingBackground />
      <div className="flex-grow flex items-center justify-center relative z-10 mt-8 sm:mt-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-4 sm:mb-6">
            <h1 className={`main-title mb-1 ${getFontClass()}`}>{t('reset_password.title')}</h1>
            <p className="sub-title"></p>
          </div>
          <div className="flex items-center justify-center">
            <div className="w-full max-w-md mx-auto">
              <div className="login-card p-5 sm:p-8 rounded-2xl shadow-2xl relative overflow-hidden">
                <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/20 rounded-full blur-3xl"></div>

                <div className="relative">
                  {error && (
                    <div className="mb-3 sm:mb-4 p-2.5 sm:p-3 rounded-lg bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
                      <p className="text-red-400 text-xs sm:text-sm font-['Inter']">{error}</p>
                    </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-5 sm:space-y-6">
                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-gray-200 mb-1.5 sm:mb-2 font-['Inter']">
                        {t('reset_password.new_password.label')}
                      </label>
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 15V17M8.8 21H15.2C16.8802 21 17.7202 21 18.362 20.673C18.9265 20.3854 19.3854 19.9265 19.673 19.362C20 18.7202 20 17.8802 20 16.2V14.8C20 13.1198 20 12.2798 19.673 11.638C19.3854 11.0735 18.9265 10.6146 18.362 10.327C17.7202 10 16.8802 10 15.2 10H8.8C7.11984 10 6.27976 10 5.63803 10.327C5.07354 10.6146 4.6146 11.0735 4.32698 11.638C4 12.2798 4 13.1198 4 14.8V16.2C4 17.8802 4 18.7202 4.32698 19.362C4.6146 19.9265 5.07354 20.3854 5.63803 20.673C6.27976 21 7.11984 21 8.8 21ZM15 10V8C15 6.34315 13.6569 5 12 5C10.3431 5 9 6.34315 9 8V10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <input
                          type={showPassword ? "text" : "password"}
                          name="password"
                          value={formData.password}
                          onChange={handleChange}
                          className="input-field block w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-xl
                                  text-white text-[14px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                  placeholder-gray-400 backdrop-blur-sm pr-12 sm:pr-14 pl-10 font-['Inter']"
                          placeholder={t('reset_password.new_password.placeholder')}
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2
                                  text-gray-400 hover:text-purple-400 transition-colors
                                  focus:outline-none focus:text-purple-400 z-20
                                  p-2"
                          aria-label={showPassword ? t('reset_password.hide_password') : t('reset_password.show_password')}
                        >
                          {showPassword ? (
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                            </svg>
                          ) : (
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-gray-200 mb-1.5 sm:mb-2 font-['Inter']">
                        {t('reset_password.confirm_password.label')}
                      </label>
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 15V17M8.8 21H15.2C16.8802 21 17.7202 21 18.362 20.673C18.9265 20.3854 19.3854 19.9265 19.673 19.362C20 18.7202 20 17.8802 20 16.2V14.8C20 13.1198 20 12.2798 19.673 11.638C19.3854 11.0735 18.9265 10.6146 18.362 10.327C17.7202 10 16.8802 10 15.2 10H8.8C7.11984 10 6.27976 10 5.63803 10.327C5.07354 10.6146 4.6146 11.0735 4.32698 11.638C4 12.2798 4 13.1198 4 14.8V16.2C4 17.8802 4 18.7202 4.32698 19.362C4.6146 19.9265 5.07354 20.3854 5.63803 20.673C6.27976 21 7.11984 21 8.8 21ZM15 10V8C15 6.34315 13.6569 5 12 5C10.3431 5 9 6.34315 9 8V10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <input
                          type={showPassword ? "text" : "password"}
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleChange}
                          className="input-field block w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-xl
                                  text-white text-[14px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                  placeholder-gray-400 backdrop-blur-sm pr-12 sm:pr-14 pl-10 font-['Inter']"
                          placeholder={t('reset_password.confirm_password.placeholder')}
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2
                                  text-gray-400 hover:text-purple-400 transition-colors
                                  focus:outline-none focus:text-purple-400 z-20
                                  p-2"
                          aria-label={showPassword ? t('reset_password.hide_password') : t('reset_password.show_password')}
                        >
                          {showPassword ? (
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                            </svg>
                          ) : (
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>

                    <div className="pt-2">
                      <button
                        type="submit"
                        disabled={isLoading || isSuccess}
                        className="w-full relative bg-purple-600 rounded-xl 
                                hover:bg-purple-500 active:bg-purple-700 
                                transition-colors duration-200 disabled:opacity-50"
                      >
                        <div className="relative px-4 sm:px-6 h-[40px] sm:h-[48px] flex items-center justify-center">
                          <div className="flex items-center justify-center w-[28px] sm:w-[32px] h-[28px] sm:h-[32px]">
                            {isLoading ? (
                              <div className="w-5 sm:w-6 h-5 sm:h-6 border-2 border-white/20 border-t-white rounded-full animate-spin"></div>
                            ) : isSuccess ? (
                              <CheckmarkAnimation size={28} strokeWidth={4} />
                            ) : (
                              <span className="text-white text-sm sm:text-base font-medium whitespace-nowrap font-['Inter']">{t('reset_password.submit')}</span>
                            )}
                          </div>
                        </div>
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="relative z-10 mt-24 sm:mt-auto">
        <Footer />
      </div>
      <style>
        {`
          .input-field {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.2s ease-in-out;
            z-index: 1;
            position: relative;
          }
          
          /* 确保提交按钮在浅色主题下文字为白色 */
          body:not(.dark) .login-card button[type="submit"],
          html:not(.dark) .login-card button[type="submit"],
          :not(.dark) .login-card button[type="submit"] {
            color: white !important;
          }
          
          /* 确保按钮内的所有文字元素在浅色主题下都是白色 */
          body:not(.dark) .login-card button[type="submit"] *,
          html:not(.dark) .login-card button[type="submit"] *,
          :not(.dark) .login-card button[type="submit"] span,
          :not(.dark) .login-card button[type="submit"] div {
            color: white !important;
          }
          
          /* CSS变量 - 主题相关颜色 */
          :root, html, body {
            --input-bg-dark: rgba(255, 255, 255, 0.05);
            --input-border-dark: rgba(255, 255, 255, 0.1);
            --input-text-dark: rgba(255, 255, 255, 0.9);
            --input-bg-light: #F4F4F5;
            --input-border-light: rgba(168, 85, 247, 0.2);
            --input-text-light: #1F2937;
          }
          
          /* 彻底覆盖输入框样式 */
          body:not(.dark) .login-card input, 
          body:not(.dark) .login-card textarea,
          html:not(.dark) .login-card input, 
          html:not(.dark) .login-card textarea,
          div:not(.dark) .login-card input,
          div:not(.dark) .login-card textarea,
          :not(.dark) .input-field[type="email"],
          :not(.dark) .input-field[type="password"],
          :not(.dark) .input-field[type="text"] {
            background-color: #F4F4F5 !important;
            color: #1F2937 !important;
            border: 1px solid rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 输入框聚焦状态 */
          body:not(.dark) .login-card input:focus, 
          html:not(.dark) .login-card input:focus,
          :not(.dark) .input-field[type="email"]:focus,
          :not(.dark) .input-field[type="password"]:focus,
          :not(.dark) .input-field[type="text"]:focus {
            background-color: #FFFFFF !important;
            color: #1F2937 !important;
            border: 1px solid rgba(168, 85, 247, 0.5) !important;
            box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 占位符文本样式 */
          body:not(.dark) .login-card input::placeholder,
          html:not(.dark) .login-card input::placeholder,
          :not(.dark) .input-field::placeholder {
            color: #6B7280 !important;
          }
          
          /* 深色主题输入框样式 */
          body.dark .login-card input, 
          html.dark .login-card input,
          .dark .input-field[type="email"],
          .dark .input-field[type="password"],
          .dark .input-field[type="text"] {
            background-color: rgba(255, 255, 255, 0.05) !important;
            color: #FFFFFF !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
          }
          
          /* 深色主题输入框聚焦状态 */
          body.dark .login-card input:focus, 
          html.dark .login-card input:focus,
          .dark .input-field[type="email"]:focus,
          .dark .input-field[type="password"]:focus,
          .dark .input-field[type="text"]:focus {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: #FFFFFF !important;
            border: 1px solid rgba(168, 85, 247, 0.5) !important;
            box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
          }

          /* 修复自动填充状态下的输入框样式 */
          body:not(.dark) .login-card input:-webkit-autofill,
          body:not(.dark) .login-card input:-webkit-autofill:hover,
          body:not(.dark) .login-card input:-webkit-autofill:focus,
          body:not(.dark) .login-card input:-webkit-autofill:active,
          html:not(.dark) .login-card input:-webkit-autofill,
          html:not(.dark) .login-card input:-webkit-autofill:hover,
          html:not(.dark) .login-card input:-webkit-autofill:focus,
          html:not(.dark) .login-card input:-webkit-autofill:active,
          :not(.dark) .login-card input:-webkit-autofill,
          :not(.dark) .login-card input:-webkit-autofill:hover,
          :not(.dark) .login-card input:-webkit-autofill:focus,
          :not(.dark) .login-card input:-webkit-autofill:active {
            -webkit-text-fill-color: #1F2937 !important;
            -webkit-box-shadow: 0 0 0px 1000px #F4F4F5 inset !important;
            box-shadow: 0 0 0px 1000px #F4F4F5 inset !important;
            background-color: #F4F4F5 !important;
            border: 1px solid rgba(168, 85, 247, 0.2) !important;
            caret-color: #1F2937 !important;
            transition: background-color 5000s ease-in-out 0s !important;
          }
          
          /* 深色主题下自动填充状态的输入框样式 */
          body.dark .login-card input:-webkit-autofill,
          body.dark .login-card input:-webkit-autofill:hover,
          body.dark .login-card input:-webkit-autofill:focus,
          body.dark .login-card input:-webkit-autofill:active,
          html.dark .login-card input:-webkit-autofill,
          html.dark .login-card input:-webkit-autofill:hover,
          html.dark .login-card input:-webkit-autofill:focus,
          html.dark .login-card input:-webkit-autofill:active,
          .dark .login-card input:-webkit-autofill,
          .dark .login-card input:-webkit-autofill:hover,
          .dark .login-card input:-webkit-autofill:focus,
          .dark .login-card input:-webkit-autofill:active {
            -webkit-text-fill-color: #FFFFFF !important;
            -webkit-box-shadow: 0 0 0px 1000px rgba(255, 255, 255, 0.05) inset !important;
            box-shadow: 0 0 0px 1000px rgba(255, 255, 255, 0.05) inset !important;
            background-color: rgba(255, 255, 255, 0.05) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            caret-color: #FFFFFF !important;
            transition: background-color 5000s ease-in-out 0s !important;
          }
          
          .login-card {
            position: relative;
            background: rgba(13, 12, 15, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(236, 72, 153, 0.3);
            box-shadow: 
              0 0 0 1px rgba(168, 85, 247, 0.2),
              0 0 15px rgba(168, 85, 247, 0.15),
              0 0 30px rgba(236, 72, 153, 0.15),
              inset 0 0 15px rgba(168, 85, 247, 0.1);
          }
          /* 深色主题下的样式保持不变 */
          :root.dark .login-card, 
          html.dark .login-card, 
          .dark .login-card {
            background: rgba(13, 12, 15, 0.95);
          }
          /* 浅色主题下的卡片背景颜色 */
          :root:not(.dark) .login-card, 
          html:not(.dark) .login-card, 
          :not(.dark) .login-card {
            background: #F4F4F5;
          }
          .login-card::before {
            content: '';
            position: absolute;
            inset: -1px;
            padding: 1px;
            background: linear-gradient(
              135deg,
              rgba(168, 85, 247, 0.5),
              rgba(236, 72, 153, 0.5)
            );
            -webkit-mask: 
              linear-gradient(#fff 0 0) content-box, 
              linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
          }
        `}
      </style>
    </div>
  );
};

export default ResetPassword;
