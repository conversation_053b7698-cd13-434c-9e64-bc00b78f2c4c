from flask import Blueprint, jsonify, request, current_app
import asyncio
import base64
import traceback

# 创建蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api')

# 导入会话管理器和日志记录器
from .session_manager import SessionManager
from .error_handler import APIError, SessionError, AudioError
from .logger import api_logger, LogTimer

def get_session_manager():
    """获取会话管理器实例"""
    return SessionManager.get_instance()

@api_bp.route('/status', methods=['GET'])
def status():
    """服务状态检查接口"""
    api_logger.info("收到服务状态检查请求")
    return jsonify({
        'status': 'ok',
        'message': 'Service is running'
    })

# 语音聊天相关API
@api_bp.route('/voice/start', methods=['POST'])
async def start_voice_chat():
    """启动语音聊天会话"""
    try:
        data = request.json or {}
        user_id = data.get('user_id', 'anonymous')
        
        api_logger.info(f"用户 {user_id} 请求启动语音会话")
        
        # 创建会话
        with LogTimer(api_logger, "创建语音会话"):
            session_manager = get_session_manager()
            result = await session_manager.create_user_session(user_id)
        
        if 'error' in result:
            api_logger.error(f"启动语音会话失败: {result['error']}")
            raise SessionError(result['error'])
        
        api_logger.info(f"成功创建语音会话: {result['session_id']}")
        return jsonify({
            'status': 'ok',
            'message': '语音会话已创建',
            'data': {
                'session_id': result['session_id']
            }
        })
    except SessionError as e:
        # 让错误处理器处理会话错误
        raise
    except Exception as e:
        # 记录未预期的错误
        api_logger.error(f"启动语音会话时发生错误: {str(e)}")
        api_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'启动语音会话失败: {str(e)}'
        }), 500

@api_bp.route('/voice/stop', methods=['POST'])
async def stop_voice_chat():
    """停止语音聊天会话"""
    try:
        data = request.json or {}
        user_id = data.get('user_id', 'anonymous')
        session_id = data.get('session_id')
        
        if not session_id:
            raise SessionError('缺少session_id参数')
        
        api_logger.info(f"用户 {user_id} 请求停止语音会话 {session_id}")
        
        # 关闭会话
        with LogTimer(api_logger, f"关闭语音会话 {session_id}"):
            session_manager = get_session_manager()
            result = await session_manager.close_user_session(user_id, session_id)
        
        if 'error' in result:
            api_logger.error(f"停止语音会话失败: {result['error']}")
            raise SessionError(result['error'])
        
        api_logger.info(f"成功停止语音会话: {session_id}")
        return jsonify({
            'status': 'ok',
            'message': f'语音会话 {session_id} 已停止'
        })
    except SessionError as e:
        # 让错误处理器处理会话错误
        raise
    except Exception as e:
        # 记录未预期的错误
        api_logger.error(f"停止语音会话时发生错误: {str(e)}")
        api_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'停止语音会话失败: {str(e)}'
        }), 500

@api_bp.route('/voice/sessions', methods=['GET'])
def get_voice_sessions():
    """获取用户的语音会话列表"""
    try:
        user_id = request.args.get('user_id', 'anonymous')
        
        api_logger.info(f"获取用户 {user_id} 的语音会话列表")
        
        # 获取会话列表
        session_manager = get_session_manager()
        sessions = session_manager.get_user_sessions(user_id)
        
        api_logger.info(f"成功获取用户 {user_id} 的语音会话列表，共 {len(sessions)} 个会话")
        return jsonify({
            'status': 'ok',
            'data': {
                'sessions': sessions
            }
        })
    except Exception as e:
        # 记录未预期的错误
        api_logger.error(f"获取语音会话列表时发生错误: {str(e)}")
        api_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取语音会话列表失败: {str(e)}'
        }), 500

@api_bp.route('/voice/audio', methods=['POST'])
async def send_voice_audio():
    """发送语音数据"""
    try:
        data = request.json or {}
        session_id = data.get('session_id')
        audio_base64 = data.get('audio')
        
        if not session_id or not audio_base64:
            raise AudioError('缺少必要参数')
        
        api_logger.debug(f"接收到会话 {session_id} 的音频数据")
        
        try:
            # 解码Base64音频数据
            audio_data = base64.b64decode(audio_base64)
            
            # 发送到会话
            session_manager = get_session_manager()
            result = await session_manager.send_audio(session_id, audio_data)
            
            if 'error' in result:
                api_logger.error(f"发送音频数据失败: {result['error']}")
                raise AudioError(result['error'])
            
            api_logger.debug(f"成功发送会话 {session_id} 的音频数据")
            return jsonify({
                'status': 'ok',
                'message': '音频数据已发送'
            })
            
        except Exception as e:
            raise AudioError(f'处理音频数据出错: {str(e)}')
            
    except AudioError as e:
        # 让错误处理器处理音频错误
        raise
    except Exception as e:
        # 记录未预期的错误
        api_logger.error(f"发送音频数据时发生错误: {str(e)}")
        api_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'发送音频数据失败: {str(e)}'
        }), 500

@api_bp.route('/voice/data', methods=['GET'])
def get_voice_data():
    """获取语音会话数据（短轮询方式）"""
    try:
        session_id = request.args.get('session_id')
        
        if not session_id:
            raise SessionError('缺少session_id参数')
        
        api_logger.debug(f"获取会话 {session_id} 的数据")
        
        # 获取会话数据
        session_manager = get_session_manager()
        data = session_manager.get_session_data(session_id)
        
        if data is None:
            return jsonify({
                'status': 'ok',
                'data': None
            })
        
        api_logger.debug(f"成功获取会话 {session_id} 的数据: {data.get('type')}")
        return jsonify({
            'status': 'ok',
            'data': data
        })
    except SessionError as e:
        # 让错误处理器处理会话错误
        raise
    except Exception as e:
        # 记录未预期的错误
        api_logger.error(f"获取语音会话数据时发生错误: {str(e)}")
        api_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取语音会话数据失败: {str(e)}'
        }), 500 