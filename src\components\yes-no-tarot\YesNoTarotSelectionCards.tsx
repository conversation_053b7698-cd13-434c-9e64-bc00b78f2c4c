import React from 'react';
import { useTranslation } from 'react-i18next';
import { CdnLazyImage } from '../../components/CdnImageExport';

interface YesNoTarotSelectionCardsProps {
  onStartSingleCardReading: () => void;
  onStartThreeCardReading: () => void;
}

const YesNoTarotSelectionCards: React.FC<YesNoTarotSelectionCardsProps> = ({
  onStartSingleCardReading,
  onStartThreeCardReading
}) => {
  const { t } = useTranslation();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      {/* 单卡塔罗卡片 */}
      <div className="relative backdrop-blur-xl rounded-xl overflow-hidden dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col h-full">
        <div className="relative h-48 sm:h-56 md:h-64 overflow-hidden">
          <CdnLazyImage
            src="/images-optimized/yes-no-tarot/Yes-No-Single-Card-Spread.webp"
            alt="One Card Tarot Reading"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/60"></div>
          <div className="absolute bottom-0 left-0 right-0 p-4">
            <h3 className="text-xl font-bold text-white">{t('yes_no_tarot.reading_options.single_card.title')}</h3>
          </div>
        </div>
        <div className="p-5 flex flex-col flex-grow">
          <p className="body-text dark:text-gray-300 text-gray-600 mb-6 flex-grow">
            {t('yes_no_tarot.reading_options.single_card.description')}
          </p>
          <button
            onClick={onStartSingleCardReading}
            className="w-full px-4 py-3 bg-purple-600 hover:bg-purple-500 active:bg-purple-700 text-white font-medium rounded-xl transition-colors"
          >
            {t('yes_no_tarot.reading_options.single_card.button')}
          </button>
        </div>
      </div>
      
      {/* 三卡塔罗卡片 */}
      <div className="relative backdrop-blur-xl rounded-xl overflow-hidden dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col h-full">
        <div className="relative h-48 sm:h-56 md:h-64 overflow-hidden">
          <CdnLazyImage
            src="/images-optimized/yes-no-tarot/Yes-No-Three-Card-Spread.webp"
            alt="Three Card Tarot Reading"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/60"></div>
          <div className="absolute bottom-0 left-0 right-0 p-4">
            <h3 className="text-xl font-bold text-white">{t('yes_no_tarot.reading_options.three_card.title')}</h3>
          </div>
        </div>
        <div className="p-5 flex flex-col flex-grow">
          <p className="body-text dark:text-gray-300 text-gray-600 mb-6 flex-grow">
            {t('yes_no_tarot.reading_options.three_card.description')}
          </p>
          <button
            onClick={onStartThreeCardReading}
            className="w-full px-4 py-3 bg-purple-600 hover:bg-purple-500 active:bg-purple-700 text-white font-medium rounded-xl transition-colors"
          >
            {t('yes_no_tarot.reading_options.three_card.button')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default YesNoTarotSelectionCards; 