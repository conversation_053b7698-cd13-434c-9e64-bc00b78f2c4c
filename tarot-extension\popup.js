// Tarot card data
const tarotCards = [
  // <PERSON> (0-21)
  { id: 0, name: 'The Fool', nameEn: 'The_Fool', yesNo: 'maybe' },
  { id: 1, name: 'The Magician', nameEn: 'The_Magician', yesNo: 'yes' },
  { id: 2, name: 'The High Priestess', nameEn: 'The_High_Priestess', yesNo: 'maybe' },
  { id: 3, name: 'The Empress', nameEn: 'The_Empress', yesNo: 'yes' },
  { id: 4, name: 'The Emperor', nameEn: 'The_Emperor', yesNo: 'yes' },
  { id: 5, name: 'The Hierophant', nameEn: 'The_Hierophant', yesNo: 'yes' },
  { id: 6, name: 'The Lovers', nameEn: 'The_Lovers', yesNo: 'yes' },
  { id: 7, name: 'The Chariot', nameEn: 'The_Chariot', yesNo: 'yes' },
  { id: 8, name: 'Strength', nameEn: 'Strength', yesNo: 'yes' },
  { id: 9, name: 'The Hermit', nameEn: 'The_Hermit', yesNo: 'no' },
  { id: 10, name: 'Wheel of Fortune', nameEn: 'Wheel_of_Fortune', yesNo: 'maybe' },
  { id: 11, name: 'Justice', nameEn: 'Justice', yesNo: 'yes' },
  { id: 12, name: 'The Hanged Man', nameEn: 'The_Hanged_Man', yesNo: 'no' },
  { id: 13, name: 'Death', nameEn: 'Death', yesNo: 'no' },
  { id: 14, name: 'Temperance', nameEn: 'Temperance', yesNo: 'maybe' },
  { id: 15, name: 'The Devil', nameEn: 'The_Devil', yesNo: 'no' },
  { id: 16, name: 'The Tower', nameEn: 'The_Tower', yesNo: 'no' },
  { id: 17, name: 'The Star', nameEn: 'The_Star', yesNo: 'yes' },
  { id: 18, name: 'The Moon', nameEn: 'The_Moon', yesNo: 'no' },
  { id: 19, name: 'The Sun', nameEn: 'The_Sun', yesNo: 'yes' },
  { id: 20, name: 'Judgement', nameEn: 'Judgement', yesNo: 'yes' },
  { id: 21, name: 'The World', nameEn: 'The_World', yesNo: 'yes' },
];

// Get DOM elements
const drawButton = document.getElementById('draw-button');
const tarotCard = document.getElementById('tarot-card');
const cardImage = document.getElementById('card-image');
const cardName = document.getElementById('card-name');
const cardAnswer = document.getElementById('card-answer');

// Initial state
let isFlipped = false;
let selectedCard = null;
let isReversed = false;

// Card drawing function
function drawCard() {
  // Disable button to prevent multiple clicks
  drawButton.disabled = true;
  
  // Randomly select a card
  const randomIndex = Math.floor(Math.random() * tarotCards.length);
  selectedCard = tarotCards[randomIndex];
  
  // Randomly decide upright or reversed
  isReversed = Math.random() > 0.5;
  
  // Set card image
  cardImage.src = `https://cdn.tarotqa.com/images-optimized/tarot/${selectedCard.nameEn}.webp`;
  
  // Set card name
  cardName.textContent = `${selectedCard.name} (${isReversed ? 'Reversed' : 'Upright'})`;
  
  // Set Yes/No answer
  let answer;
  if (selectedCard.yesNo === 'maybe') {
    answer = 'Maybe';
    cardAnswer.className = 'card-answer';
  } else if (selectedCard.yesNo === 'yes') {
    answer = isReversed ? 'No' : 'Yes';
    cardAnswer.className = `card-answer ${isReversed ? 'no' : 'yes'}`;
  } else { // no
    answer = isReversed ? 'Yes' : 'No';
    cardAnswer.className = `card-answer ${isReversed ? 'yes' : 'no'}`;
  }
  cardAnswer.textContent = answer;
  
  // Flip the card
  setTimeout(() => {
    tarotCard.classList.add('flipped');
    isFlipped = true;
    
    // Reset button
    setTimeout(() => {
      drawButton.textContent = 'Draw Another Card';
      drawButton.disabled = false;
    }, 600);
  }, 500);
}

// Button click event
drawButton.addEventListener('click', () => {
  if (isFlipped) {
    // If card is already flipped, flip it back first
    tarotCard.classList.remove('flipped');
    isFlipped = false;
    
    // Then draw a new card
    setTimeout(drawCard, 600);
  } else {
    // Draw card directly
    drawCard();
  }
}); 