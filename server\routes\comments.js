const express = require('express');
const router = express.Router();
const Comment = require('../models/Comment');
const CommentLike = require('../models/CommentLike');
const { getConnection } = require('../services/database');
const { authenticateToken, optionalAuthenticateToken } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

// 异步错误处理中间件
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * @route POST /api/comments
 * @desc 创建评论
 * @access Private
 */
router.post(
  '/',
  authenticateToken,
  [
    body('sessionId').optional().isString().withMessage('会话ID无效'),
    body('rating').optional({ nullable: true }).custom((value) => {
      if (value === null || value === undefined) {
        return true; // 允许空值
      }
      if (!Number.isInteger(value) || value < 1 || value > 5) {
        throw new Error('评分必须是1-5之间的整数');
      }
      return true;
    }),
    body('content').trim().isLength({ min: 1, max: 1000 }).withMessage('评论内容长度必须在1-1000字符之间'),
    body('parentId').optional().isString().withMessage('父评论ID无效'),
    body('replyToUserId').optional().isString().withMessage('回复用户ID无效'),
    body('pageType').optional().isIn(['yes-no-tarot', 'tarot-result', 'daily-fortune']).withMessage('页面类型无效'),
    body('language').optional().isString().withMessage('语言参数无效')
  ],
  asyncHandler(async (req, res) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        success: false, 
        message: '输入数据无效',
        errors: errors.array() 
      });
    }

    try {
      const { sessionId, rating, content, parentId, replyToUserId, pageType, language } = req.body;

      // 如果是回复，验证父评论存在且不是回复（只允许一级回复）
      if (parentId) {
        const parentComment = await Comment.findById(parentId);
        
        if (!parentComment) {
          return res.status(400).json({
            success: false,
            message: '父评论不存在'
          });
        }
        
        if (parentComment.parentId) {
          return res.status(400).json({
            success: false,
            message: '只能回复顶级评论'
          });
        }
      }

      // 准备评论数据
      const commentData = {
        sessionId: sessionId || null,
        userId: req.user.userId,
        parentId: parentId || null,
        replyToUserId: replyToUserId || null,
        rating: parentId ? null : rating, // 回复不需要评分
        content: content.trim(),
        pageType: pageType || 'yes-no-tarot',
        language: language || 'zh-CN',
        metadata: {
          userAgent: req.headers['user-agent'],
          platform: req.headers['sec-ch-ua-platform'],
          ipAddress: req.ip,
          isReply: !!parentId
        }
      };

      // 创建评论
      const comment = await Comment.create(commentData);

      // 如果是回复，更新父评论的回复数量
      if (parentId) {
        await Comment.updateReplyCount(parentId);
      }
      
      res.status(201).json({
        success: true,
        message: '评论提交成功',
        data: comment
      });
    } catch (error) {
      console.log('创建评论失败:', error);
      
      // 处理特定错误
      if (error.message === '您已经对此内容评价过了') {
        return res.status(409).json({ 
          success: false, 
          message: error.message 
        });
      }
      
      res.status(500).json({ 
        success: false, 
        message: '创建评论失败，请稍后重试' 
      });
    }
  })
);

/**
 * @route GET /api/comments/all
 * @desc 获取所有评论列表
 * @access Public
 */
router.get(
  '/all',
  optionalAuthenticateToken,
  asyncHandler(async (req, res) => {
    try {
      const { limit = 1000, offset = 0 } = req.query;

      // 获取所有评论列表
      const comments = await Comment.findAll({
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      res.json({
        success: true,
        data: {
          comments,
          pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset),
            total: comments.length
          }
        }
      });
    } catch (error) {
      console.log('获取评论列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取评论列表失败'
      });
    }
  })
);

/**
 * @route GET /api/comments/:sessionId
 * @desc 获取指定会话的评论列表
 * @access Public
 */
router.get(
  '/:sessionId',
  optionalAuthenticateToken,
  asyncHandler(async (req, res) => {
    try {
      const { sessionId } = req.params;
      const { limit = 1000, offset = 0 } = req.query;

      // 获取评论列表
      const comments = await Comment.findBySessionId(sessionId, {
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      // 获取统计信息
      const stats = await Comment.getStats(sessionId);

      res.json({
        success: true,
        data: {
          comments,
          stats,
          pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset),
            total: stats.totalComments
          }
        }
      });
    } catch (error) {
      console.log('获取评论列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取评论列表失败'
      });
    }
  })
);

/**
 * @route GET /api/comments/:commentId/replies
 * @desc 获取评论的回复列表
 * @access Public
 */
router.get(
  '/:commentId/replies',
  optionalAuthenticateToken,
  asyncHandler(async (req, res) => {
    try {
      const { commentId } = req.params;
      const { limit = 100, offset = 0 } = req.query;

      const replies = await Comment.getReplies(commentId, {
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      res.json({
        success: true,
        data: {
          replies,
          pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset)
          }
        }
      });
    } catch (error) {
      console.log('获取回复列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取回复列表失败'
      });
    }
  })
);

/**
 * @route GET /api/comments/stats/:sessionId
 * @desc 获取指定会话的评论统计信息
 * @access Public
 */
router.get(
  '/stats/:sessionId',
  optionalAuthenticateToken,
  asyncHandler(async (req, res) => {
    try {
      const { sessionId } = req.params;

      const stats = await Comment.getStats(sessionId);

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.log('获取评论统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取评论统计失败'
      });
    }
  })
);

/**
 * @route DELETE /api/comments/:id
 * @desc 删除评论（只能删除自己的评论）
 * @access Private
 */
router.delete(
  '/:id',
  authenticateToken,
  asyncHandler(async (req, res) => {
    try {
      const { id } = req.params;
      const userId = req.user.userId;
      
      const success = await Comment.delete(id, userId);
      
      if (success) {
        res.json({
          success: true,
          message: '评论删除成功'
        });
      } else {
        res.status(404).json({
          success: false,
          message: '评论不存在或无权删除'
        });
      }
    } catch (error) {
      console.log('删除评论失败:', error);
      res.status(500).json({ 
        success: false, 
        message: '删除评论失败' 
      });
    }
  })
);

/**
 * @route GET /api/comments/user/history
 * @desc 获取用户的评论历史
 * @access Private
 */
router.get(
  '/user/history',
  authenticateToken,
  asyncHandler(async (req, res) => {
    try {
      const userId = req.user.userId;
      const { limit = 100, offset = 0 } = req.query;
      
      const comments = await Comment.findByUserId(userId, {
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
      
      res.json({
        success: true,
        data: {
          comments,
          pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset)
          }
        }
      });
    } catch (error) {
      console.log('获取用户评论历史失败:', error);
      res.status(500).json({ 
        success: false, 
        message: '获取评论历史失败' 
      });
    }
  })
);

/**
 * @route POST /api/comments/:commentId/like
 * @desc 添加评论点赞（支持连击）
 * @access Private
 */
router.post(
  '/:commentId/like',
  authenticateToken,
  asyncHandler(async (req, res) => {
    try {
      const { commentId } = req.params;
      const { count = 1 } = req.body;
      const userId = req.user.userId;

      // 添加点赞
      const result = await CommentLike.addLike(commentId, userId, count);

      res.json({
        success: true,
        data: {
          totalLikes: result.totalLikes,
          userLikes: result.userLikes
        }
      });
    } catch (error) {
      console.log('添加点赞失败:', error);
      res.status(500).json({
        success: false,
        message: '操作失败'
      });
    }
  })
);

/**
 * @route POST /api/comments/batch-likes
 * @desc 批量处理点赞操作
 * @access Private
 */
router.post(
  '/batch-likes',
  authenticateToken,
  asyncHandler(async (req, res) => {
    try {
      const { likes } = req.body;
      const userId = req.user.userId;

      if (!Array.isArray(likes) || likes.length === 0) {
        return res.json({
          success: true,
          message: '没有待处理的点赞操作'
        });
      }

      const results = [];

      // 处理每个点赞操作
      for (const like of likes) {
        try {
          const { commentId, count } = like;

          if (count > 0) {
            await CommentLike.like(commentId, userId, count);
          }

          results.push({ commentId, success: true });
        } catch (error) {
          console.log(`处理评论 ${like.commentId} 点赞失败:`, error);
          results.push({ commentId: like.commentId, success: false, error: error.message });
        }
      }

      res.json({
        success: true,
        message: `处理了 ${likes.length} 个点赞操作`,
        results
      });
    } catch (error) {
      console.log('批量处理点赞失败:', error);
      res.status(500).json({
        success: false,
        message: '批量处理点赞失败'
      });
    }
  })
);

/**
 * @route GET /api/comments/user-likes
 * @desc 获取用户对所有评论的点赞次数
 * @access Private
 */
router.get(
  '/user-likes',
  authenticateToken,
  asyncHandler(async (req, res) => {
    try {
      const userId = req.user.userId;

      // 查询用户点赞的所有评论及次数
      const pool = await getConnection();
      const [rows] = await pool.query(
        `SELECT id, user_like_counts FROM comments WHERE JSON_EXTRACT(user_like_counts, ?) IS NOT NULL`,
        [`$.${userId}`]
      );

      const userLikeCounts = {};
      rows.forEach(row => {
        try {
          const likeCounts = JSON.parse(row.user_like_counts || '{}');
          if (likeCounts[userId]) {
            userLikeCounts[row.id] = likeCounts[userId];
          }
        } catch (e) {
          // 忽略解析错误
        }
      });

      res.json({
        success: true,
        data: {
          userLikeCounts
        }
      });
    } catch (error) {
      console.log('获取用户点赞次数失败:', error);
      res.status(500).json({
        success: false,
        message: '获取用户点赞次数失败'
      });
    }
  })
);

module.exports = router;
