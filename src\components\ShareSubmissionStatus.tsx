import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { getUserShareSubmissions, ShareSubmission } from '../services/shareService';
import { Clock, CheckCircle, XCircle, Share2, AlertCircle, Image, MessageSquare, Upload } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { getCurrentPageUrl } from '../utils/shareUtils';
import ResubmitModal from './ResubmitModal';
import axiosInstance from '../utils/axios';

interface ShareSubmissionStatusProps {
  userId: string;
}

const ShareSubmissionStatus: React.FC<ShareSubmissionStatusProps> = ({ userId }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [submissions, setSubmissions] = useState<ShareSubmission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedSubmission, setSelectedSubmission] = useState<ShareSubmission | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showResubmitModal, setShowResubmitModal] = useState(false);
  const [resubmitSubmission, setResubmitSubmission] = useState<ShareSubmission | null>(null);
  const [originalReadingUrl, setOriginalReadingUrl] = useState<string>('');
  
  // 获取完整的图片URL - 统一使用/share-screenshots/路径
  const getFullImageUrl = (imageUrl: string): string => {
    if (!imageUrl) return '';

    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // 兼容旧的路径格式，转换为新格式
    if (imageUrl.startsWith('/uploads/share-screenshots/')) {
      const filename = imageUrl.replace('/uploads/share-screenshots/', '');
      return `${window.location.origin}/share-screenshots/${filename}`;
    }

    // 直接使用新的路径格式
    return `${window.location.origin}${imageUrl}`;
  };

  // 根据sessionId生成原始解读URL
  const getOriginalReadingUrl = async (sessionId: string): Promise<string> => {
    if (!sessionId) return '';

    try {
      // 查询会话信息以确定页面类型
      const response = await axiosInstance.get(`/api/session/types/${sessionId}`);
      const sessionData = response.data;

      // 使用shareUtils中的getCurrentPageUrl函数，强制使用生产环境URL
      const currentUrl = getCurrentPageUrl(false, undefined, true);
      const url = new URL(currentUrl);

      // 根据spread_id确定页面路径和参数
      if (sessionData.spreadId === 'yes-no-single-card') {
        // Yes/No单卡占卜页面
        url.pathname = '/yes-no-tarot/single-card/result';
        url.search = `?session=${sessionId}`;
      } else if (sessionData.spreadId === 'daily-fortune') {
        // 每日运势页面
        url.pathname = '/daily-fortune-result';
        url.search = `?sessionId=${sessionId}`;
      } else {
        // 默认塔罗解读页面
        url.pathname = '/tarot-result';
        url.search = `?session=${sessionId}`;
      }

      return url.toString();
    } catch (error) {
      console.error('获取会话信息失败:', error);

      // 如果查询失败，回退到默认的tarot-result页面
      const currentUrl = getCurrentPageUrl(false, undefined, true);
      const url = new URL(currentUrl);
      url.pathname = '/tarot-result';
      url.search = `?session=${sessionId}`;
      return url.toString();
    }
  };

  // 获取用户分享提交记录的函数
  const fetchSubmissions = async () => {
    if (!userId) return;

    setIsLoading(true);
    try {
      const result = await getUserShareSubmissions();

      // 确保数据格式正确
      const formattedSubmissions = result.submissions.map((sub: any) => {
        // 检查并转换字段名，确保与前端模型匹配
        return {
          id: sub.id,
          userId: sub.user_id || sub.userId,
          shareUrl: sub.share_url || sub.shareUrl || '',
          imageUrl: sub.image_url || sub.imageUrl || '',
          platform: sub.platform || '',
          sessionId: sub.session_id || sub.sessionId,
          status: sub.status || 'pending',
          reviewerId: sub.reviewer_id || sub.reviewerId,
          reviewNote: sub.review_note || sub.reviewNote || '',
          reviewedAt: sub.reviewed_at || sub.reviewedAt,
          rewardGranted: typeof sub.reward_granted !== 'undefined' ? sub.reward_granted : sub.rewardGranted,
          rewardGrantedAt: sub.reward_granted_at || sub.rewardGrantedAt,
          createdAt: sub.created_at || sub.createdAt,
          updatedAt: sub.updated_at || sub.updatedAt
        };
      });

      setSubmissions(formattedSubmissions);
    } catch (error) {
      console.error('获取分享提交记录失败:', error);
      setError(t('share.fetch_failed', '获取分享记录失败'));
    } finally {
      setIsLoading(false);
    }
  };

  // 获取用户分享提交记录
  useEffect(() => {
    fetchSubmissions();
  }, [userId, t]);
  
  // 格式化日期
  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '-';
      }
      return date.toLocaleString();
    } catch (error) {
      return '-';
    }
  };
  
  // 获取状态图标和颜色
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          icon: <Clock className="w-4 h-4 text-yellow-500" />,
          text: t('share.status.pending', '待审核'),
          color: 'text-yellow-500'
        };
      case 'approved':
        return {
          icon: <CheckCircle className="w-4 h-4 text-green-500" />,
          text: t('share.status.approved', '已通过'),
          color: 'text-green-500'
        };
      case 'rejected':
        return {
          icon: <XCircle className="w-4 h-4 text-red-500" />,
          text: t('share.status.rejected', '已拒绝'),
          color: 'text-red-500'
        };
      default:
        return {
          icon: <AlertCircle className="w-4 h-4 text-gray-500" />,
          text: t('share.status.unknown', '未知状态'),
          color: 'text-gray-500'
        };
    }
  };
  
  // 获取最新的分享提交
  const getLatestSubmission = () => {
    if (submissions.length === 0) return null;
    
    return submissions.reduce((latest, current) => {
      const latestDate = new Date(latest.createdAt).getTime();
      const currentDate = new Date(current.createdAt).getTime();
      return currentDate > latestDate ? current : latest;
    }, submissions[0]);
  };
  
  // 显示提交详情
  const showSubmissionDetail = async (submission: ShareSubmission) => {
    setSelectedSubmission(submission);
    setShowDetailModal(true);

    // 异步获取原始解读URL
    if (submission.sessionId) {
      try {
        const url = await getOriginalReadingUrl(submission.sessionId);
        setOriginalReadingUrl(url);
      } catch (error) {
        console.error('获取原始解读URL失败:', error);
        setOriginalReadingUrl('');
      }
    } else {
      setOriginalReadingUrl('');
    }
  };
  
  // 关闭详情模态框
  const closeDetailModal = () => {
    setShowDetailModal(false);
    setSelectedSubmission(null);
    setOriginalReadingUrl('');
  };

  // 处理重新上传
  const handleResubmit = (submission: ShareSubmission) => {
    setResubmitSubmission(submission);
    setShowResubmitModal(true);
    setShowDetailModal(false); // 关闭详情模态框
  };

  // 关闭重新上传模态框
  const closeResubmitModal = () => {
    setShowResubmitModal(false);
    setResubmitSubmission(null);
  };

  // 重新上传成功后的回调
  const onResubmitSuccess = () => {
    closeResubmitModal();
    // 重新获取提交记录
    fetchSubmissions();
  };
  
  // 渲染提交详情模态框
  const renderDetailModal = () => {
    if (!selectedSubmission || !showDetailModal) return null;
    
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
        <div className={`relative w-full max-w-sm sm:max-w-md p-4 rounded-lg shadow-lg ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
          <button 
            onClick={closeDetailModal}
            className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          
          <h3 className={`text-lg font-medium mb-3 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            {t('share.submission_detail', '分享详情')}
          </h3>
          
          <div className="space-y-3">
            {/* 状态 */}
            <div className="flex items-center">
              <div className={`mr-2 ${getStatusInfo(selectedSubmission.status).color}`}>
                {getStatusInfo(selectedSubmission.status).icon}
              </div>
              <span className={`font-medium ${getStatusInfo(selectedSubmission.status).color}`}>
                {getStatusInfo(selectedSubmission.status).text}
              </span>
            </div>
            
            {/* 平台 */}
            <div className="flex items-center">
              <Share2 className="w-4 h-4 mr-2 text-gray-500" />
              <span className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} text-sm`}>
                {selectedSubmission.platform}
              </span>
            </div>

            {/* 原始解读URL */}
            {selectedSubmission.sessionId && (
              <div className="flex items-start">
                <svg className="w-4 h-4 mr-2 text-gray-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                <div className="flex-1">
                  <p className={`mb-1 font-medium text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                    {t('share.original_reading_url', '原始解读链接')}:
                  </p>
                  {originalReadingUrl ? (
                    <a
                      href={originalReadingUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 break-all underline"
                    >
                      {originalReadingUrl}
                    </a>
                  ) : (
                    <span className="text-xs text-gray-500">
                      {t('share.loading_url', '正在加载链接...')}
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* 分享时间 */}
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2 text-gray-500" />
              <span className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} text-sm`}>
                {t('share.submission_time', '分享时间')}: {formatDate(selectedSubmission.createdAt)}
              </span>
            </div>
            
            {/* 截图 */}
            {selectedSubmission.imageUrl && (
              <div className="mt-3">
                <div className="flex items-center mb-2">
                  <Image className="w-4 h-4 mr-2 text-gray-500" />
                  <span className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} text-sm font-medium`}>
                    {t('share.reward.screenshot', '分享截图')}:
                  </span>
                </div>
                
                <div className="mt-1 flex justify-center">
                  {/* 直接显示图片，没有额外的容器 */}
                  <img 
                    src={getFullImageUrl(selectedSubmission.imageUrl)} 
                    alt={t('share.reward.screenshot', '分享截图')}
                    className="w-auto h-auto max-h-[200px] max-w-[90%] object-contain"
                    onError={(e) => {
                      // 图片加载失败时显示替代文本
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>';
                      target.alt = t('share.image_not_found', '图片未找到');
                      target.style.padding = '2rem';
                      target.style.backgroundColor = '#f3f4f6';
                    }}
                  />
                </div>
              </div>
            )}
            
            {/* 审核备注 */}
            {selectedSubmission.reviewNote && (
              <div className="flex items-start mt-3">
                <MessageSquare className="w-4 h-4 mr-2 text-gray-500 mt-0.5" />
                <div>
                  <p className={`mb-1 font-medium text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                    {t('share.review_note', '审核备注')}:
                  </p>
                  <p className="text-xs italic text-gray-500 bg-gray-100 dark:bg-gray-700 p-2 rounded-md">
                    "{selectedSubmission.reviewNote}"
                  </p>
                </div>
              </div>
            )}
            


            {/* 重新上传按钮 - 仅对审核失败的申请显示 */}
            {selectedSubmission.status === 'rejected' && (
              <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
                <button
                  onClick={() => handleResubmit(selectedSubmission)}
                  className={`w-full flex items-center justify-center px-4 py-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'bg-purple-600 hover:bg-purple-700 text-white'
                      : 'bg-purple-600 hover:bg-purple-700 text-white'
                  }`}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  {t('share.resubmit', '重新上传')}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };
  
  const latestSubmission = getLatestSubmission();
  
  // 如果没有分享记录且没有加载中，则不显示组件
  if (!isLoading && submissions.length === 0 && !error) {
    return (
      <div className={`py-3 px-4 text-center ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
        {t('share.no_submissions', '您还没有提交过分享')}
      </div>
    );
  }
  
  return (
    <div>
      {/* 将"已获得奖励"标签移到父组件中与标题同一行，所以这里不再需要 */}
      
      <div className="px-4 py-3">
        {isLoading ? (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        ) : error ? (
          <div className="text-center py-3 text-red-500">{error}</div>
        ) : (
          <>
            {latestSubmission && (
              <div className="mb-3">
                <div 
                  className="p-2 rounded-md border border-gray-200 dark:border-gray-700 cursor-pointer transition-colors hover:border-gray-300 dark:hover:border-gray-600"
                  onClick={() => showSubmissionDetail(latestSubmission)}
                >
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center">
                      <div className={`mr-2 ${getStatusInfo(latestSubmission.status).color}`}>
                        {getStatusInfo(latestSubmission.status).icon}
                      </div>
                      <div>
                        <div className={`text-sm font-medium ${getStatusInfo(latestSubmission.status).color}`}>
                          {getStatusInfo(latestSubmission.status).text}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {latestSubmission.platform}
                        </div>
                      </div>
                    </div>
                    
                    {/* 添加查看详情按钮 */}
                    <button 
                      className={`text-xs ${theme === 'dark' ? 'text-purple-400 hover:text-purple-300' : 'text-purple-600 hover:text-purple-700'}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        showSubmissionDetail(latestSubmission);
                      }}
                    >
                      {t('share.view_detail', '查看详情')}
                    </button>
                  </div>
                  
                  {/* 显示部分信息 */}
                  <div className="flex flex-wrap gap-2 mt-2">
                    {latestSubmission.imageUrl && (
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <Image className="w-3 h-3 mr-1" />
                        <span>{t('share.has_image', '有截图')}</span>
                        
                        {/* 添加小型预览图 */}
                        <div className="ml-2 w-5 h-5">
                          <img 
                            src={getFullImageUrl(latestSubmission.imageUrl)} 
                            alt="" 
                            className="w-full h-full object-cover rounded"
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display = 'none';
                            }}
                          />
                        </div>
                      </div>
                    )}
                    
                    {latestSubmission.reviewNote && (
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <MessageSquare className="w-3 h-3 mr-1" />
                        <span>{t('share.has_note', '有备注')}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            {/* 查看更多按钮 */}
            {submissions.length > 1 && (
              <div>
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className={`text-sm ${theme === 'dark' ? 'text-purple-400 hover:text-purple-300' : 'text-purple-600 hover:text-purple-700'} flex items-center`}
                >
                  {isExpanded ? t('share.show_less', '收起') : t('share.show_more', '查看更多')}
                  <svg
                    className={`ml-1 w-4 h-4 transform ${isExpanded ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                
                {/* 展开的历史记录 */}
                {isExpanded && (
                  <div className="mt-3 space-y-3">
                    {submissions.slice(1).map((submission) => (
                      <div 
                        key={submission.id} 
                        className="p-2 rounded-md border border-gray-200 dark:border-gray-700 cursor-pointer transition-colors hover:border-gray-300 dark:hover:border-gray-600"
                        onClick={() => showSubmissionDetail(submission)}
                      >
                        <div className="flex justify-between items-center mb-1">
                          <div className="flex items-center">
                            <div className={`mr-2 ${getStatusInfo(submission.status).color}`}>
                              {getStatusInfo(submission.status).icon}
                            </div>
                            <div className={`text-sm font-medium ${getStatusInfo(submission.status).color}`}>
                              {getStatusInfo(submission.status).text}
                            </div>
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(submission.createdAt)}
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {submission.platform}
                          </div>
                          <button 
                            className={`text-xs ${theme === 'dark' ? 'text-purple-400 hover:text-purple-300' : 'text-purple-600 hover:text-purple-700'}`}
                            onClick={(e) => {
                              e.stopPropagation();
                              showSubmissionDetail(submission);
                            }}
                          >
                            {t('share.view_detail', '查看详情')}
                          </button>
                        </div>
                        
                        {/* 添加图片和链接的简要预览 */}
                        {submission.imageUrl && (
                          <div className="flex flex-wrap gap-2 mt-2">
                            {submission.imageUrl && (
                              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                <Image className="w-3 h-3 mr-1" />
                                <span>{t('share.has_image', '有截图')}</span>
                                
                                {/* 添加小型预览图 */}
                                <div className="ml-2 w-5 h-5">
                                  <img 
                                    src={getFullImageUrl(submission.imageUrl)} 
                                    alt="" 
                                    className="w-full h-full object-cover rounded"
                                    onError={(e) => {
                                      (e.target as HTMLImageElement).style.display = 'none';
                                    }}
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            {/* 奖励状态 */}
            {latestSubmission && latestSubmission.status === 'approved' && latestSubmission.rewardGranted && (
              <div className="mt-3 p-2 rounded-md bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm">
                {t('share.reward_granted', '您的分享已获得14天VIP奖励')}
              </div>
            )}
            
            {/* 提示信息 */}
            {latestSubmission && latestSubmission.status === 'pending' && (
              <div className="mt-3 p-2 rounded-md bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm">
                {t('share.pending_note', '您的分享正在审核中，通过后将获得14天VIP奖励')}
              </div>
            )}

            {/* 审核失败提示信息 */}
            {latestSubmission && latestSubmission.status === 'rejected' && (
              <div className="mt-3 p-3 rounded-md bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-sm">
                <div className="flex items-start">
                  <XCircle className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="font-medium mb-1">
                      {t('share.rejected_note', '您的分享申请未通过审核')}
                    </p>
                    {latestSubmission.reviewNote && (
                      <p className="text-xs mb-2 opacity-90">
                        {t('share.rejection_reason', '拒绝原因')}: {latestSubmission.reviewNote}
                      </p>
                    )}
                    <button
                      onClick={() => handleResubmit(latestSubmission)}
                      className="inline-flex items-center text-xs font-medium text-red-700 dark:text-red-300 hover:text-red-900 dark:hover:text-red-100 transition-colors"
                    >
                      <Upload className="w-3 h-3 mr-1" />
                      {t('share.resubmit', '重新上传')}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
      
      {/* 详情模态框 */}
      {renderDetailModal()}

      {/* 重新上传模态框 */}
      {showResubmitModal && resubmitSubmission && (
        <ResubmitModal
          isOpen={showResubmitModal}
          onClose={closeResubmitModal}
          submission={resubmitSubmission}
          onSuccess={onResubmitSuccess}
        />
      )}
    </div>
  );
};

export default ShareSubmissionStatus; 