# 依赖目录
node_modules/
venv/
__pycache__/

# 构建输出
dist/
build/
*.pyc

# IDE相关
.idea/
.vscode/
*.swp
*.swo

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 系统文件
.DS_Store
Thumbs.db


# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

deploys/*.tar
*.tar

*.pack

# 忽略以deploy_开头的文件夹
deploy_*/
 
 
