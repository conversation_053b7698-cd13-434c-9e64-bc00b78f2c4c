import React, { useEffect, useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import LandingLink from '../components/LandingLink';
import { useTranslation } from 'react-i18next';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

const Landing: React.FC = () => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const { i18n } = useTranslation();
  const { navigate } = useLanguageNavigate();

  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    const injectScript = () => {
      const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDocument) return;

      // 注入点击事件处理脚本
      const script = iframeDocument.createElement('script');
      script.innerHTML = `
        document.addEventListener('click', function(e) {
          const link = e.target.closest('.destiny-link');
          if (link) {
            e.preventDefault();
            window.parent.postMessage('navigateToHome', window.location.origin);
          }
        });
      `;
      iframeDocument.body.appendChild(script);

      // 发送当前语言设置给 iframe
      iframe.contentWindow?.postMessage({
        type: 'LANGUAGE_CHANGE',
        language: i18n.language
      }, '*');
    };

    // 等待iframe加载完成
    iframe.addEventListener('load', injectScript);

    // 处理从iframe发来的消息
    const handleMessage = (event: MessageEvent) => {
      if (event.data === 'navigateToHome') {
        navigate('/home');
      }
    };

    window.addEventListener('message', handleMessage);
    
    return () => {
      iframe.removeEventListener('load', injectScript);
      window.removeEventListener('message', handleMessage);
    };
  }, [i18n.language, navigate]); // 添加 navigate 作为依赖

  return (
    <>
      <Helmet>
        <title>Tarot - AI</title>
        <meta name="description" content="用智慧与温度，陪伴每个人的心灵旅程" />
        <meta name="keywords" content="tarot,ai,塔罗牌,占卜" />
      </Helmet>
      <div className="fixed inset-0 bg-black">
        <iframe 
          ref={iframeRef}
          src="/images-optimized/landing/index.html" 
          style={{
            width: '100vw',
            height: '100vh',
            border: 'none',
            position: 'fixed',
            top: 0,
            left: 0
          }}
        />
      </div>
      <LandingLink />
    </>
  );
};

export default Landing; 