import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { horoscopeSigns, horoscopePeriods } from '../../data/horoscopes';
import { useTheme } from '../../contexts/ThemeContext';
import { getFontClass } from '../../utils/fontUtils';
import LandingBackground from '../../components/LandingBackground';
import Footer from '../../components/Footer';
import SEO from '../../components/SEO';
import CdnLazyImage from '../../components/CdnLazyImage';
import LanguageLink from '../../components/LanguageLink';

const HoroscopeSelection: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const navigate = useNavigate();
  const isDark = theme === 'dark';
  
  const [selectedPeriod, setSelectedPeriod] = useState<string>('daily');
  
  const handleSignClick = (signId: string) => {
    if (selectedPeriod === 'daily') {
      navigate(`/horoscope/${signId}/daily/today`);
    } else {
      navigate(`/horoscope/${signId}/${selectedPeriod}`);
    }
  };

  return (
    <div className={`min-h-screen flex flex-col relative antialiased ${isDark ? 'text-white' : 'text-gray-800'}`}>
      <SEO 
        title={t('horoscope.selection.title', '星座运势选择')}
        description={t('horoscope.selection.description', '查看12星座的运势预测，包括每日、每周和年度运势解读')}
      />
      <LandingBackground />
      
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
            <h1 className={`main-title mb-1 sm:mb-2 ${getFontClass(i18n.language)}`}>
              {t('horoscope.selection.heading', '星座运势')}
            </h1>
            <p className={`text-base sm:text-lg ${isDark ? 'text-purple-300' : 'text-purple-600'} italic ${getFontClass(i18n.language)}`}>
              {t('horoscope.selection.subheading', '选择您的星座，查看专属运势解读')}
            </p>
          </div>

          {/* 周期选择 */}
          <div className="py-1 px-4 mb-6">
            <div className="grid grid-cols-3 sm:flex sm:flex-nowrap justify-center gap-2 md:gap-3">
              {horoscopePeriods.map((period) => (
                <button
                  key={period.id}
                  className={`w-full sm:min-w-[140px] lg:w-[180px] h-[44px] flex items-center justify-center whitespace-normal text-center px-3 md:px-4 rounded-full text-sm font-medium transition-colors duration-200 font-['Inter'] ${
                    selectedPeriod === period.id
                    ? 'bg-purple-600 text-white'
                    : theme === 'dark'
                      ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                      : 'bg-gray-200 text-gray-300 hover:bg-gray-300'
                  }`}
                  onClick={() => setSelectedPeriod(period.id)}
                >
                  <span className="line-clamp-2" style={{color: selectedPeriod === period.id || theme === 'dark' ? 'white' : '#4B5563'}}>
                    {t(period.nameKey, period.defaultName)}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* 星座选择网格 */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 md:gap-6">
            {horoscopeSigns.map((sign) => (
              <motion.div
                key={sign.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleSignClick(sign.id)}
                className={`cursor-pointer rounded-xl overflow-hidden ${
                  isDark 
                    ? 'bg-gray-800/30 backdrop-blur-sm border border-white/10 hover:border-purple-500/50 hover:shadow-purple-500/20' 
                    : 'bg-white/80 backdrop-blur-sm border border-gray-200 hover:border-purple-400/50 hover:shadow-purple-300/20'
                } transition-all duration-300 flex flex-col items-center p-4`}
              >
                <div className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 mb-3 relative">
                  <CdnLazyImage 
                    src={sign.iconPath} 
                    alt={t(sign.nameKey, sign.defaultName)} 
                    className="w-full h-full object-contain"
                  />
                </div>
                <h3 className={`text-center font-medium ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t(sign.nameKey, sign.defaultName)}
                </h3>
                <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                  {sign.dateRange}
                </p>
              </motion.div>
            ))}
          </div>

          {/* 返回博客页面链接 */}
          <div className="mt-8 text-center">
            <LanguageLink 
              to="/blog" 
              className={`inline-block text-sm ${isDark ? 'text-purple-400 hover:text-purple-300' : 'text-purple-600 hover:text-purple-500'}`}
            >
              {t('horoscope.back_to_blog', '返回博客页面')}
            </LanguageLink>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default HoroscopeSelection; 