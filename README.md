# React + TypeScript + Vite

## 安装依赖

如果你使用npm:

```bash
npm install
npm install antd
```

如果你使用pnpm:

```bash
pnpm install
pnpm add antd
```

## 开发

```bash
npm run dev
```

或者

```bash
pnpm dev
```

## 构建

```bash
npm run build
```

或者

```bash
pnpm build
```

## 新添加功能：百度 URL 推送

本项目新添加了百度搜索引擎推送 URL 的功能，包括以下内容：

1. 自动推送：系统每天凌晨2点自动推送站点地图中的 URL 到百度
2. 手动推送：管理员可通过 `/admin/seo` 页面手动推送 URL

### 如何使用

1. 访问 `/admin/seo` 管理页面（需要管理员权限）
2. 在"推送指定 URL"选项中手动输入要推送的 URL（每行一个）
3. 或者在"从站点地图推送"中输入站点地图 URL
4. 点击"推送默认站点地图"可一键推送网站默认的 sitemap.xml 和 baidusitemap.xml

### 百度 API 配置

API 参数已配置好：
- 站点：https://tarotqa.com
- 令牌：gXW20EqkPLSoyYrz

This template provides a minimal setup to get React working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default tseslint.config({
  languageOptions: {
    // other options...
    parserOptions: {
      project: ["./tsconfig.node.json", "./tsconfig.app.json"],
      tsconfigRootDir: import.meta.dirname,
    },
  },
});
```

- Replace `tseslint.configs.recommended` to `tseslint.configs.recommendedTypeChecked` or `tseslint.configs.strictTypeChecked`
- Optionally add `...tseslint.configs.stylisticTypeChecked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and update the config:

```js
// eslint.config.js
import react from "eslint-plugin-react";

export default tseslint.config({
  // Set the react version
  settings: { react: { version: "18.3" } },
  plugins: {
    // Add the react plugin
    react,
  },
  rules: {
    // other rules...
    // Enable its recommended rules
    ...react.configs.recommended.rules,
    ...react.configs["jsx-runtime"].rules,
  },
});
```
