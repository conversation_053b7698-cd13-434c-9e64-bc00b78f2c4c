import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

const SingleCardHowItWords: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <div className="mt-24 sm:mt-32">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('yes_no_tarot.single_card_how_it_works.title', 'How One Card Tarot Yes No Reading Works')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
      </div>
      
      {/* The Power of Simplicity */}
      <div className="max-w-4xl mx-auto mb-12">
        <h3 className={`text-xl sm:text-2xl font-semibold mb-4 ${
          theme === 'light' ? 'text-purple-700' : 'text-purple-300'
        }`}>
          {t('yes_no_tarot.single_card_how_it_works.simplicity_title', 'The Power of Simplicity')}
        </h3>
        <p className={`${
          theme === 'light' ? 'text-gray-700' : 'text-gray-300'
        } text-lg mb-6`}>
          {t('yes_no_tarot.single_card_how_it_works.simplicity_description', 
            'When you need a quick, decisive answer, one card tarot yes/no readings cut through confusion with crystal clarity. Each tarot card carries specific energies that naturally align with "yes" or "no" responses.')}
        </p>
      </div>
      
      {/* AI Tarot Card Analysis Process */}
      <div className="max-w-4xl mx-auto">
        <h3 className={`text-xl sm:text-2xl font-semibold mb-4 ${
          theme === 'light' ? 'text-purple-700' : 'text-purple-300'
        }`}>
          {t('yes_no_tarot.single_card_how_it_works.ai_process_title', 'AI Tarot Card Analysis Process')}
        </h3>
        <p className={`${
          theme === 'light' ? 'text-gray-700' : 'text-gray-300'
        } text-lg mb-6`}>
          {t('yes_no_tarot.single_card_how_it_works.ai_process_intro', 'Our advanced AI tarot reader analyzes:')}
        </p>
        
        {/* Analysis Points */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Card Position */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">↕️</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.single_card_how_it_works.position_title', 'Card Position')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.single_card_how_it_works.position_description', 'Upright or reversed orientation')}
            </p>
          </div>
          
          {/* Suit Energy */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">♠️</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.single_card_how_it_works.suit_title', 'Suit Energy')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.single_card_how_it_works.suit_description', 'Cups, Wands, Swords, or Pentacles influence')}
            </p>
          </div>
          
          {/* Numerical Vibration */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">🔢</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.single_card_how_it_works.numerical_title', 'Numerical Vibration')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.single_card_how_it_works.numerical_description', 'Each number carries specific yes/no tendencies')}
            </p>
          </div>
          
          {/* Elemental Power */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">🌍</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.single_card_how_it_works.elemental_title', 'Elemental Power')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.single_card_how_it_works.elemental_description', 'Fire, Water, Air, Earth elemental guidance')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SingleCardHowItWords; 