/**
 * 百度URL提交相关路由 - 简化版，无需管理员权限
 */
const express = require('express');
const router = express.Router();
const baiduUrlService = require('../services/baiduUrlService');
const { asyncHandler } = require('../utils/asyncHandler');

/**
 * 手动提交指定URL到百度
 * @route POST /api/seo/baidu/push
 * @access 公开
 */
router.post('/baidu/push', asyncHandler(async (req, res) => {
  const { urls } = req.body;
  
  if (!urls || !Array.isArray(urls) || urls.length === 0) {
    return res.status(400).json({
      success: false,
      message: '请提供有效的URL列表'
    });
  }
  
  const result = await baiduUrlService.pushUrlsToBaidu(urls);
  
  if (result.success) {
    res.json({
      success: true,
      data: result.data
    });
  } else {
    res.status(500).json({
      success: false,
      message: result.message,
      error: result.error
    });
  }
}));

/**
 * 从站点地图获取URL并提交到百度
 * @route POST /api/seo/baidu/push-sitemap
 * @access 公开
 */
router.post('/baidu/push-sitemap', asyncHandler(async (req, res) => {
  const { sitemapUrl } = req.body;
  
  if (!sitemapUrl) {
    return res.status(400).json({
      success: false,
      message: '请提供站点地图URL'
    });
  }
  
  const result = await baiduUrlService.pushUrlsFromSitemap(sitemapUrl);
  
  if (result.success) {
    res.json({
      success: true,
      data: result.data
    });
  } else {
    res.status(500).json({
      success: false,
      message: result.message,
      error: result.error
    });
  }
}));

/**
 * 自动提交默认站点地图URL到百度
 * @route POST /api/seo/baidu/push-default-sitemap
 * @access 公开
 */
router.post('/baidu/push-default-sitemap', asyncHandler(async (req, res) => {
  const result = await baiduUrlService.pushDefaultSitemaps();
  
  res.json({
    success: true,
    ...result
  });
}));

/**
 * 测试端点 - 简单推送几个URL
 * @route GET /api/seo/test
 * @access 公开
 */
router.get('/test', asyncHandler(async (req, res) => {
  // 推送几个URL作为测试
  const testUrls = [
    'https://tarotqa.com/',
    'https://tarotqa.com/spreads'
  ];
  
  const result = await baiduUrlService.pushUrlsToBaidu(testUrls);
  
  res.json({
    success: true,
    message: '测试推送完成',
    result: result
  });
}));

/**
 * 获取最新的百度URL推送报告
 * @route GET /api/seo/baidu/report
 * @access 公开
 */
router.get('/baidu/report', asyncHandler(async (req, res) => {
  const report = baiduUrlService.getLatestPushReport();
  
  if (report) {
    res.json({
      success: true,
      data: report
    });
  } else {
    res.json({
      success: false,
      message: '暂无推送报告'
    });
  }
}));

/**
 * 手动触发完整的百度URL推送流程
 * @route POST /api/seo/baidu/push-all
 * @access 公开
 */
router.post('/baidu/push-all', asyncHandler(async (req, res) => {
  const result = await baiduUrlService.executeFullPushProcess();
  
  res.json({
    success: !result.error,
    message: result.message || '推送流程执行完成',
    data: result
  });
}));

module.exports = router; 