import zipfile
import os

def create_firefox_extension_zip():
    # 源目录
    source_dir = 'tarot-extension-firefox'
    # 输出zip文件
    output_zip = 'tarot-firefox-extension.zip'
    
    # 创建zip文件
    with zipfile.ZipFile(output_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # 遍历源目录中的所有文件
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                # 获取文件的完整路径
                file_path = os.path.join(root, file)
                # 计算在zip中的相对路径，使用正斜杠
                arcname = os.path.relpath(file_path, source_dir).replace('\\', '/')
                # 添加文件到zip
                zipf.write(file_path, arcname)
                print(f"Added: {arcname}")
    
    print(f"Firefox extension zip created: {output_zip}")

if __name__ == "__main__":
    create_firefox_extension_zip()
