import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import LanguageLink from '../LanguageLink';
import { useHoroscopeData } from '../../data/horoscopes';

interface HoroscopeBreadcrumbProps {
  className?: string;
  pageType: string;
  signId?: string;
}

const HoroscopeBreadcrumb: React.FC<HoroscopeBreadcrumbProps> = ({ 
  className = '', 
  pageType,
  signId
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const { getSignName } = useHoroscopeData();
  
  // 获取页面类型显示名称
  const getPageTypeName = (type: string) => {
    switch (type) {
      case 'daily':
        return t('horoscope.daily_title', '每日运势');
      case 'yesterday':
        return t('horoscope.yesterday_title', '昨日运势');
      case 'tomorrow':
        return t('horoscope.tomorrow_title', '明日运势');
      case 'weekly':
        return t('horoscope.weekly_title', '每周运势');
      case 'lastweek':
        return t('horoscope.lastweek_title', '上周运势');
      case 'nextweek':
        return t('horoscope.nextweek_title', '下周运势');
      case 'monthly':
        return t('horoscope.monthly_title', '每月运势');
      case 'lastmonth':
        return t('horoscope.lastmonth_title', '上月运势');
      case 'nextmonth':
        return t('horoscope.nextmonth_title', '下月运势');
      case 'yearly':
        return t('horoscope.yearly_title', '年度运势');
      case 'lastyear':
        return t('horoscope.lastyear_title', '去年运势');
      case 'nextyear':
        return t('horoscope.nextyear_title', '明年运势');
      case 'love':
        return t('horoscope.love_title', '爱情运势');
      case 'lastlove':
        return t('horoscope.lastlove_title', '上月爱情运势');
      case 'nextlove':
        return t('horoscope.nextlove_title', '下月爱情运势');
      default:
        return t('horoscope.title_short', '星座运势');
    }
  };

  return (
    <nav className={`flex ${className}`} aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        <li className="inline-flex items-center">
          <LanguageLink 
            to="/" 
            className={`inline-flex items-center text-sm font-medium ${
              isDark ? 'text-gray-400 hover:text-gray-300' : 'text-gray-700 hover:text-gray-900'
            }`}
          >
            <svg className="w-3 h-3 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
            </svg>
            {t('common.home', '首页')}
          </LanguageLink>
        </li>
        <li>
          <div className="flex items-center">
            <svg className="w-3 h-3 mx-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
            </svg>
            <LanguageLink 
              to="/horoscope" 
              className={`ml-1 text-sm font-medium ${
                isDark ? 'text-gray-400 hover:text-gray-300' : 'text-gray-700 hover:text-gray-900'
              }`}
            >
              {t('horoscope.title_short', '星座运势')}
            </LanguageLink>
          </div>
        </li>
        <li>
          <div className="flex items-center">
            <svg className="w-3 h-3 mx-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
            </svg>
            <LanguageLink 
              to={`/horoscope/${pageType}-horoscope`}
              className={`ml-1 text-sm font-medium ${
                isDark ? 'text-gray-400 hover:text-gray-300' : 'text-gray-700 hover:text-gray-900'
              }`}
            >
              {getPageTypeName(pageType)}
            </LanguageLink>
          </div>
        </li>
        {signId && (
          <li aria-current="page">
            <div className="flex items-center">
              <svg className="w-3 h-3 mx-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
              </svg>
              <span className={`ml-1 text-sm font-medium ${
                isDark ? 'text-gray-300' : 'text-gray-500'
              }`}>
                {getSignName(signId)}
              </span>
            </div>
          </li>
        )}
      </ol>
    </nav>
  );
};

export default HoroscopeBreadcrumb; 