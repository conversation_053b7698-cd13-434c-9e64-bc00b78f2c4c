/* 
  主标题样式覆盖
  这个文件专门处理浅色主题下主标题的样式
  使用!important确保最高优先级
*/

/* 浅色主题下的主标题 */
body:not(.dark) .main-title,
html:not(.dark) .main-title,
:root:not(.dark) .main-title,
:not(.dark) .main-title,
.main-title:not(.dark) {
  background: none !important;
  background-image: none !important;
  -webkit-background-clip: initial !important;
  -webkit-text-fill-color: #000000 !important;
  background-clip: initial !important;
  color: #000000 !important;
  text-shadow: none !important;
}

/* 深色主题下保持原有样式 */
.dark .main-title,
body.dark .main-title {
  background: linear-gradient(to right, #A78BFA, #EC4899) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
} 