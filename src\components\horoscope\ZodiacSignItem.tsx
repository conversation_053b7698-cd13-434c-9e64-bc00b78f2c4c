import { forwardRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import CdnLazyImage from '../CdnLazyImage';

interface ZodiacSignItemProps {
  id: string;
  nameKey: string;
  defaultName: string;
  iconPath: string;
  dateRange: string;
  onClick: (signId: string) => void;
  isSelected?: boolean;
}

/**
 * 星座选择项组件，显示星座图标、名称和日期范围
 * 移除了悬停效果和点击动效
 */
const ZodiacSignItem = forwardRef<HTMLDivElement, ZodiacSignItemProps>(({
  id,
  nameKey,
  defaultName,
  iconPath,
  dateRange,
  onClick,
  isSelected = false
}, ref) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // 根据是否选中决定使用哪个图片
  const getSelectedImagePath = (path: string, id: string) => {
    // 从路径中提取文件名部分
    const lastSlashIndex = path.lastIndexOf('/');
    const pathPrefix = lastSlashIndex !== -1 ? path.substring(0, lastSlashIndex + 1) : '';
    
    // 根据文件扩展名确定新的文件名
    if (path.endsWith('.webp')) {
      return `${pathPrefix}${id}-pin.webp`;
    } else if (path.endsWith('.png')) {
      return `${pathPrefix}${id}-pin.png`;
    } else {
      // 默认返回原路径
      return path;
    }
  };
  
  // 选择使用哪个图片
  const imagePath = isSelected ? getSelectedImagePath(iconPath, id) : iconPath;
  
  return (
    <div
      ref={ref}
      onClick={() => onClick(id)}
      className="cursor-pointer flex flex-col items-center sm:flex-shrink-0 py-2"
    >
      <div className="w-12 h-12 sm:w-20 sm:h-20 mb-1 relative">
        <CdnLazyImage 
          src={imagePath} 
          alt={t(nameKey, defaultName)} 
          className="w-full h-full object-contain"
        />
      </div>
      <div className="relative">
        <h3 className={`text-center text-xs sm:text-sm font-medium cursor-pointer ${
          isSelected 
            ? `${isDark ? 'text-purple-400' : 'text-purple-600'} underline` 
            : `${isDark ? 'text-white' : 'text-gray-800'}`
        }`}>
          {t(nameKey, defaultName)}
        </h3>
      </div>
      <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} mt-0.5 hidden sm:block`}>
        {dateRange}
      </p>
    </div>
  );
});

ZodiacSignItem.displayName = 'ZodiacSignItem';

export default ZodiacSignItem; 