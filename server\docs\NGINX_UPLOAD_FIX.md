# 修复413文件上传错误的Nginx配置

## 问题描述

用户在上传分享截图时遇到413错误（Request Entity Too Large），这是因为Nginx的默认配置限制了请求体大小。

## 解决方案

### 1. 更新Nginx配置

需要在Nginx配置中添加以下关键设置：

#### 全局设置（在server块中）
```nginx
# 全局文件上传大小限制 - 支持10MB文件上传
client_max_body_size 10m;
client_body_buffer_size 128k;
client_body_timeout 60s;
```

#### API反向代理设置
```nginx
# API 反向代理 - 关键配置，支持文件上传
location /api/ {
    proxy_pass http://127.0.0.1:5000/;

    # 添加这两行以支持流式响应
    proxy_buffering off;
    proxy_max_temp_file_size 0;
    
    # 增加超时时间以支持长时间连接
    proxy_read_timeout 300s;

    # 支持文件上传 - 设置最大请求体大小为10MB
    client_max_body_size 10m;
    client_body_buffer_size 128k;
    client_body_timeout 60s;

    # ... 其他配置
}
```

#### 分享截图静态文件访问
```nginx
# 添加持久化分享截图目录的访问支持
location /uploads/share-screenshots/ {
    alias /var/www/tarot/share-screenshots/;
    
    # 允许跨域访问
    add_header Access-Control-Allow-Origin "*";
    add_header Access-Control-Allow-Methods "GET, OPTIONS";
    
    # 缓存设置
    expires 7d;
    add_header Cache-Control "public, no-transform";
    
    # 安全设置 - 只允许图片文件
    location ~* \.(jpg|jpeg|png|gif|webp)$ {
        try_files $uri =404;
    }
    
    # 拒绝其他文件类型
    location ~ {
        return 403;
    }
}
```

### 2. 部署步骤

#### 步骤1：备份现有配置
```bash
sudo cp /etc/nginx/sites-available/tarotqa.com /etc/nginx/sites-available/tarotqa.com.backup
```

#### 步骤2：更新配置文件
将 `server/config/nginx-tarotqa.conf` 的内容复制到 `/etc/nginx/sites-available/tarotqa.com`

或者直接复制文件：
```bash
sudo cp server/config/nginx-tarotqa.conf /etc/nginx/sites-available/tarotqa.com
```

#### 步骤3：测试配置
```bash
sudo nginx -t
```

#### 步骤4：重新加载Nginx
```bash
sudo systemctl reload nginx
```

### 3. 验证修复

#### 检查配置是否生效
```bash
# 检查Nginx配置
sudo nginx -T | grep client_max_body_size

# 检查Nginx状态
sudo systemctl status nginx
```

#### 测试文件上传
1. 访问网站的分享功能
2. 尝试上传一个小于10MB的图片文件
3. 确认不再出现413错误

### 4. 故障排除

#### 如果仍然出现413错误

1. **检查Nginx错误日志**：
```bash
sudo tail -f /var/log/nginx/tarot_error.log
```

2. **检查配置是否正确加载**：
```bash
sudo nginx -T | grep -A 5 -B 5 "client_max_body_size"
```

3. **确认配置文件语法正确**：
```bash
sudo nginx -t
```

4. **重启Nginx服务**（如果重新加载不够）：
```bash
sudo systemctl restart nginx
```

#### 如果需要支持更大的文件

修改以下配置值：
```nginx
# 支持更大文件，例如20MB
client_max_body_size 20m;
client_body_buffer_size 256k;
```

同时需要更新Node.js应用的配置：
```javascript
// 在server/index.js中
app.use(express.json({ limit: '20mb' }));
app.use(express.urlencoded({ limit: '20mb', extended: true }));
```

```javascript
// 在server/routes/shareSubmission.js中
const upload = multer({ 
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 20 * 1024 * 1024, // 限制20MB
  }
});
```

### 5. 监控和维护

#### 定期检查上传日志
```bash
# 检查访问日志中的上传请求
sudo grep "POST.*api.*share-submission" /var/log/nginx/tarot_access.log

# 检查是否有413错误
sudo grep "413" /var/log/nginx/tarot_error.log
```

#### 监控磁盘空间
```bash
# 检查分享截图目录大小
du -sh /var/www/tarot/share-screenshots/

# 检查总磁盘使用情况
df -h
```

### 6. 安全考虑

1. **文件类型限制**：配置中已限制只允许图片文件访问
2. **大小限制**：设置了合理的文件大小限制（10MB）
3. **路径安全**：使用alias而不是root，防止路径遍历攻击
4. **访问控制**：只允许GET和OPTIONS方法访问静态文件

### 7. 性能优化

配置中已包含以下优化：
- 启用gzip压缩
- 设置适当的缓存头
- 使用sendfile优化文件传输
- 禁用不必要的访问日志（对于静态文件）

## 总结

通过更新Nginx配置，现在系统支持：
- ✅ 10MB以内的文件上传
- ✅ 持久化存储的分享截图访问
- ✅ 适当的安全限制
- ✅ 性能优化
- ✅ 错误处理和日志记录

这个配置解决了413错误，同时保持了系统的安全性和性能。
