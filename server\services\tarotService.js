const { Session } = require('../models/tarot');

// 创建新的占卜会话
const createSession = async (userId, question) => {
  try {
    const session = new Session({
      userId,
      question,
      status: 'pending'
    });
    await session.save();
    return session;
  } catch (error) {
    console.error('Error creating session:', error);
    throw error;
  }
};

// 获取用户的占卜历史
const getUserSessions = async (userId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;
    const sessions = await Session.find({ userId })
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Session.countDocuments({ userId });

    return {
      sessions,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  } catch (error) {
    console.error('Error getting user sessions:', error);
    throw error;
  }
};

module.exports = {
  createSession,
  getUserSessions
}; 