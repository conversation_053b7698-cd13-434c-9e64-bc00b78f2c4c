import React from 'react';
import { getImageUrl } from '../utils/cdnImageUrl';

interface CdnLazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt?: string;
  fallbackSrc?: string;
}

/**
 * CDN懒加载图片组件
 * 自动处理CDN URL和本地资源的切换
 * 基于现有的LazyImage组件逻辑
 */
const CdnLazyImage: React.FC<CdnLazyImageProps> = ({
  src,
  alt = "",
  fallbackSrc,
  className,
  onError,
  ...props
}) => {
  // 处理图片URL
  const processedSrc = getImageUrl(src);
  const processedFallbackSrc = fallbackSrc ? getImageUrl(fallbackSrc) : undefined;

  // 处理图片加载错误
  const handleError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    if (processedFallbackSrc) {
      (e.target as HTMLImageElement).src = processedFallbackSrc;
    }
    
    if (onError) {
      onError(e);
    }
  };

  return (
    <img 
      src={processedSrc} 
      alt={alt} 
      className={className} 
      loading="lazy" 
      onError={handleError}
      {...props}
    />
  );
};

export default CdnLazyImage; 