import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

import enTranslation from './locales/en.json';
import zhCNTranslation from './locales/zh-CN.json';
import zhTWTranslation from './locales/zh-TW.json';
import jaTranslation from './locales/ja.json';

export const availableLanguages = ['zh-CN', 'zh-TW', 'en', 'ja'];

// 初始化i18n
const initI18n = async () => {
  try {
    // 直接使用zh-TW作为默认语言
    const defaultLanguage = 'zh-TW';
    
    // 初始化i18n
    await i18n
      .use(LanguageDetector)
      .use(initReactI18next)
      .init({
        resources: {
          en: { translation: enTranslation },
          'zh-CN': { translation: zhCNTranslation },
          'zh-TW': { translation: zhTWTranslation },
          ja: { translation: jaTranslation }
        },
        lng: defaultLanguage,
        fallbackLng: 'zh-TW',
        supportedLngs: availableLanguages,
        interpolation: {
          escapeValue: false
        },
        detection: {
          order: ['localStorage', 'navigator'],
          caches: ['localStorage']
        },
        debug: false
      });

    return i18n;
  } catch (error) {
    throw error;
  }
};

// 导出初始化函数和i18n实例
export { initI18n };
export default i18n; 