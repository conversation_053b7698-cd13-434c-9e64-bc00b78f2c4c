import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

interface FAQSectionProps {}

const FAQSection: React.FC<FAQSectionProps> = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  
  // FAQ问题配置
  const faqItems = [
    {
      id: 'accuracy',
      question: t('daily.faq.q1', 'TarotQA的每日運勢占卜準確嗎？'),
      answer: t('daily.faq.a1', '我們的系統結合了傳統占星學的智慧、塔羅牌的直覺象徵，並透過AI演算法進行深度整合分析，旨在提供市面上最精準、最個人化的免費運勢指引。我們相信，真正的準確來自於深刻的自我洞察，而我們的工具正是為此而生。')
    },
    {
      id: 'free',
      question: t('daily.faq.q2', '這個每日運勢占卜需要付費嗎？'),
      answer: t('daily.faq.a2', '完全免費。我們相信深刻的洞察力應該讓所有人都能接觸，因此我們承諾提供高品質且完全免費的每日運勢解析服務。您可以每天都來測運勢，無需任何費用。')
    },
    {
      id: 'daily',
      question: t('daily.faq.q3', '我每天都可以來測運勢嗎？'),
      answer: t('daily.faq.a3', '當然！宇宙的能量每天都在變化，塔羅牌的指引也是即時的。我們強烈建議您將這裡當作每日的心靈加油站，每天都來抽取新的指引，以更好地順應當下的能量流動。')
    },
    {
      id: 'principle',
      question: t('daily.faq.questions.principle.question', '這個AI每日運勢占卜的解讀原理是什麼？'),
      answer: t('daily.faq.questions.principle.answer', '我們的系統結合了三大核心元素：您生日所對應的傳統星座學、您當下直覺抽出的塔羅牌原型象徵，以及能理解和翻譯這兩者複雜關聯的先進AI技術。AI的角色是作為一個解碼器，將星辰與牌義的象徵語言，轉化為對您清晰且個人化的<strong>今日運勢</strong>指引。')
    },
    {
      id: 'why_combine',
      question: t('daily.faq.questions.why_combine.question', '為什麼要結合星座和塔羅牌？'),
      answer: t('daily.faq.questions.why_combine.answer', '單獨的星座運勢是針對數百萬人的宏觀預測，而單獨的塔羅抽牌則可能缺乏個人生命週期的背景。我們將兩者結合，讓星座（您的先天性格藍圖）為塔羅牌（當下的能量快照）提供個人化情境，從而產生一份比單一占卜方法更深刻、更具針對性的「超個人化」解讀。')
    },
    {
      id: 'privacy',
      question: t('daily.faq.questions.privacy.question', '我提供的個人資料安全嗎？'),
      answer: t('daily.faq.questions.privacy.answer', '我們極度重視您的隱私。您輸入的出生日期僅用於即時計算您的星座以完成本次<strong>運勢查詢</strong>，我們絕不會儲存或將其用於任何其他目的。整個占卜過程都是匿名的，旨在提供一個安全、可信的自我探索環境。')
    },
    {
      id: 'ai_vs_human',
      question: t('daily.faq.questions.ai_vs_human.question', 'AI占卜和真人占卜師有何不同？'),
      answer: t('daily.faq.questions.ai_vs_human.answer', 'AI占卜提供的是一種即時、私密且24小時可用的服務。它基於龐大的數據和符號學知識庫進行客觀分析，為您提供一個穩定的參考基準。真人占卜師則能提供更具溫度的互動與對話式諮詢。兩者各有優勢，我們的AI占卜是您隨時隨地探索每日能量的絕佳工具。')
    }
  ];

  return (
    <div className="max-w-4xl mx-auto mt-28 sm:mt-36 px-4 sm:px-6 relative z-10">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('daily.faq.title', '每日運勢常見問題 (FAQ)')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
        <p className={`${
          theme === 'light' ? 'text-gray-700' : 'text-gray-300'
        } text-lg max-w-3xl mx-auto`}>
          {t('daily.faq.subtitle', '關於每日運勢占卜的熱門問題解答')}
        </p>
      </div>

      {/* FAQ内容 - 使用结构化数据标记 */}
      <div 
        className="space-y-6"
        itemScope 
        itemType="https://schema.org/FAQPage"
      >
        {faqItems.map(item => (
          <div 
            key={item.id}
            className={`p-6 rounded-lg ${
              theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
            }`}
            itemScope 
            itemProp="mainEntity" 
            itemType="https://schema.org/Question"
          >
            <h3 
              className={`text-lg font-semibold mb-2 ${
                theme === 'light' ? 'text-gray-800' : 'text-white'
              }`}
              itemProp="name"
            >
              {item.question}
            </h3>
            <div 
              itemScope 
              itemProp="acceptedAnswer" 
              itemType="https://schema.org/Answer"
            >
              <p 
                className={`${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}
                itemProp="text"
                dangerouslySetInnerHTML={{ __html: item.answer }}
              >
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Horoscope Schema 标记 */}
      <script 
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Horoscope",
            "name": "TarotQA每日運勢占卜",
            "datePublished": new Date().toISOString().split('T')[0],
            "description": "結合塔羅牌與星座的每日運勢分析，提供整體、愛情、事業、財富與健康等多方面的個人化指引。",
            "about": {
              "@type": "Thing",
              "name": "每日運勢占卜"
            }
          })
        }}
      />
    </div>
  );
};

export default FAQSection; 