import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { getFontClass } from '../../utils/fontUtils';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import type { Components } from 'react-markdown';
import useHoroscopeCache from '../../hooks/useHoroscopeCache';

interface HoroscopeContentProps {
  signId: string;
  pageType: string;
  date: Date;
}

interface HoroscopeData {
  sign: string;
  type: string;
  date: string;
  content: string;
  language: string;
  generated_at: string;
}

const HoroscopeContent: React.FC<HoroscopeContentProps> = ({ signId, pageType, date }) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [error, setError] = useState<string | null>(null);
  const [horoscopeData, setHoroscopeData] = useState<HoroscopeData | null>(null);
  const [localLoading, setLocalLoading] = useState<boolean>(true);
  const requestAttemptedRef = useRef<{[key: string]: boolean}>({});
  
  // 将pageType映射到API所需的类型
  const getApiType = (pageType: string): 'daily' | 'weekly' | 'monthly' | 'yearly' | 'love' => {
    if (pageType === 'yesterday' || pageType === 'tomorrow') {
      return 'daily';
    } else if (pageType === 'lastweek' || pageType === 'nextweek') {
      return 'weekly';
    } else if (pageType === 'lastmonth' || pageType === 'nextmonth') {
      return 'monthly';
    } else if (pageType === 'lastyear' || pageType === 'nextyear') {
      return 'yearly';
    } else if (pageType === 'lastlove' || pageType === 'nextlove') {
      return 'love';
    } else if (['daily', 'weekly', 'monthly', 'yearly', 'love'].includes(pageType)) {
      return pageType as 'daily' | 'weekly' | 'monthly' | 'yearly' | 'love';
    }
    return 'daily'; // 默认返回每日运势
  };
  
  // 检查是否是特殊日期类型（非当前日期）
  const isSpecialDateType = ['yesterday', 'tomorrow', 'lastweek', 'nextweek', 'lastmonth', 'nextmonth', 'lastyear', 'nextyear', 'lastlove', 'nextlove'].includes(pageType);
  
  const apiType = getApiType(pageType);
  // 获取星座运势数据的hook
  const { getHoroscopeForSign, getHoroscopeForSignWithDate } = useHoroscopeCache(apiType);
  
  // 获取请求的唯一键
  const getRequestKey = () => {
    const dateStr = date.toISOString().split('T')[0];
    return `${apiType}_${dateStr}_${signId}_${i18n.language}`;
  };
  
  // 从缓存中获取星座运势数据
  const updateHoroscopeData = async () => {
    if (!signId || !pageType) return;
    
    const requestKey = getRequestKey();
    
    // 如果已经有数据，不再重复请求
    if (horoscopeData && horoscopeData.sign === signId) {
      console.log(`已有数据，跳过重复请求: ${requestKey}`);
      return;
    }
    
    // 如果已经尝试过请求且有错误，不再重复请求
    if (requestAttemptedRef.current[requestKey] && error) {
      console.log(`已尝试过请求且有错误，跳过重复请求: ${requestKey}`);
      return;
    }
    
    // console.log(`\n===== 更新星座数据 =====\nsignId=${signId}, pageType=${pageType}, apiType=${apiType}, date=${date.toISOString().split('T')[0]}, isSpecialDateType=${isSpecialDateType}`);
    
    setError(null);
    setLocalLoading(true);
    
    try {
      // 根据页面类型决定是否需要传递特定日期
      if (isSpecialDateType) {
        // 对于特殊日期类型，使用传入的日期参数
        const data = await getHoroscopeForSignWithDate(signId, date);
        
        if (data) {
          setHoroscopeData(data);
          setLocalLoading(false);
          // 重置错误状态
          setError(null);
        } else {
          setError(t('horoscope.no_data', '暂无星座运势数据'));
          setLocalLoading(false);
          // 标记已尝试请求
          requestAttemptedRef.current[requestKey] = true;
        }
      } else {
        // 对于当前日期的运势，使用默认方法
        const data = await getHoroscopeForSign(signId);
        
        if (data) {
          setHoroscopeData(data);
          setLocalLoading(false);
          // 重置错误状态
          setError(null);
        } else {
          setError(t('horoscope.no_data', '暂无星座运势数据'));
          setLocalLoading(false);
          // 标记已尝试请求
          requestAttemptedRef.current[requestKey] = true;
        }
      }
    } catch (error: any) {
      setError(t('horoscope.error_loading', '加载星座运势数据失败，请稍后重试'));
      setLocalLoading(false);
      // 标记已尝试请求
      requestAttemptedRef.current[requestKey] = true;
    }
  };
  
  // 当signId或pageType变化时更新数据
  useEffect(() => {
    // 当参数变化时，重置请求尝试状态
    if (signId && pageType && date) {
      const requestKey = getRequestKey();
      if (requestAttemptedRef.current[requestKey] !== true) {
        // 只有当未尝试过请求时才更新数据
        updateHoroscopeData();
      } else {
        console.log(`跳过重复请求: ${requestKey} (已尝试过)`);
      }
    }
  }, [signId, pageType, date, i18n.language]);
  
  // 自定义组件，仅保留确保字体大小一致的样式
  const MarkdownComponents: Components = {
    p: (props) => <p className="text-lg mb-4" {...props} />,
    h1: (props) => <h1 className="text-lg mb-4" {...props} />,
    h2: (props) => <h2 className="text-lg mb-4" {...props} />,
    h3: (props) => <h3 className="text-lg mb-4" {...props} />,
    strong: (props) => <strong {...props} />
  };
  
  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6">
      {localLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-500">{error}</p>
          <button 
            onClick={() => {
              // 重置请求尝试状态并重试
              const requestKey = getRequestKey();
              requestAttemptedRef.current[requestKey] = false;
              updateHoroscopeData();
            }}
            className={`mt-4 px-6 py-2 rounded-md ${isDark ? 'bg-purple-600 hover:bg-purple-700' : 'bg-purple-500 hover:bg-purple-600'} text-white transition-colors`}
          >
            {t('common.retry', '重试')}
          </button>
        </div>
      ) : horoscopeData ? (
        <div className="relative backdrop-blur-xl pt-8 pb-6 sm:pt-10 sm:pb-8 px-6 sm:px-8 rounded-2xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20">
          <div className="relative z-10">
            {/* 使用ReactMarkdown渲染整个内容，包括标题和正文 */}
            <div className={`${getFontClass(i18n.language)} ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
              <ReactMarkdown 
                remarkPlugins={[remarkGfm]} 
                components={MarkdownComponents}
              >
                {horoscopeData.content}
              </ReactMarkdown>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <p>{t('horoscope.no_data', '暂无星座运势数据')}</p>
        </div>
      )}
    </div>
  );
};

export default HoroscopeContent;