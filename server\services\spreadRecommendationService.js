const { SPREAD_OPTIONS } = require('../constants/spreads');
const { zhipuAPI, qwenAPI } = require('./apiClients');


// 支持的语言
const SUPPORTED_LANGUAGES = {
  'zh-CN': '简体中文',
  'zh-TW': '繁体中文',
  'en': 'English',
  'ja': '日本語'
};

// 类别名称映射
const CATEGORY_MAP = {
  'love_relationships': '爱情人际',
  // 'fortune_prediction': '运势预测',
  'self-exploration': '自我探索',
  'career_wealth': '事业财富',
};

// 反向映射：从名称到ID
const REVERSE_CATEGORY_MAP = Object.entries(CATEGORY_MAP).reduce((acc, [id, name]) => {
  acc[name] = id;
  return acc;
}, {});

// 根据问题和语言推荐牌阵类型 (使用AI)
async function recommendSpreadCategory(question, language = 'zh-CN') {
  // 如果语言不支持，默认使用简体中文
  if (!SUPPORTED_LANGUAGES[language]) {
    console.warn(`Unsupported language: ${language}, falling back to zh-CN`);
    language = 'zh-CN';
  }

  // console.log('\n========== AI 推荐牌阵类别开始 ==========')
  // console.log('问题内容:', question);
  // console.log('当前语言:', SUPPORTED_LANGUAGES[language]);

  // 系统提示
  const systemPrompt = `
  你是一个专业的塔罗牌阵推荐助手。你需要根据用户的问题推荐最合适的牌阵类别。
  你必须直接返回JSON格式数据，格式如下：
{
  "category": "love_relationships/self-exploration/career_wealth",
  "reason": 简要分析原因,
  "confidence": 0.0-1.0之间的数值
}
注意：不要包含任何其他代码、代码块标记或解释、markdown格式。`;

  // 所有可用的类别
  const availableCategories = Object.keys(CATEGORY_MAP).map(key => ({
    id: key,
    name: CATEGORY_MAP[key]
  }));

  // 用户提示模板
  const userPrompt = `
用户问题：${question}
`;

  // console.log('\n系统提示:');
  // console.log(systemPrompt);
  // console.log('\n用户提示:');
  // console.log(userPrompt);

  try {
    // console.log('\n正在调用 AI API...');
    // const response = await zhipuAPI.post('chat/completions', {
      const response = await qwenAPI.post('chat/completions', {

      // model: "GLM-4-Air",
      model: "qwen-turbo-latest",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ]
    });

    if (!response.data?.choices?.[0]?.message?.content) {
      console.error('API 响应格式错误:', response.data);
      throw new Error('Invalid API response format');
    }

    const content = response.data.choices[0].message.content;
    // console.log('\nAI 响应内容:', content);
    
    // 提取token使用情况
    const inputTokens = response.data.usage?.prompt_tokens || 0;
    const outputTokens = response.data.usage?.completion_tokens || 0;
    // console.log(`类别推荐API使用: 输入tokens: ${inputTokens}, 输出tokens: ${outputTokens}`);

    try {
      // 清理 markdown 代码块标记
      const cleanContent = content.replace(/```json\s*|\s*```/g, '').trim();
      
      // 使用正则表达式直接提取category字段值，而不是解析整个JSON
      const categoryMatch = cleanContent.match(/"category"\s*:\s*"([^"]+)"/);
      if (!categoryMatch) {
        throw new Error('Missing required field: category');
      }
      
      const category = categoryMatch[1];
      
      // 检查category是否在预定义的类别中
      if (!CATEGORY_MAP[category] && !REVERSE_CATEGORY_MAP[category]) {
        console.error('AI推荐的类别无效:', category);
        throw new Error('Invalid category recommended');
      }

      // 确定最终的类别ID
      let finalCategory = category;

      // 尝试提取reason和confidence，但不强制要求
      let reason = "未提供理由";
      let confidence = 0.5;
      
      const reasonMatch = cleanContent.match(/"reason"\s*:\s*"?([^",}]+)"?/);
      if (reasonMatch) {
        reason = reasonMatch[1].trim();
      }
      
      const confidenceMatch = cleanContent.match(/"confidence"\s*:\s*([0-9.]+)/);
      if (confidenceMatch) {
        confidence = parseFloat(confidenceMatch[1]);
      }


      // console.log('\nAI推荐类别详情:');
      // console.log('类别ID:', finalCategory);
      // console.log('推荐理由:', reason);
      // console.log('信心指数:', confidence);
      // console.log('\n========== AI 推荐牌阵类别结束 ==========\n');

      return {
        category: finalCategory,
        matchDetails: {
          reason: reason,
          confidence: confidence,
          maxScore: confidence * 10, // 为了与原算法保持兼容
        },
        tokens: {
          input: inputTokens,
          output: outputTokens
        }
      };
    } catch (parseError) {
      console.error('解析 AI 响应失败:', parseError);
      throw new Error('Failed to parse AI response');
    }

  } catch (error) {
    console.error('AI推荐类别失败:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
    console.error('使用默认推荐');
    // console.log('\n========== AI 推荐牌阵类别结束 ==========\n');
    
    // 返回默认类别 (general通用)
    const defaultCategory = 'general'; // 使用一个存在于 CATEGORY_MAP 中的键
    return {
      category: defaultCategory,
      matchDetails: {
        reason: "AI 推荐失败，使用默认推荐通用牌阵。",
        confidence: 0.5,
        maxScore: 5
      },
      tokens: {
        input: 0,
        output: 0
      }
    };
  }
}
  
// 使用 API 推荐具体牌阵
async function recommendSpreadWithAI(question, categorySpreads, language = 'zh-CN') {
  // 如果语言不支持，默认使用简体中文
  if (!SUPPORTED_LANGUAGES[language]) {
    console.warn(`Unsupported language: ${language}, falling back to zh-CN`);
    language = 'zh-CN';
  }

  // console.log('\n========== AI 推荐牌阵开始 ==========')

  // 系统提示
  const systemPrompt = `
  你是一个专业的塔罗牌阵推荐助手。你需要根据用户的问题，从给定的牌阵列表中选择最合适的牌阵。
  可选牌阵列表：
${categorySpreads.map(spread => `
ID: ${spread.id}
名称: ${spread.name}
描述: ${spread.description}
`).join('\n')}
  你必须直接返回JSON格式数据，格式如下：
{
  "recommendedSpreadId": "推荐牌阵的ID",
  "reason": 简要分析原因,
  "confidence": 0.0-1.0之间的数值
}
注意：不要包含任何其他代码、代码块标记或解释、markdown格式。`;

  // 用户提示模板
  const userPrompt = `
用户问题：${question}
`;

  // console.log('\n系统提示:');
  // console.log(systemPrompt);
  // console.log('\n用户提示:');
  // console.log(userPrompt);

  try {
    // console.log('\n正在调用 AI API...');
    // const response = await zhipuAPI.post('chat/completions', {
    //   model: "GLM-4-Air",
    const response = await qwenAPI.post('chat/completions', {
      model: "qwen-turbo-latest",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ]
    });

    if (!response.data?.choices?.[0]?.message?.content) {
      console.error('API 响应格式错误:', response.data);
      throw new Error('Invalid API response format');
    }

    const content = response.data.choices[0].message.content;
    // console.log('\nAI 响应内容:', content);
    
    // 提取token使用情况
    const inputTokens = response.data.usage?.prompt_tokens || 0;
    const outputTokens = response.data.usage?.completion_tokens || 0;
    // console.log(`牌阵推荐API使用: 输入tokens: ${inputTokens}, 输出tokens: ${outputTokens}`);

    try {
      // 清理 markdown 代码块标记
      const cleanContent = content.replace(/```json\s*|\s*```/g, '').trim();
      
      // 直接用正则表达式提取推荐牌阵ID
      const spreadIdMatch = cleanContent.match(/"recommendedSpreadId"\s*:\s*"([^"]+)"/);
      if (!spreadIdMatch) {
        throw new Error('Missing required field: recommendedSpreadId');
      }
      
      const recommendedSpreadId = spreadIdMatch[1];
      
      // 验证推荐的牌阵ID是否存在
      const recommendedSpread = categorySpreads.find(s => s.id === recommendedSpreadId);
      if (!recommendedSpread) {
        console.error('推荐的牌阵ID无效:', recommendedSpreadId);
        throw new Error('Invalid spread ID recommended');
      }
      
      // 尝试提取reason字段，但不强制要求
      let reason = "未提供理由";
      
      const reasonMatch = cleanContent.match(/"reason"\s*:\s*"?([^",}]+)"?/);
      if (reasonMatch) {
        reason = reasonMatch[1].trim();
      }
      
      // 尝试提取confidence字段，但不强制要求
      let confidence = 0.5;
      const confidenceMatch = cleanContent.match(/"confidence"\s*:\s*([0-9.]+)/);
      if (confidenceMatch) {
        confidence = parseFloat(confidenceMatch[1]);
      }
      
      // console.log('\nAI推荐详情:');
      // console.log('牌阵ID:', recommendedSpread.id);
      // console.log('牌阵名称:', recommendedSpread.name);
      // console.log('牌阵描述:', recommendedSpread.description);
      // console.log('牌阵类别:', recommendedSpread.category.join(', '));
      // console.log('\n========== AI 推荐牌阵结束 ==========\n');
      
      return {
        recommendedSpreadId: recommendedSpreadId,
        reason: reason,
        confidence: confidence,
        tokens: {
          input: inputTokens,
          output: outputTokens
        }
      };
    } catch (parseError) {
      console.error('解析 AI 响应失败:', parseError);
      throw new Error('Failed to parse AI response');
    }

  } catch (error) {
    console.error('AI推荐失败:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
    console.error('使用默认推荐');
    // console.log('\n========== AI 推荐牌阵结束 ==========\n');
    return {
      recommendedSpreadId: categorySpreads[0].id,
      reason: "AI 推荐失败，使用默认推荐。",
      tokens: {
        input: 0,
        output: 0
      }
    };
  }
}

module.exports = {
  recommendSpreadCategory,
  recommendSpreadWithAI,
  SUPPORTED_LANGUAGES
}; 