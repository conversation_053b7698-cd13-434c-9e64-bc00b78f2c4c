// import { useRef } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { Reader, Message } from '../../types/tarot';
import { getFontClass } from '../../utils/tarotUtils';
import { VipBadge } from '../../components/VipBadge';
import TarotProgressCircle from '../../components/TarotProgressCircle';
import { TarotSpeech, TarotBlockSpeech, PlayingProvider } from '../../components/speech';
import { useMemo, useEffect, useState } from 'react';
import React from 'react';
import CdnLazyImage from '../../components/CdnLazyImage';
import ShareDialog from '../../components/ShareDialog';
import { checkRewardStatus } from '../../services/shareService';

interface TarotReadingContentProps {
  messages: Message[];
  reader: Reader | null;
  initialReadingCompleted: boolean;
  deepAnalysisLoading: boolean;
  isSubmitting: boolean;
  receivingStreamContent: boolean;
  progressBarCompleted: boolean;
  showDeepAnalysis: boolean;
  deepAnalysisProgressValue: number;
  messagesContainerRef: React.RefObject<HTMLDivElement>;
  onDeepAnalysisClick: () => void;
  onSubmitFollowUp: (e: React.FormEvent) => void;
  followUpQuestion: string;
  setFollowUpQuestion: (value: string) => void;
  onShowFeedback: () => void; // 现在用于显示VIP提示
  hasUsedDeepAnalysis: boolean;
  hasUsedFollowup: boolean;
  useTarotProgressIsGenerating: boolean;
  user: any;
  sessionId?: string;
  hasEthicalIssue?: boolean;
  isSharedView?: boolean;
  onShowShare?: () => void;
  onShowComment?: () => void;
}

const TarotReadingContent: React.FC<TarotReadingContentProps> = ({
  messages,
  reader,
  initialReadingCompleted,
  deepAnalysisLoading,
  isSubmitting,
  receivingStreamContent,
  progressBarCompleted,
  showDeepAnalysis,
  deepAnalysisProgressValue,
  messagesContainerRef,
  onDeepAnalysisClick,
  onSubmitFollowUp,
  followUpQuestion,
  setFollowUpQuestion,
  onShowFeedback,
  hasUsedDeepAnalysis,
  hasUsedFollowup,
  useTarotProgressIsGenerating,
  user,
  sessionId,
  hasEthicalIssue = false,
  isSharedView = false,
  onShowShare,
  onShowComment
}) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // 添加状态来存储实际的伦理问题状态（结合props和localStorage）
  const [actualEthicalIssue, setActualEthicalIssue] = useState(hasEthicalIssue);

  // 添加状态来管理错误消息
  const [errorMessage, setErrorMessage] = useState<string>('');

  // 添加分享对话框状态
  const [showShareDialog, setShowShareDialog] = useState(false);
  // 添加状态来跟踪用户是否已获得分享奖励
  const [hasReceivedShareReward, setHasReceivedShareReward] = useState(false);
  
  // 检查localStorage中的hasEthicalIssue标记
  useEffect(() => {
    if (sessionId) {
      const localStorageEthicalIssue = localStorage.getItem(`${sessionId}_hasEthicalIssue`) === 'true';
      // 如果localStorage或props中有一个为true，则设置为true
      const shouldBeEthicalIssue = localStorageEthicalIssue || hasEthicalIssue;
      
      if (shouldBeEthicalIssue !== actualEthicalIssue) {
        // console.log('[安全检测] 更新actualEthicalIssue状态:', { 
        //   localStorageEthicalIssue, 
        //   propsHasEthicalIssue: hasEthicalIssue, 
        //   newValue: shouldBeEthicalIssue 
        // });
        setActualEthicalIssue(shouldBeEthicalIssue);
      }
    }
  }, [sessionId, hasEthicalIssue, actualEthicalIssue]);
  
  // 检查用户是否已经获得过分享奖励
  useEffect(() => {
    if (user) {
      checkRewardStatus()
        .then(hasReceived => {
          setHasReceivedShareReward(hasReceived);
        })
        .catch(error => {
          console.error('获取奖励状态失败:', error);
        });
    }
  }, [user]);
  
  // 创建段落引用字典，用于存储不同板块的段落引用
  const paragraphRefsMap = useMemo(() => {
    const map: Record<string, React.RefObject<HTMLDivElement>[]> = {};
    // 预设基础板块和追问板块的引用
    map['base'] = Array(20).fill(null).map(() => React.createRef<HTMLDivElement>());
    map['followup'] = Array(10).fill(null).map(() => React.createRef<HTMLDivElement>());
    map['deep_analysis'] = Array(10).fill(null).map(() => React.createRef<HTMLDivElement>());
    map['advice'] = Array(10).fill(null).map(() => React.createRef<HTMLDivElement>());
    return map;
  }, []);
  
  // 缓存消息板块分组
  const messageBlocks = useMemo(() => {
    // 存储分组后的消息
    const blocks: {startIndex: number, endIndex: number, type: string, messages: Message[]}[] = [];
    
    // 消息为空时直接返回空数组
    if (messages.length === 0) return blocks;
    
    let currentBlock: {startIndex: number, endIndex: number, type: string, messages: Message[]} = {
      startIndex: 0,
      endIndex: 0,
      type: 'base', // 初始类型为基础解读（统一使用base而非basic）
      messages: []
    };
    
    // 板块是指对话的逻辑单元，包括：
    // 1. base: 基础解读 - 初始的塔罗牌解读 (使用base而非basic，与后端一致)
    // 2. deep_analysis: 深度解析 - 用户请求的深度分析
    // 3. followup: 追问回复 - 用户的后续提问和AI的回答
    // 4. advice: 高维建议 - 用户请求的建议
    
    // 遍历所有消息，根据用户消息类型划分板块
    messages.forEach((message, index) => {
      // 第一条消息，开始一个初始区块
      if (index === 0) {
        currentBlock = {
          startIndex: 0,
          endIndex: 0,
          type: 'base',
          messages: [message]
        };
      }
      // 当遇到用户消息时，判断是否为板块分隔点
      else if (message.type === 'user') {
        const content = message.content || '';
        let blockType = 'followup'; // 默认为追问
        
        // 根据内容判断类型
        if (content.includes('深度解析') || content.includes('深度分析') || content === t('reading.deep_analysis.request') || content === t('reading.deep_analysis.waiting')) {
          blockType = 'deep_analysis';
        } else if (content.includes('高维建议') || content === t('reading.advice.request')) {
          blockType = 'advice';
        }
        
        // 结束上一个区块
        currentBlock.endIndex = index - 1;
        blocks.push(currentBlock);
        
        // 开始新区块
        currentBlock = {
          startIndex: index,
          endIndex: index,
          type: blockType,
          messages: [message]
        };
      } 
      // 持续添加到当前区块
      else {
        currentBlock.messages.push(message);
        currentBlock.endIndex = index;
      }
      
      // 最后一条消息，结束当前区块
      if (index === messages.length - 1) {
        blocks.push(currentBlock);
      }
    });
    
    return blocks;
  }, [messages, t]);
  
  // 在组件内部添加辅助函数
  const getSegmentParaId = (messageId: string | undefined, index: number, blockType: string = 'base'): string => {
    // 如果没有messageId，生成一个带有板块类型前缀的ID
    if (!messageId) {
      const paraId = `${blockType}-para-${index}`;
      return paraId;
    }
    
    // 如果已经是para-x格式，但没有板块类型前缀，添加板块类型前缀
    if (messageId.startsWith('para-') && !messageId.includes('-para-')) {
      const newParaId = `${blockType}-${messageId}`;
      return newParaId;
    }
    
    // 如果已经有板块类型前缀，直接返回
    if (messageId.includes('-para-')) {
      return messageId;
    }
    
    // 从messageId中提取数字部分
    const numMatch = messageId.match(/(\d+)$/);
    if (numMatch) {
      const paraId = `${blockType}-para-${numMatch[1]}`;
      return paraId;
    }
    
    // 如果无法提取，使用原始索引作为段落ID，并添加板块类型前缀
    const fallbackId = `${blockType}-para-${index}`;
    return fallbackId;
  };

  // 修改提交表单处理函数，添加表情符号检测
  const handleSubmitFollowUp = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 检查是否包含表情符号
    const emojiRegex = /[\u{1F000}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/u;
    if (emojiRegex.test(followUpQuestion)) {
      setErrorMessage(t('home.validation.contains_emoji'));
      return;
    }
    
    // 清除错误消息并调用原始提交函数
    setErrorMessage('');
    onSubmitFollowUp(e);
  };

  return (
    <PlayingProvider>
      <motion.div 
        className={`flex-1 ${
          isDark 
            ? 'bg-gradient-to-b from-gray-800/40 to-gray-900/40' 
            : 'bg-[#F4F4F5]'
        } backdrop-blur-md rounded-xl sm:rounded-2xl overflow-hidden shadow-2xl ${
          isDark ? 'border border-purple-500/10' : 'border border-purple-300/30'
        }`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Header with reader info */}
        <div className={`px-4 sm:px-8 py-2 sm:py-3 ${
          isDark ? 'border-b border-gray-700/30' : 'border-b border-gray-300/50'
        } flex justify-center items-center relative`}>
          {reader && (
            <div className="text-center space-y-0">
              <h2 className={`text-lg font-bold ${isDark ? 'text-white' : 'text-gray-800'} ${getFontClass(i18n.language)}`}>
                {reader ? t(`reader.${reader.id}.name`) : t('reading.reader.ai_reader')}
              </h2>
              <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} ${getFontClass(i18n.language)}`}>
                {t(`reader.${reader.id}.type`)}
              </p>
            </div>
          )}

        </div>
        
        {/* Messages container */}
        <div 
          ref={messagesContainerRef}
          className="p-2 sm:p-4 md:p-5 lg:p-6 max-h-[50vh] sm:max-h-[60vh] overflow-y-auto scroll-smooth scrollbar-tarot"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: isDark 
              ? 'rgba(139, 92, 246, 0.3) rgba(17, 24, 39, 0.1)' 
              : 'rgba(139, 92, 246, 0.3) rgba(240, 240, 240, 0.1)'
          }}
        >
          <div className="space-y-4 sm:space-y-6">
            {/* 渲染分组后的板块内容 */}
            {messageBlocks.map((block, blockIndex) => {
              // 筛选出当前板块的消息
              const blockMessages = block.messages;
              
              return (
                <div key={`block-${blockIndex}`} className="space-y-4">
                  {/* 渲染当前板块中的消息 */}
                  {(() => {
                    let readerMessageIndex = 0;
                    
                    return blockMessages.map((message, index) => {
                      // 如果是AI消息，增加计数器
                      const currentReaderIndex = message.type === 'reader' ? readerMessageIndex++ : -1;
                      
                      // 设置边框样式
                      const borderStyle = message.type === 'reader'
                          ? isDark 
                            ? 'border-gray-700/30' 
                            : 'border-gray-300/50'
                          : '';
                      
                      // 渲染消息组件
                      const messageComponent = (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.4 }}
                          className={`max-w-3xl sm:max-w-3xl md:max-w-4xl ${
                            message.type === 'user'
                              ? 'ml-auto'
                              : ''
                          }`}
                        >
                          <div className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start items-start'}`}>
                            {message.type === 'user' && (
                              <div className={`rounded-2xl px-4 py-3 ${
                                message.type === 'user'
                                  ? 'bg-blue-600 border border-blue-600 rounded-tr-none'
                                  : isDark 
                                    ? 'bg-gray-800/50 border border-gray-700/30 rounded-tl-none'
                                    : 'bg-gray-200/60 border border-gray-300/50 rounded-tl-none'
                              }`}>
                                <div 
                                  className={`${isDark ? 'text-gray-200' : 'text-gray-800'} leading-relaxed flex flex-col ${getFontClass(i18n.language)}`}
                                  style={{color: 'white'}}
                                >
                                  {message.content}
                                </div>
                              </div>
                            )}
                            {message.type === 'reader' && !message.isParagraph && reader && (
                              <>
                                <CdnLazyImage
                                  src={`/images-optimized/readers/${reader.nameEn || 'Molly'}.webp`}
                                  alt={reader.nameEn || 'Molly'}
                                  className={`w-10 h-10 rounded-lg ${
                                    isDark ? 'border border-purple-500/30' : 'border border-purple-400/30'
                                  } object-cover mr-4 mt-1`}
                                />
                                <div className={`rounded-2xl px-4 sm:px-5 py-3 sm:py-4 ${
                                  isDark 
                                    ? 'bg-gray-800/50 border' 
                                    : 'bg-gray-200/60 border'
                                } ${borderStyle} rounded-tl-none transition-all duration-300`}
                                ref={message.type === 'reader' ? paragraphRefsMap[block.type][currentReaderIndex] : undefined}>
                                  <div className={`${isDark ? 'text-gray-200' : 'text-gray-800'} leading-relaxed flex flex-col ${message.className || getFontClass(i18n.language)}`}>
                                    {message.className?.includes('loading-message') ? (
                                      <div className="flex items-center space-x-1">
                                        <motion.div
                                          className="w-2 h-2 bg-purple-500 rounded-full"
                                          animate={{ opacity: [0.3, 1, 0.3] }}
                                          transition={{ duration: 1, repeat: Infinity, repeatType: 'loop' }}
                                        />
                                        <motion.div
                                          className="w-2 h-2 bg-purple-500 rounded-full"
                                          animate={{ opacity: [0.3, 1, 0.3] }}
                                          transition={{ duration: 1, repeat: Infinity, repeatType: 'loop', delay: 0.2 }}
                                        />
                                        <motion.div
                                          className="w-2 h-2 bg-purple-500 rounded-full"
                                          animate={{ opacity: [0.3, 1, 0.3] }}
                                          transition={{ duration: 1, repeat: Infinity, repeatType: 'loop', delay: 0.4 }}
                                        />
                                      </div>
                                    ) : (
                                      <div>
                                        <span>{message.content}</span>
                                        {initialReadingCompleted &&
                                         !message.className?.includes('loading-message') &&
                                         block.type !== 'deep_analysis' &&
                                         !actualEthicalIssue &&
                                         !isSharedView && (
                                          <span className="inline-flex align-text-bottom ml-1">
                                            <TarotSpeech 
                                              text={message.content}
                                              sessionId={sessionId}
                                              readerId={reader?.id}
                                              messageId={getSegmentParaId(message.id, currentReaderIndex, block.type)}
                                              blockType={block.type}
                                              paragraphRef={paragraphRefsMap[block.type][currentReaderIndex]}
                                              user={user}
                                              setShowVipPrompt={(show) => {
                                                if (show) onShowFeedback();
                                              }}
                                              language={i18n.language === 'zh-CN' || i18n.language === 'zh-TW' ? 'zh' : i18n.language}
                                            />
                                          </span>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </>
                            )}
                            {message.type === 'reader' && message.isParagraph && (
                              <>
                                {reader && (
                                  <CdnLazyImage
                                    src={`/images-optimized/readers/${reader.nameEn || 'Molly'}.webp`}
                                    alt={reader.nameEn || 'Molly'}
                                    className={`w-10 h-10 rounded-lg ${
                                      isDark ? 'border border-purple-500/30' : 'border border-purple-400/30'
                                    } object-cover mr-4 mt-1`}
                                  />
                                )}
                                <div className={`rounded-2xl px-4 sm:px-5 py-3 sm:py-4 ${
                                  isDark 
                                    ? 'bg-gray-800/50 border' 
                                    : 'bg-gray-200/60 border'
                                } ${borderStyle} rounded-tl-none transition-all duration-300`}
                                ref={message.type === 'reader' ? paragraphRefsMap[block.type][currentReaderIndex] : undefined}>
                                  <div className={`${isDark ? 'text-gray-200' : 'text-gray-800'} leading-relaxed flex flex-col ${message.className || getFontClass(i18n.language)}`}>
                                    {message.className?.includes('loading-message') ? (
                                      <div className="flex items-center space-x-1">
                                        <motion.div
                                          className="w-2 h-2 bg-purple-500 rounded-full"
                                          animate={{ opacity: [0.3, 1, 0.3] }}
                                          transition={{ duration: 1, repeat: Infinity, repeatType: 'loop' }}
                                        />
                                        <motion.div
                                          className="w-2 h-2 bg-purple-500 rounded-full"
                                          animate={{ opacity: [0.3, 1, 0.3] }}
                                          transition={{ duration: 1, repeat: Infinity, repeatType: 'loop', delay: 0.2 }}
                                        />
                                        <motion.div
                                          className="w-2 h-2 bg-purple-500 rounded-full"
                                          animate={{ opacity: [0.3, 1, 0.3] }}
                                          transition={{ duration: 1, repeat: Infinity, repeatType: 'loop', delay: 0.4 }}
                                        />
                                      </div>
                                    ) : (
                                      <div>
                                        <span>{message.content}</span>
                                        {initialReadingCompleted &&
                                         !message.className?.includes('loading-message') &&
                                         block.type !== 'deep_analysis' &&
                                         !actualEthicalIssue &&
                                         !isSharedView && (
                                          <span className="inline-flex align-text-bottom ml-1">
                                            <TarotSpeech 
                                              text={message.content}
                                              sessionId={sessionId}
                                              readerId={reader?.id}
                                              messageId={getSegmentParaId(message.id, currentReaderIndex, block.type)}
                                              blockType={block.type}
                                              paragraphRef={paragraphRefsMap[block.type][currentReaderIndex]}
                                              user={user}
                                              setShowVipPrompt={(show) => {
                                                if (show) onShowFeedback();
                                              }}
                                              language={i18n.language === 'zh-CN' || i18n.language === 'zh-TW' ? 'zh' : i18n.language}
                                            />
                                          </span>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </>
                            )}
                            {message.type === 'user' && (
                              <div className="w-10 h-10 rounded-lg border border-blue-600 bg-blue-600 flex items-center justify-center ml-4 mt-1">
                                <span className="text-sm font-medium text-white" style={{color: 'white'}}>
                                  {isSharedView ? 'U' : user?.username.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            )}
                          </div>
                        </motion.div>
                      );
                      
                      return messageComponent;
                    });
                  })()}
                  
                  {/* 添加注意文本，显示在板块末尾 */}
                  {initialReadingCompleted && 
                   !receivingStreamContent &&  
                   block.messages.length > 0 && 
                   block.messages.filter(m => m.type === 'reader').length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4 }}
                      className="w-full"
                    >
                      <div className="max-w-3xl sm:max-w-3xl md:max-w-4xl flex items-start">
                        {reader && (
                          <div className="w-10 h-10 flex-shrink-0 mr-4 mt-1"></div>
                        )}
                        <div className={`${
                          isDark 
                            ? 'text-red-500 font-semibold' 
                            : 'text-red-600 font-semibold'
                          } flex items-start`}>
                          <svg className={`h-5 w-5 mr-2 flex-shrink-0 mt-0.5 ${
                            isDark ? 'text-red-500' : 'text-red-600'
                          }`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path fillRule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 01.67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 11-.671-1.34l.041-.022zM12 9a.75.75 0 100-********* 0 000 1.5z" clipRule="evenodd" />
                          </svg>
                          <div>
                            {t('reading.disclaimer', '本回答由 AI 生成，内容仅供参考，请仔细甄别')}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                  
                  {/* 在板块末尾添加"聆听上述解读"按钮 */}
                  {initialReadingCompleted &&
                   !receivingStreamContent &&
                   block.messages.length > 0 &&
                   block.messages.filter(m => m.type === 'reader').length > 0 &&
                   block.type !== 'deep_analysis' &&
                   !actualEthicalIssue &&
                   !isSharedView && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.2 }}
                      className="w-full"
                    >
                      <TarotBlockSpeech
                        messages={block.messages.filter(m => m.type === 'reader')}
                        sessionId={sessionId}
                        readerId={reader?.id}
                        blockId={`${blockIndex}-${block.type}`}
                        paragraphRefs={paragraphRefsMap[block.type]}
                        user={user}
                        setShowVipPrompt={(show) => {
                          if (show) onShowFeedback();
                        }}
                        language={i18n.language === 'zh-CN' || i18n.language === 'zh-TW' ? 'zh' : i18n.language}
                      />
                    </motion.div>
                  )}
                </div>
              );
            })}

            {/* AI回复等待指示器 */}
            {isSubmitting && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="max-w-4xl"
              >
                <div className="flex justify-start items-start">
                  {reader && (
                    <CdnLazyImage
                      src={`/images-optimized/readers/${reader.nameEn || 'Molly'}.webp`}
                      alt={reader.nameEn || 'Molly'}
                      className={`w-10 h-10 rounded-lg ${
                        isDark ? 'border border-purple-500/30' : 'border border-purple-400/30'
                      } object-cover mr-4 mt-1`}
                    />
                  )}
                  <div className={`rounded-2xl px-4 sm:px-5 py-3 sm:py-4 ${
                    isDark 
                      ? 'bg-gray-800/50 border border-gray-700/30' 
                      : 'bg-gray-200/60 border border-gray-300/50'
                  } rounded-tl-none`}>
                    <div className="flex items-center space-x-1">
                      <motion.div
                        className="w-2 h-2 bg-purple-500 rounded-full"
                        animate={{ opacity: [0.3, 1, 0.3] }}
                        transition={{ duration: 1, repeat: Infinity, repeatType: 'loop' }}
                      />
                      <motion.div
                        className="w-2 h-2 bg-purple-500 rounded-full"
                        animate={{ opacity: [0.3, 1, 0.3] }}
                        transition={{ duration: 1, repeat: Infinity, repeatType: 'loop', delay: 0.2 }}
                      />
                      <motion.div
                        className="w-2 h-2 bg-purple-500 rounded-full"
                        animate={{ opacity: [0.3, 1, 0.3] }}
                        transition={{ duration: 1, repeat: Infinity, repeatType: 'loop', delay: 0.4 }}
                      />
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* 仅在深度解析过程中显示进度环，且确保不是在基础解读中显示 */}
            {deepAnalysisLoading && !receivingStreamContent && !progressBarCompleted && initialReadingCompleted && !useTarotProgressIsGenerating && (
              <div className="flex justify-center py-4">
                <TarotProgressCircle 
                  progress={deepAnalysisProgressValue} 
                  isGenerating={true} 
                />
              </div>
            )}
          </div>
        </div>

        {/* 功能按钮和追问输入框容器 */}
        <div className={`px-4 sm:px-8 py-2 sm:py-4 ${
          isDark 
            ? 'border-t border-gray-700/30 bg-gray-800/30' 
            : 'border-t border-gray-300/50 bg-gray-200/30'
        } space-y-2 sm:space-y-4`}>
          {/* 功能按钮 */}
          <div className="flex flex-row flex-wrap gap-2 justify-start items-center">
            {/* Deep Analysis Button - 隐藏按钮但保留代码 */}
            <button
              onClick={onDeepAnalysisClick}
              className={`hidden px-4 py-2 rounded-full flex items-center justify-center transition-all duration-300 text-xs sm:text-sm relative ${
                !initialReadingCompleted || deepAnalysisLoading || isSubmitting || showDeepAnalysis || actualEthicalIssue
                  ? 'bg-gray-700 opacity-50 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-500/30'
              }`}
              style={{color: 'white', display: 'none'}} /* 添加内联样式确保隐藏 */
              disabled={!initialReadingCompleted || deepAnalysisLoading || isSubmitting || showDeepAnalysis || actualEthicalIssue}
            >
              {!showDeepAnalysis && (user?.vipStatus === 'active' || hasUsedDeepAnalysis) && (
                <VipBadge className="scale-[0.7]" />
              )}
              {deepAnalysisLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span style={{color: 'white'}}>{t('reading.result.sending')}</span>
                </span>
              ) : (
                <span className="flex items-center justify-center w-full text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                  <span style={{color: 'white'}}>
                    {showDeepAnalysis 
                      ? t('reading.deep_analysis.completed') 
                      : user?.vipStatus === 'active' || !hasUsedDeepAnalysis 
                        ? (user?.vipStatus === 'active' 
                          ? t('reading.deep_analysis.deep_analysis')
                          : t('reading.deep_analysis.free_trial'))
                        : t('reading.deep_analysis.deep_analysis')}
                  </span>
                </span>
              )}
            </button>
            
            {/* 分享按钮 */}
            {!isSharedView && (
              <button
                onClick={onShowShare || (() => setShowShareDialog(true))}
                className={`flex px-4 py-2 rounded-full items-center justify-center transition-all duration-300 text-xs sm:text-sm ${
                  !initialReadingCompleted || deepAnalysisLoading || isSubmitting
                    ? 'bg-gray-700 opacity-50 cursor-not-allowed'
                    : hasReceivedShareReward
                      ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-500/30'
                      : 'bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-green-500/30'
                }`}
                style={{color: 'white'}}
                disabled={!initialReadingCompleted || deepAnalysisLoading || isSubmitting}
              >
                <span className="flex items-center text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                  </svg>
                  <span style={{color: 'white'}}>
                    {hasReceivedShareReward
                      ? t('share.pc_share', '分享')
                      : t('share.pc_share_reward', '首次分享得VIP')}
                  </span>
                </span>
              </button>
            )}

            {/* 评价按钮 */}
            {!isSharedView && (
              <button
              onClick={onShowComment || onShowFeedback}
              className={`flex px-4 py-2 rounded-full items-center justify-center transition-all duration-300 text-xs sm:text-sm ${
                !initialReadingCompleted || deepAnalysisLoading || isSubmitting
                  ? 'bg-gray-700 opacity-50 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-500/30'
              }`}
              style={{color: 'white'}}
              disabled={!initialReadingCompleted || deepAnalysisLoading || isSubmitting}
            >
              <span className="flex items-center text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
                <span style={{color: 'white'}}>{t('feedback.mobile_feedback', '评价')}</span>
              </span>
            </button>
            )}
          </div>

          {/* 追问输入框 */}
          {!isSharedView && (
            <form onSubmit={handleSubmitFollowUp}>
            {/* 错误消息显示区域 */}
            {errorMessage && (
              <div className="mb-2 px-4 py-2 rounded-lg bg-red-500/10 border border-red-500/20 text-red-500 text-sm">
                {errorMessage}
              </div>
            )}
            
            <div className="flex items-center gap-2">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={followUpQuestion}
                  onChange={(e) => setFollowUpQuestion(e.target.value.slice(0, 100))}
                  placeholder={
                    user?.vipStatus !== 'active' && !hasUsedFollowup
                      ? t('reading.result.free_followup_placeholder', '您获得一次免费追问机会')
                      : t('reading.result.follow_up_placeholder')
                  }
                  maxLength={100}
                  className={`w-full bg-gray-700/30 border rounded-full pl-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-0 transition-colors duration-300 ease-in-out ${getFontClass(i18n.language)} ${
                    user?.vipStatus !== 'active' && !hasUsedFollowup
                      ? 'border-purple-500 placeholder-purple-300 focus:border-purple-400'
                      : 'border-gray-600/30 focus:border-purple-500/50'
                  }`}
                  style={{
                    paddingRight: '80px'  /* 固定的右侧填充，为计数器留出空间 */
                  }}
                  disabled={!initialReadingCompleted || isSubmitting || deepAnalysisLoading || useTarotProgressIsGenerating || actualEthicalIssue}
                />
                <div 
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-xs min-w-[60px] text-right z-10" 
                  style={{ 
                    color: user?.vipStatus !== 'active' && !hasUsedFollowup ? '#ddd6fe' : '#9ca3af'
                  }}
                >
                  {followUpQuestion.length}/100
                </div>
              </div>

              {/* 移动端发送按钮 */}
              <button
                type="submit"
                disabled={!initialReadingCompleted || isSubmitting || !followUpQuestion.trim() || deepAnalysisLoading || useTarotProgressIsGenerating || actualEthicalIssue}
                className={`sm:hidden p-3 rounded-full flex items-center justify-center transition-all duration-300
                  ${!initialReadingCompleted || isSubmitting || !followUpQuestion.trim() || deepAnalysisLoading || useTarotProgressIsGenerating || actualEthicalIssue
                    ? 'bg-gray-700 opacity-50 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-500/30'}`}
              >
                {isSubmitting ? (
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                )}
              </button>

              {/* 桌面端发送按钮 */}
              <button
                type="submit"
                disabled={!initialReadingCompleted || isSubmitting || !followUpQuestion.trim() || deepAnalysisLoading || useTarotProgressIsGenerating || actualEthicalIssue}
                className={`hidden sm:flex px-6 py-3 rounded-full items-center justify-center transition-all duration-300
                  ${!initialReadingCompleted || isSubmitting || !followUpQuestion.trim() || deepAnalysisLoading || useTarotProgressIsGenerating || actualEthicalIssue
                    ? 'bg-gray-700 opacity-50 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-500/30'}`}
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="text-white" style={{color: 'white'}}>{t('reading.result.sending')}</span>
                  </span>
                ) : (
                  <span className="flex items-center text-white" style={{color: 'white'}}>
                    {t('reading.result.send')}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </span>
                )}
              </button>
            </div>
          </form>
          )}
        </div>
      </motion.div>

      {/* 分享对话框 */}
      <ShareDialog
        isOpen={showShareDialog}
        onClose={() => setShowShareDialog(false)}
        pageType="tarot-result"
        sessionId={sessionId}
        hasReceivedReward={hasReceivedShareReward}
      />
    </PlayingProvider>
  );
};

export default TarotReadingContent; 