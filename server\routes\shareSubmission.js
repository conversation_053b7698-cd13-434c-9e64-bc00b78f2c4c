const express = require('express');
const router = express.Router();
const { authenticateToken, isAdmin } = require('../middleware/auth');
const ShareRewardService = require('../services/shareRewardService');
const { asyncHandler } = require('../utils/asyncHandler');
const multer = require('multer');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database'); // 修正导入路径
const shareScreenshotService = require('../services/shareScreenshotService');
const EmailService = require('../services/emailService');
const { translate } = require('../i18n');

// 配置文件上传 - 使用持久化存储目录
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 使用分享截图服务获取存储目录
    const uploadDir = shareScreenshotService.getStorageDir();

    // 确保目录存在
    shareScreenshotService.ensureShareScreenshotDirExists();

    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + uuidv4();
    const ext = path.extname(file.originalname);
    cb(null, 'share-' + uniqueSuffix + ext);
  }
});

// 文件过滤器，只允许图片
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件'), false);
  }
};

const upload = multer({ 
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 限制5MB
  }
});

/**
 * @route POST /api/share-submission
 * @desc 创建分享提交
 * @access Private
 */
router.post('/', authenticateToken, upload.single('screenshot'), asyncHandler(async (req, res) => {
  const { shareUrl, platform, sessionId, language } = req.body;
  const userId = req.user.userId;
  
  // 验证必填字段
  if (!platform) {
    return res.status(400).json({ message: '请选择分享平台' });
  }
  
  // 验证必须上传截图
  if (!req.file) {
    return res.status(400).json({ message: '请上传分享截图' });
  }
  
  // 检查用户是否已经获得过奖励
  const hasReceivedReward = await ShareRewardService.hasUserReceivedReward(userId);
  
  // 构建图片URL（如果有上传图片）
  let imageUrl = '';
  if (req.file) {
    imageUrl = `/share-screenshots/${req.file.filename}`;
  }
  
  // 创建分享提交
  const submission = await ShareRewardService.createSubmission({
    userId,
    shareUrl: shareUrl || '', // 确保提供默认值
    imageUrl,
    platform,
    sessionId,
    language
  });
  
  res.status(201).json({
    message: '分享提交成功，等待审核',
    submission,
    hasReceivedReward
  });
}));

/**
 * @route GET /api/share-submission/user
 * @desc 获取当前用户的分享提交
 * @access Private
 */
router.get('/user', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.userId;
  const submissions = await ShareRewardService.getUserSubmissions(userId);
  const hasReceivedReward = await ShareRewardService.hasUserReceivedReward(userId);
  
  res.json({
    submissions,
    hasReceivedReward
  });
}));

/**
 * @route GET /api/share-submission/pending
 * @desc 获取待审核的分享提交
 * @access Admin
 */
router.get('/pending', authenticateToken, asyncHandler(async (req, res) => {
  // 硬编码管理员邮箱
  const adminEmails = ['<EMAIL>', '<EMAIL>'];
  
  // 检查用户是否为管理员
  if (!req.user.isAdmin && !(req.user.email && adminEmails.includes(req.user.email))) {
    return res.status(403).json({ message: '需要管理员权限才能访问此功能' });
  }
  
  const limit = parseInt(req.query.limit) || 10;
  const offset = parseInt(req.query.offset) || 0;
  
  const submissions = await ShareRewardService.getPendingSubmissions(limit, offset);
  
  // 获取总记录数
  const pool = await getConnection();
  const [countRows] = await pool.query(
    `SELECT COUNT(*) as total FROM share_submissions WHERE status = 'pending'`
  );
  const total = countRows[0].total || submissions.length;
  
  res.json({
    submissions,
    total
  });
}));

/**
 * @route PUT /api/share-submission/:id/review
 * @desc 审核分享提交
 * @access Admin
 */
router.put('/:id/review', authenticateToken, asyncHandler(async (req, res) => {
  // 硬编码管理员邮箱
  const adminEmails = ['<EMAIL>', '<EMAIL>'];

  // 检查用户是否为管理员
  if (!req.user.isAdmin && !(req.user.email && adminEmails.includes(req.user.email))) {
    return res.status(403).json({ message: '需要管理员权限才能访问此功能' });
  }

  const { id } = req.params;
  const { status, reviewNote } = req.body;
  const reviewerId = req.user.userId;

  // 验证状态
  if (!status || !['approved', 'rejected'].includes(status)) {
    return res.status(400).json({ message: '无效的审核状态' });
  }

  // 在审核前获取分享提交的详细信息，包括用户信息
  const pool = await getConnection();
  const [submissionRows] = await pool.query(
    `SELECT s.*, u.email, u.username
     FROM share_submissions s
     JOIN users u ON s.user_id = u.id
     WHERE s.id = ?`,
    [id]
  );

  if (submissionRows.length === 0) {
    return res.status(404).json({ message: '分享提交不存在' });
  }

  const submission = submissionRows[0];

  const updated = await ShareRewardService.reviewSubmission(id, status, reviewerId, reviewNote);

  if (updated) {
    // 审核成功后发送邮件通知
    try {
      await sendReviewNotificationEmail(submission, status, reviewNote || '');
    } catch (emailError) {
      console.error('发送审核通知邮件失败:', emailError);
      // 邮件发送失败不影响审核结果
    }

    res.json({
      message: status === 'approved' ? '审核通过，已自动发放奖励，邮件通知已发送' : '审核已拒绝，邮件通知已发送',
      status
    });
  } else {
    res.status(404).json({ message: '分享提交不存在或无法更新' });
  }
}));

/**
 * @route GET /api/share-submission/reward-status
 * @desc 检查用户是否已获得分享奖励
 * @access Private
 */
router.get('/reward-status', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.userId;
  const hasReceivedReward = await ShareRewardService.hasUserReceivedReward(userId);
  
  res.json({
    hasReceivedReward
  });
}));

/**
 * 发送审核通知邮件
 * @param {Object} submission 分享提交信息
 * @param {string} status 审核状态：'approved' 或 'rejected'
 * @param {string} reviewNote 审核备注
 */
async function sendReviewNotificationEmail(submission, status, reviewNote) {
  if (!submission.email) {
    console.log('用户邮箱为空，跳过邮件发送');
    return;
  }

  // 确定用户语言，使用分享时的语言，如果没有则默认为繁体中文
  const userLanguage = submission.language || 'zh-TW';

  // 创建特定语言的翻译函数
  const t = (key, params = {}) => translate(key, userLanguage, params);

  // 根据审核状态选择邮件模板
  const isApproved = status === 'approved';
  const subjectKey = isApproved ? 'auth.email.shareReviewApprovedSubject' : 'auth.email.shareReviewRejectedSubject';
  const titleKey = isApproved ? 'auth.email.shareReviewApprovedTitle' : 'auth.email.shareReviewRejectedTitle';
  const greetingKey = isApproved ? 'auth.email.shareReviewApprovedGreeting' : 'auth.email.shareReviewRejectedGreeting';
  const messageKey = isApproved ? 'auth.email.shareReviewApprovedMessage' : 'auth.email.shareReviewRejectedMessage';
  const noteKey = isApproved ? 'auth.email.shareReviewApprovedNote' : 'auth.email.shareReviewRejectedNote';

  // 构建邮件内容
  let emailContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;">
      <div style="background-color: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <h1 style="color: #9333ea; margin-bottom: 20px; text-align: center;">${t(titleKey)}</h1>
        <p style="color: #4b5563; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
          ${t(greetingKey)}
        </p>
        <p style="color: #4b5563; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
          ${t(messageKey)}
        </p>
  `;

  // 如果是审核通过，添加奖励信息
  if (isApproved) {
    emailContent += `
        <p style="color: #059669; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
          ${t('auth.email.shareReviewApprovedReward')}
        </p>
    `;
  } else {
    // 如果是审核拒绝，添加鼓励信息
    emailContent += `
        <p style="color: #4b5563; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
          ${t('auth.email.shareReviewRejectedEncouragement')}
        </p>
    `;
  }

  // 如果有审核备注，添加备注信息
  if (reviewNote && reviewNote.trim()) {
    emailContent += `
        <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #9333ea;">
          <p style="color: #374151; font-size: 14px; margin: 0;">
            ${t(noteKey, { note: reviewNote })}
          </p>
        </div>
    `;
  }

  emailContent += `
        <hr style="margin: 30px 0; border: 0; height: 1px; background-color: #e5e7eb;">
        <p style="color: #6b7280; font-size: 12px; text-align: center; margin: 0;">
          ${t('auth.email.footer')}
        </p>
      </div>
    </div>
  `;

  // 发送邮件
  await EmailService.sendMailWithRetry({
    to: submission.email,
    subject: t(subjectKey),
    html: emailContent
  });

  console.log(`审核通知邮件已发送至: ${submission.email}, 状态: ${status}, 语言: ${userLanguage}`);
}

module.exports = router;