import React, { useState, useRef, useEffect } from 'react';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';
import { horoscopeSigns } from '../../data/horoscopes';
import ZodiacSignItem from './ZodiacSignItem';
import { useLocation } from 'react-router-dom';
import HoroscopePreview from './HoroscopePreview';
import { AnimatePresence } from 'framer-motion';
import useHoroscopeCache from '../../hooks/useHoroscopeCache';

type HoroscopeType = 'daily' | 'weekly' | 'monthly' | 'yearly' | 'love';

interface ZodiacSignsGridProps {
  containerClassName?: string;
  onSignClick?: (signId: string) => void;
  defaultSelectedSign?: string;
  horoscopeType?: HoroscopeType;
  showPreview?: boolean; // 控制是否显示预览气泡
  fullPageType?: string; // 添加完整页面类型属性
  date?: Date; // 添加日期参数
}

/**
 * 星座选择网格组件，显示所有星座选项
 */
const ZodiacSignsGrid: React.FC<ZodiacSignsGridProps> = ({ 
  containerClassName = '', 
  onSignClick,
  defaultSelectedSign,
  horoscopeType = 'daily',
  showPreview = true, // 默认显示预览气泡
  fullPageType, // 完整的页面类型，包括next/last前缀
  date = new Date() // 默认为当前日期
}) => {
  const { navigate } = useLanguageNavigate();
  const location = useLocation();
  const [selectedSign, setSelectedSign] = useState<string | null>(defaultSelectedSign || null);
  const signsGridRef = useRef<HTMLDivElement>(null);
  const signRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const previewContainerRef = useRef<HTMLDivElement>(null);
  const [arrowPosition, setArrowPosition] = useState<number>(50);
  const [dataLoaded, setDataLoaded] = useState<boolean>(false);
  
  // 预加载所有星座数据
  const { loading, data } = useHoroscopeCache(horoscopeType);
  
  // 监控数据加载状态
  useEffect(() => {
    if (!loading && data) {
      setDataLoaded(true);
    }
  }, [loading, data, horoscopeType]);
  
  // 当defaultSelectedSign变化时更新内部状态
  useEffect(() => {
    if (defaultSelectedSign) {
      setSelectedSign(defaultSelectedSign);
      
      // 如果数据已加载且需要显示预览，则计算箭头位置
      if (dataLoaded && showPreview) {
        // 延迟一下计算位置，确保DOM已完全渲染
        setTimeout(() => {
          if (signsGridRef.current && signRefs.current[defaultSelectedSign] && previewContainerRef.current) {
            calculateArrowPosition(defaultSelectedSign);
          }
        }, 100);
      }
    }
  }, [defaultSelectedSign, dataLoaded, showPreview]);
  
  // 计算箭头位置
  useEffect(() => {
    if (selectedSign && showPreview && signsGridRef.current && signRefs.current[selectedSign] && previewContainerRef.current) {
      calculateArrowPosition(selectedSign);
    }
  }, [selectedSign, showPreview]);
  
  // 优化计算箭头位置的函数
  const calculateArrowPosition = (signId: string) => {
    if (!signsGridRef.current || !signRefs.current[signId] || !previewContainerRef.current) return;
    
    const signElement = signRefs.current[signId];
    const signRect = signElement.getBoundingClientRect();
    const previewRect = previewContainerRef.current.getBoundingClientRect();
    
    // 计算选中星座中心相对于视口的水平位置
    const signCenterX = signRect.left + (signRect.width / 2);
    
    // 计算预览容器相对于视口的水平位置
    const previewLeft = previewRect.left;
    
    // 计算箭头位置：(星座中心X坐标 - 预览容器左边界) / 预览容器宽度 * 100
    const position = ((signCenterX - previewLeft) / previewRect.width) * 100;
    
    // 确保位置在合理范围内 (5%-95%)，避免箭头太靠近边缘
    const safePosition = Math.max(5, Math.min(95, position));
    
    // 更新箭头位置
    setArrowPosition(safePosition);
  };
  
  // 当窗口大小改变时重新计算箭头位置
  useEffect(() => {
    if (!showPreview) return; // 如果不显示预览，则不需要监听resize事件
    
    const handleResize = () => {
      if (selectedSign) {
        // 重新触发计算逻辑
        const timer = setTimeout(() => {
          calculateArrowPosition(selectedSign);
        }, 100); // 添加延迟以确保DOM已更新
        
        return () => clearTimeout(timer);
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [selectedSign, showPreview]);
  
  const handleSignClick = (signId: string) => {
    // 如果传入了自定义点击处理函数，则调用它
    if (onSignClick) {
      onSignClick(signId);
      return;
    }
    
    // 如果显示预览，则更新选中状态
    if (showPreview) {
      if (selectedSign === signId) {
        setSelectedSign(null);
      } else {
        setSelectedSign(signId);
        // 延迟计算位置，确保DOM已更新
        setTimeout(() => {
          calculateArrowPosition(signId);
        }, 50);
      }
      return;
    }
    
    // 默认行为：根据当前路径判断导航目标
    const currentPath = location.pathname;
    
    // 使用完整的页面类型（如果提供）
    let targetPageType = fullPageType || horoscopeType;
    
    // 如果没有提供完整页面类型，尝试从URL中解析
    if (!fullPageType) {
      const pageTypePatterns = [
        { base: 'daily', variants: ['yesterday', 'tomorrow', 'daily'] },
        { base: 'weekly', variants: ['lastweek', 'nextweek', 'weekly'] },
        { base: 'monthly', variants: ['lastmonth', 'nextmonth', 'monthly'] },
        { base: 'yearly', variants: ['lastyear', 'nextyear', 'yearly'] },
        { base: 'love', variants: ['lastlove', 'nextlove', 'love'] }
      ];
      
      // 检查当前URL是否包含任何变体
      for (const pattern of pageTypePatterns) {
        for (const variant of pattern.variants) {
          if (currentPath.includes(`-${variant}-`)) {
            targetPageType = variant;
            break;
          }
        }
      }
    }
    
    // 导航到对应的星座页面，使用完整的页面类型
    navigate(`/horoscope/${signId}-${targetPageType}-horoscope`);
  };
  
  const handleReadMore = (signId: string) => {
    // 默认行为：根据当前路径判断导航目标
    const currentPath = location.pathname;
    
    // 使用完整的页面类型（如果提供）
    let targetPageType = fullPageType || horoscopeType;
    
    // 如果没有提供完整页面类型，尝试从URL中解析
    if (!fullPageType) {
      const pageTypePatterns = [
        { base: 'daily', variants: ['yesterday', 'tomorrow', 'daily'] },
        { base: 'weekly', variants: ['lastweek', 'nextweek', 'weekly'] },
        { base: 'monthly', variants: ['lastmonth', 'nextmonth', 'monthly'] },
        { base: 'yearly', variants: ['lastyear', 'nextyear', 'yearly'] },
        { base: 'love', variants: ['lastlove', 'nextlove', 'love'] }
      ];
      
      // 检查当前URL是否包含任何变体
      for (const pattern of pageTypePatterns) {
        for (const variant of pattern.variants) {
          if (currentPath.includes(`-${variant}-`)) {
            targetPageType = variant;
            break;
          }
        }
      }
    }
    
    // 导航到对应的星座页面，使用完整的页面类型
    navigate(`/horoscope/${signId}-${targetPageType}-horoscope`);
    
    // 关闭预览
    setSelectedSign(null);
  };
  
  return (
    <div className={`mb-12 ${containerClassName}`}>
      <div ref={signsGridRef} className="grid grid-cols-6 sm:flex sm:flex-nowrap gap-2 sm:justify-center pb-2">
        {horoscopeSigns.map((sign) => (
          <ZodiacSignItem
            key={sign.id}
            id={sign.id}
            nameKey={sign.nameKey}
            defaultName={sign.defaultName}
            iconPath={sign.iconPath}
            dateRange={sign.dateRange}
            onClick={handleSignClick}
            isSelected={selectedSign === sign.id}
            ref={(el: HTMLDivElement | null) => signRefs.current[sign.id] = el}
          />
        ))}
      </div>
      
      {showPreview && (
        <AnimatePresence>
          {selectedSign && (
            <div className="flex justify-center w-full">
              <div ref={previewContainerRef} className="w-full sm:w-auto sm:max-w-[90%] md:max-w-[80%] lg:max-w-[70%]">
                <HoroscopePreview 
                  signId={selectedSign} 
                  onReadMore={handleReadMore}
                  arrowPosition={arrowPosition}
                  type={horoscopeType}
                  pageType={fullPageType}
                  date={date}
                />
              </div>
            </div>
          )}
        </AnimatePresence>
      )}
    </div>
  );
};

export default ZodiacSignsGrid; 