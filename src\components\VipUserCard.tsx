import { <PERSON>, CardContent, <PERSON><PERSON>eader } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Mail, Calendar, Sparkles, Pencil, User2, Cake, FileText, ChevronRight, MapPin, Ticket, Clipboard<PERSON>ist, Check, X } from "lucide-react"
import { Link } from "react-router-dom"
import { VipBadge } from "./VipBadge"
import { useTranslation } from 'react-i18next'
import { useState, useRef, useEffect } from 'react'
import '../styles/fonts.css'
import { updateUserInfo, getCurrentUser } from '../services/userService'
import { useUser } from '../contexts/UserContext'
import { toast } from 'react-hot-toast'
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { redeemInvitationCode, checkPrivilegeStatus, checkIsSalesPerson } from '../services/invitationService'
import { DateInput } from './DateInput'
import { useTheme } from '../contexts/ThemeContext'
import { useLanguageNavigate } from '../hooks/useLanguageNavigate'
import "react-datepicker/dist/react-datepicker.css"
import '../pages/History.css'
import ShareSubmissionStatus from './ShareSubmissionStatus'
import { Share2 } from 'lucide-react'
import { getUserShareSubmissions } from '../services/shareService';

const profileCardStyles = `
  .profile-card {
    position: relative;
    background: #F4F4F5;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(236, 72, 153, 0.3);
    box-shadow: 
      0 0 0 1px rgba(168, 85, 247, 0.2),
      0 0 15px rgba(168, 85, 247, 0.15),
      0 0 30px rgba(236, 72, 153, 0.15),
      inset 0 0 15px rgba(168, 85, 247, 0.1);
  }
  .profile-card::before {
    content: '';
    position: absolute;
    inset: -1px;
    padding: 1px;
    background: linear-gradient(
      135deg,
      rgba(168, 85, 247, 0.5),
      rgba(236, 72, 153, 0.5)
    );
    -webkit-mask: 
      linear-gradient(#fff 0 0) content-box, 
      linear-gradient(#fff 0 0);
    mask: 
      linear-gradient(#fff 0 0) content-box, 
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }
`;

if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = profileCardStyles;
  document.head.appendChild(style);
}

interface VipUserCardProps {
  email: string
  isVip: boolean
  expiryDate: string
  name: string
  freeReadingsLeft: number
}

export default function VipUserCard({ email, isVip, expiryDate, name, freeReadingsLeft }: VipUserCardProps) {
  const { t, i18n } = useTranslation();
  const { user, setUser } = useUser();
  const { theme } = useTheme();
  const { navigate } = useLanguageNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(name);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editedGender, setEditedGender] = useState(user?.gender || '');
  const [editedBirthday, setEditedBirthday] = useState(user?.birthday || '');
  const [editedProfile, setEditedProfile] = useState(user?.userProfile || '');
  const [editedLocation, setEditedLocation] = useState(user?.location || '');
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [invitationCode, setInvitationCode] = useState('');
  const [isSubmittingCode, setIsSubmittingCode] = useState(false);
  const [hasPrivilege, setHasPrivilege] = useState(false);
  const [privilegeInfo, setPrivilegeInfo] = useState<any>(null);
  const [isSalesPerson, setIsSalesPerson] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  
  // 组件内部
  const [shareRewardReceived, setShareRewardReceived] = useState(false);

  // 组件挂载时获取最新用户数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const refreshedUser = await getCurrentUser();
        if (refreshedUser) {
          setUser(refreshedUser);
          localStorage.setItem('cachedUser', JSON.stringify(refreshedUser));
        }
        
        // 检查用户是否有内部权限
        const privilegeStatus = await checkPrivilegeStatus();
        setHasPrivilege(privilegeStatus.hasInternalPrivilege);
        setPrivilegeInfo(privilegeStatus.privilegeInfo);
        
        // 检查用户是否为销售人员
        if (refreshedUser) {
          try {
            const salesStatus = await checkIsSalesPerson(refreshedUser.id);
            setIsSalesPerson(salesStatus);
          } catch (error) {
            setIsSalesPerson(false);
          }
        }
        
        // 检查用户是否已获得分享奖励
        try {
          const shareResult = await getUserShareSubmissions();
          setShareRewardReceived(shareResult.hasReceivedReward);
        } catch (error) {
          console.error('获取分享奖励状态失败:', error);
        }
      } catch (error) {
      }
    };
    fetchUserData();
  }, []);

  // 监听user数据变化，更新编辑状态
  useEffect(() => {
    if (user) {
      setEditedGender(user.gender || '');
      // 处理生日日期
      if (user.birthday) {
        const date = new Date(user.birthday);
        if (!isNaN(date.getTime())) {
          setEditedBirthday(user.birthday.split('T')[0]);
        }
      } else {
        setEditedBirthday('');
      }
      setEditedProfile(user.userProfile || '');
      setEditedLocation(user.location || '');
    }
  }, [user]);

  useEffect(() => {
    setEditedName(name);
  }, [name]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isEditing && 
          inputRef.current && 
          buttonRef.current && 
          !inputRef.current.contains(event.target as Node) &&
          !buttonRef.current.contains(event.target as Node)) {
        handleEditClick();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isEditing]);

  // 根据主题获取toast样式
  const getToastStyle = (isError = false) => {
    return {
      marginTop: '4rem',
      fontFamily: 'var(--font-sans)',
      fontSize: '1rem',
      letterSpacing: '0.01em',
      width: '400px',
      maxWidth: '90vw',
      animation: 'none',
      backgroundColor: theme === 'dark' ? '#2a2a2a' : '#ffffff',
      color: isError 
        ? (theme === 'dark' ? '#ff6b6b' : '#e53e3e')
        : (theme === 'dark' ? '#ffffff' : '#333333'),
      border: theme === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.05)',
      boxShadow: theme === 'dark' 
        ? '0 4px 12px rgba(0, 0, 0, 0.5)' 
        : '0 4px 12px rgba(0, 0, 0, 0.05)'
    };
  };

  const handleEditClick = async () => {
    if (isEditing && user) {
      try {
        // 调用API更新用户名
        const updatedUser = await updateUserInfo(user.id, { username: editedName });
        // 更新本地状态

        // 更新本地状态
        setUser(updatedUser);

        // 更新localStorage中的缓存用户信息
        const cachedUser = localStorage.getItem('cachedUser');
        if (cachedUser) {
          const parsedUser = JSON.parse(cachedUser);
          parsedUser.username = editedName;
          localStorage.setItem('cachedUser', JSON.stringify(parsedUser));
        }

        // 立即刷新用户数据
        const refreshedUser = await getCurrentUser();
        setUser(refreshedUser);
        localStorage.setItem('cachedUser', JSON.stringify(refreshedUser));

        toast.success(t('profile.name_updated'), {
          position: 'top-center',
          style: getToastStyle()
        });
      } catch (error) {
        toast.error(t('profile.update_failed'), {
          position: 'top-center',
          style: getToastStyle(true)
        });
        // 恢复原来的用户名
        setEditedName(name);
      }
    }
    setIsEditing(!isEditing);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleEditClick();
    } else if (e.key === 'Escape') {
      setEditedName(name);
      setIsEditing(false);
    }
  };

  const handleFieldEdit = async (field: string, value: string) => {
    if (!user) return;
    
    try {
      const updateData = { [field]: value };
      
      const updatedUser = await updateUserInfo(user.id, updateData);
      if (updatedUser) {
        // 确保更新的用户数据包含所有必要字段
        const newUser = {
          ...user,
          ...updatedUser,
          [field]: value
        };
        
        setUser(newUser);
        localStorage.setItem('cachedUser', JSON.stringify(newUser));
        
        // 再次获取最新数据以确保同步
        const refreshedUser = await getCurrentUser();
        
        if (refreshedUser) {
          // 确保保留所有字段
          const finalUser = {
            ...newUser,
            ...refreshedUser
          };
          setUser(finalUser);
          localStorage.setItem('cachedUser', JSON.stringify(finalUser));
        }
        
        return newUser;
      }
    } catch (error) {
      toast.error(t('profile.update_failed'), {
          position: 'top-center',
          style: getToastStyle(true)
      });
      throw error;
    }
  };

  const handleSheetClose = () => {
    setIsSheetOpen(false);
    setEditingField(null);
  };

  const handleSave = async () => {
    if (!user || !editingField) return;
    
    let value = '';
    switch (editingField) {
      case 'gender':
        value = editedGender;
        break;
      case 'birthday':
        if (!editedBirthday) {
          toast.error(t('errors.select_birthday'), {
            position: 'top-center',
            style: getToastStyle(true)
          });
          return;
        }

        // 验证日期格式是否为 YYYY-MM-DD
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(editedBirthday)) {
          toast.error(t('errors.invalid_date_format'), {
            position: 'top-center',
            style: getToastStyle(true)
          });
          return;
        }

        const selectedDate = new Date(editedBirthday);
        const minDate = new Date('1900-01-01');
        const maxDate = new Date();

        // 验证日期是否有效
        if (isNaN(selectedDate.getTime()) || selectedDate.toISOString().split('T')[0] !== editedBirthday) {
          toast.error(t('errors.invalid_date'), {
            position: 'top-center',
            style: getToastStyle(true)
          });
          return;
        }

        // 重置时间部分，只比较日期
        selectedDate.setHours(0, 0, 0, 0);
        minDate.setHours(0, 0, 0, 0);
        maxDate.setHours(0, 0, 0, 0);

        if (selectedDate < minDate || selectedDate > maxDate) {
          toast.error(t('errors.invalid_date_range'), {
            position: 'top-center',
            style: getToastStyle(true)
          });
          return;
        }

        // 使用用户选择的原始日期字符串，避免时区转换
        value = editedBirthday;
        break;
      case 'userProfile':
        value = editedProfile;
        break;
      case 'location':
        value = editedLocation;
        break;
    }
    
    try {
      const updatedUser = await handleFieldEdit(editingField, value);
      
      if (updatedUser) {
        setUser(updatedUser);
        handleSheetClose();
        toast.success(t('profile.update_success'), {
          position: 'top-center',
          style: getToastStyle()
        });
      }
    } catch (error) {
      toast.error(t('profile.update_failed'), {
          position: 'top-center',
          style: getToastStyle(true)
      });
    }
  };

  // 格式化生日显示
  const formatBirthday = (birthday: string | null | undefined) => {
    if (!birthday) {
      return t('profile.not_set');
    }
    
    try {
      // 创建一个新的日期对象，并设置为用户时区的午夜时间
      const [year, month, day] = birthday.split('-').map(Number);
      const date = new Date(year, month - 1, day);
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return t('profile.not_set');
      }
      
      const locale = i18n.language;
      
      let formattedDate;
      switch(locale) {
        case 'ja':
          formattedDate = date.toLocaleDateString('ja-JP', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
          break;
        case 'zh-TW':
          formattedDate = date.toLocaleDateString('zh-TW', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
          break;
        case 'en':
          formattedDate = date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
          break;
        default:
          formattedDate = date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
      }
      
      return formattedDate;
    } catch (error) {
      return t('profile.not_set');
    }
  };

  const renderSheetContent = () => {
    const inputBgColor = theme === 'dark' ? 'bg-black/40' : 'bg-white/80';
    const inputTextColor = theme === 'dark' ? 'text-white' : 'text-gray-800';
    const inputBorderColor = theme === 'dark' ? 'border-gray-600' : 'border-gray-300';
    const placeholderColor = theme === 'dark' ? 'text-gray-400' : 'text-gray-500';
    const hoverBgColor = theme === 'dark' ? 'hover:bg-white/5' : 'hover:bg-gray-200/20';
    const isDark = theme === 'dark';

    switch (editingField) {
      case 'gender':
        return (
          <>
            <SheetHeader className="text-left border-b border-gray-300/20 pb-4">
              <SheetTitle>{t('profile.gender')}</SheetTitle>
            </SheetHeader>
            <div className="py-8">
              <RadioGroup
                value={editedGender}
                onValueChange={setEditedGender}
                className="flex flex-col space-y-6"
              >
                <Label
                  htmlFor="male"
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg ${hoverBgColor} transition-colors cursor-pointer`}
                >
                  <RadioGroupItem value="male" id="male" isDark={isDark} />
                  <span className={inputTextColor}>{t('profile.male')}</span>
                </Label>
                <Label
                  htmlFor="female"
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg ${hoverBgColor} transition-colors cursor-pointer`}
                >
                  <RadioGroupItem value="female" id="female" isDark={isDark} />
                  <span className={inputTextColor}>{t('profile.female')}</span>
                </Label>
                <Label
                  htmlFor="other"
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg ${hoverBgColor} transition-colors cursor-pointer`}
                >
                  <RadioGroupItem value="other" id="other" isDark={isDark} />
                  <span className={inputTextColor}>{t('profile.other')}</span>
                </Label>
              </RadioGroup>
            </div>
          </>
        );
      case 'birthday':
        return (
          <>
            <SheetHeader className="text-left border-b border-gray-300/20 pb-4">
              <SheetTitle>{t('profile.birthday')}</SheetTitle>
            </SheetHeader>
            <div className="py-6 flex-1">
              <div className="block sm:hidden w-full">
                <div className="relative">
                  <input
                    type="date"
                    value={editedBirthday}
                    onChange={(e) => {
                      const date = e.target.value ? e.target.value : '';
                      setEditedBirthday(date);
                    }}
                    className={`relative w-full h-[52px] px-4 ${inputBgColor} border ${inputBorderColor} rounded-xl ${inputTextColor}
                           focus:outline-none focus:border-purple-500/60 focus-visible:outline-none focus-visible:border-purple-500/60
                           focus-within:outline-none focus-within:border-purple-500/60
                           hover:border-purple-500/50 hover:shadow-[0_0_15px_rgba(168,85,247,0.15)]
                           transition-all duration-300 backdrop-blur-sm font-['Noto_Sans_SC']
                           text-base appearance-none`}
                  />
                  <div className={`absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none ${placeholderColor}`}>
                    {!editedBirthday && t('profile.select_birthday')}
                  </div>
                </div>
              </div>
              <div className="hidden sm:block">
                <DateInput
                  label={t('profile.select_birthday')}
                  value={editedBirthday}
                  onChange={(value) => setEditedBirthday(value)}
                />
              </div>
            </div>
          </>
        );
      case 'userProfile':
        return (
          <>
            <SheetHeader className="text-left border-b border-gray-300/20 pb-4">
              <SheetTitle>{t('profile.user_profile')}</SheetTitle>
            </SheetHeader>
            <div className="py-4 flex-1">
              <Textarea
                value={editedProfile}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setEditedProfile(e.target.value)}
                placeholder={t('profile.profile_placeholder')}
                className={`w-full text-base resize-none h-[180px] overflow-y-auto ${inputBgColor} border ${inputBorderColor} focus:border-purple-500 focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none transition-colors ${inputTextColor}`}
                autoFocus
              />
            </div>
          </>
        );
      case 'location':
        return (
          <>
            <SheetHeader className="text-left border-b border-gray-300/20 pb-4">
              <SheetTitle>{t('profile.location')}</SheetTitle>
            </SheetHeader>
            <div className="py-4 flex-1">
              <Input
                value={editedLocation}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditedLocation(e.target.value)}
                placeholder={t('profile.location_placeholder')}
                className={`w-full text-base ${inputBgColor} border ${inputBorderColor} focus:border-purple-500 focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none transition-colors ${inputTextColor}`}
                autoFocus
              />
            </div>
          </>
        );
      default:
        return null;
    }
  };

  // 在组件顶部添加调试
  useEffect(() => {
  }, [user]);

  const handleInvitationCodeSubmit = async () => {
    if (!invitationCode.trim()) {
      toast.error(t('invitation.code_empty'), {
        position: 'top-center',
        style: getToastStyle(true)
      });
      return;
    }

    setIsSubmittingCode(true);
    try {
      const result = await redeemInvitationCode(invitationCode);
      toast.success(result.message, {
        position: 'top-center',
        style: getToastStyle()
      });
      
      // 重新检查权限状态
      const privilegeStatus = await checkPrivilegeStatus();
      setHasPrivilege(privilegeStatus.hasInternalPrivilege);
      setPrivilegeInfo(privilegeStatus.privilegeInfo);
      
      // 清空输入框
      setInvitationCode('');
      
      // 刷新用户信息
      const refreshedUser = await getCurrentUser();
      setUser(refreshedUser);
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || '';
      // 判断错误消息是否为翻译键（不包含中文字符）
      const errorKey = /[\u4e00-\u9fa5]/.test(errorMessage) 
        ? 'invitation.redeem_failed' 
        : `invitation.${errorMessage}`;
      
      toast.error(t(errorKey), {
        position: 'top-center',
        style: getToastStyle(true)
      });
    } finally {
      setIsSubmittingCode(false);
    }
  };

  // 会员订阅链接处理
  const handleMembershipClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (hasPrivilege) {
      // 有内部权限，跳转到内部会员页面
      navigate('/membership-inter');
    } else {
      // 无内部权限，跳转到普通会员页面
      navigate('/membership');
    }
  };

  // 按钮样式根据主题
  const getButtonVariant = (isOutline = false) => {
    if (isOutline) {
      return theme === 'dark' 
        ? "border border-white/20 bg-[#2a2a2a] text-white hover:bg-white/10" 
        : "border border-gray-300 bg-white text-gray-700 hover:bg-gray-100";
    } else {
      return "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white";
    }
  };

  return (
    <>
      <Card className="w-full max-w-6xl shadow-xl overflow-hidden font-sans profile-card">
        <CardHeader className="pb-4 px-5 sm:px-6 pt-5 sm:pt-6">
          <div className="flex items-center gap-3">
            <Avatar className="w-14 h-14 sm:w-16 sm:h-16 border-2 border-yellow-500">
              <AvatarFallback className="text-xl font-bold bg-yellow-500 text-gray-900 font-sans">
                {editedName
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                {isEditing ? (
                  <input
                    ref={inputRef}
                    type="text"
                    value={editedName}
                    onChange={(e) => setEditedName(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="text-xl sm:text-2xl font-bold bg-transparent border-b border-gray-400/20 focus:border-gray-400/50 outline-none px-1 w-full max-w-[200px] text-gray-800"
                    autoFocus
                  />
                ) : (
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-800 font-sans">{editedName}</h2>
                )}
                {isEditing ? (
                  <div className="flex items-center">
                    <button
                      onClick={() => {
                        handleEditClick();
                      }}
                      className="p-1 hover:bg-gray-200/40 rounded-lg transition-colors mr-1"
                      title={t('common.save')}
                    >
                      <Check className="w-4 h-4 text-green-500 hover:text-green-600" />
                    </button>
                    <button
                      onClick={() => {
                        setEditedName(name);
                        setIsEditing(false);
                      }}
                      className="p-1 hover:bg-gray-200/40 rounded-lg transition-colors"
                      title={t('common.cancel')}
                    >
                      <X className="w-4 h-4 text-red-500 hover:text-red-600" />
                    </button>
                  </div>
                ) : (
                  <button
                    ref={buttonRef}
                    onClick={handleEditClick}
                    className="p-1 hover:bg-gray-200/40 rounded-lg transition-colors"
                    title={t('common.edit')}
                  >
                    <Pencil className="w-4 h-4 text-gray-500 hover:text-gray-800" />
                  </button>
                )}
                {isVip && <VipBadge variant="badge" className="scale-[0.65]" />}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-5 sm:px-6 pb-5 sm:pb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 左侧：用户基本信息 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between py-1.5 border-b border-gray-300/20 hover:bg-gray-200/20 transition-colors">
                <div className="text-gray-800 text-sm sm:text-base font-medium min-w-[100px]">{t('profile.email')}</div>
                <div className="flex items-center gap-2 max-w-[calc(100%-120px)]">
                  <Mail className="w-4 h-4 sm:w-5 sm:h-5 text-blue-400 flex-shrink-0" />
                  <span className="text-sm sm:text-base text-gray-600 truncate">{email}</span>
                </div>
              </div>

              <div className="flex items-center justify-between py-1.5 border-b border-gray-300/20 hover:bg-gray-200/20 transition-colors">
                <div className="text-gray-800 text-sm sm:text-base font-medium min-w-[100px]">{t('profile.vip_expiry')}</div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-purple-400 flex-shrink-0" />
                  <span className="text-sm sm:text-base text-gray-600">{expiryDate}</span>
                </div>
              </div>

              <div className="flex items-center justify-between py-1.5 border-b border-gray-300/20 hover:bg-gray-200/20 transition-colors">
                <div className="text-gray-800 text-sm sm:text-base font-medium min-w-[100px]">{t('profile.remaining_reads')}</div>
                <div className="flex items-center gap-2">
                  <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400 flex-shrink-0" />
                  <span className="text-sm sm:text-base text-gray-600">
                    {isVip ? '∞' : freeReadingsLeft}
                  </span>
                </div>
              </div>

              <div 
                className="flex items-center justify-between py-1.5 border-b border-gray-300/20 hover:bg-gray-200/20 cursor-pointer transition-colors"
                onClick={() => {
                  setEditingField('gender');
                  setIsSheetOpen(true);
                }}
              >
                <div className="text-gray-800 text-sm sm:text-base font-medium min-w-[100px]">{t('profile.gender')}</div>
                <div className="flex items-center gap-2">
                  <User2 className="w-4 h-4 sm:w-5 sm:h-5 text-green-400 flex-shrink-0" />
                  <span className="text-sm sm:text-base text-gray-600">
                    {user?.gender ? t(`profile.${user.gender}`) : t('profile.not_set')}
                  </span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // 防止触发父元素的onClick
                      setEditingField('gender');
                      setIsSheetOpen(true);
                    }}
                    className="p-1 hover:bg-gray-200/40 rounded-lg transition-colors"
                  >
                    <ChevronRight className="w-4 h-4 text-gray-500 hover:text-gray-800" />
                  </button>
                </div>
              </div>

              <div 
                className="flex items-center justify-between py-1.5 border-b border-gray-300/20 hover:bg-gray-200/20 cursor-pointer transition-colors"
                onClick={() => {
                  setEditedBirthday('');
                  setEditingField('birthday');
                  setIsSheetOpen(true);
                }}
              >
                <div className="text-gray-800 text-sm sm:text-base font-medium min-w-[100px]">{t('profile.birthday')}</div>
                <div className="flex items-center gap-2">
                  <Cake className="w-4 h-4 sm:w-5 sm:h-5 text-pink-400 flex-shrink-0" />
                  <span className="text-sm sm:text-base text-gray-600">
                    {formatBirthday(user?.birthday)}
                  </span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // 防止触发父元素的onClick
                      setEditedBirthday('');
                      setEditingField('birthday');
                      setIsSheetOpen(true);
                    }}
                    className="p-1 hover:bg-gray-200/40 rounded-lg transition-colors"
                  >
                    <ChevronRight className="w-4 h-4 text-gray-500 hover:text-gray-800" />
                  </button>
                </div>
              </div>

              <div 
                className="flex items-center justify-between py-1.5 border-b border-gray-300/20 hover:bg-gray-200/20 cursor-pointer transition-colors"
                onClick={() => {
                  setEditingField('location');
                  setIsSheetOpen(true);
                }}
              >
                <div className="text-gray-800 text-sm sm:text-base font-medium min-w-[100px]">{t('profile.location')}</div>
                <div className="flex items-center gap-2 max-w-[calc(100%-120px)]">
                  <MapPin className="w-4 h-4 sm:w-5 sm:h-5 text-orange-400 flex-shrink-0" />
                  <span className="text-sm sm:text-base text-gray-600 truncate">
                    {user?.location || t('profile.not_set')}
                  </span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // 防止触发父元素的onClick
                      setEditingField('location');
                      setIsSheetOpen(true);
                    }}
                    className="p-1 hover:bg-gray-200/40 rounded-lg transition-colors"
                  >
                    <ChevronRight className="w-4 h-4 text-gray-500 hover:text-gray-800" />
                  </button>
                </div>
              </div>

              <div 
                className="flex items-center justify-between py-1.5 hover:bg-gray-200/20 cursor-pointer transition-colors"
                onClick={() => {
                  setEditingField('userProfile');
                  setIsSheetOpen(true);
                }}
              >
                <div className="text-gray-800 text-sm sm:text-base font-medium min-w-[100px]">{t('profile.user_profile')}</div>
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-violet-400 flex-shrink-0" />
                  <span className="text-sm sm:text-base text-gray-600">
                    {user?.userProfile ? t('profile.already_set') : t('profile.not_set')}
                  </span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // 防止触发父元素的onClick
                      setEditingField('userProfile');
                      setIsSheetOpen(true);
                    }}
                    className="p-1 hover:bg-gray-200/40 rounded-lg transition-colors"
                  >
                    <ChevronRight className="w-4 h-4 text-gray-500 hover:text-gray-800" />
                  </button>
                </div>
              </div>
            </div>

            {/* 右侧：分享状态和邀请码 */}
            <div className="space-y-6">
              {/* 分享状态部分 */}
              {user?.id && (
                <div className="rounded-lg overflow-hidden bg-gradient-to-br from-white to-gray-100 dark:from-gray-800 dark:to-gray-900 shadow-sm border border-gray-200/50 dark:border-white/5">
                  <div className="flex items-center justify-between mb-3 px-4 pt-4 border-b border-gray-200/50 dark:border-gray-700/30 pb-2">
                    <div className="flex items-center">
                      <Share2 className={`w-5 h-5 mr-2 text-pink-500 dark:text-pink-400`} />
                      <h3 className={`font-medium text-base text-gray-800 dark:text-gray-100`}>
                        {t('share.my_submissions', '我的分享')}
                      </h3>
                    </div>
                    
                    {/* 已获得奖励标签 - 显示在标题同一行 */}
                    {shareRewardReceived && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        {t('share.reward_received', '已获得奖励')}
                      </span>
                    )}
                  </div>
                  
                  <ShareSubmissionStatus userId={user.id} />
                </div>
              )}

              {/* 邀请码管理链接（仅销售人员可见） */}
              {isSalesPerson && (
                <div className="mt-4">
                  <Link to="/sales/invitation-management" className="flex items-center gap-2 py-3 px-4 bg-gradient-to-r from-white to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-lg border border-gray-200/50 dark:border-white/5 shadow-sm transition-all hover:shadow">
                    <ClipboardList size={18} className="text-pink-500 dark:text-pink-400" />
                    <span className="text-sm sm:text-base font-medium text-gray-800 dark:text-gray-100">
                      {t('nav.invitation_management')}
                    </span>
                    <ChevronRight className="w-4 h-4 text-gray-500 ml-auto" />
                  </Link>
                </div>
              )}

              {/* 邀请码输入或显示部分 */}
              <div className="rounded-lg overflow-hidden bg-gradient-to-br from-white to-gray-100 dark:from-gray-800 dark:to-gray-900 shadow-sm border border-gray-200/50 dark:border-white/5">
                <div className="flex items-center mb-3 px-4 pt-4 border-b border-gray-200/50 dark:border-gray-700/30 pb-2">
                  <Ticket className={`w-5 h-5 mr-2 text-pink-500 dark:text-pink-400`} />
                  <h3 className={`font-medium text-base text-gray-800 dark:text-gray-100`}>
                    {t('invitation.redeem_title', '邀请码兑换')}
                  </h3>
                </div>
                
                <div className="px-4 pb-4">
                  {hasPrivilege ? (
                    <div className="py-2">
                      <div className="flex items-center gap-2 text-gray-800 dark:text-gray-200 mb-2">
                        <span className="text-sm sm:text-base">{t('invitation.internal_status')}</span>
                      </div>
                      {privilegeInfo && (
                        <div className="text-xs space-y-1 text-gray-700 dark:text-gray-300">
                          <p>{t('invitation.code_used')}: {privilegeInfo.code}</p>
                          {privilegeInfo.sales_person_name && (
                            <p>{t('invitation.sales_person')}: {privilegeInfo.sales_person_name}</p>
                          )}
                          <p>
                            {t('invitation.granted_at')}: {
                              new Date(privilegeInfo.privilege_granted_at).toLocaleDateString(
                                i18n.language === 'en' ? 'en-US' : 'zh-CN',
                                { year: 'numeric', month: 'long', day: 'numeric' }
                              )
                            }
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="py-2">
                      <div className="flex items-center gap-2 text-gray-800 dark:text-gray-200 mb-2">
                        <span className="text-sm">{t('invitation.redeem_description', '输入邀请码获取特殊权限')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input
                          value={invitationCode}
                          onChange={(e) => setInvitationCode(e.target.value)}
                          placeholder={t('invitation.code_placeholder')}
                          className="h-8 bg-white/70 dark:bg-gray-900/50 border-gray-300 dark:border-gray-700 focus:border-pink-400"
                        />
                        <Button 
                          onClick={handleInvitationCodeSubmit}
                          disabled={isSubmittingCode || !invitationCode.trim()}
                          className={`h-8 px-3 ${getButtonVariant()}`}
                        >
                          {isSubmittingCode ? t('common.submitting') : t('invitation.redeem')}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-6 text-center px-3 sm:px-6 pb-1 sm:pb-2">
            <p className="text-gray-500 text-xs leading-relaxed">
              {t('profile.privacy_tip', {
                defaultValue: '完善个人信息有助于我们为您提供更专业、更准确的塔罗解读服务。您的所有信息都将被严格保密。'
              })}
            </p>
          </div>
          
          <div className="mt-4 flex justify-center">
            <Link
              to="/membership"
              onClick={handleMembershipClick}
              className={`flex items-center justify-center gap-2 w-fit mx-auto ${getButtonVariant()} font-medium py-1.5 sm:py-2 px-3 sm:px-4 rounded-lg transition-colors duration-200 text-sm shadow-lg`}
            >
              <Sparkles className="w-4 h-4 text-white" />
              <span style={{color: 'white'}}>{isVip ? t('profile.vip.manage') : t('profile.vip.upgrade')}</span>
            </Link>
          </div>
        </CardContent>
      </Card>

      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetContent 
          side="bottom" 
          className="px-6"
          style={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            height: 'auto',
            maxHeight: '80vh',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            paddingBottom: 'env(safe-area-inset-bottom)',
            backgroundColor: theme === 'dark' ? '#1a1a1a' : '#F4F4F5',
            color: theme === 'dark' ? '#ffffff' : '#333333'
          }}
        >
          <div className="flex-1 overflow-hidden">
            {renderSheetContent()}
          </div>
          <div className="flex-shrink-0 pt-2 pb-4 px-6" style={{ backgroundColor: theme === 'dark' ? '#1a1a1a' : '#F4F4F5' }}>
            <div className="flex justify-end gap-4">
              <Button 
                variant="outline" 
                onClick={handleSheetClose}
                className={`min-w-[100px] ${theme === 'dark' ? '' : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-100'}`}
              >
                {t('common.cancel')}
              </Button>
              <Button 
                onClick={handleSave}
                className={`min-w-[100px] ${theme === 'dark' ? '' : 'bg-purple-600 hover:bg-purple-700'}`}
              >
                {t('common.save')}
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
