/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

import {
  MediaType,
  IRTCEngine,
  onUserJoinedEvent,
  onUserLeaveEvent,
  LocalStreamStats,
  RemoteStreamStats,
  StreamRemoveReason,
  LocalAudioPropertiesInfo,
  RemoteAudioPropertiesInfo,
  DeviceInfo,
  AutoPlayFailedEvent,
  PlayerEvent,
  NetworkQuality,
} from '@volcengine/rtc';
import { COMMAND, INTERRUPT_PRIORITY } from '@/utils/handler';

export interface IEventListener {
  handleError: (e: { errorCode: any }) => void;
  handleUserJoin: (e: onUserJoinedEvent) => void;
  handleUserLeave: (e: onUserLeaveEvent) => void;
  handleTrackEnded: (e: { kind: string; isScreen: boolean }) => void;
  handleUserPublishStream: (e: { userId: string; mediaType: MediaType }) => void;
  handleUserUnpublishStream: (e: {
    userId: string;
    mediaType: MediaType;
    reason: StreamRemoveReason;
  }) => void;
  handleRemoteStreamStats: (e: RemoteStreamStats) => void;
  handleLocalStreamStats: (e: LocalStreamStats) => void;
  handleLocalAudioPropertiesReport: (e: LocalAudioPropertiesInfo[]) => void;
  handleRemoteAudioPropertiesReport: (e: RemoteAudioPropertiesInfo[]) => void;
  handleAudioDeviceStateChanged: (e: DeviceInfo) => void;
  handleAutoPlayFail: (e: AutoPlayFailedEvent) => void;
  handlePlayerEvent: (e: PlayerEvent) => void;
  handleRoomBinaryMessageReceived: (e: { userId: string; message: ArrayBuffer }) => void;
  handleNetworkQuality: (
    uplinkNetworkQuality: NetworkQuality,
    downlinkNetworkQuality: NetworkQuality
  ) => void;
}

export interface BasicBody {
  app_id: string;
  room_id: string;
  user_id: string;
  token?: string;
}

/**
 * @brief RTC Core Client
 * @notes Refer to official website documentation to get more information about the API.
 */
export class RTCClient {
  engine!: IRTCEngine;

  basicInfo!: BasicBody;

  private _audioCaptureDevice?: string;

  private _videoCaptureDevice?: string;

  audioBotEnabled = false;

  audioBotStartTime = 0;

  private _audioStream: MediaStream | null = null;

  private _audioContext: AudioContext | null = null;

  private _analyser: AnalyserNode | null = null;

  private _audioDataArray: Uint8Array | null = null;

  private _audioVisualizationInterval: number | null = null;

  // 新增：音频电平回调
  private _audioLevelCallback: ((level: number) => void) | null = null;

  createEngine = async () => {
    console.log('RTC引擎创建已被模拟，不会实际创建RTC引擎');
    // 创建一个模拟的engine对象，包含必要的方法
    this.engine = {
      on: () => {},
      joinRoom: () => Promise.resolve(),
      leaveRoom: () => Promise.resolve(),
      setLocalVideoPlayer: () => {},
      // 其他可能需要的方法
    } as unknown as IRTCEngine;
    return Promise.resolve();
  };

  addEventListeners = ({
    handleError,
    handleUserJoin,
    handleUserLeave,
    handleTrackEnded,
    handleUserPublishStream,
    handleUserUnpublishStream,
    handleRemoteStreamStats,
    handleLocalStreamStats,
    handleLocalAudioPropertiesReport,
    handleRemoteAudioPropertiesReport,
    handleAudioDeviceStateChanged,
    handleAutoPlayFail,
    handlePlayerEvent,
    handleRoomBinaryMessageReceived,
    handleNetworkQuality,
  }: IEventListener) => {
    console.log('事件监听已被模拟，不会实际添加事件监听器');
    // 不再实际添加事件监听器
  };

  joinRoom = () => {
    console.log(' ------ userJoinRoom\n', `roomId: ${this.basicInfo.room_id}\n`, `uid: ${this.basicInfo.user_id}`);
    console.log('RTC连接已被模拟，不会实际连接到服务器');
    // 返回一个成功的Promise，不再实际调用RTC连接
    return Promise.resolve();
  };

  leaveRoom = () => {
    this.audioBotEnabled = false;
    console.log('RTC离开房间已被模拟，不会实际调用RTC SDK');
    // 不再调用实际的RTC方法
    this._audioCaptureDevice = undefined;
    return Promise.resolve();
  };

  checkPermission(): Promise<{
    video: boolean;
    audio: boolean;
  }> {
    return new Promise((resolve) => {
      const checkPermissions = async () => {
        try {
          let audioPermission = false;
          let videoPermission = false;
          
          // 检查音频权限
          try {
            await navigator.mediaDevices.getUserMedia({ audio: true });
            audioPermission = true;
            console.log('已获取麦克风权限');
          } catch (e) {
            console.warn('麦克风权限检查失败:', e);
          }
          
          // 检查视频权限
          try {
            await navigator.mediaDevices.getUserMedia({ video: true });
            videoPermission = true;
            console.log('已获取摄像头权限');
          } catch (e) {
            console.warn('摄像头权限检查失败:', e);
          }
          
          resolve({
            video: videoPermission,
            audio: audioPermission
          });
        } catch (error) {
          console.error('权限检查出错:', error);
          // 出错时返回默认值
          resolve({
            video: false,
            audio: false
          });
        }
      };
      
      // 执行权限检查
      checkPermissions();
    });
  }

  /**
   * @brief get the devices
   * @returns
   */
  async getDevices(props?: { video?: boolean; audio?: boolean }): Promise<{
    audioInputs: MediaDeviceInfo[];
    audioOutputs: MediaDeviceInfo[];
    videoInputs: MediaDeviceInfo[];
  }> {
    const { video = false, audio = true } = props || {};
    let audioInputs: MediaDeviceInfo[] = [];
    let audioOutputs: MediaDeviceInfo[] = [];
    let videoInputs: MediaDeviceInfo[] = [];
    
    try {
      // 使用浏览器原生API检测真实设备
      if (navigator.mediaDevices) {
        // 请求设备权限
        if (audio) {
          try {
            await navigator.mediaDevices.getUserMedia({ audio: true });
          } catch (e) {
            console.warn('无法获取麦克风权限', e);
          }
        }
        if (video) {
          try {
            await navigator.mediaDevices.getUserMedia({ video: true });
          } catch (e) {
            console.warn('无法获取摄像头权限', e);
          }
        }
        
        // 获取设备列表
        const devices = await navigator.mediaDevices.enumerateDevices();
        
        audioInputs = devices.filter(device => device.kind === 'audioinput');
        audioOutputs = devices.filter(device => device.kind === 'audiooutput');
        videoInputs = devices.filter(device => device.kind === 'videoinput');
        
        this._audioCaptureDevice = audioInputs[0]?.deviceId;
        this._videoCaptureDevice = videoInputs[0]?.deviceId;
        
        console.log('检测到音频输入设备:', audioInputs.length);
        console.log('检测到音频输出设备:', audioOutputs.length);
        console.log('检测到视频输入设备:', videoInputs.length);
      }
    } catch (error) {
      console.error('获取设备列表失败:', error);
      // 如果失败则使用模拟数据作为备选
      return {
        audioInputs: [{
          deviceId: 'mock-audio-input',
          groupId: '',
          kind: 'audioinput',
          label: '模拟麦克风',
          toJSON: () => ({})
        } as unknown as MediaDeviceInfo],
        audioOutputs: [{
          deviceId: 'mock-audio-output',
          groupId: '',
          kind: 'audiooutput',
          label: '模拟扬声器',
          toJSON: () => ({})
        } as unknown as MediaDeviceInfo],
        videoInputs: [{
          deviceId: 'mock-video-input',
          groupId: '',
          kind: 'videoinput',
          label: '模拟摄像头',
          toJSON: () => ({})
        } as unknown as MediaDeviceInfo]
      };
    }

    return {
      audioInputs,
      audioOutputs,
      videoInputs
    };
  }

  startVideoCapture = async (camera?: string) => {
    console.log('视频捕获已被模拟');
    return Promise.resolve();
  };

  stopVideoCapture = async () => {
    console.log('停止视频捕获已被模拟');
    return Promise.resolve();
  };

  startScreenCapture = async (enableAudio = false) => {
    console.log('屏幕共享已被模拟');
    return Promise.resolve();
  };

  stopScreenCapture = async () => {
    console.log('停止屏幕共享已被模拟');
    return Promise.resolve();
  };

  /**
   * 设置音频电平回调函数
   * @param callback 回调函数，参数为0-100的音频电平值
   */
  setAudioLevelCallback(callback: ((level: number) => void) | null) {
    this._audioLevelCallback = callback;
    
    // 如果设置了回调但还没有开始分析，则开始分析
    if (callback && this._audioStream && !this._audioVisualizationInterval) {
      this._startAudioAnalysis();
    }
    
    // 如果取消了回调且正在分析，则停止分析
    if (!callback && this._audioVisualizationInterval) {
      this._stopAudioAnalysis();
    }
  }

  /**
   * 开始音频分析
   */
  private _startAudioAnalysis() {
    if (!this._audioStream) return;
    
    try {
      // 创建音频上下文和分析器
      if (!this._audioContext) {
        this._audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      }
      
      if (!this._analyser) {
        this._analyser = this._audioContext.createAnalyser();
        this._analyser.fftSize = 256;
        const bufferLength = this._analyser.frequencyBinCount;
        this._audioDataArray = new Uint8Array(bufferLength);
      }
      
      // 连接音频源到分析器
      const source = this._audioContext.createMediaStreamSource(this._audioStream);
      source.connect(this._analyser);
      
      // 定期获取音频电平
      this._audioVisualizationInterval = window.setInterval(() => {
        if (!this._analyser || !this._audioDataArray || !this._audioLevelCallback) {
          this._stopAudioAnalysis();
          return;
        }
        
        this._analyser.getByteFrequencyData(this._audioDataArray);
        
        // 计算音频电平 (0-100)
        let sum = 0;
        for (let i = 0; i < this._audioDataArray.length; i++) {
          sum += this._audioDataArray[i];
        }
        const average = sum / this._audioDataArray.length;
        const level = Math.min(100, Math.round(average * 100 / 256));
        
        // 调用回调函数
        this._audioLevelCallback(level);
      }, 100); // 每100毫秒更新一次
      
      console.log('音频分析已启动');
    } catch (error) {
      console.error('启动音频分析失败:', error);
    }
  }

  /**
   * 停止音频分析
   */
  private _stopAudioAnalysis() {
    if (this._audioVisualizationInterval) {
      clearInterval(this._audioVisualizationInterval);
      this._audioVisualizationInterval = null;
    }
    
    // 如果存在回调，通知音频电平为0
    if (this._audioLevelCallback) {
      this._audioLevelCallback(0);
    }
    
    console.log('音频分析已停止');
  }

  startAudioCapture = async (mic?: string) => {
    console.log('开始音频捕获', mic || this._audioCaptureDevice);
    try {
      // 保存流的引用以便后续关闭
      if (!this._audioStream) {
        const constraints = {
          audio: mic ? { deviceId: { exact: mic } } : true
        };
        this._audioStream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('音频捕获已启动', this._audioStream);
        
        // 如果已设置回调，则开始音频分析
        if (this._audioLevelCallback) {
          this._startAudioAnalysis();
        }
      }
      return Promise.resolve();
    } catch (error) {
      console.error('音频捕获失败:', error);
      return Promise.resolve(); // 保持接口一致性，即使失败也返回resolved promise
    }
  };

  stopAudioCapture = async () => {
    console.log('停止音频捕获');
    try {
      // 先停止音频分析
      this._stopAudioAnalysis();
      
      if (this._audioStream) {
        this._audioStream.getTracks().forEach(track => {
          track.stop();
        });
        this._audioStream = null;
        console.log('音频捕获已停止');
      }
    } catch (error) {
      console.error('停止音频捕获失败:', error);
    }
    return Promise.resolve();
  };

  publishStream = (mediaType: MediaType) => {
    console.log('发布流已被模拟');
    return Promise.resolve();
  };

  unpublishStream = (mediaType: MediaType) => {
    console.log('取消发布流已被模拟');
    return Promise.resolve();
  };

  publishScreenStream = async (mediaType: MediaType) => {
    console.log('发布屏幕流已被模拟');
    return Promise.resolve();
  };

  unpublishScreenStream = async (mediaType: MediaType) => {
    console.log('取消发布屏幕流已被模拟');
    return Promise.resolve();
  };

  setScreenEncoderConfig = async (description: any) => {
    console.log('设置屏幕编码配置已被模拟');
    return Promise.resolve();
  };

  /**
   * @brief 设置业务标识参数
   * @param businessId
   */
  setBusinessId = (businessId: string) => {
    console.log('设置业务ID已被模拟');
  };

  setAudioVolume = (volume: number) => {
    console.log('设置音量已被模拟');
  };

  /**
   * @brief 设置音质档位
   */
  setAudioProfile = (profile: any) => {
    console.log('设置音频配置已被模拟');
  };

  /**
   * @brief 切换设备
   */
  switchDevice = async (deviceType: MediaType, deviceId: string) => {
    console.log('切换设备', deviceType, deviceId);
    
    try {
      if (deviceType === MediaType.AUDIO) {
        this._audioCaptureDevice = deviceId;
        
        // 如果当前有活跃的音频流，重新启动以使用新设备
        if (this._audioStream) {
          // 先停止当前流
          this._audioStream.getTracks().forEach(track => {
            track.stop();
          });
          this._audioStream = null;
          
          // 使用新设备启动
          await this.startAudioCapture(deviceId);
        }
      }
      
      if (deviceType === MediaType.VIDEO) {
        this._videoCaptureDevice = deviceId;
        // 视频设备切换逻辑可以按需实现
      }
      
      if (deviceType === MediaType.AUDIO_AND_VIDEO) {
        this._audioCaptureDevice = deviceId;
        this._videoCaptureDevice = deviceId;
        // 同时切换音视频设备的逻辑可以按需实现
      }
    } catch (error) {
      console.error('切换设备失败:', error);
    }
  };

  setLocalVideoMirrorType = (type: any) => {
    console.log('设置本地视频镜像类型已被模拟');
    return Promise.resolve();
  };

  setLocalVideoPlayer = (
    userId: string,
    renderDom?: string | HTMLElement,
    isScreenShare = false,
    renderMode = 1
  ) => {
    console.log('设置本地视频播放器已被模拟');
    return Promise.resolve();
  };

  /**
   * @brief 移除播放器
   */
  removeVideoPlayer = (userId: string, scope: any = 'Both') => {
    console.log('移除视频播放器已被模拟');
  };

  /**
   * @brief 启用 AIGC
   */
  startAgent = async (scene: string) => {
    if (this.audioBotEnabled) {
      await this.stopAgent(scene);
    }
    
    try {
      // 使用voiceApi启动语音会话
      console.log('正在启动AIGC服务...');
      
      // 导入voiceApi
      const voiceApi = (await import('../app/voiceApi')).default;
      
      // 使用当前用户ID启动会话
      const result = await voiceApi.startSession();
      if (result.success) {
        await voiceApi.connect();
        await voiceApi.joinSession();
        
        console.log('AIGC服务已启动');
        this.audioBotEnabled = true;
        this.audioBotStartTime = Date.now();
      } else {
        console.error('启动AIGC服务失败:', result.message);
      }
    } catch (error) {
      console.error('启动AIGC服务出错:', error);
      this.audioBotEnabled = false;
    }
  };

  /**
   * @brief 关闭 AIGC
   */
  stopAgent = async (scene: string) => {
    if (this.audioBotEnabled || sessionStorage.getItem('audioBotEnabled')) {
      try {
        // 使用voiceApi停止语音会话
        console.log('正在停止AIGC服务...');
        
        // 导入voiceApi
        const voiceApi = (await import('../app/voiceApi')).default;
        
        // 停止会话
        await voiceApi.stopSession();
        
        console.log('AIGC服务已停止');
        this.audioBotStartTime = 0;
        sessionStorage.removeItem('audioBotEnabled');
      } catch (error) {
        console.error('停止AIGC服务出错:', error);
      }
    }
    this.audioBotEnabled = false;
  };

  /**
   * @brief 命令 AIGC
   */
  commandAgent = ({
    command,
    agentName,
    interruptMode = INTERRUPT_PRIORITY.NONE,
    message = '',
  }: {
    command: COMMAND;
    agentName: string;
    interruptMode?: INTERRUPT_PRIORITY;
    message?: string;
  }) => {
    try {
      if (!this.audioBotEnabled) {
        console.warn('AIGC服务未启动，无法发送命令');
        return;
      }
      
      console.log('发送AIGC命令:', { command, agentName, interruptMode, message });
      
      // 这里可以根据command和message实现不同的命令处理逻辑
      // 如果有消息文本，可以通过voiceApi发送
      if (command === COMMAND.EXTERNAL_TEXT_TO_LLM && message && message.length > 0) {
        (async () => {
          const voiceApi = (await import('../app/voiceApi')).default;
          // 这里应该实现从文本到音频的转换，然后发送到后端
          // 这只是一个示例，实际实现可能会更复杂
          console.log('发送文本消息到AIGC:', message);
        })();
      }
    } catch (error) {
      console.error('发送AIGC命令出错:', error);
    }
  };

  /**
   * @brief 更新 AIGC 配置
   */
  updateAgent = async (scene: string) => {
    if (this.audioBotEnabled) {
      await this.stopAgent(scene);
      await this.startAgent(scene);
    } else {
      await this.startAgent(scene);
    }
  };

  /**
   * @brief 获取当前 AI 是否启用
   */
  getAgentEnabled = () => {
    return this.audioBotEnabled;
  };
}

export default new RTCClient();
