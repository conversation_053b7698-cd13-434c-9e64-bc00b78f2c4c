/**
 * 管理员路由中间件
 * 提供统一的管理员路由认证和授权
 */

const { authenticateToken } = require('./auth');

/**
 * 管理员认证中间件
 * 验证用户是否已登录并具有管理员权限
 */
const authenticateAdmin = (req, res, next) => {
  // 先验证用户是否已登录
  authenticateToken(req, res, (err) => {
    if (err) {
      return next(err);
    }
    
    // 验证用户是否有管理员权限
    if (!req.user || !req.user.isAdmin) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }
    
    next();
  });
};

module.exports = {
  authenticateAdmin
}; 