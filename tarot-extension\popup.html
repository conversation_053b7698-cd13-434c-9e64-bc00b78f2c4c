<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Tarot Draw</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      width: 300px;
      padding: 15px;
      text-align: center;
      background-color: #f9f6ff;
    }
    h1 {
      color: #6200ee;
      margin-bottom: 15px;
      font-size: 24px;
    }
    .subtitle {
      color: #666;
      margin-bottom: 20px;
      font-size: 14px;
    }
    #card-container {
      position: relative;
      height: 320px;
      margin: 10px auto;
      perspective: 1000px;
    }
    .card {
      width: 180px;
      height: 300px;
      margin: 0 auto;
      position: relative;
      transition: transform 0.6s;
      transform-style: preserve-3d;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
      border-radius: 10px;
    }
    .card.flipped {
      transform: rotateY(180deg);
    }
    .card-face {
      position: absolute;
      width: 100%;
      height: 100%;
      backface-visibility: hidden;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .card-back {
      background-color: #3a0ca3;
      background-image: url('images/card-back.jpg');
      background-size: cover;
    }
    .card-front {
      background-color: white;
      transform: rotateY(180deg);
      flex-direction: column;
    }
    .card-image {
      max-width: 90%;
      max-height: 75%;
      border-radius: 5px;
    }
    .card-name {
      margin-top: 10px;
      font-weight: bold;
      color: #333;
    }
    .card-answer {
      margin-top: 5px;
      font-size: 20px;
      font-weight: bold;
    }
    .yes {
      color: #4caf50;
    }
    .no {
      color: #f44336;
    }
    button {
      background-color: #6200ee;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 10px;
      transition: background-color 0.3s;
    }
    button:hover {
      background-color: #3700b3;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .visit-site {
      margin-top: 15px;
      font-size: 12px;
    }
    .visit-site a {
      color: #6200ee;
      text-decoration: none;
    }
    .visit-site a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <h1>Tarot Draw</h1>
  <div class="subtitle">AI-Powered Tarot Reading</div>
  
  <div id="card-container">
    <div class="card" id="tarot-card">
      <div class="card-face card-back"></div>
      <div class="card-face card-front">
        <img id="card-image" class="card-image" src="" alt="Tarot Card">
        <div id="card-name" class="card-name"></div>
        <div id="card-answer" class="card-answer"></div>
      </div>
    </div>
  </div>
  
  <button id="draw-button">Draw a Card</button>
  
  <div class="visit-site">
    <a href="https://tarotqa.com" target="_blank">Visit TarotQA for Full Reading</a>
  </div>
  
  <script src="popup.js"></script>
</body>
</html> 