<!doctype html>
<html lang="zh-CN">

<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-LDGP6DBZ41"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-LDGP6DBZ41');
  </script>

  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

  <!-- 资源提示优化 - 预连接关键域名 -->
  <link rel="preconnect" href="https://cdn.tarotqa.com" crossorigin>
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://www.googletagmanager.com" crossorigin>
  <link rel="preconnect" href="https://www.google-analytics.com" crossorigin>
  <link rel="preconnect" href="https://www.paypal.com" crossorigin>

  <!-- DNS预解析优化 -->
  <link rel="dns-prefetch" href="https://cdn.tarotqa.com">
  <link rel="dns-prefetch" href="https://fonts.googleapis.com">
  <link rel="dns-prefetch" href="https://fonts.gstatic.com">
  <link rel="dns-prefetch" href="https://www.googletagmanager.com">
  <link rel="dns-prefetch" href="https://www.google-analytics.com">
  <link rel="dns-prefetch" href="https://accounts.google.com">

  <!-- 预加载关键公共资源 - 网站Logo和背景 -->
  <link rel="preload" as="image" href="https://cdn.tarotqa.com/images-optimized/favicon_small.webp" fetchpriority="high">
  <link rel="preload" as="image" href="https://cdn.tarotqa.com/images-optimized/bg.webp" fetchpriority="high">
  
  <!-- 预加载字体资源 -->
  <link rel="preload"
    href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&family=Raleway:wght@300;400;500;600;700&family=Inter:wght@400;500;600&display=swap"
    as="style">
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&family=Raleway:wght@300;400;500;600;700&family=Inter:wght@400;500;600&display=swap"
    media="print" onload="this.media='all'">
    
  <link rel="icon" type="image/webp" href="https://cdn.tarotqa.com/images-optimized/favicon_small.webp" />
  

  <!-- Open Graph / Facebook / Instagram / Threads -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://tarotqa.com/" />
  <meta property="og:title" content="免費線上AI塔羅牌占卜 | 準確愛情運勢解讀 - TarotQA" />
  <meta property="og:image" content="https://tarotqa.com/threads-preview.jpg" />
  <meta property="og:image:secure_url" content="/threads-preview.jpg" />
  <meta property="og:description" content="專業直覺塔羅牌占卜網站，提供免費線上抽牌服務。我們的AI塔羅師精準解讀愛情、事業與運勢，幫您洞察未來、掌握命運。立即體驗多種線上占卜方式，獲取神準預測，解答您的困惑！" />
  <meta property="og:image:type" content="jpg" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:image:alt" content="免費線上AI塔羅牌占卜 | 準確愛情運勢解讀 - TarotQA" />
  <meta property="og:site_name" content="TarotQA" />
  
  
  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:domain" content="tarotqa.com" />
  <meta name="twitter:url" content="https://tarotqa.com/" />
  <meta name="twitter:title" content="免費線上AI塔羅牌占卜 | 準確愛情運勢解讀 - TarotQA" />
  <meta name="twitter:image" content="https://tarotqa.com/threads-preview.jpg" />
  <meta name="twitter:image:alt" content="免費線上AI塔羅牌占卜 | 準確愛情運勢解讀 - TarotQA" />
  <meta name="twitter:site" content="@tarotqa" />
  <meta name="twitter:creator" content="@tarotqa" />
  <meta name="twitter:description" content="專業直覺塔羅牌占卜網站，提供免費線上抽牌服務。我們的AI塔羅師精準解讀愛情、事業與運勢，幫您洞察未來、掌握命運。立即體驗多種線上占卜方式，獲取神準預測，解答您的困惑！" />

  <!-- Pinterest -->
  <meta name="pinterest:description" content="專業直覺塔羅牌占卜網站，提供免費線上抽牌服務。我們的AI塔羅師精準解讀愛情、事業與運勢，幫您洞察未來、掌握命運。立即體驗多種線上占卜方式，獲取神準預測，解答您的困惑！" />

  <!-- 禁止移动端双指缩放 -->
  <script>
    // 只阻止双指缩放，不影响正常滚动
    document.addEventListener('touchstart', function(event) {
      // 仅当检测到多点触控且距离变化时阻止默认行为（缩放）
      if (event.touches.length > 1) {
        event.preventDefault();
      }
    }, { passive: false });
    
    var lastTouchEnd = 0;
    document.addEventListener('touchend', function(event) {
      // 防止快速双击缩放
      var now = Date.now();
      if (now - lastTouchEnd <= 300 && event.touches.length > 1) {
        event.preventDefault();
      }
      lastTouchEnd = now;
    }, { passive: false });
    
    // 防止输入框聚焦时的页面缩放
    document.addEventListener('DOMContentLoaded', function() {
      let viewportMeta = document.querySelector('meta[name="viewport"]');
      let timer;
      
      // 确保viewportMeta存在
      if (viewportMeta) {
        // 监听所有输入框的focus事件
        document.addEventListener('focus', function(e) {
          if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            clearTimeout(timer);
            viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
          }
        }, true);
        
        // 监听blur事件
        document.addEventListener('blur', function(e) {
          if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            timer = setTimeout(function() {
              // 输入框失去焦点后等待一段时间再恢复viewport设置
              viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
            }, 300);
          }
        }, true);
      }
    });
  </script>



  <!-- 设置资源加载优先级 -->
  <meta name="priority-hints" content="on">

  <!-- 在开发环境中不预加载main.tsx，避免产生控制台警告 -->
  <!-- <link rel="preload" as="script" href="/src/main.tsx"> -->

  <!-- PayPal SDK将在应用程序中动态加载 -->

  <style>
    body {
      background-color: #000;
      color: #fff;
      margin: 0;
      padding: 0;
    }

    #root {
      min-height: 100vh;
    }
  </style>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>