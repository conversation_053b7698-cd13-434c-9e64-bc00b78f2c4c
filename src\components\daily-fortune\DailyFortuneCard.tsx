import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import TiltImage from "../../blocks/Components/TiltImage";

interface DailyFortuneCardProps {
  cardBackImage: string;
  flipped: boolean;
  canPredictToday: boolean;
  cardImage: string | null;
  selectedCard: number | null;
  cardOrientation: boolean;
  tarotCards: any[];
  handleCardFlip: () => void;
  getFontClass: () => string;
}

const DailyFortuneCard: React.FC<DailyFortuneCardProps> = ({
  cardBackImage,
  flipped,
  canPredictToday,
  cardImage,
  selectedCard,
  cardOrientation,
  tarotCards,
  handleCardFlip,
  getFontClass
}) => {
  const { t } = useTranslation();
  const [processedCardImage, setProcessedCardImage] = useState<string | null>(null);
  
  // 处理卡牌图像，为逆位卡牌直接使用CSS旋转，而不是生成新图像
  useEffect(() => {
    if (!flipped || !cardImage || selectedCard === null) {
      setProcessedCardImage(null);
      return;
    }

    // 无论正位还是逆位，都直接使用原始图像
    setProcessedCardImage(cardImage);
    
  }, [cardImage, cardOrientation, flipped, selectedCard]);

  return (
    <motion.div 
      className="relative w-full max-w-md mx-auto mt-8 sm:mt-10 mb-0"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <div className="flex justify-center">
        <div className="relative">
          <div className="w-[160px] h-[285px] sm:w-[240px] sm:h-[428px]">
            {!flipped ? (
              <motion.div
                initial={{ rotateY: 0 }}
                exit={{ rotateY: 90 }}
                transition={{ duration: 0.3 }}
                className={`${!canPredictToday ? 'opacity-70' : ''}`}
                onClick={canPredictToday ? handleCardFlip : undefined}
              >
                <TiltImage
                  imageUrl={cardBackImage}
                  alt={t('daily.card_back', '塔罗牌背面')}
                  className={`cursor-pointer ${!canPredictToday ? 'opacity-70' : ''}`}
                  enableTilt={canPredictToday}
                  onClick={canPredictToday ? handleCardFlip : undefined}
                />
              </motion.div>
            ) : (
              <motion.div
                initial={{ rotateY: -90 }}
                animate={{ rotateY: 0 }}
                transition={{ duration: 0.3 }}
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'relative',
                }}
              >
                <TiltImage
                  imageUrl={processedCardImage || (selectedCard !== null ? `/images-optimized/tarot/${tarotCards[selectedCard].nameEn}.webp` : '')}
                  alt={(() => {
                    if (selectedCard === null) return '';
                    const card = tarotCards[selectedCard];
                    const nameEn = card.nameEn?.toLowerCase();
                    
                    if (selectedCard <= 21) {
                      // Major Arcana
                      const translationKey = `reading.cards.major.${selectedCard}`;
                      const translatedName = String(t(translationKey));
                      return translatedName;
                    } else {
                      // Minor Arcana
                      const suit = nameEn.includes('wands') ? 'wands' :
                                 nameEn.includes('cups') ? 'cups' :
                                 nameEn.includes('swords') ? 'swords' :
                                 'pentacles';
                      
                      const rankMap: { [key: string]: string } = {
                        'ace': 'ace',
                        'two': '2',
                        'three': '3',
                        'four': '4',
                        'five': '5',
                        'six': '6',
                        'seven': '7',
                        'eight': '8',
                        'nine': '9',
                        'ten': '10',
                        'page': 'page',
                        'knight': 'knight',
                        'queen': 'queen',
                        'king': 'king'
                      };
                      
                      const rank = Object.keys(rankMap).find(r => nameEn.includes(r));
                      return rank ? String(t(`reading.cards.${suit}.${rankMap[rank]}`)) : card.name;
                    }
                  })()}
                  className={`cursor-pointer ${cardOrientation ? 'rotate-180' : ''}`}
                  enableTilt={true}
                />
              </motion.div>
            )}
          </div>
        </div>
      </div>
      
      {/* 显示卡牌名称和正逆位 */}
      {flipped && selectedCard !== null && (
        <motion.div 
          className={`text-center ${getFontClass()} mt-2`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <div className="flex items-center justify-center space-x-2">
            <span className="text-purple-200 font-medium text-lg">
              {(() => {
                const card = tarotCards[selectedCard];
                const nameEn = card.nameEn?.toLowerCase();
                
                if (selectedCard <= 21) {
                  // Major Arcana
                  const translationKey = `reading.cards.major.${selectedCard}`;
                  const translatedName = String(t(translationKey));
                  return translatedName;
                } else {
                  // Minor Arcana
                  const suit = nameEn.includes('wands') ? 'wands' :
                             nameEn.includes('cups') ? 'cups' :
                             nameEn.includes('swords') ? 'swords' :
                             'pentacles';
                  
                  const rankMap: { [key: string]: string } = {
                    'ace': 'ace',
                    'two': '2',
                    'three': '3',
                    'four': '4',
                    'five': '5',
                    'six': '6',
                    'seven': '7',
                    'eight': '8',
                    'nine': '9',
                    'ten': '10',
                    'page': 'page',
                    'knight': 'knight',
                    'queen': 'queen',
                    'king': 'king'
                  };
                  
                  const rank = Object.keys(rankMap).find(r => nameEn.includes(r));
                  return rank ? String(t(`reading.cards.${suit}.${rankMap[rank]}`)) : card.name;
                }
              })()}
            </span>
            <span className="text-gray-300 text-sm">
              ({cardOrientation ? t('reading.result.reversed', '逆位') : t('reading.result.upright', '正位')})
            </span>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

export default DailyFortuneCard; 