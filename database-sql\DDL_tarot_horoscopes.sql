CREATE TABLE IF NOT EXISTS `tarot_horoscopes` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `type` VARCHAR(20) NOT NULL COMMENT '运势类型(daily/weekly/monthly/yearly/love)',
  `date` DATE NOT NULL COMMENT '运势日期',
  `aries` TEXT COMMENT '白羊座运势内容',
  `taurus` TEXT COMMENT '金牛座运势内容',
  `gemini` TEXT COMMENT '双子座运势内容',
  `cancer` TEXT COMMENT '巨蟹座运势内容',
  `leo` TEXT COMMENT '狮子座运势内容',
  `virgo` TEXT COMMENT '处女座运势内容',
  `libra` TEXT COMMENT '天秤座运势内容',
  `scorpio` TEXT COMMENT '天蝎座运势内容',
  `sagittarius` TEXT COMMENT '射手座运势内容',
  `capricorn` TEXT COMMENT '摩羯座运势内容',
  `aquarius` TEXT COMMENT '水瓶座运势内容',
  `pisces` TEXT COMMENT '双鱼座运势内容',
  `raw_content` TEXT NOT NULL COMMENT 'API返回的原始内容',
  `language` VARCHAR(10) NOT NULL DEFAULT 'zh-CN' COMMENT '语言',
  `input_tokens` INT NOT NULL DEFAULT 0 COMMENT '输入token数量',
  `output_tokens` INT NOT NULL DEFAULT 0 COMMENT '输出token数量',
  `generated_at` DATETIME NOT NULL COMMENT '生成时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY `idx_type_date_lang` (`type`, `date`, `language`),
  INDEX `idx_date` (`date`),
  INDEX `idx_type_date` (`type`, `date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='星座运势数据表'; 