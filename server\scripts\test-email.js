const path = require('path');
// 修改环境变量文件路径，指向 server/.env
const dotenvPath = path.join(__dirname, '../.env');
console.log('尝试加载环境变量文件:', dotenvPath);
require('dotenv').config({ path: dotenvPath });

// 确保设置NODE_ENV环境变量
// 如果主程序是生产环境，这里也应该是生产环境；如果是开发环境，这里也应该是开发环境
// 默认情况下，设为开发环境
if (!process.env.NODE_ENV) {
  console.log('NODE_ENV未设置，默认使用development环境');
  process.env.NODE_ENV = 'development';
} else {
  console.log('当前NODE_ENV:', process.env.NODE_ENV);
}

const EmailService = require('../services/emailService');
const { initializePool } = require('../services/database');

// 打印MySQL配置信息（密码除外）
console.log('MySQL连接配置:');
console.log('- 主机(production):', process.env.MYSQL_HOST_INTERNAL || '未配置');
console.log('- 主机(development):', process.env.MYSQL_HOST_PUBLIC || '未配置');
console.log('- 端口:', process.env.MYSQL_PORT || '未配置');
console.log('- 用户名:', process.env.MYSQL_USERNAME || '未配置');
console.log('- 数据库:', process.env.MYSQL_DATABASE || '未配置');

/**
 * 测试发送统计邮件
 * 使用方法：node scripts/test-email.js <EMAIL>
 */
async function testSendStatsEmail() {
  try {
    // 初始化数据库连接池
    console.log('正在初始化数据库连接...');
    await initializePool()
      .then(() => {
        console.log('成功连接到数据库');
      })
      .catch(err => {
        console.error('无法连接到数据库:', err);
        throw err;
      });
    
    // 获取命令行参数中的邮箱地址
    const email = process.argv[2];
    
    if (!email) {
      console.error('请提供接收邮件的邮箱地址!');
      console.log('使用方法: node scripts/test-email.js <EMAIL>');
      process.exit(1);
    }
    
    console.log(`准备发送测试统计邮件到: ${email}`);
    await EmailService.sendTestStatsEmail(email);
    console.log('邮件发送完成!');
    
    // 脚本执行完毕后退出
    process.exit(0);
  } catch (error) {
    console.error('发送测试邮件失败:', error);
    process.exit(1);
  }
}

// 执行测试函数
testSendStatsEmail(); 