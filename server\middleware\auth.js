const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: '未授权访问' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: '无效的token' });
    }
    req.user = user;
    next();
  });
};

// 可选认证中间件，允许未登录用户继续操作
const optionalAuthenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  // 如果没有token，继续但不设置用户信息
  if (!token) {
    return next();
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (!err) {
      req.user = user;
    }
    // 即使token无效也继续，只是不设置用户信息
    next();
  });
};

// VIP用户检查中间件
const checkVipStatus = (req, res, next) => {
  // 如果用户未登录，返回401未授权
  if (!req.user) {
    return res.status(401).json({ message: '需要登录才能访问此功能' });
  }
  
  // 检查用户是否为VIP
  if (!req.user.vipStatus || req.user.vipStatus !== 'active') {
    return res.status(403).json({ message: '需要VIP会员才能访问此功能', errorCode: 'VIP_REQUIRED' });
  }
  
  // 用户是VIP，继续处理请求
  next();
};

// 管理员检查中间件
const isAdmin = async (req, res, next) => {
  // 如果用户未登录，返回401未授权
  if (!req.user) {
    return res.status(401).json({ message: '需要登录才能访问此功能' });
  }
  
  // 硬编码特定邮箱为管理员
  const adminEmails = ['<EMAIL>', '<EMAIL>'];
  
  // 检查用户是否为管理员
  if (!req.user.isAdmin && !(req.user.email && adminEmails.includes(req.user.email))) {
    return res.status(403).json({ message: '需要管理员权限才能访问此功能' });
  }
  
  // 用户是管理员，继续处理请求
  next();
};

module.exports = {
  authenticateToken,
  optionalAuthenticateToken,
  checkVipStatus,
  isAdmin
}; 