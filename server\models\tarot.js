const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');

// 添加卡牌英文名称映射
const cardNameToEnglish = {
  // 大阿卡纳
  '愚者': 'The Fool',
  '魔术师': 'The Magician',
  '女祭司': 'The High Priestess',
  '女皇': 'The Empress',
  '皇帝': 'The Emperor',
  '教皇': 'The Hierophant',
  '恋人': 'The Lovers',
  '战车': 'The Chariot',
  '力量': 'Strength',
  '隐士': 'The Hermit',
  '命运之轮': 'Wheel of Fortune',
  '正义': 'Justice',
  '倒吊人': 'The Hanged Man',
  '死神': 'Death',
  '节制': 'Temperance',
  '恶魔': 'The Devil',
  '塔': 'The Tower',
  '星星': 'The Star',
  '月亮': 'The Moon',
  '太阳': 'The Sun',
  '审判': 'Judgement',
  '世界': 'The World',

  // 权杖
  '权杖王': 'King of Wands',
  '权杖皇后': 'Queen of Wands',
  '权杖骑士': 'Knight of Wands',
  '权杖侍从': 'Page of Wands',
  '权杖一': 'Ace of Wands',
  '权杖二': 'Two of Wands',
  '权杖三': 'Three of Wands',
  '权杖四': 'Four of Wands',
  '权杖五': 'Five of Wands',
  '权杖六': 'Six of Wands',
  '权杖七': 'Seven of Wands',
  '权杖八': 'Eight of Wands',
  '权杖九': 'Nine of Wands',
  '权杖十': 'Ten of Wands',

  // 圣杯
  '圣杯王': 'King of Cups',
  '圣杯皇后': 'Queen of Cups',
  '圣杯骑士': 'Knight of Cups',
  '圣杯侍从': 'Page of Cups',
  '圣杯一': 'Ace of Cups',
  '圣杯二': 'Two of Cups',
  '圣杯三': 'Three of Cups',
  '圣杯四': 'Four of Cups',
  '圣杯五': 'Five of Cups',
  '圣杯六': 'Six of Cups',
  '圣杯七': 'Seven of Cups',
  '圣杯八': 'Eight of Cups',
  '圣杯九': 'Nine of Cups',
  '圣杯十': 'Ten of Cups',

  // 宝剑
  '宝剑王': 'King of Swords',
  '宝剑皇后': 'Queen of Swords',
  '宝剑骑士': 'Knight of Swords',
  '宝剑侍从': 'Page of Swords',
  '宝剑一': 'Ace of Swords',
  '宝剑二': 'Two of Swords',
  '宝剑三': 'Three of Swords',
  '宝剑四': 'Four of Swords',
  '宝剑五': 'Five of Swords',
  '宝剑六': 'Six of Swords',
  '宝剑七': 'Seven of Swords',
  '宝剑八': 'Eight of Swords',
  '宝剑九': 'Nine of Swords',
  '宝剑十': 'Ten of Swords',

  // 金币
  '金币王': 'King of Pentacles',
  '金币皇后': 'Queen of Pentacles',
  '金币骑士': 'Knight of Pentacles',
  '金币侍从': 'Page of Pentacles',
  '金币一': 'Ace of Pentacles',
  '金币二': 'Two of Pentacles',
  '金币三': 'Three of Pentacles',
  '金币四': 'Four of Pentacles',
  '金币五': 'Five of Pentacles',
  '金币六': 'Six of Pentacles',
  '金币七': 'Seven of Pentacles',
  '金币八': 'Eight of Pentacles',
  '金币九': 'Nine of Pentacles',
  '金币十': 'Ten of Pentacles'
};

class Session {
  static async create(sessionData) {
    const { user_id, question, reader_id, reader_name, reader_type, spread_id, spread_name, spread_card_count, reading_result, selected_cards, selected_positions } = sessionData;
    
    // 处理问题文本，确保可以安全存储表情符号
    // 如果需要过滤表情符号，可以使用正则表达式
    const processedQuestion = question ? question.replace(/[\u{1F000}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, '') : question;
    
    const session_id = uuidv4();
    const connection = await getConnection();
    
    try {
        await connection.query(
            `INSERT INTO sessions (id, user_id, question, reader_id, reader_name, reader_type, spread_id, spread_name, spread_card_count, reading_result, selected_cards, selected_positions)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [session_id, user_id, processedQuestion, 
             reader_id ? reader_id.toLowerCase() : null, 
             reader_name, reader_type, spread_id, spread_name, spread_card_count, 
             reading_result ? JSON.stringify(reading_result) : null,
             selected_cards ? JSON.stringify(selected_cards) : null,
             selected_positions ? JSON.stringify(selected_positions) : null]
        );
        
        return { id: session_id, ...sessionData, question: processedQuestion };
    } catch (error) {
        console.error('Error creating session:', error);
        throw error;
    }
  }

  static async findById(sessionId) {
    const pool = await getConnection();
    
    // Get session data
    const [sessions] = await pool.query('SELECT * FROM sessions WHERE id = ?', [sessionId]);
    if (sessions.length === 0) return null;
    
    const session = sessions[0];

    // Parse JSON fields with error handling
    let selectedCards = [];
    let selectedPositions = [];
    let readingResult = null;

    try {
      if (session.selected_cards) {
        selectedCards = typeof session.selected_cards === 'string' 
          ? JSON.parse(session.selected_cards)
          : session.selected_cards;
      }
    } catch (error) {
      console.error('Error parsing selected_cards:', error);
    }

    try {
      if (session.selected_positions) {
        selectedPositions = typeof session.selected_positions === 'string'
          ? JSON.parse(session.selected_positions)
          : session.selected_positions;
      }
    } catch (error) {
      console.error('Error parsing selected_positions:', error);
    }

    try {
      if (session.reading_result) {
        readingResult = typeof session.reading_result === 'string'
          ? JSON.parse(session.reading_result)
          : session.reading_result;
      }
    } catch (error) {
      console.error('Error parsing reading_result:', error);
    }

    // Reconstruct the session object
    return {
      ...session,
      selectedSpread: session.spread_id ? {
        id: session.spread_id,
        name: session.spread_name,
        cardCount: session.spread_card_count,
        positions: selectedPositions
      } : null,
      selectedReader: session.reader_id ? {
        id: session.reader_id,
        name: session.reader_name,
        type: session.reader_type
      } : null,
      selectedCards,
      readingResult,
      llmStats: {
        inputTokens: session.input_tokens,
        outputTokens: session.output_tokens,
        promptCacheHitTokens: session.prompt_cache_hit_tokens,
        promptCacheMissTokens: session.prompt_cache_miss_tokens,
        model: session.llm_model,
        responseTime: session.response_time
      },
      ethicalCheck: {
        inputTokens: session.Ethical_input_token,
        outputTokens: session.Ethical_output_token,
        category: session.Ethical_category,
        reason: session.Ethical_reason
      },
      similarityCheck: {
        inputTokens: session.Similarity_input_token,
        outputTokens: session.Similarity_output_token,
        isRepeated: session.Similarity_is_repeated === 1,
        matchedQuestion: session.Similarity_matched_question
      }
    };
  }

  static async findActiveByUserId(userId) {
    const pool = await getConnection();
    const [sessions] = await pool.query(
      'SELECT * FROM sessions WHERE user_id = ? AND status != ? ORDER BY timestamp DESC LIMIT 1',
      [userId, 'completed']
    );

    if (sessions.length === 0) return null;
    return this.findById(sessions[0].id);
  }

  static async findByUserId(userId) {
    const pool = await getConnection();
    const [sessions] = await pool.query(
      'SELECT * FROM sessions WHERE user_id = ? AND (status = ? OR status = ? OR status = ?) ORDER BY timestamp DESC',
      [userId, 'completed', 'ethical_intervention', 'ethical_intervention_follow']
    );
    
    // Map through each session and get its full details
    const fullSessions = await Promise.all(
      sessions.map(session => this.findById(session.id))
    );

    return fullSessions;
  }

  static async update(sessionId, updateData) {
    const pool = await getConnection();
    const connection = await pool.getConnection();
    await connection.beginTransaction();

    try {
      // Update main session data
      const updateFields = [];
      const updateValues = [];

      if (updateData.status) {
        updateFields.push('status = ?');
        updateValues.push(updateData.status);
      }

      if (updateData.selectedReader) {
        updateFields.push('reader_id = ?', 'reader_name = ?', 'reader_type = ?');
        updateValues.push(
          updateData.selectedReader.id.toLowerCase(),
          updateData.selectedReader.name,
          updateData.selectedReader.type
        );
      }

      if (updateData.selectedSpread) {
        updateFields.push('spread_id = ?', 'spread_name = ?', 'spread_card_count = ?');
        updateValues.push(
          updateData.selectedSpread.id,
          updateData.selectedSpread.name,
          updateData.selectedSpread.cardCount
        );

        if (updateData.selectedSpread.positions) {
          updateFields.push('selected_positions = ?');
          updateValues.push(JSON.stringify(updateData.selectedSpread.positions));
        }
      }

      if (updateData.selectedCards) {
        updateFields.push('selected_cards = ?');
        const cardsToStore = Array.isArray(updateData.selectedCards)
          ? updateData.selectedCards
          : typeof updateData.selectedCards === 'string'
            ? JSON.parse(updateData.selectedCards)
            : [];
        updateValues.push(JSON.stringify(cardsToStore));
      }

      // 移除对 userZodiacInfo 的数据库存储支持
      // 星座信息只在前端localStorage中保存，不存储到数据库
      // if (updateData.userZodiacInfo) {
      //   updateFields.push('user_data = ?');
      //   const zodiacInfoToStore = typeof updateData.userZodiacInfo === 'string'
      //     ? updateData.userZodiacInfo
      //     : JSON.stringify(updateData.userZodiacInfo);
      //   updateValues.push(zodiacInfoToStore);
      // }

      if (updateData.readingResult) {
        updateFields.push('reading_result = ?');
        let resultToStore = typeof updateData.readingResult === 'string'
          ? JSON.parse(updateData.readingResult)
          : updateData.readingResult;

        // 如果存在mainReading，将其移除，只保留dialogHistory
        if (resultToStore.mainReading && resultToStore.dialogHistory) {
          resultToStore = {
            timestamp: resultToStore.timestamp,
            dialogHistory: resultToStore.dialogHistory
          };
        }
        
        updateValues.push(JSON.stringify(resultToStore));
      }

      // 添加 LLM 相关字段的更新
      if (updateData.llmStats) {
        if (updateData.llmStats.inputTokens !== undefined) {
          updateFields.push('input_tokens = ?');
          updateValues.push(updateData.llmStats.inputTokens);
        }
        if (updateData.llmStats.outputTokens !== undefined) {
          updateFields.push('output_tokens = ?');
          updateValues.push(updateData.llmStats.outputTokens);
        }
        if (updateData.llmStats.promptCacheHitTokens !== undefined) {
          updateFields.push('prompt_cache_hit_tokens = ?');
          updateValues.push(updateData.llmStats.promptCacheHitTokens);
        }
        if (updateData.llmStats.promptCacheMissTokens !== undefined) {
          updateFields.push('prompt_cache_miss_tokens = ?');
          updateValues.push(updateData.llmStats.promptCacheMissTokens);
        }
        if (updateData.llmStats.model) {
          updateFields.push('llm_model = ?');
          updateValues.push(updateData.llmStats.model);
        }
        if (updateData.llmStats.responseTime !== undefined) {
          updateFields.push('response_time = ?');
          updateValues.push(updateData.llmStats.responseTime);
        }
      }

      // 添加伦理检测相关字段的更新
      if (updateData.ethicalCheck) {
        if (updateData.ethicalCheck.inputTokens !== undefined) {
          updateFields.push('Ethical_input_token = ?');
          updateValues.push(updateData.ethicalCheck.inputTokens);
        }
        if (updateData.ethicalCheck.outputTokens !== undefined) {
          updateFields.push('Ethical_output_token = ?');
          updateValues.push(updateData.ethicalCheck.outputTokens);
        }
        if (updateData.ethicalCheck.category) {
          updateFields.push('Ethical_category = ?');
          updateValues.push(updateData.ethicalCheck.category);
        }
        if (updateData.ethicalCheck.reason) {
          updateFields.push('Ethical_reason = ?');
          updateValues.push(updateData.ethicalCheck.reason);
        }
      }

      // 添加相似度检测相关字段的更新
      if (updateData.similarityCheck) {
        if (updateData.similarityCheck.inputTokens !== undefined) {
          updateFields.push('Similarity_input_token = ?');
          updateValues.push(updateData.similarityCheck.inputTokens);
        }
        if (updateData.similarityCheck.outputTokens !== undefined) {
          updateFields.push('Similarity_output_token = ?');
          updateValues.push(updateData.similarityCheck.outputTokens);
        }
        if (updateData.similarityCheck.isRepeated !== undefined) {
          updateFields.push('Similarity_is_repeated = ?');
          updateValues.push(updateData.similarityCheck.isRepeated ? 1 : 0);
        }
        if (updateData.similarityCheck.matchedQuestion !== undefined) {
          updateFields.push('Similarity_matched_question = ?');
          updateValues.push(updateData.similarityCheck.matchedQuestion);
        }
      }

      // Execute update if there are fields to update
      if (updateFields.length > 0) {
        const query = `UPDATE sessions SET ${updateFields.join(', ')} WHERE id = ?`;
        updateValues.push(sessionId);
        await connection.query(query, updateValues);
      }

      await connection.commit();
      return this.findById(sessionId);
    } catch (error) {
      console.error('Error in update:', error);
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
}

module.exports = {
  Session
};
