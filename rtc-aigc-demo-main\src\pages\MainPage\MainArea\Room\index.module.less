/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

 .wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 16px;
    padding: 32px;
    box-sizing: border-box;
  
    .conversation,
    .fullScreen,
    .mobileConversation {
      width: 100%;
      position: relative;
      height: 100%;
      /**
           * 100% 为容器高度
           * 128px 为上层 DouBao Card Height
           * 24px 为 margin top
           * 36px * 2 为容器 padding
           * 128 + 24 + 36 * 2 = 224px
           */
      max-height: calc(100% - 224px - 8px);
      display: flex;
      flex-direction: column;
      padding-bottom: 12px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-top: 48px;
  
      .sentence {
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        flex-wrap: wrap;
        align-items: center;
        width: max-content;
        white-space: normal;
        max-width: 70%;
        padding: 12px 16px;
        margin-left: 32px;
        gap: 8px;
  
        .content {
          width: max-content;
        }
      }
  
      .user {
        width: max-content;
        border: 0px solid;
        padding: 8px 12px 8px 12px;
        border-radius: 12px;
        background: #f1f3f5;
        margin-bottom: 12px;
      }
      .robot {
        font-family: PingFang SC;
        color: #0c0d0e;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0.003em;
        border: 1px solid transparent;
        border-radius: 12px;
        background: linear-gradient(77.86deg, #fff -3.23%, #fff 51.11%, #fff 98.65%) padding-box,
          linear-gradient(77.86deg, #e5f2ff -3.23%, #d9e5ff 51.11%, #f6e2ff 98.65%) border-box;
        margin-bottom: 12px;
      }
  
      .loading-wrapper {
        width: max-content;
        display: inline-block;
  
        .loading {
          margin-left: 8px;
          width: max-content;
        }
  
        .dot {
          background-color: rgba(193, 163, 237, 1);
          width: 8px;
          height: 8px;
        }
      }
  
      .aiReadying {
        font-family: PingFang SC;
        font-size: 16px;
        font-weight: 500;
        color: rgba(27, 30, 61, 0.6);
        text-align: center;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        line-height: 28px;
      }
  
      .aiReading-spin {
        margin-right: 12px;
        line-height: 16px;
      }
  
      .msgName {
        display: flex;
        gap: 8px;
        align-items: center;
        font-size: 12px;
        line-height: 20px;
        color: #737a87;
        margin-bottom: 4px;
  
        .avatar {
          border-radius: 50%;
          width: 24px;
          height: 24px;
  
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  
    .fullScreen {
      .msgName {
        color: #fff;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
      }
  
      .sentence {
        color: #fff;
      }
  
      .user {
        background: rgba(0, 0, 0, 0.25);
      }
      .robot {
        background: rgba(0, 12, 71, 0.5);
      }
    }
  
    .conversation::-webkit-scrollbar {
      width: 0px;
      height: 0px;
    }
  
    .conversation::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0);
      border-radius: 0px;
    }
  
    .conversation::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0);
      border-radius: 0px;
    }
  
    .toolBar {
      position: absolute;
      right: 0px;
      margin-right: 36px;
      bottom: 36px;
    }
  
    .controller {
      position: absolute;
      left: 0px;
      bottom: 36px;
      margin-left: 50%;
      transform: translateX(-50%);
    }
  
    .declare {
      position: absolute;
      bottom: 8px;
      left: 12px;
      color: var(--text-color-text-4, rgba(199, 204, 214, 1));
      font-size: 10px;
      font-weight: 400;
      line-height: 20px;
    }
  }
  
  .text {
    width: 100%;
    text-align: center;
    color: rgba(148, 116, 255, 1);
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
  }
  
  .closed {
    width: 100%;
    text-align: center;
    color: #737a87;
    font-size: 14px;
    font-weight: 400;
    line-height: 19.6px;
  }
  
  .btns {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
  
    .setting {
      background-color: rgba(111, 111, 111, 0.497);
      border-radius: 50%;
      width: 48px;
      height: 48px;
      padding: 12px;
      box-sizing: border-box;
      cursor: pointer;
    }
  
    .btn {
      cursor: pointer;
    }
  
    .btn:hover {
      opacity: 0.8;
    }
  
    .btn:active {
      opacity: 1;
    }
  }
  
  .column {
    margin-right: 0 !important;
    justify-content: space-around;
    align-items: center;
    bottom: 64px !important;
    gap: 0;
  
    img {
      width: 84px;
      height: 84px;
    }
  }
  
  .interruptContainer {
    color: #635bff;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
  }
  
  .interruptIcon {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #635bff;
    border-radius: 2px;
  }
  
  .interrupt {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background: rgba(99, 91, 255, 0.1);
    border-radius: 4px;
    width: max-content;
    height: 26px;
    padding: 0 8px;
    gap: 4px;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
  
    &:hover {
      opacity: 0.8;
    }
  
    &:active {
      opacity: 1;
    }
  }
  
  .camera-wrapper {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 264px;
    border-radius: 8px;
    background: var(--line-color-border-2, rgba(234, 237, 241, 1));
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 0.81px solid var(--line-color-border-3, rgba(221, 226, 233, 1));
    z-index: 4;
  
    .camera-player {
      width: 100%;
      height: 184px;
      border-radius: 8px;
    }
  
    .camera-player-hidden {
      display: none !important;
    }
  
    .camera-placeholder {
      width: 100%;
      height: 184px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #737a87;
      border-bottom-left-radius: inherit;
      border-bottom-right-radius: inherit;
      text-align: center;
  
      .camera-placeholder-close-note {
        margin-bottom: 8px;
        width: 60px;
        height: 60px;
      }
  
      .camera-open-btn {
        color: var(--primary-color-primary-6, rgba(22, 100, 255, 1));
        cursor: pointer;
        margin-left: 2px;
      }
    }
  
    .userTag {
      position: absolute;
      top: 4px;
      left: 4px;
    }
  
    .subTitleUserTag {
      position: absolute;
      top: -16px;
      right: -16px;
    }
  }
  
  .visionDescriptionArea {
    width: 100%;
    background: linear-gradient(77.86deg, #f1f9ff -3.23%, #edf3ff 51.11%, #faf4ff 98.65%);
    padding: 10px 0;
    text-align: center;
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
    box-sizing: border-box;
    font-size: 12px;
    line-height: 20px;
    color: #737a87;
  
    .visionTitleText {
      color: #42464e;
      font-weight: 500;
    }
  }
  
  .subtitleAiAvatar {
    opacity: 0.3;
  }
  
  .fullScreenAiAvatar {
    height: 184px;
  }
  
  .mobile {
    background: 
    /* 图层1 (最上层): 背景图片 */
    /* url(...) [position] / [size] [repeat] */ url('../../../../assets/img/mobileBg.png')
        center center / cover no-repeat,
      /* 图层2 (下层): 渐变背景 */ linear-gradient(167.98deg, #f5f7ff 0%, #faf3ff 100%);
  
    .controller {
      bottom: 156px;
    }
    border-radius: 0;
  }
  
  .mobileConversation {
    display: flex;
    max-height: calc(100% - 324px) !important;
    margin-top: 64px !important;
  
    .sentence {
      margin-left: 0 !important;
      max-width: 85% !important;
    }

    .mobileLine {
      display: flex;
    }
  }
  
  .mobilePlayer {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  
  @media (max-width: 767px) {
    .mobileLine {
      display: flex;
      justify-content: flex-start;
    }
    .user {
      align-self: flex-end;
    }
  }
  
.micContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 10px;
}

.micStatus {
  font-size: 12px;
  margin-top: 5px;
  color: #666;
}
  