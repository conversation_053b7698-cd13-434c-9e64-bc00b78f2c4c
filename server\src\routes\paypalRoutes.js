const express = require('express');
const router = express.Router();
const fetch = require('node-fetch');
const path = require('path');
const { getConnection } = require('../../services/database');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// 诊断环境变量
console.log('=== PayPal环境变量诊断 ===');
console.log('当前工作目录:', process.cwd());
console.log('环境变量文件路径:', path.join(__dirname, '../../.env'));
console.log('PayPal环境变量:');
console.log('PAYPAL_ENVIRONMENT:', process.env.PAYPAL_ENVIRONMENT);
console.log('PAYPAL_CLIENT_ID 长度:', process.env.PAYPAL_CLIENT_ID?.length || 0);
console.log('PAYPAL_CLIENT_SECRET 长度:', process.env.PAYPAL_CLIENT_SECRET?.length || 0);

// 验证PayPal凭证
const validatePayPalCredentials = () => {
    const clientId = process.env.PAYPAL_CLIENT_ID?.trim();
    const clientSecret = process.env.PAYPAL_CLIENT_SECRET?.trim();
    
    // console.log('PayPal凭证验证:');
    // console.log('- Client ID:', clientId ? '已设置' : '未设置', `(长度: ${clientId?.length || 0})`);
    // console.log('- Client Secret:', clientSecret ? '已设置' : '未设置', `(长度: ${clientSecret?.length || 0})`);
    
    // 验证格式
    const isValidClientId = clientId && clientId.length >= 20 && clientId.length <= 80;
    const isValidClientSecret = clientSecret && clientSecret.length >= 20 && clientSecret.length <= 80;
    
    // 检查是否包含不可见字符
    const hasInvisibleChars = (str) => /[\x00-\x1F\x7F-\x9F\xAD\u0378\u0379\u037F-\u0383\u038B\u038D\u03A2\u0530\u0557\u0558\u0560\u0588\u058B-\u058E\u0590\u05C8-\u05CF\u05EB-\u05EF\u05F5-\u0605\u061C\u061D\u06DD\u070E\u070F\u074B\u074C\u07B2-\u07BF\u07FB-\u07FF\u082E\u082F\u083F\u085C\u085D\u085F-\u089F\u08A1\u08AD-\u08E3\u08FF\u0978\u0980\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09FC-\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF2-\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B55\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B78-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BFB-\u0C00\u0C04\u0C0D\u0C11\u0C29\u0C34\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5A-\u0C5F\u0C64\u0C65\u0C70-\u0C77\u0C80\u0C81\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0D01\u0D04\u0D0D\u0D11\u0D3B\u0D3C\u0D45\u0D49\u0D4F-\u0D56\u0D58-\u0D5F\u0D64\u0D65\u0D76-\u0D78\u0D80\u0D81\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DF1\u0DF5-\u0E00\u0E3B-\u0E3E\u0E5C-\u0E80\u0E83\u0E85\u0E86\u0E89\u0E8B\u0E8C\u0E8E-\u0E93\u0E98\u0EA0\u0EA4\u0EA6\u0EA8\u0EA9\u0EAC\u0EBA\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F48\u0F6D-\u0F70\u0F98\u0FBD\u0FCD\u0FDB-\u0FFF\u10C6\u10C8-\u10CC\u10CE\u10CF\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u137D-\u137F\u139A-\u139F\u13F5-\u13FF\u169D-\u169F\u16F1-\u16FF\u170D\u1715-\u171F\u1737-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17DE\u17DF\u17EA-\u17EF\u17FA-\u17FF\u180F\u181A-\u181F\u1878-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191D-\u191F\u192C-\u192F\u193C-\u193F\u1941-\u1943\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DB-\u19DD\u1A1C\u1A1D\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1A9F\u1AAE-\u1AFF\u1B4C-\u1B4F\u1B7D-\u1B7F\u1BF4-\u1BFB\u1C38-\u1C3A\u1C4A-\u1C4C\u1C80-\u1CBF\u1CC8-\u1CCF\u1CF7-\u1CFF\u1DE7-\u1DFB\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FC5\u1FD4\u1FD5\u1FDC\u1FF0\u1FF1\u1FF5\u1FFF\u200B-\u200F\u202A-\u202E\u2060-\u206F\u2072\u2073\u208F\u209D-\u209F\u20BB-\u20CF\u20F1-\u20FF\u218A-\u218F\u23F4-\u23FF\u2427-\u243F\u244B-\u245F\u2700\u2B4D-\u2B4F\u2B5A-\u2BFF\u2C2F\u2C5F\u2CF4-\u2CF8\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D71-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E3C-\u2E7F\u2E9A\u2EF4-\u2EFF\u2FD6-\u2FEF\u2FFC-\u2FFF\u3040\u3097\u3098\u3100-\u3104\u312E-\u3130\u318F\u31BB-\u31BF\u31E4-\u31EF\u321F\u32FF\u4DB6-\u4DBF\u9FCD-\u9FFF\uA48D-\uA48F\uA4C7-\uA4CF\uA62C-\uA63F\uA698-\uA69E\uA6F8-\uA6FF\uA78F\uA794-\uA79F\uA7AB-\uA7F7\uA82C-\uA82F\uA83A-\uA83F\uA878-\uA87F\uA8C5-\uA8CD\uA8DA-\uA8DF\uA8FC-\uA8FF\uA954-\uA95E\uA97D-\uA97F\uA9CE\uA9DA-\uA9DD\uA9E0-\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A\uAA5B\uAA7C-\uAA7F\uAAC3-\uAADA\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F-\uABBF\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBC2-\uFBD2\uFD40-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFE\uFDFF\uFE1A-\uFE1F\uFE27-\uFE2F\uFE53\uFE67\uFE6C-\uFE6F\uFE75\uFEFD-\uFF00\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFDF\uFFE7\uFFEF-\uFFFB\uFFFE\uFFFF]/.test(str);

    // 检查凭证格式
    if (hasInvisibleChars(clientId)) {
        console.error('Client ID 包含不可见字符');
        throw new Error('Client ID contains invisible characters');
    }
    if (hasInvisibleChars(clientSecret)) {
        console.error('Client Secret 包含不可见字符');
        throw new Error('Client Secret contains invisible characters');
    }

    // 检查是否以正确的前缀开始
    if (!clientId.startsWith('A') && !clientId.startsWith('sb-')) {
        console.error('Client ID 格式不正确，应该以 "A" 或 "sb-" 开头');
        throw new Error('Invalid Client ID format');
    }

    // console.log('凭证格式验证:');
    // console.log('- Client ID 格式:', isValidClientId ? '有效' : '无效');
    // console.log('- Client Secret 格式:', isValidClientSecret ? '有效' : '无效');
    // console.log('- Client ID 前缀:', clientId.substring(0, 3));
    
    if (!isValidClientId || !isValidClientSecret) {
        throw new Error('PayPal credentials are invalid. Please check your environment variables.');
    }
    
    return {
        clientId,
        clientSecret,
        environment: process.env.PAYPAL_ENVIRONMENT || 'sandbox'
    };
};

// 初始化PayPal配置
const paypalConfig = validatePayPalCredentials();
const endpoint_url = paypalConfig.environment === 'sandbox' 
    ? 'https://api-m.sandbox.paypal.com' 
    : 'https://api-m.paypal.com';

// console.log('PayPal配置:');
// console.log('- 环境:', paypalConfig.environment);
// console.log('- API端点:', endpoint_url);

// Get PayPal access token
async function get_access_token() {
    console.log('=== 获取PayPal访问令牌 ===');
    console.log('时间:', new Date().toISOString());
    
    try {
        const auth = `${paypalConfig.clientId}:${paypalConfig.clientSecret}`;
        const authBase64 = Buffer.from(auth).toString('base64');
        
        console.log('认证信息:');
        console.log('- 环境:', paypalConfig.environment);
        console.log('- 端点:', endpoint_url);
        console.log('- Authorization Header 长度:', `Basic ${authBase64}`.length);
        
        const requestOptions = {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Accept-Language': 'en_US',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': `Basic ${authBase64}`
            },
            body: 'grant_type=client_credentials'
        };

        console.log('请求配置:');
        console.log('- Method:', requestOptions.method);
        console.log('- Headers:', Object.keys(requestOptions.headers));
        console.log('- Body:', requestOptions.body);

        console.log('开始请求访问令牌...');
        const response = await fetch(`${endpoint_url}/v1/oauth2/token`, requestOptions);

        const responseText = await response.text();
        console.log('PayPal API原始响应:', responseText);

        if (!response.ok) {
            let errorData;
            try {
                errorData = JSON.parse(responseText);
            } catch (e) {
                errorData = { error: 'Unknown error', error_description: responseText };
            }
            
            console.error('获取访问令牌失败:', {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries()),
                error: errorData
            });

            // 特定错误处理
            if (response.status === 401) {
                console.error('认证失败，可能的原因:');
                console.error('1. Client ID 或 Secret 不正确');
                console.error('2. 使用了生产环境的凭证访问沙箱环境，或反之');
                console.error('3. 凭证已过期或被撤销');
            }

            throw new Error(`Failed to get access token: ${errorData.error_description || 'Unknown error'}`);
        }

        const data = JSON.parse(responseText);
        console.log('获取访问令牌成功:', {
            tokenType: data.token_type,
            expiresIn: data.expires_in,
            scope: data.scope,
            nonce: data.nonce
        });
        
        return data.access_token;
    } catch (error) {
        console.error('获取PayPal访问令牌失败:', {
            error: error.message,
            stack: error.stack,
            details: error.details || '无详细信息'
        });
        throw error;
    }
}

// 生成订单号：时间戳（10位）+ 随机数（6位）
function generateOrderId() {
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
  return `${timestamp}${random}`;
}

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Create PayPal order
router.post('/create-order', async (req, res) => {
    const startTime = new Date();
    console.log('=== 创建PayPal订单 ===');
    console.log('请求时间:', startTime.toISOString());
    console.log('请求参数:', req.body);

    try {
        const { amount, currency = 'USD' } = req.body;
        
        // 验证金额
        if (!amount || isNaN(amount) || amount <= 0) {
            throw new Error('Invalid amount');
        }

        // 确保金额最多只有两位小数，避免PayPal 422错误
        const formattedAmount = parseFloat(amount).toFixed(2);
        console.log('格式化后的金额:', formattedAmount);

        console.log('开始获取访问令牌...');
        const access_token = await get_access_token();
        
        const order_data = {
            'intent': 'CAPTURE',
            'purchase_units': [{
                'amount': {
                    'currency_code': currency,
                    'value': formattedAmount
                }
            }]
        };

        console.log('创建订单数据:', order_data);

        const response = await fetch(`${endpoint_url}/v2/checkout/orders`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${access_token}`,
                'Prefer': 'return=representation'
            },
            body: JSON.stringify(order_data)
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('PayPal API错误:', {
                status: response.status,
                statusText: response.statusText,
                error: errorData
            });
            throw new Error(errorData.message || 'Failed to create PayPal order');
        }

        const orderData = await response.json();
        console.log('PayPal订单创建响应:', orderData);
        
        const endTime = new Date();
        const duration = endTime - startTime;
        console.log('订单创建完成:', {
            orderId: orderData.id,
            status: orderData.status,
            duration: `${duration}ms`
        });
        
        // 生成本地订单ID和UUID
        const localOrderId = generateOrderId();
        const uuid = generateUUID();
        
        // 保存订单到数据库
        const pool = await getConnection();
        const sql = `
            INSERT INTO payment_orders (
                id, order_id, user_id, product_id, product_name, 
                amount, payment_method, status, currency, trade_no,
                trade_status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;
        
        await pool.query(sql, [
            uuid,
            localOrderId,
            req.body.userId,
            req.body.productId,
            req.body.productName,
            formattedAmount, // 使用格式化后的金额
            'paypal',
            'pending',
            currency,
            orderData.id,  // PayPal订单ID作为trade_no
            orderData.status
        ]);
        
        res.json({
            success: true,
            data: {
                orderId: localOrderId,
                paypalOrderId: orderData.id,
                raw: orderData
            }
        });
    } catch (error) {
        console.error('创建PayPal订单失败:', {
            error: error.message,
            stack: error.stack,
            details: error.details || '无详细信息'
        });
        res.status(500).json({ 
            success: false, 
            error: error.message || 'Failed to create PayPal order'
        });
    }
});

// Capture PayPal payment
router.post('/capture-payment/:orderId', async (req, res) => {
    try {
        const { orderId } = req.params;
        const access_token = await get_access_token();

        const response = await fetch(`${endpoint_url}/v2/checkout/orders/${orderId}/capture`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${access_token}`
            }
        });

        const captureData = await response.json();
        
        if (captureData.error) {
            throw new Error(captureData.error.message);
        }

        res.json({
            success: true,
            data: captureData
        });
    } catch (error) {
        console.error('PayPal capture payment error:', error);
        res.status(500).json({ 
            success: false, 
            error: error.message || 'Failed to capture PayPal payment'
        });
    }
});

// Verify and capture PayPal payment
router.post('/verify-payment', async (req, res) => {
    const startTime = new Date();
    console.log('=== 验证PayPal支付 ===');
    console.log('请求时间:', startTime.toISOString());
    console.log('请求参数:', req.body);

    try {
        const { orderId, paypalOrderId } = req.body;
        
        if (!orderId || !paypalOrderId) {
            throw new Error('Missing required parameters');
        }

        // 获取访问令牌
        console.log('开始获取访问令牌...');
        const access_token = await get_access_token();

        // 获取PayPal订单详情
        console.log('获取PayPal订单详情...');
        const orderResponse = await fetch(`${endpoint_url}/v2/checkout/orders/${paypalOrderId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${access_token}`
            }
        });

        if (!orderResponse.ok) {
            const errorData = await orderResponse.json();
            console.error('获取PayPal订单详情失败:', {
                status: orderResponse.status,
                statusText: orderResponse.statusText,
                error: errorData
            });
            throw new Error('Failed to get PayPal order details');
        }

        const orderDetails = await orderResponse.json();
        console.log('PayPal订单详情:', orderDetails);

        // 获取数据库连接
        const pool = await getConnection();

        // 查询原始订单信息
        const [orderRows] = await pool.query(
            'SELECT * FROM payment_orders WHERE order_id = ?',
            [orderId]
        );

        if (orderRows.length === 0) {
            throw new Error('Order not found in database');
        }

        const dbOrder = orderRows[0];

        // 根据订单状态进行不同处理
        if (orderDetails.status === 'COMPLETED') {
            // 订单已经完成，直接更新状态
            await updateOrderStatus(pool, orderId, orderDetails, 'success', 'COMPLETED');
            await updateUserMembership(pool, dbOrder);
        } else if (orderDetails.status === 'APPROVED') {
            // 订单已批准，需要进行捕获
            console.log('开始捕获支付...');
            const captureResponse = await fetch(`${endpoint_url}/v2/checkout/orders/${paypalOrderId}/capture`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${access_token}`,
                    'Prefer': 'return=representation'
                }
            });

            if (!captureResponse.ok) {
                const errorData = await captureResponse.json();
                console.error('PayPal支付捕获失败:', {
                    status: captureResponse.status,
                    statusText: captureResponse.statusText,
                    error: errorData
                });
                
                // 捕获失败，更新订单状态为失败
                await updateOrderStatus(pool, orderId, orderDetails, 'failed', 'CAPTURE_FAILED');
                throw new Error('Failed to capture PayPal payment');
            }

            const captureData = await captureResponse.json();
            console.log('PayPal支付捕获成功:', captureData);

            if (captureData.status === 'COMPLETED') {
                // 捕获成功，更新订单状态
                await updateOrderStatus(pool, orderId, captureData, 'success', 'COMPLETED');
                await updateUserMembership(pool, dbOrder);
            } else {
                // 捕获完成但状态不是COMPLETED
                await updateOrderStatus(pool, orderId, captureData, 'failed', captureData.status);
                throw new Error(`Unexpected capture status: ${captureData.status}`);
            }
        } else {
            // 其他状态都视为失败
            await updateOrderStatus(pool, orderId, orderDetails, 'failed', orderDetails.status);
            throw new Error(`Invalid order status: ${orderDetails.status}`);
        }

        const endTime = new Date();
        const duration = endTime - startTime;
        console.log('支付验证完成:', {
            orderId,
            paypalOrderId,
            status: orderDetails.status,
            duration: `${duration}ms`
        });

        res.json({
            success: true,
            data: {
                orderId,
                paypalOrderId,
                status: orderDetails.status
            }
        });
    } catch (error) {
        console.error('PayPal支付验证失败:', {
            error: error.message,
            stack: error.stack,
            details: error.details || '无详细信息'
        });
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to verify PayPal payment'
        });
    }
});

// Verify PayPal subscription
router.post('/verify-subscription', async (req, res) => {
    const startTime = new Date();
    console.log('=== 验证PayPal订阅 ===');
    console.log('请求时间:', startTime.toISOString());
    console.log('请求参数:', req.body);

    try {
        const { orderId, subscriptionId } = req.body;
        
        if (!orderId || !subscriptionId) {
            throw new Error('Missing required parameters');
        }

        // 获取访问令牌
        console.log('开始获取访问令牌...');
        const access_token = await get_access_token();

        // 获取PayPal订阅详情
        console.log('获取PayPal订阅详情...');
        const subscriptionResponse = await fetch(`${endpoint_url}/v1/billing/subscriptions/${subscriptionId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${access_token}`
            }
        });

        if (!subscriptionResponse.ok) {
            const errorData = await subscriptionResponse.json();
            console.error('获取PayPal订阅详情失败:', {
                status: subscriptionResponse.status,
                statusText: subscriptionResponse.statusText,
                error: errorData
            });
            throw new Error('Failed to get PayPal subscription details');
        }

        const subscriptionDetails = await subscriptionResponse.json();
        console.log('PayPal订阅详情:', subscriptionDetails);

        // 获取数据库连接
        const pool = await getConnection();

        // 查询原始订单信息
        const [orderRows] = await pool.query(
            'SELECT * FROM payment_orders WHERE order_id = ?',
            [orderId]
        );

        if (orderRows.length === 0) {
            throw new Error('Order not found in database');
        }

        const dbOrder = orderRows[0];

        // 根据订阅状态进行处理
        if (subscriptionDetails.status === 'ACTIVE') {
            // 更新订单状态
            await pool.query(
                `UPDATE payment_orders 
                 SET status = 'success',
                     trade_status = ?,
                     updated_at = NOW()
                 WHERE order_id = ?`,
                [subscriptionDetails.status, orderId]
            );

            // 更新用户会员状态
            await updateUserMembership(pool, dbOrder);

            const endTime = new Date();
            const duration = endTime - startTime;
            console.log('订阅验证完成:', {
                orderId,
                subscriptionId,
                status: subscriptionDetails.status,
                duration: `${duration}ms`
            });

            res.json({
                success: true,
                data: {
                    orderId,
                    subscriptionId,
                    status: subscriptionDetails.status
                }
            });
        } else {
            // 订阅状态不是ACTIVE，更新订单状态为失败
            await pool.query(
                `UPDATE payment_orders 
                 SET status = 'failed',
                     trade_status = ?,
                     updated_at = NOW()
                 WHERE order_id = ?`,
                [subscriptionDetails.status, orderId]
            );

            throw new Error(`Invalid subscription status: ${subscriptionDetails.status}`);
        }
    } catch (error) {
        console.error('PayPal订阅验证失败:', {
            error: error.message,
            stack: error.stack,
            details: error.details || '无详细信息'
        });
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to verify PayPal subscription'
        });
    }
});

// 辅助函数：更新订单状态
async function updateOrderStatus(pool, orderId, paypalData, status, tradeStatus) {
    // 格式化时间为MySQL格式 (YYYY-MM-DD HH:mm:ss)
    const formatDateTime = (date) => {
        return date.toISOString().slice(0, 19).replace('T', ' ');
    };

    const currentTime = formatDateTime(new Date());
    let captureDetails = null;

    // 获取capture details（如果存在）
    if (paypalData.purchase_units && 
        paypalData.purchase_units[0].payments && 
        paypalData.purchase_units[0].payments.captures) {
        captureDetails = paypalData.purchase_units[0].payments.captures[0];
    }

    const updateData = {
        status,
        trade_status: tradeStatus,
        trade_state_desc: `PayPal payment ${tradeStatus}`,
        trade_time: currentTime,
        updated_at: currentTime
    };

    // 如果是成功状态，添加额外信息
    if (status === 'success' && captureDetails) {
        updateData.success_time = currentTime;
        updateData.trade_no = captureDetails.id;
        updateData.payer_currency = captureDetails.amount.currency_code;
    }

    // 构建SQL查询
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);
    const sql = `
        UPDATE payment_orders 
        SET ${fields.map(field => `${field} = ?`).join(', ')}
        WHERE order_id = ?
    `;

    await pool.query(sql, [...values, orderId]);
    
    console.log('订单状态已更新:', {
        orderId,
        status,
        tradeStatus,
        updateTime: currentTime
    });
}

// 辅助函数：更新用户会员状态
async function updateUserMembership(pool, order) {
    if (!order.user_id) return;

    // 根据商品ID判断支付类型
    if (order.product_id.includes('reads') || order.product_id === 'pay_per_use') {
        console.log(`[PayPal支付] ${order.order_id} 检测到按次付费订单`);
        // 从商品名称中解析购买次数
        const readCount = parseInt(order.product_name?.split('-').pop()) || 1;
        console.log(`[PayPal支付] ${order.order_id} 获取购买次数:`, {
            product_name: order.product_name,
            readCount
        });
        
        if (readCount > 0) {
            // 先查询用户当前的剩余次数
            const [userRows] = await pool.query(
                'SELECT remaining_reads, has_internal_privilege, has_used_discount FROM users WHERE id = ?',
                [order.user_id]
            );
            const currentReads = userRows[0]?.remaining_reads || 0;
            
            console.log(`[PayPal支付] ${order.order_id} 更新前用户信息:`, {
                user_id: order.user_id,
                currentReads,
                readCount,
                newTotal: currentReads + readCount
            });

            // 更新剩余次数
            await pool.query(
                `UPDATE users 
                 SET remaining_reads = remaining_reads + ?,
                     vip_status = 'none',
                     vip_type = 'none',
                     vip_start_date = NULL,
                     vip_end_date = NULL
                 WHERE id = ?`,
                [readCount, order.user_id]
            );

            // 验证更新是否成功
            const [verifyRows] = await pool.query(
                'SELECT remaining_reads FROM users WHERE id = ?',
                [order.user_id]
            );
            console.log(`[PayPal支付] ${order.order_id} 更新后用户信息:`, {
                user_id: order.user_id,
                newReads: verifyRows[0]?.remaining_reads
            });
            
            // 检查用户是否有内部折扣权限且未使用过
            const hasInternalPrivilege = userRows[0]?.has_internal_privilege === 1;
            const hasUsedDiscount = userRows[0]?.has_used_discount === 1;
            
            if (hasInternalPrivilege && !hasUsedDiscount) {
                console.log(`[PayPal支付] ${order.order_id} 用户有内部折扣权限且未使用过，标记订单和用户`);
                
                // 更新订单的discount_applied字段
                await pool.query(
                    `UPDATE payment_orders SET discount_applied = 1 WHERE order_id = ?`,
                    [order.order_id]
                );
                
                // 更新用户的has_used_discount字段
                await pool.query(
                    `UPDATE users SET has_used_discount = 1 WHERE id = ?`,
                    [order.user_id]
                );
                
                console.log(`[PayPal支付] ${order.order_id} 内部折扣标记完成`);
            }
        }
    } else {
        // 会员订单处理逻辑
        const vipType = order.product_id === 'yearly' ? 'yearly' : 'monthly';
        const membershipDays = vipType === 'yearly' ? 365 : 30;
        
        console.log(`[PayPal支付] ${order.order_id} 会员订单信息:`, {
            vipType,
            membershipDays
        });

        // 先查询用户当前的会员状态
        const [userRows] = await pool.query(
            'SELECT vip_status, vip_type, vip_end_date, has_internal_privilege, has_used_discount FROM users WHERE id = ?',
            [order.user_id]
        );
        
        const currentUser = userRows[0];
        let newEndDate;
        
        if (currentUser && currentUser.vip_status === 'active' && currentUser.vip_end_date) {
            // 如果用户已经是活跃会员，从当前到期日开始计算
            const currentEndDate = new Date(currentUser.vip_end_date);
            
            // 如果是月度会员升级到年度会员，则从当前到期日增加一年
            if (currentUser.vip_type === 'monthly' && vipType === 'yearly') {
                newEndDate = new Date(currentEndDate.setFullYear(currentEndDate.getFullYear() + 1));
            } else {
                // 其他情况，从当前到期日增加相应天数
                newEndDate = new Date(currentEndDate.setDate(currentEndDate.getDate() + membershipDays));
            }
        } else {
            // 如果用户不是活跃会员，从当前时间开始计算
            newEndDate = new Date();
            newEndDate.setDate(newEndDate.getDate() + membershipDays);
        }

        // 格式化日期为MySQL datetime格式
        const formattedEndDate = newEndDate.toISOString().slice(0, 19).replace('T', ' ');

        await pool.query(
            `UPDATE users 
             SET vip_status = 'active',
                 vip_type = ?,
                 vip_start_date = CURRENT_TIMESTAMP,
                 vip_end_date = ?,
                 whether_paypal = TRUE
             WHERE id = ?`,
            [vipType, formattedEndDate, order.user_id]
        );

        console.log('用户会员状态已更新:', {
            userId: order.user_id,
            vipType,
            endDate: formattedEndDate,
            productId: order.product_id,
            whether_paypal: true
        });
        
        // 检查用户是否有内部折扣权限且未使用过
        const hasInternalPrivilege = currentUser?.has_internal_privilege === 1;
        const hasUsedDiscount = currentUser?.has_used_discount === 1;
        
        if (hasInternalPrivilege && !hasUsedDiscount) {
            console.log(`[PayPal支付] ${order.order_id} 用户有内部折扣权限且未使用过，标记订单和用户`);
            
            // 更新订单的discount_applied字段
            await pool.query(
                `UPDATE payment_orders SET discount_applied = 1 WHERE order_id = ?`,
                [order.order_id]
            );
            
            // 更新用户的has_used_discount字段
            await pool.query(
                `UPDATE users SET has_used_discount = 1 WHERE id = ?`,
                [order.user_id]
            );
            
            console.log(`[PayPal支付] ${order.order_id} 内部折扣标记完成`);
        }
    }
}

// Check order status
router.get('/order-status/:orderId', async (req, res) => {
    console.log('=== 检查PayPal订单状态 ===');
    console.log('订单ID:', req.params.orderId);

    try {
        const access_token = await get_access_token();
        const response = await fetch(`${endpoint_url}/v2/checkout/orders/${req.params.orderId}`, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${access_token}`
            }
        });

        const orderData = await response.json();
        console.log('PayPal订单状态:', orderData);
        
        if (orderData.error) {
            throw new Error(orderData.error.message);
        }

        res.json({
            success: true,
            data: {
                tradeState: orderData.status,
                tradeStateDesc: orderData.status
            }
        });
    } catch (error) {
        console.error('检查PayPal订单状态失败:', {
            error: error.message,
            stack: error.stack,
            details: error.details || '无详细信息'
        });
        res.status(500).json({ 
            success: false, 
            error: error.message || 'Failed to check PayPal order status'
        });
    }
});

module.exports = router;