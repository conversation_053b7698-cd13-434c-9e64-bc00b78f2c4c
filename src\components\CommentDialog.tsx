import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { FaStar, FaChevronDown, FaCheck, FaHandsClapping } from 'react-icons/fa6';
import axiosInstance from '../utils/axios';
import { isMobileDevice } from '../utils/shareUtils';

interface Comment {
  id: string;
  userId: string;
  username: string;
  rating: number;
  content: string;
  createdAt: string;
  parentId?: string;
  replyToUserId?: string;
  replyToUsername?: string;
  replyCount?: number;
  likeCount?: number;
}

interface CommentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId?: string;
  pageType?: 'yes-no-tarot' | 'tarot-result' | 'daily-fortune';
}

const CommentDialog: React.FC<CommentDialogProps> = ({ 
  isOpen, 
  onClose, 
  sessionId,
  pageType = 'yes-no-tarot'
}) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const { user } = useUser();
  const isDark = theme === 'dark';
  
  const [rating, setRating] = useState<number | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [showToast, setShowToast] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  // 移除activeTab状态，不再需要标签页切换
  const [isLoading, setIsLoading] = useState(false);
  const [editorExpanded, setEditorExpanded] = useState(false);
  const [editorContent, setEditorContent] = useState(''); // 添加内容状态

  // 文本编辑相关状态
  const editorRef = useRef<HTMLDivElement>(null);
  const [isBoldActive, setIsBoldActive] = useState(false);
  const [isItalicActive, setIsItalicActive] = useState(false);


  // 回复相关状态
  const [replyingTo, setReplyingTo] = useState<any>(null);
  const [replyContent, setReplyContent] = useState('');
  const [expandedReplies, setExpandedReplies] = useState<Set<string>>(new Set());
  const [commentReplies, setCommentReplies] = useState<{[key: string]: any[]}>({});

  // 回复输入框格式状态
  const [replyBoldActive, setReplyBoldActive] = useState(false);
  const [replyItalicActive, setReplyItalicActive] = useState(false);

  // 点赞相关状态
  const [userLikeCounts, setUserLikeCounts] = useState<{[key: string]: number}>({});
  const [commentLikeCounts, setCommentLikeCounts] = useState<{[key: string]: number}>({});

  // 拍手动画状态
  const [clapAnimations, setClapAnimations] = useState<{[key: string]: {count: number, visible: boolean, fadeOut: boolean, bounce: boolean}}>({});
  const [clickAnimations, setClickAnimations] = useState<{[key: string]: boolean}>({});


  // 排序相关状态
  const [sortBy, setSortBy] = useState<'hot' | 'recent'>('hot');
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [allComments, setAllComments] = useState<Comment[]>([]); // 存储所有评论数据

  // 使用useRef来存储定时器，避免状态更新导致的问题
  const clapTimeouts = useRef<{[key: string]: NodeJS.Timeout}>({});

  // 排序评论
  const sortComments = (comments: Comment[], sortType: 'hot' | 'recent') => {
    const sorted = [...comments];

    // 分离当前用户的评论和其他评论
    const userComments = sorted.filter(comment => user && comment.userId === user.id);
    const otherComments = sorted.filter(comment => !user || comment.userId !== user.id);

    // 对其他评论进行排序
    let sortedOtherComments;
    if (sortType === 'recent') {
      // 按时间排序（最新的在前）
      sortedOtherComments = otherComments.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    } else {
      // 按相关性排序（点赞数 + 评分权重）
      sortedOtherComments = otherComments.sort((a, b) => {
        const scoreA = (a.likeCount || 0) + (a.rating || 0) * 2;
        const scoreB = (b.likeCount || 0) + (b.rating || 0) * 2;
        return scoreB - scoreA;
      });
    }

    // 将当前用户的评论放在最前面，然后是其他排序后的评论
    return [...userComments, ...sortedOtherComments];
  };

  // 处理排序变更
  const handleSortChange = (newSortBy: 'hot' | 'recent') => {
    setSortBy(newSortBy);
    setShowSortDropdown(false);
    const sortedComments = sortComments(allComments, newSortBy);
    setComments(sortedComments);
  };



  // 粗体功能
  const handleBold = () => {
    const editor = editorRef.current;
    if (!editor) return;

    editor.focus();
    document.execCommand('bold', false);

    // 切换按钮状态
    setIsBoldActive(!isBoldActive);
  };

  // 斜体功能
  const handleItalic = () => {
    const editor = editorRef.current;
    if (!editor) return;

    editor.focus();
    document.execCommand('italic', false);

    // 切换按钮状态
    setIsItalicActive(!isItalicActive);
  };

  // 回复输入框粗体功能
  const handleReplyBold = () => {
    document.execCommand('bold', false);
    setReplyBoldActive(!replyBoldActive);
  };

  // 回复输入框斜体功能
  const handleReplyItalic = () => {
    document.execCommand('italic', false);
    setReplyItalicActive(!replyItalicActive);
  };

  // 键盘快捷键处理
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key.toLowerCase()) {
        case 'b':
          e.preventDefault();
          handleBold();
          break;
        case 'i':
          e.preventDefault();
          handleItalic();
          break;
      }
    }
  };

  // 处理富文本编辑器内容变化
  const handleEditorInput = () => {
    const editor = editorRef.current;
    if (!editor) return;

    const content = editor.innerText || '';
    setEditorContent(content); // 更新内容状态以触发重新渲染

    if (content.trim()) {
      setSubmitError(null);
    }
  };

  // 将HTML内容转换为纯文本用于提交
  const getPlainTextContent = () => {
    const editor = editorRef.current;
    if (!editor) return '';

    return editor.innerText || '';
  };

  // 将HTML内容转换为Markdown格式用于存储
  const getMarkdownContent = () => {
    const editor = editorRef.current;
    if (!editor) return '';

    let html = editor.innerHTML;

    // 将HTML标签转换为Markdown
    html = html.replace(/<strong>(.*?)<\/strong>/g, '**$1**');
    html = html.replace(/<b>(.*?)<\/b>/g, '**$1**');
    html = html.replace(/<em>(.*?)<\/em>/g, '*$1*');
    html = html.replace(/<i>(.*?)<\/i>/g, '*$1*');

    // 移除其他HTML标签
    html = html.replace(/<[^>]*>/g, '');

    // 处理换行
    html = html.replace(/<br\s*\/?>/gi, '\n');
    html = html.replace(/<div>/gi, '\n');
    html = html.replace(/<\/div>/gi, '');

    return html;
  };

  // 简单的Markdown渲染函数（用于显示已有评论）
  const renderMarkdown = (text: string) => {
    // 处理粗体 **text**
    let rendered = text.replace(/\*\*(.*?)\*\*/g, `<strong style="font-weight: 800; color: ${isDark ? '#F3F4F6' : '#111827'}; text-shadow: 0 0 1px ${isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.3)'};">$1</strong>`);
    // 处理斜体 *text*
    rendered = rendered.replace(/\*(.*?)\*/g, `<em style="font-style: italic; color: ${isDark ? '#E5E7EB' : '#374151'}; font-weight: 500;">$1</em>`);

    return { __html: rendered };
  };
  
  // 检测设备类型
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || isMobileDevice());
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 加载评论数据
  useEffect(() => {
    if (isOpen) {
      loadComments();
    }
  }, [isOpen]);

  // 点击外部关闭下拉菜单
  const sortDropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showSortDropdown && sortDropdownRef.current) {
        const target = event.target as HTMLElement;
        if (!sortDropdownRef.current.contains(target)) {
          setShowSortDropdown(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSortDropdown]);



  // 组件卸载时提交待处理的点赞和清理定时器
  useEffect(() => {
    return () => {
      // 清理所有定时器
      Object.values(clapTimeouts.current).forEach(timeout => {
        clearTimeout(timeout);
      });
      clapTimeouts.current = {};

      // 组件卸载时异步提交待处理的点赞
      (async () => {
        try {
          const { submitPendingLikes, getPendingLikesCount } = await import('../services/commentLikeService');
          const pendingCount = getPendingLikesCount();
          if (pendingCount > 0 && user) {
            await submitPendingLikes();
          }
        } catch (error) {
          console.log('组件卸载时提交点赞失败:', error);
        }
      })();
    };
  }, [user]);

  const loadComments = async () => {
    setIsLoading(true);
    try {
      // 加载所有评论，不区分会话
      const response = await axiosInstance.get('/api/comments/all');
      if (response.data.success) {
        const allCommentsData = response.data.data.comments || [];
        // 只显示顶级评论（没有parentId的评论）
        const topLevelComments = allCommentsData.filter((comment: any) => !comment.parentId);

        // 保存所有评论数据
        setAllComments(topLevelComments);

        // 应用当前排序
        const sortedComments = sortComments(topLevelComments, sortBy);
        setComments(sortedComments);

        // 初始化点赞数量
        const likeCounts: {[key: string]: number} = {};
        topLevelComments.forEach((comment: any) => {
          likeCounts[comment.id] = comment.likeCount || 0;
        });
        setCommentLikeCounts(likeCounts);

        // 加载用户点赞次数
        if (user && topLevelComments.length > 0) {
          const commentIds = topLevelComments.map((comment: any) => comment.id);
          await loadUserLikeCounts(commentIds);
        }

        // 注释掉自动填充用户之前评论的逻辑，避免提交后重新显示
        // 检查当前用户是否已经对当前会话评论过
        // if (user && sessionId) {
        //   const userComment = response.data.data.comments.find(
        //     (comment: any) => comment.userId === user.id && comment.sessionId === sessionId
        //   );
        //   if (userComment) {
        //     setRating(userComment.rating || null); // 评分可能为空
        //     if (editorRef.current) {
        //       editorRef.current.innerHTML = userComment.content;
        //     }
        //   }
        // }
      }
    } catch (error) {
      console.log('加载评论失败:', error);
      setComments([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 加载评论的回复
  const loadReplies = async (commentId: string) => {
    try {
      const response = await axiosInstance.get(`/api/comments/${commentId}/replies`);
      if (response.data.success) {
        setCommentReplies(prev => ({
          ...prev,
          [commentId]: response.data.data.replies || []
        }));
      }
    } catch (error) {
      console.log('加载回复失败:', error);
    }
  };

  // 切换回复展开状态
  const toggleReplies = async (commentId: string) => {
    const newExpanded = new Set(expandedReplies);
    if (newExpanded.has(commentId)) {
      newExpanded.delete(commentId);
    } else {
      newExpanded.add(commentId);
      // 如果还没有加载过回复，则加载
      if (!commentReplies[commentId]) {
        await loadReplies(commentId);
      }
    }
    setExpandedReplies(newExpanded);
  };

  // 开始回复
  const startReply = (comment: any) => {
    setReplyingTo(comment);
    setReplyContent('');
  };

  // 取消回复
  const cancelReply = () => {
    setReplyingTo(null);
    setReplyContent('');
  };

  // 提交回复
  const submitReply = async () => {
    if (!replyingTo || !replyContent.trim()) return;
    
    setIsSubmitting(true);
    
    try {
      await axiosInstance.post('/api/comments', {
        content: replyContent.trim(),
        parentId: replyingTo.id,
        replyToUserId: replyingTo.userId,
        pageType: pageType,
        sessionId: sessionId || null,
        language: i18n.language
      });

      // 重新加载回复
      await loadReplies(replyingTo.id);

      // 清空回复状态
      cancelReply();

      // 确保回复区域是展开的
      setExpandedReplies(prev => new Set([...prev, replyingTo.id]));
    } catch (error: any) {
      console.log('回复失败:', error);
      const errorMessage = error.response?.data?.message || '回复提交失败，请重试';
      console.log(errorMessage);
      // 可以添加一个提示或者Toast来显示错误信息
      // 这里暂时只在控制台输出
    } finally {
      setIsSubmitting(false);
    }
  };

  // 加载用户点赞次数
  const loadUserLikeCounts = async (commentIds: string[]) => {
    if (!user || !commentIds.length) return;

    try {
      const { getCommentsLikeCounts } = await import('../services/commentLikeService');
      const likeCounts = await getCommentsLikeCounts(commentIds);

      setUserLikeCounts(likeCounts);
    } catch (error) {
      console.log('加载用户点赞次数失败:', error);
    }
  };

  // 添加点赞（支持连击）
  const addLike = async (commentId: string) => {
    if (!user) return;

    try {
      const { addCommentLikeLocal } = await import('../services/commentLikeService');

      // 本地添加点赞
      const result = addCommentLikeLocal(commentId, 1);

      // 更新用户点赞次数显示
      setUserLikeCounts(prev => ({
        ...prev,
        [commentId]: result.totalCount
      }));

      // 更新总点赞数量显示
      setCommentLikeCounts(prev => ({
        ...prev,
        [commentId]: (prev[commentId] || 0) + 1
      }));



      // 触发点击动画
      setClickAnimations(prev => ({
        ...prev,
        [commentId]: true
      }));

      // 0.3秒后停止点击动画
      setTimeout(() => {
        setClickAnimations(prev => ({
          ...prev,
          [commentId]: false
        }));
      }, 300);

      // 添加或更新飞出动画（使用useRef防抖机制）
      // 清除之前的定时器
      if (clapTimeouts.current[commentId]) {
        clearTimeout(clapTimeouts.current[commentId]);
      }

      // 更新动画状态
      setClapAnimations(prev => {
        const existing = prev[commentId];
        return {
          ...prev,
          [commentId]: {
            count: existing ? existing.count + 1 : 1,
            visible: true,
            fadeOut: false,
            bounce: true
          }
        };
      });

      // 0.3秒后停止bounce动画
      setTimeout(() => {
        setClapAnimations(prev => ({
          ...prev,
          [commentId]: prev[commentId] ? {
            ...prev[commentId],
            bounce: false
          } : prev[commentId]
        }));
      }, 300);

      // 设置新的定时器
      clapTimeouts.current[commentId] = setTimeout(() => {
        // 先触发fadeOut动画
        setClapAnimations(prev => ({
          ...prev,
          [commentId]: prev[commentId] ? {
            ...prev[commentId],
            fadeOut: true
          } : prev[commentId]
        }));

        // 500ms后完全移除元素
        setTimeout(() => {
          setClapAnimations(prev => {
            const { [commentId]: removed, ...rest } = prev;
            return rest;
          });
        }, 500);

        delete clapTimeouts.current[commentId];
      }, 1500);

    } catch (error) {
      console.log('点赞操作失败:', error);
    }
  };

  // 提交评论
  const handleSubmit = async () => {
    const plainTextContent = getPlainTextContent();
    const markdownContent = getMarkdownContent();

    if (!plainTextContent.trim()) {
      setSubmitError(t('comment.please_enter_comment', '请输入评论内容'));
      return;
    }

    if (!user) {
      setSubmitError(t('comment.login_required', '请先登录后再评论'));
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      await axiosInstance.post('/api/comments', {
        sessionId,
        rating: rating, // 可以为 null
        content: markdownContent.trim(),
        pageType,
        language: i18n.language
      });

      setShowToast(true);
      setTimeout(() => setShowToast(false), 2000);

      // 重新加载评论列表
      await loadComments();

      // 提交成功后清空编辑框内容并收回编辑框
      if (editorRef.current) {
        editorRef.current.innerHTML = '';
      }
      setEditorContent(''); // 清空内容状态
      setRating(null);
      setEditorExpanded(false);
      setSubmitError(null); // 清空错误信息
      setIsBoldActive(false); // 重置格式化按钮状态
      setIsItalicActive(false);

    } catch (error: any) {
      // console.log('提交评论失败:', error);
      // console.log('错误详情:', error.response?.data);
      setSubmitError(error.response?.data?.message || t('comment.submit_error', '提交失败，请重试'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理对话框关闭
  const handleClose = async () => {
    // 在关闭前提交待处理的点赞
    try {
      const { submitPendingLikes, getPendingLikesCount } = await import('../services/commentLikeService');
      const pendingCount = getPendingLikesCount();
      if (pendingCount > 0 && user) {
        await submitPendingLikes();
      }
    } catch (error) {
      console.log('提交待处理点赞失败:', error);
    }

    setRating(null);
    if (editorRef.current) {
      editorRef.current.innerHTML = '';
    }
    setEditorContent(''); // 清空内容状态
    setSubmitError(null);
    onClose();
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    // 处理数据库时间格式，确保正确解析
    let date: Date;
    if (dateString.includes('T')) {
      // ISO格式
      date = new Date(dateString);
    } else {
      // MySQL DATETIME格式 (YYYY-MM-DD HH:mm:ss)，假设为UTC时间
      date = new Date(dateString + 'Z'); // 添加Z表示UTC时间
    }

    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const weeks = Math.floor(days / 7);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    if (minutes < 1) {
      return t('comment.time.just_now', '刚刚');
    } else if (hours < 1) {
      return t('comment.time.minutes_ago', '{{count}}分钟前', { count: minutes });
    } else if (days < 1) {
      return t('comment.time.hours_ago', '{{count}}小时前', { count: hours });
    } else if (days < 7) {
      return t('comment.time.days_ago', '{{count}}天前', { count: days });
    } else if (weeks < 4) {
      return t('comment.time.weeks_ago', '{{count}}周前', { count: weeks });
    } else if (months < 12) {
      return t('comment.time.months_ago', '{{count}}个月前', { count: months });
    } else {
      return t('comment.time.years_ago', '{{count}}年前', { count: years });
    }
  };

  // 渲染星级评分
  const renderStars = (rating: number, interactive: boolean = false, onRate?: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => interactive && onRate && onRate(star)}
            disabled={!interactive}
            className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`}
          >
            <FaStar
              className={`w-4 h-4 ${
                star <= rating 
                  ? 'text-yellow-400' 
                  : isDark ? 'text-gray-600' : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  return (
    <>
      {/* 添加拍手动画的CSS样式 */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes clapFloat {
            0% {
              opacity: 1;
              transform: translateX(-50%) translateY(0) scale(1);
            }
            100% {
              opacity: 1;
              transform: translateX(-50%) translateY(0) scale(1);
            }
          }

          @keyframes clapBounce {
            0% {
              transform: translateX(-50%) translateY(0) scale(1);
            }
            50% {
              transform: translateX(-50%) translateY(0) scale(1.1);
            }
            100% {
              transform: translateX(-50%) translateY(0) scale(1);
            }
          }

          @keyframes clapFadeOut {
            0% {
              opacity: 1;
              transform: translateX(-50%) translateY(0) scale(1);
            }
            100% {
              opacity: 0;
              transform: translateX(-50%) translateY(-30px) scale(1);
            }
          }

          @keyframes clapPulse {
            0% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.2);
            }
            100% {
              transform: scale(1);
            }
          }

          .clap-icon-animate {
            animation: clapPulse 0.3s ease-in-out;
          }

          /* 回复输入框placeholder样式 */
          [contenteditable][data-placeholder]:empty::before {
            content: attr(data-placeholder);
            color: #9CA3AF;
            pointer-events: none;
          }

          .dark [contenteditable][data-placeholder]:empty::before {
            color: #6B7280;
          }
        `
      }} />

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={`fixed inset-0 z-50 ${isMobile ? 'flex items-end justify-center' : ''}`}
          >
          {/* Toast提示 */}
          <div className="fixed top-0 left-0 w-full flex justify-center items-center z-[9999] pointer-events-none">
            <AnimatePresence>
              {showToast && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 60 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className={`px-4 py-2 rounded-lg shadow-lg flex items-center pointer-events-auto ${
                    isDark ? 'bg-green-700 text-white' : 'bg-green-500 text-white'
                  }`}
                >
                  <FaCheck className="mr-2" />
                  {t('comment.submit_success', '评论提交成功')}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* 背景遮罩 - 仅在移动端显示 */}
          {isMobile && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.6 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black"
              onClick={handleClose}
            />
          )}

          {/* PC端点击外部区域关闭 */}
          {!isMobile && (
            <div
              className="absolute inset-0"
              onClick={handleClose}
            />
          )}

          {/* 侧边栏内容 */}
          <motion.div
            initial={isMobile ? { y: '100%' } : { x: '100%' }}
            animate={isMobile ? { y: 0 } : { x: 0 }}
            exit={isMobile ? { y: '100%' } : { x: '100%' }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            onClick={(e) => e.stopPropagation()}
            className={`${isDark ? 'bg-gray-900' : 'bg-white'}
              ${isMobile
                ? 'relative w-full rounded-t-xl p-4'
                : 'absolute right-0 top-0 h-full w-[28rem] pt-16 px-6 pb-6 border-l border-gray-200 dark:border-gray-700'
              }
              shadow-2xl overflow-y-auto ${isMobile ? 'max-h-[75vh]' : ''} z-50`}
          >
            {/* 移动端顶部拖动条 */}
            {isMobile && (
              <div className="w-16 h-1 bg-gray-300 dark:bg-gray-700 rounded-full mx-auto mb-4"></div>
            )}

            {/* 标题和关闭按钮 */}
            <div className={`flex items-center justify-between ${isMobile ? 'mb-5' : 'mb-6'} ${isMobile ? 'pt-2' : 'pt-0'}`}>
              <h2 className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold ${isDark ? 'text-white' : 'text-gray-800'} flex-1 text-left`}>
                {t('comment.title', '评价')} ({comments.length})
              </h2>
              <button
                onClick={handleClose}
                className={`p-2 rounded-full ${
                  isDark ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-800' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                } transition-all focus:outline-none`}
                aria-label={t('common.close', '关闭')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 写评价区域 */}
            {user ? (
              <div className="mb-6">
                {/* 用户信息 */}
                <div className="flex items-center mb-4">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    isDark ? 'bg-purple-600' : 'bg-purple-500'
                  } text-white text-lg font-medium mr-3`}>
                    {user.username?.charAt(0)?.toUpperCase() || 'U'}
                  </div>
                  <div className="flex-1">
                    <div className={`font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                      {user.username || t('comment.user', '用户')}
                    </div>
                  </div>
                </div>

                {/* 评论输入框容器 */}
                <div className="mb-2"></div>
                <div 
                  className={`rounded-lg ${isDark ? 'bg-gray-800' : 'bg-gray-100'} p-4 transition-all duration-200 ${
                    editorExpanded ? 'min-h-[150px]' : 'min-h-[50px] cursor-text'
                  }`}
                  onClick={() => {
                    if (!editorExpanded) {
                      setEditorExpanded(true);
                      setTimeout(() => {
                        if (editorRef.current) {
                          editorRef.current.focus();
                        }
                      }, 100);
                    }
                  }}
                >
                  {/* 富文本编辑器 */}
                  <div
                    ref={editorRef}
                    contentEditable={editorExpanded}
                    onInput={handleEditorInput}
                    onKeyDown={handleKeyDown}
                    className={`w-full p-0 border-0 bg-transparent text-base rich-text-editor ${
                      isDark
                        ? 'text-gray-200'
                        : 'text-gray-800'
                    } focus:outline-none transition-all ${
                      editorExpanded ? 'min-h-[80px]' : 'min-h-[24px] max-h-[24px] overflow-hidden'
                    }`}
                    style={{
                      wordWrap: 'break-word',
                      overflowWrap: 'break-word'
                    }}
                    data-placeholder={t('comment.share_thoughts', '分享您的想法...')}
                    suppressContentEditableWarning={true}
                  />

                  {/* CSS样式通过className处理 */}
                  <style dangerouslySetInnerHTML={{
                    __html: `
                      .rich-text-editor:empty:before {
                        content: attr(data-placeholder);
                        color: ${isDark ? '#9CA3AF' : '#6B7280'};
                        pointer-events: none;
                      }
                      .rich-text-editor:focus:empty:before {
                        content: attr(data-placeholder);
                        color: ${isDark ? '#9CA3AF' : '#6B7280'};
                        pointer-events: none;
                      }
                      .rich-text-editor strong,
                      .rich-text-editor b {
                        font-weight: 800 !important;
                        color: ${isDark ? '#F3F4F6' : '#111827'} !important;
                        text-shadow: 0 0 1px ${isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.3)'};
                      }
                      .rich-text-editor em,
                      .rich-text-editor i {
                        font-style: italic !important;
                        color: ${isDark ? '#E5E7EB' : '#374151'} !important;
                        font-weight: 500;
                      }
                      /* 移除默认的聚焦边框 */
                      .rich-text-editor:focus {
                        outline: none !important;
                        box-shadow: none !important;
                        border: none !important;
                      }
                      /* 移除contentEditable元素的默认边框 */
                      [contenteditable] {
                        outline: none !important;
                        box-shadow: none !important;
                        border: none !important;
                      }
                      /* 防止在点击时出现闪烁边框 */
                      [contenteditable]:focus-visible {
                        outline: none !important;
                        box-shadow: none !important;
                        border: none !important;
                      }
                    `
                  }} />

                  {/* 工具栏 */}
                  {editorExpanded && (
                    <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
                      {/* 左侧区域：格式按钮和评分 */}
                      <div className="flex items-center">
                        {/* 格式按钮 */}
                        <div className="flex items-center space-x-1 mr-4">
                          <button
                            type="button"
                            onClick={handleBold}
                            className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                              isBoldActive
                                ? isDark
                                  ? 'bg-gray-600 text-gray-200 hover:bg-gray-500'
                                  : 'bg-gray-300 text-gray-800 hover:bg-gray-400'
                                : isDark
                                  ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                  : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                            }`}
                            title={t('editor.bold', '粗体 (Ctrl+B)')}
                          >
                            <span className="font-bold text-base">B</span>
                          </button>
                          <button
                            type="button"
                            onClick={handleItalic}
                            className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                              isItalicActive
                                ? isDark
                                  ? 'bg-gray-600 text-gray-200 hover:bg-gray-500'
                                  : 'bg-gray-300 text-gray-800 hover:bg-gray-400'
                                : isDark
                                  ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                  : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                            }`}
                            title={t('editor.italic', '斜体 (Ctrl+I)')}
                          >
                            <span className="italic text-base">i</span>
                          </button>
                        </div>

                                                  {/* 评分组件 */}
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center space-x-1">
                            {renderStars(rating || 0, true, (newRating) => {
                              setRating(newRating);
                            })}
                          </div>
                        </div>
                      </div>

                      {/* 右侧操作按钮 */}
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => {
                            setRating(null);
                            if (editorRef.current) {
                              editorRef.current.innerHTML = '';
                            }
                            setEditorContent(''); // 清空内容状态
                            setSubmitError(null);
                            setEditorExpanded(false);
                          }}
                          className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                            isDark
                              ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                              : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {t('comment.cancel', '取消')}
                        </button>
                        <button
                          onClick={handleSubmit}
                          disabled={isSubmitting || !editorContent.trim()}
                          className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                            isSubmitting || !editorContent.trim()
                              ? 'bg-gray-300 cursor-not-allowed text-gray-500'
                              : 'bg-blue-500 hover:bg-blue-600 text-white'
                          }`}
                          title={`Debug: isSubmitting=${isSubmitting}, hasContent=${!!editorContent.trim()}, rating=${rating}`}
                        >
                          {isSubmitting ? (
                            <div className="flex items-center justify-center">
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-t-transparent border-white"></div>
                            </div>
                          ) : t('comment.respond', '提交评价')}
                        </button>
                      </div>
                    </div>
                  )}

                  {/* 错误提示 */}
                  {submitError && (
                    <div className="text-red-500 text-sm mt-2">
                      {submitError}
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="p-4 mb-4 text-center">
                <p className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  {t('comment.login_required', '请先登录后再评价')}
                </p>
              </div>
            )}

            {/* 评论列表 */}
            <div className="flex-1">
              {/* 排序下拉菜单 */}
              {comments.length > 0 && !isLoading && (
                <div className="px-4 py-3">
                  <div className="relative" ref={sortDropdownRef}>
                    <button
                      onClick={() => setShowSortDropdown(!showSortDropdown)}
                      className={`flex items-center space-x-2 text-sm font-medium transition-colors ${
                        isDark
                          ? 'text-gray-200 hover:text-white'
                          : 'text-gray-700 hover:text-gray-900'
                      }`}
                    >
                      <span className="uppercase tracking-wide">
                        {sortBy === 'hot' ? t('comment.sort.hot', '最相关') : t('comment.sort.recent', '最新')}
                      </span>
                      <FaChevronDown
                        className={`w-3 h-3 transition-transform ${showSortDropdown ? 'rotate-180' : ''}`}
                      />
                    </button>

                    {/* 下拉菜单 */}
                    {showSortDropdown && (
                      <div className={`absolute top-full left-0 mt-1 w-48 rounded-lg shadow-lg z-50 ${
                        isDark ? 'bg-gray-800' : 'bg-white'
                      }`}>
                        <div className="py-2">
                          <button
                            onClick={() => handleSortChange('hot')}
                            className={`w-full px-4 py-2 text-left text-sm flex items-center space-x-3 transition-colors ${
                              sortBy === 'hot'
                                ? isDark
                                  ? 'text-white'
                                  : 'text-gray-900'
                                : isDark
                                  ? 'text-gray-300 hover:text-white'
                                  : 'text-gray-600 hover:text-gray-900'
                            }`}
                          >
                            {sortBy === 'hot' && <FaCheck className={`w-3 h-3 ${isDark ? 'text-gray-400' : 'text-gray-600'}`} />}
                            <span className={sortBy !== 'hot' ? 'ml-6' : ''}>{t('comment.sort.hot', '最相关')}</span>
                          </button>
                          <button
                            onClick={() => handleSortChange('recent')}
                            className={`w-full px-4 py-2 text-left text-sm flex items-center space-x-3 transition-colors ${
                              sortBy === 'recent'
                                ? isDark
                                  ? 'text-white'
                                  : 'text-gray-900'
                                : isDark
                                  ? 'text-gray-300 hover:text-white'
                                  : 'text-gray-600 hover:text-gray-900'
                            }`}
                          >
                            {sortBy === 'recent' && <FaCheck className={`w-3 h-3 ${isDark ? 'text-gray-400' : 'text-gray-600'}`} />}
                            <span className={sortBy !== 'recent' ? 'ml-6' : ''}>{t('comment.sort.recent', '最新')}</span>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {isLoading ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                </div>
              ) : comments.length > 0 ? (
                <div className="divide-y divide-gray-100 dark:divide-gray-800">
                  {comments.map((comment) => (
                    <div
                      key={comment.id}
                      className="p-4"
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`w-8 h-8 mt-1 rounded-full flex items-center justify-center ${
                          isDark ? 'bg-purple-600' : 'bg-purple-500'
                        } text-white text-xs font-medium flex-shrink-0`}>
                          {comment.username?.charAt(0)?.toUpperCase() || 'U'}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-1">
                            <p className={`text-base ${isDark ? 'text-white' : 'text-gray-900'}`}>
                              {comment.username || t('comment.anonymous_user', '匿名用户')}
                            </p>
                            {user && user.id === comment.userId && (
                              <span className={`text-xs px-1.5 py-0.5 rounded ${
                                isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-600'
                              }`}>
                                {t('comment.you', '你')}
                              </span>
                            )}
                            {comment.rating && (
                              <div className="flex items-center space-x-0.5">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <FaStar
                                    key={star}
                                    className={`w-3.5 h-3.5 ${
                                      star <= comment.rating
                                        ? 'text-yellow-400'
                                        : isDark ? 'text-gray-600' : 'text-gray-300'
                                    }`}
                                  />
                                ))}
                              </div>
                            )}
                          </div>
                          <p
                            className="text-sm mb-3"
                            style={{ color: isDark ? '#4B5563' : '#D1D5DB' }}
                          >
                            {formatTime(comment.createdAt)}
                          </p>
                          <div
                            className={`text-base ${isDark ? 'text-gray-200' : 'text-gray-800'} leading-relaxed -ml-11 mb-3`}
                            dangerouslySetInnerHTML={renderMarkdown(comment.content)}
                          />

                          {/* 回复操作按钮 */}
                          <div className="flex items-center space-x-4 -ml-11">
                            {/* 拍手点赞按钮 */}
                            <div className="relative">
                              <button
                                onClick={() => addLike(comment.id)}
                                className={`clap-button flex items-center space-x-1 text-sm transition-colors duration-200 ${
                                  (userLikeCounts[comment.id] || 0) > 0
                                    ? 'text-yellow-400'
                                    : isDark
                                      ? 'text-gray-600 hover:text-yellow-400'
                                      : 'text-gray-300 hover:text-yellow-400'
                                }`}
                              >
                                <FaHandsClapping
                                  className={`w-4 h-4 transition-all duration-200 ${
                                    clickAnimations[comment.id]
                                      ? 'clap-icon-animate'
                                      : ''
                                  }`}
                                />
                                <span className="font-medium">{commentLikeCounts[comment.id] || comment.likeCount || 0}</span>
                              </button>

                              {/* 拍手动画特效 */}
                              {clapAnimations[comment.id]?.visible && (
                                <div
                                  key={comment.id}
                                  className="absolute -top-12 left-1/2 transform -translate-x-1/2 pointer-events-none"
                                  style={{
                                    animation: clapAnimations[comment.id]?.fadeOut
                                      ? 'clapFadeOut 0.5s ease-out forwards'
                                      : clapAnimations[comment.id]?.bounce
                                        ? 'clapBounce 0.3s ease-out forwards'
                                        : 'clapFloat 0.3s ease-out forwards'
                                  }}
                                >
                                  <div
                                    className="w-10 h-10 rounded-full flex items-center justify-center text-base font-bold shadow-lg bg-yellow-400"
                                    style={{ color: '#000000' }}
                                  >
                                    +{clapAnimations[comment.id].count}
                                  </div>
                                </div>
                              )}
                            </div>

                            <button
                              onClick={() => startReply(comment)}
                              className={`text-sm ${isDark ? 'text-gray-600 hover:text-gray-500' : 'text-gray-300 hover:text-gray-400'} transition-colors`}
                            >
                              {t('comment.reply', '回复')}
                            </button>
                            {(comment.replyCount || 0) > 0 && (
                              <button
                                onClick={() => toggleReplies(comment.id)}
                                className={`text-sm ${isDark ? 'text-gray-600 hover:text-gray-500' : 'text-gray-300 hover:text-gray-400'} transition-colors`}
                              >
                                {expandedReplies.has(comment.id) ? t('comment.hide_replies', '收起') : `${t('comment.show_replies', '查看')}${comment.replyCount || 0}${t('comment.replies', '条回复')}`}
                              </button>
                            )}
                          </div>

                          {/* 回复输入框 */}
                          {replyingTo?.id === comment.id && (
                            <div className="mt-3 -ml-11">
                              <div className={`rounded-lg ${isDark ? 'bg-gray-800/80' : 'bg-gray-50'} p-4`}>
                                {/* 回复输入区域 */}
                                <div
                                  contentEditable
                                  suppressContentEditableWarning={true}
                                  onInput={(e) => {
                                    const target = e.target as HTMLDivElement;
                                    setReplyContent(target.innerHTML);
                                  }}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                                      e.preventDefault();
                                      submitReply();
                                    }
                                  }}
                                  className={`w-full p-0 border-0 bg-transparent text-base min-h-[60px] ${
                                    isDark
                                      ? 'text-gray-200'
                                      : 'text-gray-800'
                                  } focus:outline-none`}
                                  style={{
                                    minHeight: '60px',
                                    wordWrap: 'break-word',
                                    overflowWrap: 'break-word'
                                  }}
                                  data-placeholder={`${t('comment.reply_to', '回复')} @${comment.username}...`}
                                />

                                {/* 工具栏 */}
                                <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                                  {/* 左侧格式按钮 */}
                                  <div className="flex items-center space-x-1">
                                    <button
                                      type="button"
                                      onClick={handleReplyBold}
                                      className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                                        replyBoldActive
                                          ? isDark
                                            ? 'bg-gray-700 text-gray-200'
                                            : 'bg-gray-200 text-gray-800'
                                          : isDark
                                            ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                            : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                                      }`}
                                      title={t('editor.bold', '粗体')}
                                    >
                                      <span className="font-bold text-base">B</span>
                                    </button>
                                    <button
                                      type="button"
                                      onClick={handleReplyItalic}
                                      className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                                        replyItalicActive
                                          ? isDark
                                            ? 'bg-gray-700 text-gray-200'
                                            : 'bg-gray-200 text-gray-800'
                                          : isDark
                                            ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                            : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                                      }`}
                                      title={t('editor.italic', '斜体')}
                                    >
                                      <span className="italic text-base">i</span>
                                    </button>
                                  </div>

                                  {/* 右侧操作按钮 */}
                                  <div className="flex items-center space-x-2">
                                    <button
                                      onClick={cancelReply}
                                      className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                                        isDark
                                          ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                          : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                                      }`}
                                    >
                                      {t('comment.cancel_reply', '取消')}
                                    </button>
                                    <button
                                      onClick={submitReply}
                                      disabled={!replyContent.trim() || isSubmitting}
                                      className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                                        replyContent.trim() && !isSubmitting
                                          ? 'bg-blue-500 hover:bg-blue-600 text-white'
                                          : 'bg-gray-300 cursor-not-allowed text-gray-500'
                                      }`}
                                    >
                                      {isSubmitting ? (
                                        <div className="flex items-center justify-center">
                                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-t-transparent border-white"></div>
                                        </div>
                                      ) : t('comment.submit_reply', '回复')}
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* 回复列表 */}
                          {expandedReplies.has(comment.id) && commentReplies[comment.id] && (
                            <div className="mt-3 -ml-11 relative">
                              {/* 左侧垂直段落线 */}
                              <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>
                              <div className="space-y-3 pl-6">
                                {commentReplies[comment.id].map((reply: any) => (
                                  <div key={reply.id} className="p-3">
                                    <div className="flex items-start space-x-2">
                                      <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                                        isDark ? 'bg-purple-600' : 'bg-purple-500'
                                      } text-white text-xs font-medium flex-shrink-0`}>
                                        {reply.username?.charAt(0)?.toUpperCase() || 'U'}
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <div className="flex items-center space-x-2 mb-1">
                                          <p className={`text-sm ${isDark ? 'text-white' : 'text-gray-900'}`}>
                                            {reply.username || t('comment.anonymous_user', '匿名用户')}
                                          </p>
                                          {user && user.id === reply.userId && (
                                            <span className={`text-xs px-1.5 py-0.5 rounded ${
                                              isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-600'
                                            }`}>
                                              {t('comment.you', '你')}
                                            </span>
                                          )}
                                          {reply.replyToUsername && (
                                            <span
                                              className="text-sm"
                                              style={{ color: isDark ? '#4B5563' : '#D1D5DB' }}
                                            >
                                              {t('comment.reply_to', '回复')} @{reply.replyToUsername}
                                            </span>
                                          )}
                                          <span
                                            className="text-xs"
                                            style={{ color: isDark ? '#4B5563' : '#D1D5DB' }}
                                          >
                                            {formatTime(reply.createdAt)}
                                          </span>
                                        </div>
                                        <div
                                          className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-700'} leading-relaxed`}
                                          dangerouslySetInnerHTML={renderMarkdown(reply.content)}
                                        />
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className={`w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center ${isDark ? 'bg-gray-700 text-gray-500' : 'bg-gray-200 text-gray-400'}`}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <p className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    {t('comment.no_comments', '暂无评价，快来抢沙发吧！')}
                  </p>
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
    </>
  );
};

export default CommentDialog;
