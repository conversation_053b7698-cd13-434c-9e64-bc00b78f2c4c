/**
 * 防止移动设备上的过度滚动效果导致的问题
 * - iOS: 滚动到底部会反弹并造成抖动
 * - Android: 滚动到顶部可能导致页面卡顿
 */

/**
 * 检测当前设备是iOS还是Android
 */
export const isIOS = (): boolean => {
    return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
           (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  };
  
  export const isAndroid = (): boolean => {
    return /Android/.test(navigator.userAgent);
  };
  
  /**
   * 检测是否为移动设备
   */
  export const isMobileDevice = (): boolean => {
    return isIOS() || isAndroid() || 
           /webOS|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
           (window.innerWidth <= 768);
  };
  
  /**
   * 阻止iOS和Android上的过度滚动效果
   * @param element 需要处理的DOM元素，默认是document.body
   */
  export const preventOverscroll = (element: HTMLElement = document.body): () => void => {
    // 如果是PC端，不应用滚动拦截
    if (!isMobileDevice()) {
      return () => {}; // 返回空函数
    }
    
    // 用于存储初始触摸位置
    let startY = 0;
    
    // 用于判断是否到达页面顶部或底部
    const isAtTop = (el: HTMLElement): boolean => {
      return el.scrollTop <= 0;
    };
    
    const isAtBottom = (el: HTMLElement): boolean => {
      return el.scrollHeight - el.scrollTop <= el.clientHeight + 1; // 添加1px容差
    };
  
    // 触摸开始事件处理
    const handleTouchStart = (e: TouchEvent): void => {
      startY = e.touches[0].clientY;
    };
  
    // 触摸移动事件处理
    const handleTouchMove = (e: TouchEvent): void => {
      // 如果是Android设备，允许正常滚动，只在顶部特殊处理
      if (isAndroid()) {
        const currentY = e.touches[0].clientY;
        const direction = currentY > startY ? 'down' : 'up';
        const targetEl = e.target as HTMLElement;
        const scrollableEl = findScrollableParent(targetEl);
        
        // 对Android只处理顶部下拉刷新行为，避免卡顿
        if (scrollableEl && isAtTop(scrollableEl) && direction === 'down' && 
            !scrollableEl.classList.contains('allow-overscroll')) {
          // 阻止下拉刷新行为
          e.preventDefault();
        }
        
        // 其他情况允许正常滚动
        return;
      }
      
      // 以下是iOS设备的处理逻辑
      if (isIOS()) {
        const currentY = e.touches[0].clientY;
        const direction = currentY > startY ? 'down' : 'up';
        const targetEl = e.target as HTMLElement;
        const scrollableEl = findScrollableParent(targetEl);
        
        // 如果找不到可滚动元素，或者当前元素已禁止滚动，则不处理
        if (!scrollableEl || scrollableEl.classList.contains('no-bounce')) {
          return;
        }
        
        // 在顶部且向下滚动，或者在底部且向上滚动时，阻止默认行为
        if ((isAtTop(scrollableEl) && direction === 'down') || 
            (isAtBottom(scrollableEl) && direction === 'up')) {
          e.preventDefault();
        }
      }
    };
  
    // 查找可滚动的父元素
    function findScrollableParent(element: HTMLElement | null): HTMLElement | null {
      if (!element || element === document.body) {
        return document.body;
      }
      
      const style = window.getComputedStyle(element);
      const hasScrollableContent = element.scrollHeight > element.clientHeight;
      
      if (hasScrollableContent && (
        style.overflowY === 'auto' || 
        style.overflowY === 'scroll' || 
        element.tagName === 'TEXTAREA'
      )) {
        return element;
      }
      
      return findScrollableParent(element.parentElement);
    }
  
    // 添加事件监听器，对Android设备使用passive=true以保证滚动性能
    if (isAndroid()) {
      element.addEventListener('touchstart', handleTouchStart, { passive: true });
      element.addEventListener('touchmove', handleTouchMove, { passive: false });
    } else {
      element.addEventListener('touchstart', handleTouchStart, { passive: false });
      element.addEventListener('touchmove', handleTouchMove, { passive: false });
    }
    
    // 返回清理函数
    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
    };
  };
  
  /**
   * 为特定元素应用iOS和Android上滚动行为的优化
   */
  export const applyScrollBehaviorFix = (): void => {
    // 如果是PC端，不需要特殊滚动优化，确保滚动条正常显示
    if (!isMobileDevice()) {
      // 为PC端应用适当的样式以确保滚动条显示
      const style = document.createElement('style');
      style.innerHTML = `
        /* PC端滚动条样式 */
        html, body, #root, .app, .main-content {
          overflow: auto !important;
          scrollbar-width: auto !important;
        }
        
        ::-webkit-scrollbar {
          width: 6px !important;
          display: block !important;
        }
      `;
      document.head.appendChild(style);
      return;
    }
    
    // 添加CSS变量用于控制弹性滚动，根据设备类型选择不同样式
    const style = document.createElement('style');
    const isIOSDevice = isIOS();
    const isAndroidDevice = isAndroid();
    
    if (isIOSDevice) {
      // iOS专用样式 - 修复语法错误，移除嵌套CSS结构
      style.innerHTML = `
        /* iOS设备专用修复 */
        html, body {
          height: 100% !important;
          overflow: auto !important;
          -webkit-overflow-scrolling: touch !important;
          overscroll-behavior: none !important;
        }
        
        /* 修复iOS上的弹性滚动 */
        .disable-bounce {
          overscroll-behavior: none !important;
          overflow: auto !important;
          -webkit-overflow-scrolling: touch !important;
          touch-action: pan-y !important;
          -webkit-tap-highlight-color: transparent !important;
          position: relative !important;
        }
        
        /* 通用修复 */
        html, body, #root, .app, .main-content {
          overscroll-behavior: none !important;  
          overscroll-behavior-y: none !important;
          overflow-anchor: none !important;
        }
      `;
    } else if (isAndroidDevice) {
      // Android专用样式 - 修复语法错误，移除嵌套CSS结构
      style.innerHTML = `
        /* Android设备专用修复 */
        html, body {
          height: 100% !important;
          overflow: auto !important;
          width: 100% !important;
          margin: 0 !important;
          padding: 0 !important;
          overscroll-behavior: none !important;
          max-width: 100vw !important;
        }
        
        /* 确保Android的滚动流畅性且不影响布局 */
        .main-content, #root, .app {
          -webkit-overflow-scrolling: touch !important;
          touch-action: pan-y !important;
          overflow: auto !important;
          width: 100% !important;
          max-width: 100% !important;
          box-sizing: border-box !important;
          padding-right: 0 !important;
          padding-left: 0 !important;
          margin-right: 0 !important;
          margin-left: 0 !important;
        }
        
        /* 禁用顶部下拉刷新效果但保留正常滚动 */
        body {
          overscroll-behavior-y: none !important;
          padding-right: 0 !important;
          padding-left: 0 !important;
        }
        

        
        /* 确保滚动容器滚动正常 */
        .scrollable, [data-scrollable], .scroll-container {
          -webkit-overflow-scrolling: touch !important;
          overflow: auto !important;
          width: 100% !important;
          padding-right: 0 !important;
        }
        
        /* 修复Android下可能的滚动条占位问题 */
        ::-webkit-scrollbar {
          width: 0 !important;
          display: none !important;
        }
      `;
    } 
    
    document.head.appendChild(style);
    
    // 应用preventOverscroll到文档体 - 仅对移动设备
    preventOverscroll();
    
    // 根据设备类型处理可滚动容器
    const scrollableSelector = '.scrollable, [data-scrollable], .scroll-container';
    if (isIOSDevice) {
      document.querySelectorAll(scrollableSelector)
        .forEach(el => {
          if (el instanceof HTMLElement) {
            el.classList.add('disable-bounce');
            preventOverscroll(el);
          }
        });
      
      // 处理主内容区
      const mainContent = document.querySelector('.main-content');
      if (mainContent instanceof HTMLElement) {
        mainContent.classList.add('disable-bounce');
        preventOverscroll(mainContent);
      }
    } else if (isAndroidDevice) {
      // 对于Android，只处理body级别的overscroll，保持内部容器滚动正常
      const mainContent = document.querySelector('.main-content');
      if (mainContent instanceof HTMLElement) {
        // 不用添加position: relative，以避免影响布局
        mainContent.classList.add('android-scroll-fix');
        // 确保Android上的卡片和容器宽度正确
        document.querySelectorAll('.card, .container, [class*="card"], [class*="container"]')
          .forEach(el => {
            if (el instanceof HTMLElement) {
              el.style.maxWidth = '100%';
              el.style.boxSizing = 'border-box';
            }
          });
      }
    }
  };