import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';

interface AnnouncementModalProps {
  onClose: () => void;
}

const AnnouncementModal: React.FC<AnnouncementModalProps> = ({ onClose }) => {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showBadge, setShowBadge] = useState(false);
  const [hasRead, setHasRead] = useState(true);

  // 组件加载时直接设置为已读和已关闭状态
  useEffect(() => {
    // 设置为已读状态
    localStorage.setItem('announcement_has_read', 'true');
    // 设置为已关闭状态
    localStorage.setItem('announcement_closed', 'true');
    setHasRead(true);
  }, []);

  const handleOpen = () => {
    setIsModalOpen(true);
    setShowBadge(false);
    // 标记用户已阅读公告
    localStorage.setItem('announcement_has_read', 'true');
    setHasRead(true);
  };

  const handleClose = () => {
    setIsModalOpen(false);
    setShowBadge(false);
    // 标记用户已阅读公告
    localStorage.setItem('announcement_has_read', 'true');
    setHasRead(true);
    // 标记用户已关闭公告
    localStorage.setItem('announcement_closed', 'true');
    onClose();
  };

  // 如果不显示角标，则整个组件不渲染任何内容
  if (!showBadge && !isModalOpen) {
    return null;
  }

  return (
    <>
      {/* 左下角角标 */}
      <AnimatePresence>
        {showBadge && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed left-3 bottom-3 sm:left-4 sm:bottom-4 z-[9998] cursor-pointer"
            onClick={handleOpen}
          >
            <div className="relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur opacity-70"></div>
              <div className="relative flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-white dark:bg-black rounded-full shadow-lg border border-purple-200 dark:border-purple-500/30">
                <span className="text-xl sm:text-2xl">📢</span>
                {/* 只有在用户未阅读过公告时才显示红点 */}
                {!hasRead && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 sm:w-4 sm:h-4 bg-red-500 rounded-full"></span>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 弹窗部分 - 参考VipPromptDialog的实现方式 */}
      {isModalOpen && (
        <>
          <div
            className="fixed inset-0 bg-black/60 dark:bg-black/60 backdrop-blur-sm z-[9999]"
            onClick={handleClose}
          />
          <div
            className="fixed inset-0 flex items-start sm:items-center justify-center px-3 sm:px-4 z-[9999] py-4 sm:py-0 pt-[calc(env(safe-area-inset-top)+4rem)] sm:pt-4"
          >
            <div className="w-full max-w-xl bg-white/90 dark:bg-black/40 backdrop-blur-xl rounded-2xl border border-gray-200 dark:border-purple-500/20 shadow-2xl relative overflow-hidden my-auto flex flex-col max-h-[80vh] sm:max-h-[90vh]">
              {/* 装饰性光效 */}
              <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-300/30 dark:bg-purple-500/20 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-300/30 dark:bg-pink-500/20 rounded-full blur-3xl"></div>
              
              {/* 可滚动的内容区域 */}
              <div className="relative space-y-4 sm:space-y-6 overflow-y-auto p-4 sm:p-5 md:p-7">
                {/* 标题 */}
                <div className="text-center">
                  <div className="flex justify-center">
                    <span className="text-3xl sm:text-4xl">📢</span>
                  </div>
                  <h3 className="text-xl sm:text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 dark:from-purple-400 dark:via-pink-400 dark:to-purple-400 bg-clip-text text-transparent mt-2 sm:mt-3">
                    {t('announcement.title')}
                  </h3>
                </div>
                
                {/* 公告内容 */}
                <div className="space-y-4 sm:space-y-6">
                  <div className="text-center">
                    <p className="text-sm sm:text-base text-gray-700 dark:text-gray-300">{t('announcement.greeting')}</p>
                    <p className="text-sm sm:text-base text-gray-700 dark:text-gray-300 mt-2">{t('announcement.introduction')}</p>
                  </div>
                  
                  {/* 支付状态 */}
                  <div className="bg-purple-50/50 dark:bg-purple-500/10 rounded-xl p-3 sm:p-5 border border-purple-100 dark:border-purple-500/20">
                    <h5 className="font-semibold text-base sm:text-lg flex items-center text-purple-700 dark:text-purple-400 gap-2 mb-2 sm:mb-3">
                      <span>💳</span>
                      <span>{t('announcement.payment_status.title')}</span>
                    </h5>
                    <ul className="space-y-2 sm:space-y-3 ml-1">
                      <li className="flex items-center gap-2 sm:gap-3">
                        <span className="text-red-500 flex-shrink-0 text-base sm:text-lg">❌</span>
                        <span className="text-sm sm:text-base text-gray-700 dark:text-gray-300">{t('announcement.payment_status.paypal')}</span>
                      </li>
                      <li className="flex items-center gap-2 sm:gap-3">
                        <span className="text-green-500 flex-shrink-0 text-base sm:text-lg">✅</span>
                        <span className="text-sm sm:text-base text-gray-700 dark:text-gray-300">{t('announcement.payment_status.wechat')}</span>
                      </li>
                      <li className="flex items-center gap-2 sm:gap-3">
                        <span className="text-green-500 flex-shrink-0 text-base sm:text-lg">✅</span>
                        <span className="text-sm sm:text-base text-gray-700 dark:text-gray-300">{t('announcement.payment_status.alipay')}</span>
                      </li>
                    </ul>
                  </div>
                  
                  {/* 补偿措施 */}
                  <div>
                    <h5 className="font-semibold text-base sm:text-lg flex items-center text-purple-700 dark:text-purple-400 gap-2 mb-2 sm:mb-3">
                      <span>🎁</span>
                      <span>{t('announcement.compensation.title')}</span>
                    </h5>
                    <p className="text-sm sm:text-base text-gray-700 dark:text-gray-300 mb-2 sm:mb-3">{t('announcement.compensation.description')}</p>
                    <div className="bg-purple-100/50 dark:bg-purple-500/10 border border-purple-200 dark:border-purple-500/20 rounded-xl p-3 sm:p-4">
                      <p className="text-sm sm:text-base font-medium text-center text-purple-700 dark:text-purple-300">
                        {t('announcement.compensation.offer')}
                      </p>
                    </div>
                  </div>
                  
                  {/* 预计完成时间 */}
                  <div>
                    <h5 className="font-semibold text-base sm:text-lg flex items-center text-purple-700 dark:text-purple-400 gap-2 mb-2 sm:mb-3">
                      <span>⏰</span>
                      <span>{t('announcement.completion_time.title')}</span>
                    </h5>
                    <p className="text-sm sm:text-base text-gray-700 dark:text-gray-300">{t('announcement.completion_time.description')}</p>
                  </div>
                  
                  {/* 结尾 */}
                  <div className="border-t border-gray-200 dark:border-gray-700/50 pt-3 sm:pt-5">
                    <p className="text-sm sm:text-base italic text-gray-600 dark:text-gray-400">{t('announcement.footer.contact')}</p>
                    <div className="mt-1 sm:mt-2">
                      <p className="text-sm sm:text-base font-medium text-gray-700 dark:text-gray-300">{t('announcement.footer.signature')}</p>
                      <p className="text-sm sm:text-base font-medium text-gray-700 dark:text-gray-300 mt-1 sm:mt-2">{t('announcement.footer.date')}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 固定在底部的按钮 */}
              <div className="p-4 sm:p-5 border-t border-gray-200 dark:border-gray-700/30 bg-white/80 dark:bg-black/60 backdrop-blur-sm flex justify-center">
                <button
                  onClick={handleClose}
                  className="relative group w-full sm:w-auto"
                >
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-pink-600 
                                rounded-xl blur opacity-60 group-hover:opacity-100 transition duration-200">
                  </div>
                  <div className="relative px-6 sm:px-8 py-2.5 sm:py-3 bg-white dark:bg-black rounded-xl leading-none flex items-center justify-center">
                    <span className="text-purple-700 dark:text-white text-base sm:text-lg font-medium">{t('announcement.button.got_it')}</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default AnnouncementModal; 