const { getConnection } = require('../services/database');

class SalesPerson {
  static async findById(id) {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT * FROM sales_persons WHERE id = ?', [id]);
    return rows[0];
  }

  static async findAll() {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT * FROM sales_persons');
    return rows;
  }

  static async create(name) {
    const pool = await getConnection();
    const [result] = await pool.query(
      'INSERT INTO sales_persons (name) VALUES (?)',
      [name]
    );
    return { id: result.insertId, name };
  }

  static async getStats(salesId) {
    const pool = await getConnection();
    
    // 使用一个查询语句获取总邀请码数和已使用邀请码数
    const [codesStats] = await pool.query(`
      SELECT 
        COUNT(*) as total_codes,
        SUM(CASE WHEN is_used = true THEN 1 ELSE 0 END) as used_codes
      FROM invitation_codes
      WHERE sales_id = ?
    `, [salesId]);
    
    // 查询销售人员的邀请码对应的用户总支付金额（仅包含应用了邀请码折扣的订单）
    // 同时考虑不同货币的转换
    const [paymentResult] = await pool.query(`
      SELECT 
        SUM(
          CASE 
            WHEN po.currency = 'USD' THEN po.amount * 7.2
            ELSE po.amount
          END
        ) as total_payment
      FROM invitation_codes ic
      JOIN users u ON u.invitation_code = ic.code
      JOIN payment_orders po ON u.id = po.user_id
      WHERE ic.sales_id = ? 
      AND ic.is_used = true 
      AND po.status = 'success'
      AND po.discount_applied IS NOT NULL 
      AND po.discount_applied > 0
    `, [salesId]);
    
    const totalCodes = codesStats[0]?.total_codes || 0;
    const usedCodes = codesStats[0]?.used_codes || 0;
    
    return {
      totalCodes,
      usedCodes,
      unusedCodes: totalCodes - usedCodes,
      totalPayment: paymentResult[0]?.total_payment || 0
    };
  }

  static async delete(id) {
    const pool = await getConnection();
    const [result] = await pool.query('DELETE FROM sales_persons WHERE id = ?', [id]);
    return result.affectedRows > 0;
  }

  // 检查用户ID是否存在于sales_persons表中
  static async exists(userId) {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT 1 FROM sales_persons WHERE id = ? LIMIT 1', [userId]);
    return rows.length > 0;
  }

  // 通过user_id查询销售人员是否存在
  static async existsByUserId(userId) {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT 1 FROM sales_persons WHERE user_id = ? LIMIT 1', [userId]);
    return rows.length > 0;
  }

  // 通过user_id查找销售人员记录
  static async findByUserId(userId) {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT * FROM sales_persons WHERE user_id = ?', [userId]);
    return rows[0];
  }
}

module.exports = { SalesPerson }; 