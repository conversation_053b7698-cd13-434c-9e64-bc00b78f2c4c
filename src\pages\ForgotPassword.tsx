import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { sendResetCode, verifyResetCode } from '../services/userService';
import { motion } from 'framer-motion';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import { useTranslation } from 'react-i18next';
import SEO from '../components/SEO';

// 复用 CheckmarkAnimation 组件
const CheckmarkAnimation: React.FC<{
  size?: number;
  color?: string;
  strokeWidth?: number;
  animationDuration?: number;
}> = ({
  size = 32,
  color = '#FFFFFF',
  strokeWidth = 4,
}) => {
  const circleVariants = {
    hidden: { opacity: 0, scale: 0.5 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  const checkVariants = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { duration: 0.5, ease: "easeOut" },
        opacity: { duration: 0.01 },
      },
    },
  };

  return (
    <div style={{ width: size, height: size }}>
      <motion.svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        initial="hidden"
        animate="visible"
      >
        <motion.circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          variants={circleVariants}
        />
        <motion.path
          d="M30,50 L45,65 L70,40"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
          variants={checkVariants}
        />
      </motion.svg>
    </div>
  );
};

const ForgotPassword: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const [email, setEmail] = useState(location.state?.email || '');
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  useEffect(() => {
    const savedEmail = localStorage.getItem('resetPasswordEmail');
    const savedEndTime = localStorage.getItem('resetPasswordEndTime');
    
    if (savedEmail === email && savedEndTime) {
      const endTime = parseInt(savedEndTime);
      const now = Date.now();
      
      if (endTime > now) {
        const remainingSeconds = Math.floor((endTime - now) / 1000);
        if (remainingSeconds > 0) {
          setCountdown(remainingSeconds);
          setCodeSent(true);
          const timer = setInterval(() => {
            const currentTime = Date.now();
            const remaining = Math.floor((endTime - currentTime) / 1000);
            
            if (remaining <= 0) {
              clearInterval(timer);
              setCountdown(0);
              localStorage.removeItem('resetPasswordEmail');
              localStorage.removeItem('resetPasswordEndTime');
            } else {
              setCountdown(remaining);
            }
          }, 1000);

          return () => clearInterval(timer);
        }
      }
      // 清除过期的数据
      localStorage.removeItem('resetPasswordEmail');
      localStorage.removeItem('resetPasswordEndTime');
    }
  }, [email]);

  const startCountdown = () => {
    const endTime = Date.now() + 60 * 1000;
    localStorage.setItem('resetPasswordEmail', email);
    localStorage.setItem('resetPasswordEndTime', endTime.toString());
    
    setCountdown(60);
    const timer = setInterval(() => {
      const currentTime = Date.now();
      const remaining = Math.floor((endTime - currentTime) / 1000);
      
      if (remaining <= 0) {
        clearInterval(timer);
        setCountdown(0);
        localStorage.removeItem('resetPasswordEmail');
        localStorage.removeItem('resetPasswordEndTime');
      } else {
        setCountdown(remaining);
      }
    }, 1000);

    return () => clearInterval(timer);
  };

  const handleSendCode = async (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!email) {
      setError(t('forgot_password.errors.email_required'));
      return;
    }
    
    setCodeSent(true);
    startCountdown();
    
    try {
      await sendResetCode(email, localStorage.getItem('i18nextLng') || 'zh-CN');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message;
      if (errorMessage && typeof errorMessage === 'string' && errorMessage.startsWith('auth.')) {
        setError(t(errorMessage));
      } else {
        setError(errorMessage || t('forgot_password.errors.send_code_failed'));
      }
    }
  };

  const handleVerificationCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^\d]/g, '').slice(0, 6);
    setVerificationCode(value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!verificationCode) {
      setError(t('forgot_password.errors.code_required'));
      return;
    }

    if (verificationCode.length !== 6) {
      setError(t('forgot_password.errors.code_length'));
      return;
    }

    setIsLoading(true);
    setError('');
    setIsSuccess(false);

    try {
      await verifyResetCode(email, verificationCode, localStorage.getItem('i18nextLng') || 'zh-CN');
      // 延长加载圈的显示时间
      setTimeout(() => {
        setIsLoading(false);
        setIsSuccess(true);
        // 延长延迟时间，等待动画完成后再跳转
        setTimeout(() => {
          localStorage.removeItem('resetPasswordEmail');
          localStorage.removeItem('resetPasswordEndTime');
          // 由于useLanguageNavigate不支持传递state，使用localStorage传递数据
          localStorage.setItem('resetEmail', email);
          localStorage.setItem('resetCode', verificationCode);
          navigate('/reset-password', { replace: true });
        }, 800);
      }, 500);
    } catch (error: any) {
      const errorMessage = error.response?.data?.message;
      // 如果错误信息包含密码相关的内容，说明验证码是正确的
      if (errorMessage && (
        errorMessage.includes('密码') || 
        errorMessage.includes('password') || 
        errorMessage.toLowerCase().includes('password')
      )) {
        // 延长加载圈的显示时间
                  setTimeout(() => {
            setIsLoading(false);
            setIsSuccess(true);
            // 延长延迟时间，等待动画完成后再跳转
            setTimeout(() => {
              localStorage.removeItem('resetPasswordEmail');
              localStorage.removeItem('resetPasswordEndTime');
              // 由于useLanguageNavigate不支持传递state，使用localStorage传递数据
              localStorage.setItem('resetEmail', email);
              localStorage.setItem('resetCode', verificationCode);
              navigate('/reset-password', { replace: true });
            }, 800);
          }, 500);
      } else {
        // 检查错误信息是否是翻译键（以auth.开头）
        if (errorMessage && errorMessage.startsWith('auth.')) {
          setError(t(errorMessage));
        } else {
          setError(errorMessage || t('forgot_password.errors.invalid_code'));
        }
        setIsLoading(false);
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col relative font-['Inter']">
      <SEO 
        title={t('forgot_password.meta.title', '找回密码 - TarotQA')}
        description={t('forgot_password.meta.description', '安全找回您的TarotQA账户密码，发送验证码至您的邮箱以重置密码。')}
        keywords={t('forgot_password.meta.keywords', '找回密码,密码重置,忘记密码,账户恢复,验证码重置')}
      />
      <LandingBackground />
      <div className="flex-grow flex items-center justify-center relative z-10 mt-8 sm:mt-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-4 sm:mb-6">
            <h1 className={`main-title mb-1 ${getFontClass()}`}>{t('forgot_password.title')}</h1>
            <p className="sub-title"></p>
          </div>
          <div className="flex items-center justify-center">
            <div className="w-full max-w-md mx-auto">
              <div className="login-card p-5 sm:p-8 rounded-2xl shadow-2xl relative overflow-hidden">
                <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/20 rounded-full blur-3xl"></div>
                
                <div className="relative">
                  {error && (
                    <div className="mb-3 sm:mb-4 p-2.5 sm:p-3 rounded-lg bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
                      <p className="text-red-400 text-xs sm:text-sm font-['Inter']">{error}</p>
                    </div>
                  )}

                  <form onSubmit={codeSent ? handleSubmit : handleSendCode} className="space-y-5 sm:space-y-6">
                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-gray-200 mb-1.5 sm:mb-2 font-['Inter']">
                        {t('forgot_password.email.label')}
                      </label>
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 8L10.8906 13.2604C11.5624 13.7083 12.4376 13.7083 13.1094 13.2604L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <input
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="input-field login-input block w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-xl
                                   dark:text-white text-gray-800 text-[14px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                   placeholder-gray-400 backdrop-blur-sm pl-10 font-['Inter']"
                          placeholder={t('forgot_password.email.placeholder')}
                          required
                          disabled={codeSent}
                        />
                      </div>
                    </div>

                    {codeSent && (
                      <div>
                        <label className="block text-xs sm:text-sm font-medium text-gray-200 mb-1.5 sm:mb-2 font-['Inter']">
                          {t('forgot_password.code.label')}
                        </label>
                        <div className="relative">
                          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M12 15V17M8.8 21H15.2C16.8802 21 17.7202 21 18.362 20.673C18.9265 20.3854 19.3854 19.9265 19.673 19.362C20 18.7202 20 17.8802 20 16.2V14.8C20 13.1198 20 12.2798 19.673 11.638C19.3854 11.0735 18.9265 10.6146 18.362 10.327C17.7202 10 16.8802 10 15.2 10H8.8C7.11984 10 6.27976 10 5.63803 10.327C5.07354 10.6146 4.6146 11.0735 4.32698 11.638C4 12.2798 4 13.1198 4 14.8V16.2C4 17.8802 4 18.7202 4.32698 19.362C4.6146 19.9265 5.07354 20.3854 5.63803 20.673C6.27976 21 7.11984 21 8.8 21ZM15 10V8C15 6.34315 13.6569 5 12 5C10.3431 5 9 6.34315 9 8V10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </div>
                          <input
                            type="text"
                            value={verificationCode}
                            onChange={handleVerificationCodeChange}
                            className="input-field login-input block w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-xl
                                     dark:text-white text-gray-800 text-[14px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                     placeholder-gray-400 backdrop-blur-sm pl-10 font-['Inter']"
                            placeholder={t('forgot_password.code.placeholder')}
                            required
                          />
                          <button
                            type="button"
                            onClick={() => handleSendCode()}
                            disabled={countdown > 0}
                            className="absolute right-2 top-1/2 -translate-y-1/2 px-2 py-1
                                     text-xs sm:text-sm text-purple-400 hover:text-purple-300
                                     disabled:text-gray-500 disabled:hover:text-gray-500
                                     transition-colors font-['Inter']"
                          >
                            {countdown > 0 ? `${countdown}s` : t('forgot_password.resend')}
                          </button>
                        </div>
                      </div>
                    )}

                    <div className="pt-2">
                      <button
                        type="submit"
                        disabled={isLoading || isSuccess}
                        className="w-full relative bg-purple-600 rounded-xl 
                                 hover:bg-purple-500 active:bg-purple-700 
                                 transition-colors duration-200 disabled:opacity-50"
                      >
                        <div className="relative px-4 sm:px-6 h-[40px] sm:h-[48px] flex items-center justify-center">
                          <div className="flex items-center justify-center w-[28px] sm:w-[32px] h-[28px] sm:h-[32px]">
                            {isLoading ? (
                              <div className="w-5 sm:w-6 h-5 sm:h-6 border-2 border-white/20 border-t-white rounded-full animate-spin"></div>
                            ) : isSuccess ? (
                              <CheckmarkAnimation size={28} strokeWidth={4} />
                            ) : (
                              <span className="text-white text-sm sm:text-base font-medium whitespace-nowrap font-['Inter']">
                                {codeSent ? t('forgot_password.verify') : t('forgot_password.send')}
                              </span>
                            )}
                          </div>
                        </div>
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="relative z-10 mt-24 sm:mt-auto">
        <Footer />
      </div>
      <style>
        {`
          .input-field {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.2s ease-in-out;
            z-index: 1;
            position: relative;
          }
          
          /* 确保提交按钮在浅色主题下文字为白色 */
          body:not(.dark) .login-card button[type="submit"],
          html:not(.dark) .login-card button[type="submit"],
          :not(.dark) .login-card button[type="submit"] {
            color: white !important;
          }
          
          /* 确保按钮内的所有文字元素在浅色主题下都是白色 */
          body:not(.dark) .login-card button[type="submit"] *,
          html:not(.dark) .login-card button[type="submit"] *,
          :not(.dark) .login-card button[type="submit"] span,
          :not(.dark) .login-card button[type="submit"] div {
            color: white !important;
          }
          
          /* 确保发送验证码按钮在浅色主题下文字为白色 */
          body:not(.dark) .login-card button.send-code-btn,
          html:not(.dark) .login-card button.send-code-btn,
          :not(.dark) .login-card button.send-code-btn,
          body:not(.dark) .login-card button.send-code-btn *,
          html:not(.dark) .login-card button.send-code-btn *,
          :not(.dark) .login-card button.send-code-btn span,
          :not(.dark) .login-card button.send-code-btn div {
            color: white !important;
          }
          
          /* CSS变量 - 主题相关颜色 */
          :root, html, body {
            --input-bg-dark: rgba(255, 255, 255, 0.05);
            --input-border-dark: rgba(255, 255, 255, 0.1);
            --input-text-dark: rgba(255, 255, 255, 0.9);
            --input-bg-light: #F4F4F5;
            --input-border-light: rgba(168, 85, 247, 0.2);
            --input-text-light: #1F2937;
          }
          
          /* 彻底覆盖输入框样式 */
          body:not(.dark) .login-card input, 
          body:not(.dark) .login-card textarea,
          html:not(.dark) .login-card input, 
          html:not(.dark) .login-card textarea,
          div:not(.dark) .login-card input,
          div:not(.dark) .login-card textarea,
          :not(.dark) .input-field[type="email"],
          :not(.dark) .input-field[type="password"],
          :not(.dark) .input-field[type="text"] {
            background-color: #F4F4F5 !important;
            color: #1F2937 !important;
            border: 1px solid rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 输入框聚焦状态 */
          body:not(.dark) .login-card input:focus, 
          html:not(.dark) .login-card input:focus,
          :not(.dark) .input-field[type="email"]:focus,
          :not(.dark) .input-field[type="password"]:focus,
          :not(.dark) .input-field[type="text"]:focus {
            background-color: #FFFFFF !important;
            color: #1F2937 !important;
            border: 1px solid rgba(168, 85, 247, 0.5) !important;
            box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 浅色主题下图标颜色 */
          body:not(.dark) .relative .absolute.left-3 svg path {
            stroke: #6B7280 !important;
          }
          
          /* 深色主题下图标颜色 */
          .dark .relative .absolute.left-3 svg path {
            stroke: rgba(255, 255, 255, 0.7) !important;
          }
          
          /* 图标位置调整 */
          .relative .absolute.left-3 {
            z-index: 20 !important; 
            pointer-events: none !important;
          }
          
          /* 确保左侧有足够空间放置图标 */
          .input-field {
            padding-left: 40px !important;
          }
          
          /* 登录输入框特殊样式 */
          .login-input {
            background-color: var(--input-bg-light) !important;
            color: var(--input-text-light) !important;
            border-color: var(--input-border-light) !important;
          }
          
          .dark .login-input {
            background-color: var(--input-bg-dark) !important;
            color: var(--input-text-dark) !important;
            border-color: var(--input-border-dark) !important;
          }
          
          /* 浅色主题下确保文字和图标不重叠 */
          body:not(.dark) .login-input {
            color: #1F2937 !important;
            background-color: #F4F4F5 !important;
          }
          
          body:not(.dark) .login-input::placeholder {
            color: #6B7280 !important;
          }
          
          /* 深色主题输入框样式 */
          body.dark .login-card input, 
          html.dark .login-card input,
          .dark .input-field[type="email"],
          .dark .input-field[type="password"],
          .dark .input-field[type="text"] {
            background-color: #13111C !important;
            color: #FFFFFF !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
          }
          
          /* 深色主题输入框聚焦状态 */
          body.dark .login-card input:focus, 
          html.dark .login-card input:focus,
          .dark .input-field[type="email"]:focus,
          .dark .input-field[type="password"]:focus,
          .dark .input-field[type="text"]:focus {
            background-color: #13111C !important;
            color: #FFFFFF !important;
            border: 1px solid rgba(168, 85, 247, 0.5) !important;
            box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 深色主题下的登录输入框样式 */
          .dark .login-input {
            background-color: #13111C !important;
            color: rgba(255, 255, 255, 0.9) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
          }
          
          /* 占位符文本样式 */
          body:not(.dark) .login-card input::placeholder,
          html:not(.dark) .login-card input::placeholder,
          :not(.dark) .input-field::placeholder {
            color: #6B7280 !important;
          }
          
          .login-card {
            position: relative;
            background: rgba(13, 12, 15, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(236, 72, 153, 0.3);
            box-shadow: 
              0 0 0 1px rgba(168, 85, 247, 0.2),
              0 0 15px rgba(168, 85, 247, 0.15),
              0 0 30px rgba(236, 72, 153, 0.15),
              inset 0 0 15px rgba(168, 85, 247, 0.1);
          }
          /* 深色主题下的样式保持不变 */
          :root.dark .login-card, 
          html.dark .login-card, 
          .dark .login-card {
            background: rgba(13, 12, 15, 0.95);
          }
          /* 浅色主题下的卡片背景颜色 */
          :root:not(.dark) .login-card, 
          html:not(.dark) .login-card, 
          :not(.dark) .login-card {
            background: #F4F4F5;
          }
          .login-card::before {
            content: '';
            position: absolute;
            inset: -1px;
            padding: 1px;
            background: linear-gradient(
              135deg,
              rgba(168, 85, 247, 0.5),
              rgba(236, 72, 153, 0.5)
            );
            -webkit-mask: 
              linear-gradient(#fff 0 0) content-box, 
              linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
          }
        `}
      </style>
    </div>
  );
};

export default ForgotPassword;
