/**
 * 滚动相关的辅助函数
 */
import { isAndroid } from './preventOverscroll';

/**
 * 将页面滚动到顶部
 * 在不同的设备和浏览器上都有效
 */
export const scrollToTop = () => {
  // 使用多种方法确保在各种设备上都能工作
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: 'auto' // 使用 'auto' 而不是 'smooth' 来确保立即滚动
  });
  
  // 兼容旧版浏览器和某些移动设备
  document.body.scrollTop = 0; // For Safari
  document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
  
  // 如果有特定容器也需要滚动到顶部
  const mainContent = document.querySelector('.main-content');
  if (mainContent) {
    mainContent.scrollTop = 0;
  }
  
  // 尝试重置所有可滚动元素
  document.querySelectorAll('.scrollable, [data-scrollable], .scroll-container').forEach(el => {
    if (el instanceof HTMLElement) {
      el.scrollTop = 0;
    }
  });
};

/**
 * 处理页面切换时的滚动
 * 使用多层保障确保滚动正确执行
 */
export const handlePageChangeScroll = () => {
  // 立即执行
  scrollToTop();
  
  // 使用 requestAnimationFrame 确保在下一帧渲染前执行
  requestAnimationFrame(() => {
    scrollToTop();
  });
  
  // 添加延时执行，以应对某些特殊情况
  setTimeout(() => {
    scrollToTop();
  }, 10);
  
  // 针对设备类型使用不同的处理策略
  if (isAndroid()) {
    // Android设备使用较简单的滚动重置
    setTimeout(() => {
      scrollToTop();
      // 确保Android上main-content可以滚动
      const mainContent = document.querySelector('.main-content');
      if (mainContent instanceof HTMLElement) {
        mainContent.style.overflow = 'auto';
        // 确保不会影响卡片和容器布局
        document.querySelectorAll('.card, .container, [class*="card"], [class*="container"]')
          .forEach(el => {
            if (el instanceof HTMLElement) {
              // 重置可能影响布局的样式
              if (el.style.width === '100%') {
                el.style.width = '';
              }
              el.style.boxSizing = 'border-box';
            }
          });
      }
    }, 100);
  } else {
    // iOS和其他设备使用多次延时重置
    setTimeout(() => {
      scrollToTop();
    }, 100);
    
    // 针对移动端特别添加更长的延时
    setTimeout(() => {
      scrollToTop();
      
      // 特殊处理: 强制重置滚动位置
      if (window.innerWidth <= 768) { // 移动设备判断
        forceResetScroll();
      }
    }, 300);
  }
}; 

/**
 * 强制重置滚动位置
 * 针对移动端特别优化
 */
export const forceResetScroll = () => {
  // 重置html和body
  document.documentElement.scrollTop = 0;
  document.body.scrollTop = 0;
  
  // 针对iOS Safari的特殊处理
  window.scrollTo(0, 0);
  
  // 处理可能的固定容器
  const possibleContainers = [
    '.app-container',
    '.page-container',
    '.content-wrapper',
    '.scroll-wrapper',
    'main',
    '#root',
    '#app'
  ];
  
  possibleContainers.forEach(selector => {
    const container = document.querySelector(selector);
    if (container instanceof HTMLElement) {
      container.scrollTop = 0;
    }
  });
  
  // 针对Android设备的特别处理
  if (isAndroid()) {
    // 确保Android设备上主内容区域可以滚动
    const mainContent = document.querySelector('.main-content');
    if (mainContent instanceof HTMLElement) {
      // 重置滚动位置后确保保持可滚动状态
      mainContent.scrollTop = 0;
      mainContent.style.overflow = 'auto';
      
      // 移除可能影响布局的position样式
      if (mainContent.style.position === 'relative') {
        mainContent.style.position = '';
      }
      
      // 确保Android设备上卡片和容器布局正确
      document.querySelectorAll('.card, .container, [class*="card"], [class*="container"]')
        .forEach(el => {
          if (el instanceof HTMLElement) {
            // 确保卡片宽度正确，不会被挤压
            el.style.boxSizing = 'border-box';
            // 避免设置固定宽度，让布局自然流动
            if (el.style.width === '100%') {
              el.style.width = '';
            }
          }
        });
      
      // 确保不会阻止Android上的正常滚动
      setTimeout(() => {
        mainContent.style.overflow = 'auto';
      }, 50);
    }
    return;
  }
  
  // 以下是iOS和其他设备的处理
  
  // 修复移动端某些浏览器可能有的position:fixed元素导致的滚动问题
  const fixedElements = document.querySelectorAll('[style*="position: fixed"], [style*="position:fixed"], .fixed');
  if (fixedElements.length > 0) {
    // 简单地触发重排以修复某些奇怪的滚动问题
    document.body.style.paddingBottom = '1px';
    setTimeout(() => {
      document.body.style.paddingBottom = '';
    }, 10);
  }
}; 