const express = require('express');
const router = express.Router();
const axios = require('axios');
const { authenticateToken } = require('../middleware/auth');
const { User } = require('../models/User');
const { getConnection } = require('../services/database');
const { TAROT_CARDS } = require('../constants/tarot');
// 加载多语言的reader followups
const readerFollowups = require('../config/reader_followups'); // 默认中文
const readerFollowupsMultiLang = {
  'zh-CN': readerFollowups, // 默认中文
};

// 尝试加载其他语言配置文件
try {
  readerFollowupsMultiLang['zh-TW'] = require('../config/reader_followups_zh_tw');
  // console.log('成功加载繁体中文追问配置文件');
} catch (error) {
  // console.log('未找到繁体中文追问配置文件，将使用默认中文配置');
}

try {
  readerFollowupsMultiLang['en'] = require('../config/reader_followups_en');
  // console.log('成功加载英文追问配置文件');
} catch (error) {
  // console.log('未找到英文追问配置文件，将使用默认中文配置');
}

try {
  readerFollowupsMultiLang['ja'] = require('../config/reader_followups_ja');
  // console.log('成功加载日文追问配置文件');
} catch (error) {
  // console.log('未找到日文追问配置文件，将使用默认中文配置');
}
// const { checkEthicalIssues } = require('../services/ethicsCheck'); // 已禁用安全检测

// 创建用于追问的独立 Deepseek API 实例
const followupAPI = axios.create({
  baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  timeout: 60000,
  headers: {
    'Authorization': `Bearer ${process.env.QWEN_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

router.post('/', authenticateToken, async (req, res) => {
  try {
    console.log('\n========== Follow-up Request ==========');
    console.log('Received follow-up request:', {
      followUpQuestion: req.body.followUpQuestion,
      sessionId: req.body.sessionId,
      language: req.body.language
    });

    let { followUpQuestion, sessionId, language } = req.body;
    const userId = req.user.userId;

    if (!followUpQuestion || !sessionId) {
      console.log('Missing required parameters');
      return res.status(400).json({ error: '缺少必要的参数' });
    }
    
    // 限制追问文本最多100个字符
    if (followUpQuestion && followUpQuestion.length > 100) {
      console.log(`追问文本过长，已截断: ${followUpQuestion.length} -> 100字符`);
      followUpQuestion = followUpQuestion.slice(0, 100);
    }

    // 获取用户信息
    const user = await User.findById(userId);
    // console.log('User info:', {
    //   userId,
    //   username: user.username
    // });

    // 检查用户权限：VIP用户或首次使用追问的普通用户
    let hasPermission = false;
    
    // 检查是否是VIP用户
    if (user.vip_status && ['monthly', 'yearly', 'active'].includes(user.vip_status)) {
      hasPermission = true;
    } else {
      // 如果不是VIP用户，检查是否已经使用过追问
      const pool = await getConnection();
      const [usedFollowup] = await pool.query(`
        SELECT COUNT(*) as count
        FROM sessions
        WHERE user_id = ? AND dialog_history IS NOT NULL AND dialog_history != 'null' AND dialog_history != '[]'
      `, [userId]);
      
      // 如果从未使用过追问，允许使用一次
      if (usedFollowup[0].count === 0) {
        hasPermission = true;
      }
    }
    
    // 验证用户权限
    if (!hasPermission) {
      return res.status(403).json({ 
        error: '您已使用过一次免费追问，该功能仅对VIP用户开放',
        errorType: 'NO_FREE_FOLLOWUP'
      });
    }

    // 在调用追问API前，先进行伦理问题检查 - 已禁用安全检测
    // const ethicsCheckResult = await checkEthicalIssues(followUpQuestion, language || user.language || 'zh-CN');

    // 模拟安全检测结果，直接返回无安全问题
    const ethicsCheckResult = {
      whetherOrNot: false,
      category: '无安全问题',
      confidence: 0,
      reason: '安全检测已禁用',
      input_tokens: 0,
      output_tokens: 0
    };
    
    // 无论伦理检测结果如何，都要更新累计token数量
    const pool = await getConnection();
    
    // 首先获取当前的伦理检测token累计值
    const [currentEthicData] = await pool.query(`
      SELECT Ethical_input_token, Ethical_output_token, followup_ethical_categories, followup_ethical_reasons 
      FROM sessions WHERE id = ?
    `, [sessionId]);
    
    // 计算累计的token值
    const currentInputTokens = currentEthicData[0]?.Ethical_input_token || 0;
    const currentOutputTokens = currentEthicData[0]?.Ethical_output_token || 0;
    const newInputTokens = currentInputTokens + (ethicsCheckResult.input_tokens || 0);
    const newOutputTokens = currentOutputTokens + (ethicsCheckResult.output_tokens || 0);
    
    // 更新数据库中的伦理检测token累计值
    await pool.query(`
      UPDATE sessions 
      SET 
        Ethical_input_token = ?,
        Ethical_output_token = ?
      WHERE id = ?
    `, [
      newInputTokens,
      newOutputTokens,
      sessionId
    ]);
    
    // 如果检测到伦理问题，直接返回干预信息 - 已禁用安全检测
    // if (ethicsCheckResult.whetherOrNot) {
    //   console.log('\n========== Ethics Check Result ==========');
    //   console.log(`检测到伦理问题: ${ethicsCheckResult.category}, 置信度: ${ethicsCheckResult.confidence}`);
    //   console.log(`原因: ${ethicsCheckResult.reason}`);
    //
    //   // 创建新的伦理检测记录
    //   const newCategory = {
    //     timestamp: new Date().toISOString(),
    //     category: ethicsCheckResult.category,
    //     confidence: ethicsCheckResult.confidence
    //   };
    //
    //   const newReason = {
    //     timestamp: new Date().toISOString(),
    //     reason: ethicsCheckResult.reason
    //   };
    //
    //   // 使用JSON_ARRAY_APPEND更新数据库中的伦理检测记录
    //   await pool.query(`
    //     UPDATE sessions
    //     SET
    //       status = 'completed',
    //       ethical_status = 'ethical_intervention_follow',
    //       followup_ethical_categories = JSON_ARRAY_APPEND(
    //         COALESCE(followup_ethical_categories, JSON_ARRAY()),
    //         '$',
    //         CAST(? AS JSON)
    //       ),
    //       followup_ethical_reasons = JSON_ARRAY_APPEND(
    //         COALESCE(followup_ethical_reasons, JSON_ARRAY()),
    //         '$',
    //         CAST(? AS JSON)
    //       )
    //     WHERE id = ?
    //   `, [
    //     JSON.stringify(newCategory),
    //     JSON.stringify(newReason),
    //     sessionId
    //   ]);
    //
    //   const newDialogEntry = [
    //     {
    //       type: 'user',
    //       content: followUpQuestion,
    //       timestamp: new Date().toISOString()
    //     },
    //     {
    //       type: 'assistant',
    //       content: ethicsCheckResult.interventionMessage,
    //       timestamp: new Date().toISOString(),
    //       isEthicalIntervention: true,
    //       ethicalCategory: ethicsCheckResult.category,
    //       ethicalReason: ethicsCheckResult.reason
    //     }
    //   ];
    //
    //   await pool.query(
    //     `UPDATE sessions
    //      SET dialog_history = JSON_ARRAY_APPEND(
    //        COALESCE(dialog_history, JSON_ARRAY()),
    //        '$',
    //        CAST(? AS JSON),
    //        '$',
    //        CAST(? AS JSON)
    //      )
    //      WHERE id = ?`,
    //     [
    //       JSON.stringify(newDialogEntry[0]),
    //       JSON.stringify(newDialogEntry[1]),
    //       sessionId
    //     ]
    //   );
    //
    //   // 向用户返回干预信息
    //   return res.json({
    //     reading: ethicsCheckResult.interventionMessage,
    //     isEthicalIntervention: true,
    //     category: ethicsCheckResult.category,
    //     usage: {
    //       input_tokens: ethicsCheckResult.input_tokens,
    //       output_tokens: ethicsCheckResult.output_tokens,
    //       total_tokens: ethicsCheckResult.input_tokens + ethicsCheckResult.output_tokens
    //     }
    //   });
    // } else {
      // 安全检测已禁用，直接继续处理追问
      // 没有检测到伦理问题，也记录伦理检查结果 - 已禁用

      // 检查是否为潜在伦理问题（category不是安全类别且confidence<=0.9） - 已禁用
      // const safeCategories = ['无安全问题', '無安全問題', 'No Ethical Issue', '倫理的問題なし'];
      // const isPotentialEthicalIssue =
      //   ethicsCheckResult.category &&
      //   !safeCategories.includes(ethicsCheckResult.category) &&
      //   ethicsCheckResult.confidence <= 0.9;
      //
      // // 创建新的伦理检查记录（无敏感内容）
      // const newCategory = {
      //   timestamp: new Date().toISOString(),
      //   category: ethicsCheckResult.category || '无安全问题',
      //   confidence: ethicsCheckResult.confidence || 0
      // };
      //
      // const newReason = {
      //   timestamp: new Date().toISOString(),
      //   reason: ethicsCheckResult.reason || '未检测到敏感内容'
      // };
      //
      // // 使用JSON_ARRAY_APPEND更新数据库中的伦理检测记录
      // if (isPotentialEthicalIssue) {
      //   await pool.query(`
      //     UPDATE sessions
      //     SET
      //       status = 'completed',
      //       ethical_status = 'potential_ethical_issue_follow',
      //       followup_ethical_categories = JSON_ARRAY_APPEND(
      //         COALESCE(followup_ethical_categories, JSON_ARRAY()),
      //         '$',
      //         CAST(? AS JSON)
      //       ),
      //       followup_ethical_reasons = JSON_ARRAY_APPEND(
      //         COALESCE(followup_ethical_reasons, JSON_ARRAY()),
      //         '$',
      //         CAST(? AS JSON)
      //       )
      //     WHERE id = ?
      //   `, [
      //     JSON.stringify(newCategory),
      //     JSON.stringify(newReason),
      //     sessionId
      //   ]);
      // } else {
      //   await pool.query(`
      //     UPDATE sessions
      //     SET
      //       followup_ethical_categories = JSON_ARRAY_APPEND(
      //         COALESCE(followup_ethical_categories, JSON_ARRAY()),
      //         '$',
      //         CAST(? AS JSON)
      //       ),
      //       followup_ethical_reasons = JSON_ARRAY_APPEND(
      //         COALESCE(followup_ethical_reasons, JSON_ARRAY()),
      //         '$',
      //         CAST(? AS JSON)
      //       )
      //     WHERE id = ?
      //   `, [
      //     JSON.stringify(newCategory),
      //     JSON.stringify(newReason),
      //     sessionId
      //   ]);
      // }
    // }

    // 从数据库获取用户详细信息
    const [userDetails] = await pool.query(
      'SELECT gender, location, birthday, user_profile FROM users WHERE id = ?',
      [userId]
    );

    // 获取当前年月
    const now = new Date();
    const currentFullDate = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月${String(now.getDate()).padStart(2, '0')}日`;

    // 获取本次会话的历史记录
    const [sessions] = await pool.query(`
      SELECT 
        s.*
      FROM sessions s
      WHERE s.id = ? AND s.user_id = ?
    `, [sessionId, userId]);

    if (!sessions || sessions.length === 0) {
      return res.status(404).json({ error: '找不到相关会话' });
    }

    const session = sessions[0];
    
    // 根据reader_id和语言获取对应的追问prompt
    // 标准化language参数
    let normalizedLang = language || user.language || 'zh-TW';
    if (normalizedLang.startsWith('en-')) normalizedLang = 'en';
    if (normalizedLang.startsWith('ja-')) normalizedLang = 'ja';
    if (normalizedLang.startsWith('zh-TW')) normalizedLang = 'zh-TW';
    if (normalizedLang.startsWith('zh-CN')) normalizedLang = 'zh-CN';
    
    // 尝试按优先级依次获取reader prompt
    const langPrompts = readerFollowupsMultiLang[normalizedLang] || readerFollowups;
    
    // 根据reader_id获取对应的追问prompt
    let selectedFollowup = null;
    
    if (langPrompts) {
      // 如果找到对应语言的followups配置
      selectedFollowup = langPrompts.find(reader => reader.id.toLowerCase() === session.reader_id?.toLowerCase());
    }
    
    // 如果没找到对应语言的reader，尝试使用默认中文
    if (!selectedFollowup) {
      selectedFollowup = readerFollowups.find(reader => reader.id.toLowerCase() === session.reader_id?.toLowerCase());
    }
    
    const followupPrompt = selectedFollowup?.prompt || readerFollowups[0].prompt; // 如果仍找不到对应的reader，使用默认的第一个reader的prompt

    let cards = [];
    let cardsDescription = '';
    
    try {
      // 处理 selected_cards 可能为 null、undefined 或无效 JSON 的情况
      if (session.selected_cards) {
        // 如果 selected_cards 是字符串，则解析它
        if (typeof session.selected_cards === 'string') {
          try {
            cards = JSON.parse(session.selected_cards);
          } catch (parseError) {
            console.error('JSON parse error:', parseError);
            // 解析失败时设置为空数组
            cards = [];
          }
        } else {
          // 不是字符串，直接使用
          cards = session.selected_cards;
        }
        
        // 确保 cards 是数组
        if (!Array.isArray(cards)) {
          console.error('Cards data is not in array format, using empty array instead');
          cards = [];
        }
      }
      
      // 生成卡牌描述
      if (cards.length > 0) {
        // 获取翻译后的卡牌名称
        const getTranslatedCardName = (card, language) => {
          try {
            // 标准化language参数
            let normalizedLang = language;
            if (normalizedLang.startsWith('en-')) normalizedLang = 'en';
            if (normalizedLang.startsWith('ja-')) normalizedLang = 'ja';
            if (normalizedLang.startsWith('zh-TW')) normalizedLang = 'zh-TW';
            if (normalizedLang.startsWith('zh-CN')) normalizedLang = 'zh-CN';
            
            // 尝试从i18n文件中获取翻译
            const i18nFile = require(`../i18n/locales/${normalizedLang}.json`);
            
            // 判断是大阿卡纳还是小阿卡纳
            const cardName = card.name;
            
            // 检查是否是大阿卡纳牌
            const majorArcana = TAROT_CARDS.find(c => c.name === cardName && !cardName.includes('牌'));
            if (majorArcana) {
              // 找到大阿卡纳牌在数组中的索引
              const index = TAROT_CARDS.indexOf(majorArcana);
              if (index >= 0 && index < 22) {
                // 使用索引从i18n文件中获取翻译
                return i18nFile.reading.cards.major[index];
              }
            }
            
            // 处理小阿卡纳牌
            // 判断牌的花色
            let suit = '';
            if (cardName.includes('权杖') || cardName.includes('權杖')) {
              suit = 'wands';
            } else if (cardName.includes('圣杯') || cardName.includes('聖杯')) {
              suit = 'cups';
            } else if (cardName.includes('宝剑') || cardName.includes('寶劍')) {
              suit = 'swords';
            } else if (cardName.includes('钱币') || cardName.includes('錢幣') || cardName.includes('星币') || cardName.includes('星幣')) {
              suit = 'pentacles';
            }
            
            // 判断牌的数值
            let value = '';
            if (cardName.includes('王牌') || cardName.includes('A')) {
              value = 'ace';
            } else if (cardName.includes('侍者') || cardName.includes('侍從')) {
              value = 'page';
            } else if (cardName.includes('骑士') || cardName.includes('騎士')) {
              value = 'knight';
            } else if (cardName.includes('皇后')) {
              value = 'queen';
            } else if (cardName.includes('国王') || cardName.includes('國王')) {
              value = 'king';
            } else {
              // 尝试提取数字
              const numbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
              for (let i = 0; i < numbers.length; i++) {
                if (cardName.includes(numbers[i]) || cardName.includes(String(i + 1))) {
                  value = String(i + 1);
                  break;
                }
              }
            }
            
            // 如果找到了花色和数值，从i18n文件中获取翻译
            if (suit && value && i18nFile.reading.cards[suit] && i18nFile.reading.cards[suit][value]) {
              return i18nFile.reading.cards[suit][value];
            }
            
            // 如果无法获取翻译，返回原始名称
            return cardName;
          } catch (error) {
            console.error('Error translating card name:', error);
            return card.name || '未知卡牌';
          }
        };
        
        // 根据语言选择不同的逆位文本
        const getReversedText = (language) => {
          switch(language) {
            case 'en':
              return '(Reversed)';
            case 'ja':
              return '（逆位置）';
            case 'zh-TW':
              return '（逆位）';
            case 'zh-CN':
            default:
              return '（逆位）';
          }
        };
        
        cardsDescription = cards
          .map(card => {
            const translatedName = getTranslatedCardName(card, normalizedLang);
            const reversedText = card.isReversed ? getReversedText(normalizedLang) : '';
            return `${translatedName}${reversedText}`;
          })
          .join('\n');
      } else {
        // 根据语言选择不同的无卡牌信息文本
        let noCardsText;
        switch(normalizedLang) {
          case 'en':
            noCardsText = 'No card information';
            break;
          case 'ja':
            noCardsText = 'カード情報なし';
            break;
          case 'zh-TW':
            noCardsText = '無卡牌資訊';
            break;
          case 'zh-CN':
          default:
            noCardsText = '无卡牌信息';
            break;
        }
        cardsDescription = noCardsText;
      }
    } catch (error) {
      console.error('Error processing cards:', error);
      // 根据语言选择不同的错误文本
      let errorText;
      switch(normalizedLang) {
        case 'en':
          errorText = 'Error processing card data';
          break;
        case 'ja':
          errorText = 'カードデータの処理中にエラーが発生しました';
          break;
        case 'zh-TW':
          errorText = '處理卡牌數據時出現錯誤';
          break;
        case 'zh-CN':
        default:
          errorText = '处理卡牌数据时出现错误';
          break;
      }
      cardsDescription = errorText;
    }

    // 获取对话历史
    let dialogHistory = '无历史对话';
    try {
      if (session.dialog_history && session.dialog_history !== 'null' && session.dialog_history !== '[]') {
        const dialogHistoryData = typeof session.dialog_history === 'string' 
          ? JSON.parse(session.dialog_history) 
          : session.dialog_history;
        
        // 只获取最近一次对话（最后两条记录：用户问题和AI回答）
        if (Array.isArray(dialogHistoryData) && dialogHistoryData.length >= 2) {
          // 获取最后两条记录
          const lastTwoEntries = dialogHistoryData.slice(-2);
          
          // 根据语言设置角色称呼
          let userRole = '問卜者';
          let assistantRole = '塔羅師';
          
          if (normalizedLang === 'en') {
            userRole = 'Querent';
            assistantRole = 'Tarot Reader';
          } else if (normalizedLang === 'ja') {
            userRole = '質問者';
            assistantRole = 'タロット占い師';
          } else if (normalizedLang === 'zh-TW') {
            userRole = '問卜者';
            assistantRole = '塔羅師';
          }
          
          dialogHistory = lastTwoEntries.map((entry) => {
            return `${entry.type === 'user' ? userRole : assistantRole}: ${entry.content}`;
          }).join('\n\n');
        } else if (Array.isArray(dialogHistoryData) && dialogHistoryData.length > 0) {
          // 如果不足两条记录，则使用所有可用记录
          
          // 根据语言设置角色称呼
          let userRole = '問卜者';
          let assistantRole = '塔羅師';
          
          if (normalizedLang === 'en') {
            userRole = 'Querent';
            assistantRole = 'Tarot Reader';
          } else if (normalizedLang === 'ja') {
            userRole = '質問者';
            assistantRole = 'タロット占い師';
          } else if (normalizedLang === 'zh-TW') {
            userRole = '問卜者';
            assistantRole = '塔羅師';
          }
          
          dialogHistory = dialogHistoryData.map((entry) => {
            return `${entry.type === 'user' ? userRole : assistantRole}: ${entry.content}`;
          }).join('\n\n');
        }
      }
    } catch (error) {
      console.error('Error parsing dialog history:', error);
      dialogHistory = '解析歷史對話時出現錯誤';
    }

    // 根据语言选择合适的提示模板
    let userPrompt = '';
    
    if (normalizedLang === 'en') {
      userPrompt = `
Please answer in ${normalizedLang}
Current time: ${currentFullDate}

Querent Information:
Name: ${user.username}
Gender: ${userDetails[0]?.gender || 'Unknown'}
Birthday: ${userDetails[0]?.birthday || 'Unknown'}

Original question: ${session.question}
Follow-up question: ${followUpQuestion}

Cards drawn for the original question: ${cardsDescription}

History Summary:
${session.summary || 'No history record'}

Conversation History:
${dialogHistory}
`;
    } else if (normalizedLang === 'ja') {
      userPrompt = `
${normalizedLang}で回答してください
現在の時間：${currentFullDate}

質問者情報：
名前：${user.username}
性別：${userDetails[0]?.gender || '不明'}
誕生日：${userDetails[0]?.birthday || '不明'}

元の質問：${session.question}
追加質問：${followUpQuestion}

元の質問のために引かれたカード：${cardsDescription}

履歴概要：
${session.summary || '履歴記録なし'}

会話履歴：
${dialogHistory}
`;
    } else if (normalizedLang === 'zh-TW') {
      userPrompt = `
請使用${normalizedLang}回答
當前時間：${currentFullDate}

問卜者資訊：
姓名：${user.username}
性別：${userDetails[0]?.gender || '未知'}
生日：${userDetails[0]?.birthday || '未知'}

原始問題：${session.question}
追問內容：${followUpQuestion}

原始問題抽出的卡牌：${cardsDescription}

歷史總結：
${session.summary || '無歷史記錄'}

對話歷史：
${dialogHistory}
`;
    } else {
      // 默认使用繁体中文
      userPrompt = `
請使用${normalizedLang}回答
當前時間：${currentFullDate}

問卜者資訊：
姓名：${user.username}
性別：${userDetails[0]?.gender || '未知'}
生日：${userDetails[0]?.birthday || '未知'}

原始問題：${session.question}
追問內容：${followUpQuestion}

原始問題抽出的卡牌：${cardsDescription}

歷史總結：
${session.summary || '無歷史記錄'}

對話歷史：
${dialogHistory}
`;
    }

    console.log('\n========== Prompts ==========');
    console.log('Follow-up System Prompt:', followupPrompt.substring(0, 50) + '...');
    console.log('Follow-up User Prompt:', userPrompt.substring(0, 50) + '...');

    // console.log('\n========== Sending Request ==========');
    // console.log('Sending follow-up request to Deepseek API');

    const response = await followupAPI.post('/chat/completions', {
      model: "qwen-plus-latest",
      messages: [
        { role: "system", content: followupPrompt },
        { role: "user", content: userPrompt }
      ],
      temperature: 0,
      stream: false,
      parameters: {
        response_language: language || user.language || 'zh-TW'
      }
    });

    // console.log('\n========== API Response ==========');
    // console.log('Follow-up Response:', response.data.choices[0].message.content);

    if (!response.data || !response.data.choices || !response.data.choices[0]) {
      throw new Error('API 返回格式錯誤');
    }

    const content = response.data.choices[0].message.content;
    
    // 获取API调用的token使用情况
    const followupInputTokens = response.data.usage?.prompt_tokens || 0;
    const followupOutputTokens = response.data.usage?.completion_tokens || 0;
    
    // 获取当前累计的token值
    const [currentTokens] = await pool.query(`
      SELECT input_tokens, output_tokens 
      FROM sessions 
      WHERE id = ?
    `, [sessionId]);
    
    // 计算新的累计值
    const currentFollowupInputTokens = currentTokens[0]?.input_tokens || 0;
    const currentFollowupOutputTokens = currentTokens[0]?.output_tokens || 0;
    const newFollowupInputTokens = currentFollowupInputTokens + followupInputTokens;
    const newFollowupOutputTokens = currentFollowupOutputTokens + followupOutputTokens;
    
    // 更新数据库中的token累计值
    await pool.query(`
      UPDATE sessions 
      SET 
        input_tokens = ?,
        output_tokens = ?
      WHERE id = ?
    `, [
      newFollowupInputTokens,
      newFollowupOutputTokens,
      sessionId
    ]);
    
    // 更新对话历史
    const newDialogEntry = [
      {
        type: 'user',
        content: followUpQuestion,
        timestamp: new Date().toISOString()
      },
      {
        type: 'assistant',
        content: content,
        timestamp: new Date().toISOString()
      }
    ];

    await pool.query(
      `UPDATE sessions 
       SET dialog_history = JSON_ARRAY_APPEND(
         COALESCE(dialog_history, JSON_ARRAY()),
         '$',
         CAST(? AS JSON),
         '$',
         CAST(? AS JSON)
       )
       WHERE id = ?`,
      [
        JSON.stringify(newDialogEntry[0]),
        JSON.stringify(newDialogEntry[1]),
        sessionId
      ]
    );

    res.json({ 
      reading: response.data.choices[0].message.content,
      usage: response.data.usage
    });
  } catch (error) {
    console.error('Error:', error);
    res.status(500).json({ 
      error: error.response?.data?.error || '生成解讀時出現錯誤'
    });
  }
});

module.exports = router;
