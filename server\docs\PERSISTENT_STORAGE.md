# 持久化存储配置说明

## 概述

为了解决部署时用户上传文件被覆盖的问题，项目实现了持久化存储方案。所有用户上传的文件和重要数据都存储在服务器的持久化目录中，不会因为代码部署而丢失。

## 持久化存储目录

### 1. 分享截图存储
- **开发环境**: `server/public/uploads/share-screenshots`
- **生产环境**: `/var/www/tarot/share-screenshots`
- **用途**: 存储用户上传的分享截图
- **访问URL**: `/uploads/share-screenshots/filename`

### 2. 百度推送记录
- **开发环境**: `server/logs/baidu-push`
- **生产环境**: `/var/www/tarot/baidu_push`
- **用途**: 存储百度URL推送的历史记录和日志

### 3. 星座运势文件
- **生产环境**: `/var/www/tarot/horoscopes`
- **用途**: 存储生成的星座运势文件
- **访问URL**: `/horoscopes/filename`

## 实现原理

### 环境检测
```javascript
if (process.env.NODE_ENV !== 'development') {
  // 生产环境：使用持久化目录
  return '/var/www/tarot/share-screenshots';
} else {
  // 开发环境：使用本地目录
  return path.join(__dirname, '../public/uploads/share-screenshots');
}
```

### 静态文件服务
```javascript
// 生产环境配置持久化目录的静态文件服务
if (process.env.NODE_ENV !== 'development') {
  app.use('/uploads/share-screenshots', express.static('/var/www/tarot/share-screenshots'));
}
```

## 部署配置

### 1. 手动创建目录
需要手动创建必要的持久化目录：
```bash
sudo mkdir -p /var/www/tarot/baidu_push
sudo mkdir -p /var/www/tarot/horoscopes
sudo chmod 755 /var/www/tarot/baidu_push
sudo chmod 755 /var/www/tarot/horoscopes
```

注意：不再自动创建 share-screenshots 目录，需要手动创建或确保该目录存在。

### 2. 权限设置
确保Node.js进程有权限读写这些目录：
```bash
sudo chown -R www-data:www-data /var/www/tarot/
# 或者根据你的服务器配置调整用户和组
```

## 服务管理

### ShareScreenshotService
提供分享截图的存储管理功能：
- `getStorageDir()`: 获取存储目录路径
- `ensureShareScreenshotDirExists()`: 检查目录是否存在
- `checkFileExists(filename)`: 检查文件是否存在
- `deleteFile(filename)`: 删除文件
- `getFileList()`: 获取文件列表
- `getStorageStats()`: 获取存储统计信息
- `cleanupOldFiles(daysOld)`: 清理过期文件



## 监控和维护

### 存储空间监控
定期检查存储空间使用情况：
```bash
du -sh /var/www/tarot/share-screenshots
```

### 日志监控
检查应用日志中的存储相关信息：
```bash
tail -f /var/log/your-app.log | grep "分享截图"
```

### 定期清理
建议设置定时任务清理过期文件：
```bash
# 添加到crontab，每周清理超过30天的文件
0 2 * * 0 find /var/www/tarot/share-screenshots -type f -mtime +30 -delete
```

## 故障排除

### 1. 目录权限问题
```bash
# 检查目录权限
ls -la /var/www/tarot/
# 修复权限
sudo chmod 755 /var/www/tarot/share-screenshots
sudo chown www-data:www-data /var/www/tarot/share-screenshots
```

### 2. 磁盘空间不足
```bash
# 检查磁盘空间
df -h
# 清理过期文件
find /var/www/tarot/share-screenshots -type f -mtime +30 -delete
```

### 3. 文件上传失败
检查以下几点：
- 目录是否存在
- 权限是否正确
- 磁盘空间是否充足
- 文件大小是否超过限制（5MB）

## 备份建议

### 1. 定期备份
```bash
# 备份分享截图
tar -czf share-screenshots-backup-$(date +%Y%m%d).tar.gz /var/www/tarot/share-screenshots
```

### 2. 同步到云存储
考虑将重要文件同步到云存储服务，如阿里云OSS、腾讯云COS等。

## 注意事项

1. **环境变量**: 确保 `NODE_ENV` 在生产环境中设置为 `production`
2. **权限管理**: 定期检查目录权限，确保应用有足够的读写权限
3. **空间管理**: 监控存储空间使用情况，及时清理过期文件
4. **备份策略**: 制定合适的备份策略，防止数据丢失
5. **安全考虑**: 限制文件类型和大小，防止恶意文件上传
