export const loadScript = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载过该脚本
    const existingScript = document.querySelector(`script[src="${src}"]`);
    if (existingScript) {
      // 如果脚本已加载完成
      if ((existingScript as any).loaded) {
        resolve();
        return;
      }
      
      // 如果脚本正在加载中，等待加载完成
      existingScript.addEventListener('load', () => {
        (existingScript as any).loaded = true;
        resolve();
      });
      existingScript.addEventListener('error', () => {
        document.body.removeChild(existingScript);
        reject(new Error(`Failed to load script: ${src}`));
      });
      return;
    }

    // 创建新脚本
    const script = document.createElement('script');
    script.src = src;
    script.async = true;

    // 添加data-paypal-script属性以便于识别
    if (src.includes('paypal.com/sdk/js')) {
      script.setAttribute('data-paypal-script', 'true');
    }

    script.addEventListener('load', () => {
      (script as any).loaded = true;
      resolve();
    });
    script.addEventListener('error', () => {
      document.body.removeChild(script);
      reject(new Error(`Failed to load script: ${src}`));
    });

    // 移除可能存在的旧PayPal脚本
    if (src.includes('paypal.com/sdk/js')) {
      const oldPayPalScripts = document.querySelectorAll('script[data-paypal-script]');
      oldPayPalScripts.forEach(oldScript => {
        if (oldScript !== script) {
          document.body.removeChild(oldScript);
        }
      });
    }

    document.body.appendChild(script);
  });
};
