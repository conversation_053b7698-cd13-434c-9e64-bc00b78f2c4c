const http = require('http');

// 测试安全头部的函数
function testSecurityHeaders(host = 'localhost', port = 5000) {
  const options = {
    hostname: host,
    port: port,
    path: '/',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log('=== 安全头部测试结果 ===');
    console.log(`状态码: ${res.statusCode}`);
    console.log('\n安全相关头部:');
    
    // 检查点击劫持防护头部
    const xFrameOptions = res.headers['x-frame-options'];
    const csp = res.headers['content-security-policy'];
    
    console.log(`X-Frame-Options: ${xFrameOptions || '未设置'}`);
    console.log(`Content-Security-Policy: ${csp || '未设置'}`);

    // 检查其他安全头部
    console.log(`X-Content-Type-Options: ${res.headers['x-content-type-options'] || '未设置'}`);
    console.log(`X-XSS-Protection: ${res.headers['x-xss-protection'] || '未设置'}`);
    console.log(`Strict-Transport-Security: ${res.headers['strict-transport-security'] || '未设置'}`);

    // 显示所有响应头部（用于调试）
    console.log('\n=== 所有响应头部 ===');
    Object.keys(res.headers).forEach(key => {
      console.log(`${key}: ${res.headers[key]}`);
    });

    // 验证点击劫持防护
    console.log('\n=== 点击劫持防护验证 ===');
    if (xFrameOptions === 'DENY') {
      console.log('✅ X-Frame-Options 设置正确 (DENY)');
    } else if (xFrameOptions === 'SAMEORIGIN') {
      console.log('⚠️  X-Frame-Options 设置为 SAMEORIGIN，建议改为 DENY');
    } else {
      console.log('❌ X-Frame-Options 未正确设置');
    }

    if (csp && csp.includes("frame-ancestors 'none'")) {
      console.log('✅ Content-Security-Policy frame-ancestors 设置正确');
    } else if (csp && csp.includes("frame-ancestors 'self'")) {
      console.log('⚠️  Content-Security-Policy frame-ancestors 设置为 self，建议改为 none');
    } else {
      console.log('❌ Content-Security-Policy frame-ancestors 未正确设置');
      console.log(`   实际CSP内容: ${csp}`);
    }
    
    console.log('\n=== 测试完成 ===');
  });

  req.on('error', (e) => {
    console.error(`请求出错: ${e.message}`);
    console.log('请确保服务器正在运行在指定端口');
  });

  req.end();
}

// 运行测试
console.log('开始测试安全头部设置...');
testSecurityHeaders();
