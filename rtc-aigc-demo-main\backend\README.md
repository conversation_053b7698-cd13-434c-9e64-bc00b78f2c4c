# 实时对话后端API

这是基于火山引擎语音对话服务的实时对话后端API。提供WebSocket和REST API接口，支持实时语音对话功能。

## 安装说明

### 环境要求

- Python 3.7或以上
- pip包管理器
- 支持音频设备的环境（用于录音和播放）

### 安装步骤

1. 安装依赖

```bash
pip install -r requirements.txt
```

2. 配置API密钥

编辑`app/config.py`文件，填写火山引擎API密钥：

```python
VOLCANO_CONFIG = {
    "base_url": "wss://openspeech.bytedance.com/api/v3/realtime/dialogue",
    "headers": {
        "X-Api-App-ID": "填写您的App ID",
        "X-Api-Access-Key": "填写您的Access Key",
        "X-Api-Resource-Id": "volc.speech.dialog",  # 固定值
        "X-Api-App-Key": "PlgvMymc7f3tQnJ6",  # 固定值
    }
}
```

## 启动服务

```bash
python run.py
```

默认情况下，服务将在`http://localhost:5000`启动。

## API文档

### REST API

#### 1. 启动语音会话

- **URL:** `/api/voice/start`
- **方法:** POST
- **请求参数:**
  ```json
  {
    "user_id": "用户ID（可选）"
  }
  ```
- **响应:**
  ```json
  {
    "status": "ok",
    "message": "语音会话已创建",
    "data": {
      "session_id": "会话ID"
    }
  }
  ```

#### 2. 停止语音会话

- **URL:** `/api/voice/stop`
- **方法:** POST
- **请求参数:**
  ```json
  {
    "user_id": "用户ID（可选）",
    "session_id": "会话ID"
  }
  ```
- **响应:**
  ```json
  {
    "status": "ok",
    "message": "语音会话已停止"
  }
  ```

#### 3. 获取用户会话列表

- **URL:** `/api/voice/sessions`
- **方法:** GET
- **查询参数:** `user_id=用户ID（可选）`
- **响应:**
  ```json
  {
    "status": "ok",
    "data": {
      "sessions": [
        {
          "session_id": "会话ID",
          "user_id": "用户ID",
          "status": "会话状态",
          "created_at": "创建时间"
        }
      ]
    }
  }
  ```

#### 4. 发送语音数据

- **URL:** `/api/voice/audio`
- **方法:** POST
- **请求参数:**
  ```json
  {
    "session_id": "会话ID",
    "audio": "base64编码的音频数据"
  }
  ```
- **响应:**
  ```json
  {
    "status": "ok",
    "message": "音频数据已发送"
  }
  ```

#### 5. 获取语音会话数据（轮询方式）

- **URL:** `/api/voice/data`
- **方法:** GET
- **查询参数:** `session_id=会话ID`
- **响应:**
  ```json
  {
    "status": "ok",
    "data": {
      "type": "数据类型（audio/text/event/error）",
      "data": "数据内容"
    }
  }
  ```

### WebSocket API

连接到`http://localhost:5000`后，可以使用以下事件：

#### 1. 加入会话

发送`join_session`事件：

```json
{
  "user_id": "用户ID（可选）",
  "session_id": "会话ID"
}
```

接收`joined`事件表示成功加入。

#### 2. 离开会话

发送`leave_session`事件：

```json
{
  "session_id": "会话ID"
}
```

接收`left`事件表示成功离开。

#### 3. 发送音频数据

发送`audio_data`事件：

```json
{
  "session_id": "会话ID",
  "audio": "base64编码的音频数据"
}
```

接收`audio_received`事件表示成功接收。

#### 4. 接收会话数据

监听`session_data`事件，接收格式：

```json
{
  "session_id": "会话ID",
  "data": {
    "type": "数据类型（audio/text/event/error）",
    "data": "数据内容"
  }
}
```

## 错误处理

所有错误响应格式如下：

```json
{
  "status": "error",
  "message": "错误信息"
}
``` 