const express = require('express');
const router = express.Router();
const { getConnection } = require('../services/database');


// 保存指纹数据
router.post('/', async (req, res) => {
  try {
    const {
      fingerprint,
      blogId,
      question,
      promptContent,
      responseContent,
      inputTokens,
      outputTokens,
      spreadType,
      selectedCards,
      status = 'completed'
    } = req.body;

    // 验证必要参数
    if (!fingerprint || !blogId) {
      return res.status(400).json({ error: '缺少必要的参数' });
    }

    console.log('收到博客解读数据:', {
      fingerprint: fingerprint.substring(0, 10) + '...',
      blogId,
      question: question?.substring(0, 20) + '...'
    });

    // 保存到数据库
    const pool = await getConnection();
    const now = new Date();
    
    await pool.query(
      `INSERT INTO blog_readings 
       (fingerprint, blog_id, question, prompt_content, response_content, spread_type, selected_cards, status, input_tokens, output_tokens, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        fingerprint,
        blogId,
        question || '',
        promptContent || '',
        responseContent || '',
        spreadType || '',
        typeof selectedCards === 'object' ? JSON.stringify(selectedCards) : selectedCards || '',
        status,
        inputTokens || 0,
        outputTokens || 0,
        now
      ]
    );

    return res.status(201).json({ success: true, message: '博客解读数据保存成功' });

  } catch (error) {
    console.error('保存博客解读数据失败:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 获取指纹历史记录
router.get('/:fingerprint', async (req, res) => {
  try {
    const { fingerprint } = req.params;

    // 验证参数
    if (!fingerprint) {
      return res.status(400).json({ error: '缺少指纹参数' });
    }

    // 从数据库获取历史记录
    const pool = await getConnection();
    const [records] = await pool.query(
      `SELECT id, blog_id, question, response_content, spread_type, selected_cards, status, created_at 
       FROM blog_readings 
       WHERE fingerprint = ? 
       ORDER BY created_at DESC`,
      [fingerprint]
    );

    return res.status(200).json({ records });

  } catch (error) {
    console.error('获取指纹历史记录失败:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 检查指纹和博客ID组合是否存在
router.get('/check/:fingerprint/:blogId', async (req, res) => {
  try {
    const { fingerprint, blogId } = req.params;

    // 验证参数
    if (!fingerprint || !blogId) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    // 从数据库检查是否存在记录
    const pool = await getConnection();
    const [records] = await pool.query(
      `SELECT id, question, response_content, spread_type, selected_cards, status, input_tokens, output_tokens, created_at 
       FROM blog_readings 
       WHERE fingerprint = ? AND blog_id = ? 
       ORDER BY created_at DESC 
       LIMIT 1`,
      [fingerprint, blogId]
    );

    if (records.length > 0) {
      // 存在记录，返回记录信息
      return res.status(200).json({
        exists: true,
        record: records[0]
      });
    } else {
      // 不存在记录
      return res.status(200).json({
        exists: false
      });
    }

  } catch (error) {
    console.error('检查指纹博客组合失败:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router; 