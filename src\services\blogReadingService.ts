import axios from 'axios';

interface BlogReadingParams {
  question: string;
  selectedCards: any[];
  selectedSpread: any;
  sessionId?: string;
  language?: string;
  fingerprint?: string;
  blogId?: string;
}

interface BlogReadingResult {
  content: string;
  done?: boolean;
  hasRepeatedQuestion?: boolean;
  error?: string;
}

/**
 * 获取博客塔罗牌解读结果 - 直接返回完整内容
 * @param params - 博客塔罗牌解读选项
 * @returns Promise<string> - 解读结果内容
 */
export const getBlogReading = async (params: BlogReadingParams): Promise<string> => {
  try {
    const response = await axios.post('/api/blog-reading', params);
    return response.data.content || '';
  } catch (error) {
    // console.error('获取塔罗解读失败:', error);
    throw error;
  }
};

/**
 * 获取特定会话的博客解读结果
 * @param sessionId - 会话ID
 * @returns 解读结果
 */
export const getBlogSessionReading = async (sessionId: string): Promise<BlogReadingResult> => {
  try {
    const response = await axios.get(`/api/blog-reading/session/${sessionId}/reading`);
    return response.data;
  } catch (error) {
    throw error;
  }
}; 