import { majorArcanaDetails } from './details/majorArcanaDetails';
import { wandsDetails } from './details/wandsDetails';
import { cupsDetails } from './details/cupsDetails';
import { swordsDetails } from './details/swordsDetails';
import { pentaclesDetails } from './details/pentaclesDetails';

// 定义卡牌详情接口
export interface CardDetail {
  id: number;
  name: string;
  nameEn?: string;
  nameJa?: string;
  nameZhTW?: string;
  description: string;
  descriptionEn?: string;
  descriptionJa?: string;
  descriptionZhTW?: string;
  keywords: {
    upright: string;
    reversed: string;
  };
  keywordsEn?: {
    upright: string;
    reversed: string;
  };
  keywordsJa?: {
    upright: string;
    reversed: string;
  };
  keywordsZhTW?: {
    upright: string;
    reversed: string;
  };
  meanings: {
    upright: string;
    reversed: string;
  };
  meaningsEn?: {
    upright: string;
    reversed: string;
  };
  meaningsJa?: {
    upright: string;
    reversed: string;
  };
  meaningsZhTW?: {
    upright: string;
    reversed: string;
  };
  symbolism: string;
  symbolismEn?: string;
  symbolismJa?: string;
  symbolismZhTW?: string;
  archetype?: string;
  archetypeEn?: string;
  archetypeJa?: string;
  archetypeZhTW?: string;
}

/**
 * 根据卡牌ID和语言获取卡牌详情
 * @param cardId 卡牌ID
 * @param language 语言代码
 * @returns 卡牌详情对象
 */
export function getCardDetails(cardId: number): CardDetail | null {
  // 根据卡牌ID确定类别
  let details: CardDetail | undefined;
  
  if (cardId <= 21) {
    // 大阿卡纳
    details = majorArcanaDetails.find(c => c.id === cardId);
  } else if (cardId >= 22 && cardId <= 35) {
    // 权杖
    details = wandsDetails.find(c => c.id === cardId);
  } else if (cardId >= 36 && cardId <= 49) {
    // 圣杯
    details = cupsDetails.find(c => c.id === cardId);
  } else if (cardId >= 50 && cardId <= 63) {
    // 宝剑
    details = swordsDetails.find(c => c.id === cardId);
  } else if (cardId >= 64 && cardId <= 77) {
    // 星币
    details = pentaclesDetails.find(c => c.id === cardId);
  }
  
  return details || null;
} 