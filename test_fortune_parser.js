// 测试日运解读结果段落解析逻辑
import { createInterface } from 'readline';

// 从fortune.js中复制解析函数
const parseFortuneContentSections = (content) => {
  if (!content) return {};

  const sections = {
    intro: '',    // 开场语
    overall: '',  // 今日能量总览
    love: '',     // 爱情运势
    career: '',   // 事业运势
    wealth: '',   // 财富运势
    health: '',   // 健康运势
    mystery: ''   // 今日神秘指引
  };
  
  // 存储每个部分的评分 (1-5分)
  const ratings = {
    overall: 0,
    love: 0,
    career: 0,
    wealth: 0,
    health: 0
  };

  // 根据四种语言中的固定段落名称，重新定义更精确的正则表达式
  const sectionPatterns = {
    intro: [
      /^开场语$/,             // 简体中文
      /^開場語$/,             // 繁体中文
      /^Introduction$/i,      // 英文
      /^はじめに$/            // 日文
    ],
    overall: [
      /^今日能量总览$/,        // 简体中文
      /^今日能量總覽$/,        // 繁体中文
      /^Today's Energy Overview$/i, // 英文
      /^今日のエネルギー概観$/  // 日文
    ],
    love: [
      /^爱情运势$/,           // 简体中文
      /^愛情運勢$/,           // 繁体中文
      /^Love Fortune$/i,      // 英文
      /^恋愛運$/              // 日文
    ],
    career: [
      /^事业运势$/,           // 简体中文
      /^事業運勢$/,           // 繁体中文
      /^Career Fortune$/i,    // 英文
      /^仕事運$/              // 日文
    ],
    wealth: [
      /^财富运势$/,           // 简体中文
      /^財富運勢$/,           // 繁体中文
      /^Wealth Fortune$/i,    // 英文
      /^金運$/                // 日文
    ],
    health: [
      /^健康运势$/,           // 简体中文
      /^健康運勢$/,           // 繁体中文
      /^Health Fortune$/i,    // 英文
      /^健康運$/              // 日文
    ],
    mystery: [
      /^今日神秘指引$/,        // 简体中文
      /^今日神秘指引$/,        // 繁体中文
      /^Today's Mystic Guidance$/i, // 英文
      /^今日の神秘的なガイダンス$/  // 日文
    ]
  };

  // 添加可能的数字前缀或格式处理
  // 针对可能有数字前缀的标题格式（如"1. 开场语"）进行处理
  const sectionPatternsWithPrefix = {};
  for (const [section, patterns] of Object.entries(sectionPatterns)) {
    sectionPatternsWithPrefix[section] = patterns.map(pattern => {
      // 将原始模式转换为字符串，并去掉开头的^和结尾的$
      const patternStr = pattern.toString().replace(/^\/\^/, '').replace(/\$\/[a-z]*$/, '');
      // 添加前缀模式支持
      return new RegExp(`^(?:\\d+[\\.、\\s]*)?\\s*${patternStr}\\s*$`, 'i');
    });
  }

  // 使用包含前缀的模式
  const effectiveSectionPatterns = sectionPatternsWithPrefix;

  // 解析星级评分的正则表达式 - 适用于各种格式的评分
  const ratingPattern = /[\(（【]?\s*(\d+)[\/\s]5\s*(?:分?[星⭐]?|stars?)[\)）】]?|rating:\s*(\d+)[\/\s]5\s*(?:stars?)?/i;
  
  // 专门用于匹配段落末尾的评分格式，与prompt格式要求一致：(4/5星)
  const endRatingPattern = /[\(（【]\s*(\d+)\s*[\/\\]\s*5\s*(?:分?[星⭐]?|stars?)[\)）】]?\s*$/i;
  
  // 添加辅助函数来检查一行是否是标题，而不是内容
  const isContentLine = (line) => {
    // 检查是否包含常见的内容开头词汇
    const contentStartPatterns = [
      /^Today's energy code/i,
      /^Today, your energy/i,
      /^Your energy code/i,
      /^Your mystical guidance/i,
      /^The energy code/i,
      /^For today/i
    ];
    
    return contentStartPatterns.some(pattern => pattern.test(line));
  };

  // 将内容按标题分割成不同部分
  // 首先，找出所有可能的标题行及其位置
  const titlePositions = [];
  const lines = content.split('\n');
  
  // 添加调试信息 - 打印全部输入内容
  console.log("==== 输入内容行数:", lines.length, "====");
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    // 添加调试信息 - 打印当前处理的行
    console.log(`处理第${i}行: "${line}"`);
    
    // 先检查是否是内容行而不是标题行
    if (isContentLine(line)) {
      console.log(`  ⚠️ 跳过可能的内容行: "${line}"`);
      continue;
    }
    
    // 检查这行是否是某个部分的标题
    for (const [section, patterns] of Object.entries(effectiveSectionPatterns)) {
      if (patterns.some(pattern => pattern.test(line))) {
        
        // 添加调试信息 - 打印匹配到的标题
        console.log(`  ✓ 匹配到 ${section} 标题: "${line}"`);
        
        // 检查标题行中是否包含评分
        if (section !== 'intro' && section !== 'mystery') {
          const match = line.match(ratingPattern);
          if (match && (match[1] || match[2])) {
            const score = parseInt(match[1] || match[2], 10);
            if (score >= 1 && score <= 5) {
              ratings[section] = score;   
              console.log(`    提取到评分: ${score}`);
            }
          }
        }
        
        titlePositions.push({
          line: i,
          section: section
        });
        break;
      }
    }
  }
  
  // 打印所有识别到的标题位置
  console.log("\n==== 识别到的标题位置 ====");
  titlePositions.forEach((pos, idx) => {
    console.log(`${idx+1}. ${pos.section} 在第 ${pos.line} 行`);
  });
  
  // 特别检查mystery标题匹配
  console.log("\n==== Mystery标题匹配检查 ====");
  const mysteryTitleLine = lines.findIndex(line => {
    const trimmedLine = line.trim();
    return sectionPatterns.mystery.some(pattern => pattern.test(trimmedLine));
  });
  
  if (mysteryTitleLine >= 0) {
    console.log(`找到 Mystery 标题在第 ${mysteryTitleLine} 行: "${lines[mysteryTitleLine].trim()}"`);
  } else {
    console.log("未找到 Mystery 标题行");
    
    // 打印所有可能的mystery标题匹配模式
    console.log("Mystery标题匹配模式:");
    sectionPatterns.mystery.forEach((pattern, idx) => {
      console.log(`  ${idx+1}. ${pattern}`);
    });
    
    // 检查是否有类似的行
    console.log("\n可能的Mystery标题行:");
    lines.forEach((line, idx) => {
      if (line.trim().toLowerCase().includes("mystic") || 
          line.trim().toLowerCase().includes("guidance") ||
          line.trim().toLowerCase().includes("today")) {
        console.log(`  行 ${idx}: "${line.trim()}"`);
      }
    });
  }
  
  // 根据标题位置分割内容
  if (titlePositions.length > 0) {
    
    // 按顺序处理每个标题及其内容
    for (let i = 0; i < titlePositions.length; i++) {
      const currentTitle = titlePositions[i];
      const nextTitle = titlePositions[i + 1] || { line: lines.length };
      
      // 获取当前部分的内容（从当前标题的下一行到下一个标题的前一行）
      let sectionContent = [];
      for (let j = currentTitle.line + 1; j < nextTitle.line; j++) {
        const line = lines[j].trim();
        if (!line) continue;
        
        // 检查是否是评分行
        if (currentTitle.section !== 'intro' && currentTitle.section !== 'mystery') {
          const match = line.match(ratingPattern);
          if (match && (match[1] || match[2]) && line.length < 30) { // 评分行通常较短
            const score = parseInt(match[1] || match[2], 10);
            if (score >= 1 && score <= 5) {
              ratings[currentTitle.section] = score;
              continue; // 跳过评分行
            }
          }
        }
        
        sectionContent.push(line);
      }
      
      // 将内容保存到对应部分
      if (sectionContent.length > 0) {
        sections[currentTitle.section] = sectionContent.join('\n\n');
      }
    }
  } else {
    // 如果没有找到任何标题，尝试使用段落分割方法
    
    // 尝试两种分割方式：先尝试按空行分段，如果段落太少，再按换行符分行
    let paragraphs = content.split(/\n\s*\n/);
    
    // 如果段落太少（小于3），可能是没有使用空行分隔，尝试按换行符分割
    if (paragraphs.length < 3) {
      paragraphs = content.split('\n');
    }
    
    // 当前处理的类别
    let currentSection = 'overall'; // 默认为overall，确保没有找到标题时内容也能被保存
    
    // 处理每个段落
    paragraphs.forEach((paragraph, index) => {
      const trimmedParagraph = paragraph.trim();
      
      // 跳过空段落
      if (!trimmedParagraph) {
        return;
      }
      
      // 检查是否是新的部分标题
      let isNewSection = false;
      // 特别处理"Today's Mystic Guidance"部分
      if (/^Today's Mystic Guidance/i.test(trimmedParagraph)) {
        currentSection = 'mystery';
        isNewSection = true;
      } else {
        for (const [section, patterns] of Object.entries(effectiveSectionPatterns)) {
          if (patterns.some(pattern => pattern.test(trimmedParagraph))) {
            currentSection = section;
            isNewSection = true;
            
            // 检查标题行中是否包含评分 (例如: "爱情运势 (4/5星)")
            if (section !== 'intro' && section !== 'mystery') {
              const match = trimmedParagraph.match(ratingPattern);
              if (match && (match[1] || match[2])) {
                const score = parseInt(match[1] || match[2], 10);
                if (score >= 1 && score <= 5) {
                  ratings[section] = score;
                }
              }
            }
            
            break;
          }
        }
      }
      
      // 如果是新部分的标题，不添加到结果中
      if (isNewSection) return;
      
      // 检查当前行是否包含评分信息 (例如: "今日评分：4/5星")
      if (currentSection && currentSection !== 'intro' && currentSection !== 'mystery') {
        const match = trimmedParagraph.match(ratingPattern);
        if (match && (match[1] || match[2])) {
          const score = parseInt(match[1] || match[2], 10);
          if (score >= 1 && score <= 5) {
            ratings[currentSection] = score;
            return; // 跳过将评分行添加到内容中
          }
        }
      }
      
      // 将段落添加到当前部分
      if (!sections[currentSection]) {
        sections[currentSection] = trimmedParagraph;
      } else {
        sections[currentSection] += '\n\n' + trimmedParagraph;
      }
    });
  }
  
  // 清理各部分内容，去除前后空白
  Object.keys(sections).forEach(key => {
    // 做基本清理
    sections[key] = sections[key] ? sections[key].trim() : '';
  });
  
  // 如果没有成功解析出任何部分，将整个内容作为整体运势
  if (Object.values(sections).every(section => !section)) {
    sections.overall = content.trim();
  }

  // 后处理：检查并修复可能的解析问题

  // 2. 解析未能识别的评分信息
  const extractRating = (text, sectionKey) => {
    if (!text || !sectionKey || sectionKey === 'intro' || sectionKey === 'mystery') return;
    
    // 多种评分格式的匹配
    const patterns = [
      /(\d+)[\/\s]5\s*(?:分?[星⭐]?|stars?)/i,
      /rating:\s*(\d+)[\/\s]5/i,
      /fortune rating:\s*(\d+)[\/\s]5/i,
      /energy rating:\s*(\d+)[\/\s]5/i
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        const score = parseInt(match[1], 10);
        if (score >= 1 && score <= 5 && ratings[sectionKey] === 0) {
          ratings[sectionKey] = score;
          break;
        }
      }
    }
  };
  
  // 从段落末尾提取评分并清理内容
  const extractRatingFromEnd = (text, sectionKey) => {
    if (!text || !sectionKey || sectionKey === 'intro' || sectionKey === 'mystery') return text;
    
    // 检查段落末尾是否包含评分信息 (按照固定格式 (4/5星) )
    const match = text.match(endRatingPattern);
    
    if (match && match[1]) {
      const score = parseInt(match[1], 10);
      if (score >= 1 && score <= 5) {
        // 设置评分
        ratings[sectionKey] = score;
        // 返回去掉评分的文本
        return text.replace(endRatingPattern, '').trim();
      }
    }
    
    return text;
  };
  
  // 为每个部分尝试提取评分 - 先处理段落末尾格式化的评分
  Object.keys(sections).forEach(key => {
    // 先从段落末尾提取评分并清理内容
    if (sections[key]) {
      sections[key] = extractRatingFromEnd(sections[key], key);
    }
  });
  
  // 如果没有从末尾找到评分，再尝试在全文中提取
  Object.keys(sections).forEach(key => {
    extractRating(sections[key], key);
  });


  // 检查解析结果，如果有任何关键部分为空，则尝试使用双换行符分割法
  const isResultIncomplete = !sections.intro || !sections.overall || !sections.love || 
                           !sections.career || !sections.wealth || !sections.health || !sections.mystery;
  
  if (isResultIncomplete) {
    // 使用双换行符分割整个内容
    const doubleNewlineParagraphs = content.split(/\n\n+/);
    
    // 如果分割后的段落数量太少，可能不是使用双换行符分隔的
    if (doubleNewlineParagraphs.length >= 5) {
      let currentIndex = 0;
      
      // 尝试识别各个部分
      for (let i = 0; i < doubleNewlineParagraphs.length; i++) {
        const paragraph = doubleNewlineParagraphs[i].trim();
        if (!paragraph) continue;
        
        // 检查是否是某个部分的标题
        let matchedSection = null;
        
        // 检查是否匹配任何一个部分的标题
        for (const [section, patterns] of Object.entries(effectiveSectionPatterns)) {
          if (patterns.some(pattern => pattern.test(paragraph))) {
            matchedSection = section;
            
            // 检查标题中是否包含评分
            if (section !== 'intro' && section !== 'mystery') {
              const match = paragraph.match(ratingPattern);
              if (match && (match[1] || match[2])) {
                const score = parseInt(match[1] || match[2], 10);
                if (score >= 1 && score <= 5) {
                  ratings[section] = score;
                }
              }
            }
            
            break;
          }
        }
        
        // 如果是标题，保存下一个段落作为内容
        if (matchedSection && i + 1 < doubleNewlineParagraphs.length) {
          const contentParagraph = doubleNewlineParagraphs[i + 1].trim();
          
          // 只有当内容不是另一个标题时才保存
          let isNextParagraphTitle = false;
          for (const patterns of Object.values(effectiveSectionPatterns)) {
            if (patterns.some(pattern => pattern.test(contentParagraph))) {
              isNextParagraphTitle = true;
              break;
            }
          }
          
          if (!isNextParagraphTitle && contentParagraph) {
            // 检查内容段落末尾是否包含评分信息
            if (matchedSection !== 'intro' && matchedSection !== 'mystery') {
              const cleanedContent = extractRatingFromEnd(contentParagraph, matchedSection);
              sections[matchedSection] = cleanedContent;
            } else {
              sections[matchedSection] = contentParagraph;
            }
          }
        } else if (!matchedSection && i > 0) {
          // 如果不是标题且不是第一段，尝试判断这是否是前一个部分的额外内容
          // 找到最近被识别的部分
          const previousSection = (() => {
            for (let j = i - 1; j >= 0; j--) {
              const prevParagraph = doubleNewlineParagraphs[j].trim();
              for (const [section, patterns] of Object.entries(effectiveSectionPatterns)) {
                if (patterns.some(pattern => pattern.test(prevParagraph))) {
                  return section;
                }
              }
            }
            return null;
          })();
          
          if (previousSection) {
            // 检查这个段落是否包含评分信息
            const match = paragraph.match(ratingPattern);
            if (previousSection !== 'intro' && previousSection !== 'mystery' && 
                match && (match[1] || match[2]) && paragraph.length < 30) {
              const score = parseInt(match[1] || match[2], 10);
              if (score >= 1 && score <= 5) {
                ratings[previousSection] = score;
              }
            } else if (sections[previousSection]) {
              // 否则将它添加到前一部分的内容中
              sections[previousSection] += '\n\n' + paragraph;
            }
          }
        }
      }
      
      // 如果第一段没有被识别为任何部分的标题，则它可能是intro
      if (!sections.intro && doubleNewlineParagraphs.length > 0) {
        const firstParagraph = doubleNewlineParagraphs[0].trim();
        // 确保它不是其他部分的标题
        let isTitle = false;
        for (const [section, patterns] of Object.entries(effectiveSectionPatterns)) {
          if (section !== 'intro' && patterns.some(pattern => pattern.test(firstParagraph))) {
            isTitle = true;
            break;
          }
        }
        
        if (!isTitle) {
          sections.intro = firstParagraph;
        }
      }
    }
  }

  // 添加函数来检查分段检测是否成功
  const checkParagraphDetectionStatus = () => {
    // 每日运势通常需要intro, overall, love, career, wealth, health, mystery七个部分
    // 但mystery部分有时可能没有，所以不检查mystery
    const requiredSections = ['intro', 'overall', 'love', 'career', 'wealth', 'health', 'mystery'];
    
    // 检查所有必要部分是否都有内容
    let missingOrShortSections = [];
    for (const section of requiredSections) {
      if (!sections[section] || sections[section].length < 5) { // 内容至少应该有5个字符
        missingOrShortSections.push(section);
      }
    }
    
    if (missingOrShortSections.length > 0) {
      return 0; // 0表示检测失败
    } else {
      return 1; // 1表示检测成功
    }
  };

  return {
    sections,
    ratings,
    paragraphDetectionStatus: checkParagraphDetectionStatus()
  };
};

// 清理markdown格式的函数
const cleanMarkdownFormat = (text) => {
  return text
    .replace(/^#+\s+/gm, '') // 移除Markdown标题格式
    .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体
    .replace(/\*(.*?)\*/g, '$1') // 移除斜体
    .replace(/\[(.*?)\]\((.*?)\)/g, '$1') // 移除链接
    .replace(/```[a-z]*\n([\s\S]*?)```/g, '$1') // 移除代码块
    .replace(/`(.*?)`/g, '$1'); // 移除行内代码
};

// 创建命令行交互界面
const rl = createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('====== 日运解读结果段落解析测试工具 ======');
console.log('请输入日运解读结果内容，输入完成后请输入 "END" 单独一行表示结束：');

let input = '';
rl.on('line', (line) => {
  if (line.trim() === 'END') {
    // 用户输入完成，开始解析
    console.log('\n开始解析内容...\n');
    
    // 清理markdown格式
    const cleanedContent = cleanMarkdownFormat(input);
    
    // 解析内容
    const result = parseFortuneContentSections(cleanedContent);
    
    // 输出解析结果
    console.log('====== 解析结果 ======');
    
    // 输出检测状态
    console.log(`段落检测状态: ${result.paragraphDetectionStatus === 1 ? '成功' : '失败'}`);
    
    // 输出各部分内容
    console.log('\n====== 各部分内容 ======');
    for (const [section, content] of Object.entries(result.sections)) {
      console.log(`\n------- ${section} -------`);
      console.log(content || '(无内容)');
    }
    
    // 输出评分
    console.log('\n====== 评分信息 ======');
    for (const [section, rating] of Object.entries(result.ratings)) {
      console.log(`${section}: ${rating}/5`);
    }
    
    // 结束程序
    rl.close();
  } else {
    // 继续收集输入
    input += line + '\n';
  }
});

rl.on('close', () => {
  console.log('\n====== 解析测试完成 ======');
  process.exit(0);
}); 