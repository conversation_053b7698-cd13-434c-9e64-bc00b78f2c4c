/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = users   */
/******************************************/
CREATE TABLE `users` (
  `id` varchar(36) NOT NULL,
  `username` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) DEFAULT NULL COMMENT '密码哈希',
  `is_email_verified` tinyint(1) DEFAULT '0',
  `vip_status` enum('none','active','expired') DEFAULT 'none',
  `remaining_reads` int DEFAULT '2',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reset_code` varchar(6) DEFAULT NULL,
  `reset_code_expiry` datetime DEFAULT NULL,
  `vip_type` enum('none','monthly','yearly') DEFAULT 'none',
  `vip_start_date` timestamp NULL DEFAULT NULL,
  `vip_end_date` datetime DEFAULT NULL,
  `auth_provider` varchar(20) DEFAULT 'email' COMMENT '认证方式：email/google',
  `avatar` varchar(255) DEFAULT NULL COMMENT '用户头像URL',
  `whether_paypal` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否是PayPal订阅用户',
  `country` varchar(2) DEFAULT NULL COMMENT '用户所在国家的二字码，如: CN, US, JP等',
  `birthday` date DEFAULT NULL,
  `user_profile` text,
  `location` varchar(255) DEFAULT NULL COMMENT '用户居住地',
  `gender` varchar(255) DEFAULT NULL,
  `ip` varchar(100) DEFAULT NULL COMMENT '用户最后登录的IP地址',
  `has_internal_privilege` tinyint(1) NOT NULL DEFAULT '0',
  `privilege_granted_at` datetime DEFAULT NULL,
  `invitation_code` varchar(20) DEFAULT NULL,
  `has_used_discount` tinyint(1) DEFAULT '0',
  `browser_fingerprints` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3
;
