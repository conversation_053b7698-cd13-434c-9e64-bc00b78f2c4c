import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

const LoginPrompt: React.FC<Props> = ({ isOpen, onClose }) => {
  const { navigate } = useLanguageNavigate();
  const { t } = useTranslation();

  const handleLogin = () => {
    onClose();
    navigate('/login');
  };

  if (!isOpen) return null;

  return (
    <>
      <div
        className="fixed inset-0 bg-black/60 dark:bg-black/60 backdrop-blur-sm z-50"
        onClick={onClose}
      />
      <div
        className="fixed inset-0 flex items-center justify-center px-4 z-50"
      >
        <div className="w-full max-w-md bg-white/90 dark:bg-black/40 backdrop-blur-xl rounded-2xl border border-gray-200 dark:border-purple-500/20 p-8 shadow-2xl relative overflow-hidden">
          {/* 装饰性光效 */}
          <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-300/30 dark:bg-purple-500/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-300/30 dark:bg-pink-500/20 rounded-full blur-3xl"></div>
          
          <div className="relative text-center space-y-6">
            <div>
              <div className="text-5xl mb-3">✨</div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 dark:from-purple-400 dark:via-pink-400 dark:to-purple-400 bg-clip-text text-transparent font-['Inter']">
                {t('login_prompt.title')}
              </h3>
            </div>
            
            <div className="space-y-2">
              <div className="text-lg font-medium bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text text-transparent pb-2 font-['Inter']">
                {t('login_prompt.subtitle')}
              </div>
              <ul className="space-y-3 pt-2">
                <li className="flex items-center gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-lg group-hover:scale-110 transition-transform">✦</span>
                  <span className="text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors font-['Inter']">
                    {t('login_prompt.benefits.free_readings')}
                  </span>
                </li>
                <li className="flex items-center gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-lg group-hover:scale-110 transition-transform">✦</span>
                  <span className="text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors font-['Inter']">
                    {t('login_prompt.benefits.personalized_reader')}
                  </span>
                </li>
                <li className="flex items-center gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-lg group-hover:scale-110 transition-transform">✦</span>
                  <span className="text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors font-['Inter']">
                    {t('login_prompt.benefits.complete_history')}
                  </span>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="mt-8 flex justify-center gap-4">
            <button
              onClick={onClose}
              className="min-w-[160px] px-6 py-3 rounded-xl bg-gray-100 dark:bg-white/5 border border-gray-300 dark:border-purple-500/20 backdrop-blur-sm
                       text-gray-700 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-purple-500/10 hover:border-purple-300 dark:hover:border-purple-500/40
                       transition-all duration-200 text-lg whitespace-nowrap font-['Inter']"
            >
              {t('login_prompt.buttons.later')}
            </button>
            <button
              onClick={handleLogin}
              className="min-w-[160px] relative group"
            >
              <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-pink-600 
                            rounded-xl blur opacity-60 group-hover:opacity-100 transition duration-200">
              </div>
              <div className="relative px-6 py-3 bg-white dark:bg-black rounded-xl leading-none whitespace-nowrap">
                <span className="text-purple-700 dark:text-white text-lg font-['Inter']">{t('login_prompt.buttons.login')}</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default LoginPrompt;
