import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { verifyEmail, resendVerification } from '../services/userService';
import { useUser } from '../contexts/UserContext';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import SEO from '../components/SEO';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

// 添加 CheckmarkAnimation 组件
const CheckmarkAnimation: React.FC<{
  size?: number;
  color?: string;
  strokeWidth?: number;
}> = ({
  size = 32,
  color = '#FFFFFF',
  strokeWidth = 4,
}) => {
  const circleVariants = {
    hidden: { opacity: 0, scale: 0.5 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  const checkVariants = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { duration: 0.5, ease: "easeOut" },
        opacity: { duration: 0.01 },
      },
    },
  };

  return (
    <div style={{ width: size, height: size }}>
      <motion.svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        initial="hidden"
        animate="visible"
      >
        <defs>
          <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgb(168, 85, 247)" />
            <stop offset="100%" stopColor="rgb(236, 72, 153)" />
          </linearGradient>
        </defs>
        <motion.circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          variants={circleVariants}
        />
        <motion.path
          d="M30,50 L45,65 L70,40"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
          variants={checkVariants}
        />
      </motion.svg>
    </div>
  );
};

const VerifyEmail: React.FC = () => {
  const location = useLocation();
  const originalNavigate = useNavigate();
  const { navigate } = useLanguageNavigate();
  const { setUser } = useUser();
  const { t, i18n } = useTranslation();
  const { email, userId } = location.state || {};
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (!email || !userId) {
      navigate(`/register`);
    }
    
    // 从本地存储中获取倒计时结束时间
    const countdownEndTime = localStorage.getItem('verifyEmailCountdownEnd');
    if (countdownEndTime) {
      const endTime = parseInt(countdownEndTime, 10);
      const now = Date.now();
      const remainingTime = Math.floor((endTime - now) / 1000);
      
      // 如果倒计时还没结束，则恢复倒计时状态
      if (remainingTime > 0) {
        setCountdown(remainingTime);
        setMessage(t('auth.success.codeSent'));
      } else {
        // 如果倒计时已结束，清除本地存储
        localStorage.removeItem('verifyEmailCountdownEnd');
      }
    }
  }, [email, userId, navigate, t, i18n.language]);

  // 添加倒计时效果
  useEffect(() => {
    if (countdown > 0) {
      const timer = setInterval(() => {
        setCountdown(prev => {
          const newValue = prev - 1;
          if (newValue <= 0) {
            // 倒计时结束时，清除本地存储
            localStorage.removeItem('verifyEmailCountdownEnd');
          }
          return newValue;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [countdown]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setIsSuccess(false);
    setMessage(''); // 清除之前的成功消息

    if (!verificationCode) {
      setError(t('errors.please_enter_code'));
      setIsLoading(false);
      return;
    }

    try {
      const response = await verifyEmail(userId, verificationCode);
      if (response.success) {
        setTimeout(() => {
          setIsLoading(false);
          setIsSuccess(true);
          setTimeout(() => {
            if (response.token && response.user) {
              localStorage.setItem('token', response.token);
              setUser(response.user);
              // 清除验证相关的本地存储
              localStorage.removeItem('verifyEmailCountdownEnd');
              navigate(`/home`);
            } else {
              navigate(`/login`);
            }
          }, 800);
        }, 500);
      } else {
        // 根据错误类型显示不同的错误信息
        if (response.code === 'INVALID_CODE') {
          setError(t('errors.code_invalid'));
        } else if (response.code === 'CODE_EXPIRED') {
          setError(t('errors.code_expired'));
          // 如果验证码已过期，清除倒计时
          setCountdown(0);
          localStorage.removeItem('verifyEmailCountdownEnd');
        } else {
          setError(t('errors.verification_failed'));
        }
        setIsLoading(false);
      }
    } catch (error: any) {
      // 处理网络错误或其他异常
      const errorMessage = error.response?.data?.message;
      const errorCode = error.response?.data?.code;
      
      if (errorMessage && typeof errorMessage === 'string' && errorMessage.startsWith('auth.')) {
        setError(t(errorMessage));
      } else if (errorCode === 'INVALID_CODE') {
        setError(t('errors.code_invalid'));
      } else if (errorCode === 'CODE_EXPIRED') {
        setError(t('errors.code_expired'));
        // 如果验证码已过期，清除倒计时
        setCountdown(0);
        localStorage.removeItem('verifyEmailCountdownEnd');
      } else {
        setError(t('errors.verification_failed'));
      }
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^\d]/g, '').slice(0, 6);  // 只允许数字，最多6位
    setVerificationCode(value);
    setError('');
  };

  const handleResendCode = async () => {
    if (countdown > 0 || isResending) return;
    
    setIsResending(true);
    setError('');
    try {
      // 清除之前输入的验证码，避免用户使用旧验证码
      setVerificationCode('');
      
      const response = await resendVerification(
        email, 
        userId, 
        localStorage.getItem('i18nextLng') || 'zh-CN'
      );
      
      // 如果服务器返回了新的userId，更新本地状态
      if (response.userId) {
        // 使用原生navigate替换当前页面的state，更新userId
        originalNavigate(location.pathname, {
          replace: true,
          state: { ...location.state, userId: response.userId }
        });
      }
      
      // 使用前端的国际化翻译系统显示成功消息
      setMessage(t('auth.success.codeSent'));
      // 开始60秒倒计时
      setCountdown(60);
      // 保存倒计时结束时间到本地存储
      const endTime = Date.now() + 60 * 1000;
      localStorage.setItem('verifyEmailCountdownEnd', endTime.toString());
    } catch (error: any) {
      const errorMessage = error.response?.data?.message;
      if (errorMessage && typeof errorMessage === 'string' && errorMessage.startsWith('auth.')) {
        setError(t(errorMessage));
      } else {
        setError(errorMessage || t('errors.resend_failed'));
      }
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col relative font-['Inter']">
      <SEO 
      />
      <LandingBackground />
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8">
          <div className="text-center mt-8 sm:mt-10 mb-4 sm:mb-6">
            <h1 className="main-title mb-1">{t('verify.title')}</h1>
            <p className="sub-title">{t('verify.subtitle')}</p>
          </div>
          <div className="flex items-center justify-center">
            <div className="w-full max-w-sm mx-auto">
              <div className="login-card p-5 sm:p-8 rounded-2xl shadow-2xl relative overflow-hidden">
                {/* 装饰性光效 */}
                <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/20 rounded-full blur-3xl"></div>
                
                <div className="relative">
                  <p className="text-gray-300 text-center mb-4 sm:mb-6 text-sm sm:text-base font-['Inter']">
                    {t('auth.verify_email.description')}
                  </p>

                  {error && (
                    <div className="mb-3 sm:mb-4 p-2.5 sm:p-3 rounded-lg bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
                      <p className="text-red-400 text-xs sm:text-sm font-['Inter']">{error}</p>
                    </div>
                  )}

                  {message && (
                    <div className="mb-3 sm:mb-4 p-2.5 sm:p-3 rounded-lg bg-green-500/10 border border-green-500/20 backdrop-blur-sm">
                      <p className="text-green-400 text-xs sm:text-sm font-['Inter']">{message}</p>
                    </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-200 mb-1 font-['Inter']">
                        {t('auth.verify_email.code')}
                      </label>
                      <input
                        type="text"
                        value={verificationCode}
                        onChange={handleChange}
                        className="input-field block w-full px-3 py-2 sm:py-2.5 rounded-xl
                                 text-white text-[15px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                 placeholder-gray-400 backdrop-blur-sm font-['Inter']"
                        placeholder={t('auth.verify_email.code')}
                        required
                        autoComplete="off"
                      />
                    </div>

                    <div className="pt-1 sm:pt-2">
                      <button
                        type="submit"
                        disabled={isLoading || isSuccess}
                        className="w-full relative group disabled:opacity-50"
                      >
                        <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-pink-600 
                                      rounded-xl blur opacity-60 group-hover:opacity-100 transition duration-200
                                      group-disabled:opacity-30">
                        </div>
                        <div className="relative px-3 sm:px-4 h-[34px] sm:h-[40px] bg-black rounded-xl flex items-center justify-center">
                          <div className="flex items-center justify-center w-[24px] sm:w-[28px] h-[24px] sm:h-[28px]">
                            {isLoading ? (
                              <div className="w-4 sm:w-5 h-4 sm:h-5 border-2 border-white/20 border-t-white rounded-full animate-spin"></div>
                            ) : isSuccess ? (
                              <CheckmarkAnimation size={24} strokeWidth={4} />
                            ) : (
                              <span className="text-white text-sm sm:text-base font-medium whitespace-nowrap font-['Inter']">{t('auth.verify_email.submit')}</span>
                            )}
                          </div>
                        </div>
                      </button>
                    </div>
                  </form>

                  <div className="mt-2.5 sm:mt-4 text-center">
                    <button
                      onClick={handleResendCode}
                      disabled={countdown > 0 || isResending}
                      className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 
                               hover:from-purple-500 hover:to-pink-500 text-sm font-medium font-['Inter']
                               transition-all duration-200 disabled:opacity-50"
                    >
                      {countdown > 0 ? `${t('auth.verify_email.resend')} (${countdown}s)` : t('auth.verify_email.resend')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="relative z-10">
        <Footer />
      </div>
      <style>
        {`
          .input-field {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.2s ease-in-out;
          }
          
          /* 彻底覆盖输入框样式 */
          body:not(.dark) .login-card input, 
          body:not(.dark) .login-card textarea,
          html:not(.dark) .login-card input, 
          html:not(.dark) .login-card textarea,
          div:not(.dark) .login-card input,
          div:not(.dark) .login-card textarea,
          :not(.dark) .input-field[type="email"],
          :not(.dark) .input-field[type="password"],
          :not(.dark) .input-field[type="text"] {
            background-color: #F4F4F5 !important;
            color: #1F2937 !important;
            border: 1px solid rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 输入框聚焦状态 */
          body:not(.dark) .login-card input:focus, 
          html:not(.dark) .login-card input:focus,
          :not(.dark) .input-field[type="email"]:focus,
          :not(.dark) .input-field[type="password"]:focus,
          :not(.dark) .input-field[type="text"]:focus {
            background-color: #FFFFFF !important;
            color: #1F2937 !important;
            border: 1px solid rgba(168, 85, 247, 0.5) !important;
            box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 占位符文本样式 */
          body:not(.dark) .login-card input::placeholder,
          html:not(.dark) .login-card input::placeholder,
          :not(.dark) .input-field::placeholder {
            color: #6B7280 !important;
          }
          
          /* 深色主题输入框样式 */
          body.dark .login-card input, 
          html.dark .login-card input,
          .dark .input-field[type="email"],
          .dark .input-field[type="password"],
          .dark .input-field[type="text"] {
            background-color: rgba(255, 255, 255, 0.05) !important;
            color: #FFFFFF !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
          }
          
          /* 深色主题输入框聚焦状态 */
          body.dark .login-card input:focus, 
          html.dark .login-card input:focus,
          .dark .input-field[type="email"]:focus,
          .dark .input-field[type="password"]:focus,
          .dark .input-field[type="text"]:focus {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: #FFFFFF !important;
            border: 1px solid rgba(168, 85, 247, 0.5) !important;
            box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
          }
          .login-card {
            position: relative;
            background: rgba(13, 12, 15, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(236, 72, 153, 0.3);
            box-shadow: 
              0 0 0 1px rgba(168, 85, 247, 0.2),
              0 0 15px rgba(168, 85, 247, 0.15),
              0 0 30px rgba(236, 72, 153, 0.15),
              inset 0 0 15px rgba(168, 85, 247, 0.1);
          }
          /* 深色主题下的样式保持不变 */
          :root.dark .login-card, 
          html.dark .login-card, 
          .dark .login-card {
            background: rgba(13, 12, 15, 0.95);
          }
          /* 浅色主题下的卡片背景颜色 */
          :root:not(.dark) .login-card, 
          html:not(.dark) .login-card, 
          :not(.dark) .login-card {
            background: #F4F4F5;
          }
          .login-card::before {
            content: '';
            position: absolute;
            inset: -1px;
            padding: 1px;
            background: linear-gradient(
              135deg,
              rgba(168, 85, 247, 0.5),
              rgba(236, 72, 153, 0.5)
            );
            -webkit-mask: 
              linear-gradient(#fff 0 0) content-box, 
              linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
          }
        `}
      </style>
    </div>
  );
};

export default VerifyEmail;
