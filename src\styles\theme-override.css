/* 主题样式覆盖 */

/* 浅色主题下的标题和文本颜色 */
:root:not(.dark) {
  --text-color: #000000;
  --heading-color: #000000;
}

/* 浅色主题下的正文文本 */
:not(.dark) body,
:not(.dark) p,
:not(.dark) span,
:not(.dark) div:not(.text-gradient):not([class*="bg-gradient"]),
:not(.dark) li,
:not(.dark) a:not(.text-gradient) {
  color: var(--text-color);
}

/* 浅色主题下的标题 */
:not(.dark) h1:not(.text-gradient):not(.main-title),
:not(.dark) h2:not(.text-gradient):not(.main-title),
:not(.dark) h3:not(.text-gradient):not(.main-title),
:not(.dark) h4:not(.text-gradient):not(.main-title),
:not(.dark) h5:not(.text-gradient):not(.main-title),
:not(.dark) h6:not(.text-gradient):not(.main-title) {
  color: var(--heading-color);
}

/* 浅色主题下的主标题 - 使用多种选择器确保覆盖所有情况 */
:not(.dark) .main-title,
body:not(.dark) .main-title,
html:not(.dark) .main-title {
  background: none !important;
  -webkit-background-clip: initial !important;
  -webkit-text-fill-color: var(--heading-color) !important;
  background-clip: initial !important;
  color: var(--heading-color) !important;
  text-shadow: none !important;
}

/* 保留特殊类的样式 */
.text-gradient,
[class*="bg-clip-text"],
.bg-gradient-to-r {
  /* 保持原有渐变样式 */
}

/* 浅色主题下的非渐变主标题 */
:not(.dark) .main-title:not(.text-gradient) {
  color: var(--heading-color);
}

/* 浅色主题下的基础按钮 */
:not(.dark) button:not([class*="bg-"]):not([class*="text-"]) {
  color: var(--text-color);
}

/* 浅色主题下的表单元素 */
:not(.dark) input:not([type="submit"]):not([type="button"]):not([class*="bg-"]):not([class*="text-"]),
:not(.dark) select,
:not(.dark) textarea {
  color: var(--text-color);
}

/* 浅色主题下的标签 */
:not(.dark) label {
  color: var(--text-color);
}

/* 浅色主题下的列表元素 */
:not(.dark) ul,
:not(.dark) ol {
  color: var(--text-color);
}

/* 保留其他特殊组件的原样式 */
:not(.dark) .special-component,
:not(.dark) [class*="special-"] {
  /* 原样式保持不变 */
}

/* 计数器文本颜色 - 与placeholder保持一致 */
:not(.dark) .counter-text,
:not(.dark) .counter-text span {
  color: rgb(107 114 128) !important; /* gray-500 */
}

.dark .counter-text,
.dark .counter-text span {
  color: rgb(156 163 175) !important; /* gray-400 */
}

/* 问题图标颜色 */
.question-icon {
  color: #8b5cf6 !important; /* purple-500 */
}

.dark .question-icon {
  color: #8b5cf6 !important; /* purple-500 */
}

/* 深色主题下保持原有的渐变主标题样式 */
.dark .main-title {
  background: linear-gradient(to right, #A78BFA, #EC4899) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
} 