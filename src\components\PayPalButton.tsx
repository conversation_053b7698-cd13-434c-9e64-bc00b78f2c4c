import React, { useEffect, useRef } from 'react';
import { loadScript } from '../utils/loadScript';

declare global {
  interface Window {
    paypal?: any;
  }
}

interface PayPalButtonProps {
  amount: number;
  currency: string;
  onSuccess: (details: any) => void;
  onError: (error: any) => void;
  onCancel: () => void;
}

const PayPalButton: React.FC<PayPalButtonProps> = ({
  amount,
  currency,
  onSuccess,
  onError,
  onCancel,
}) => {
  const paypalButtonsRef = useRef<HTMLDivElement>(null);
  const isComponentMounted = useRef(true);

  useEffect(() => {
    isComponentMounted.current = true;

    const loadPayPalScript = async () => {
      try {
        const clientId = import.meta.env.VITE_PAYPAL_CLIENT_ID;
        if (!clientId) {
          throw new Error('PayPal client ID is not configured');
        }

        // 确保组件仍然挂载
        if (!isComponentMounted.current || !paypalButtonsRef.current) {
          return;
        }

        // 清除已有的按钮
        paypalButtonsRef.current.innerHTML = '';

        // 加载PayPal SDK
        const sdkUrl = `https://www.paypal.com/sdk/js?client-id=${clientId}&currency=${currency}`;
        await loadScript(sdkUrl);

        // 再次确保组件仍然挂载
        if (!isComponentMounted.current || !paypalButtonsRef.current) {
          return;
        }

        // 确保PayPal对象可用
        if (!window.paypal) {
          throw new Error('PayPal SDK not initialized properly');
        }

        // 确保金额最多只有两位小数
        const formattedAmount = parseFloat(amount.toFixed(2));

        const buttons = window.paypal.Buttons({
          style: {
            layout: 'vertical',
            color: 'gold',
            shape: 'rect',
            label: 'paypal'
          },
          createOrder: (_data: any, actions: any) => {
            if (!isComponentMounted.current) {
              return Promise.reject(new Error('Component unmounted'));
            }
            return actions.order.create({
              purchase_units: [{
                amount: {
                  currency_code: currency,
                  value: formattedAmount.toString()
                }
              }]
            });
          },
          onApprove: async (_data: any, actions: any) => {
            if (!isComponentMounted.current) return;
            try {
              const details = await actions.order.capture();
              onSuccess(details);
            } catch (error) {
              if (isComponentMounted.current) {
                onError(error);
              }
            }
          },
          onCancel: () => {
            if (isComponentMounted.current) {
              onCancel();
            }
          },
          onError: (err: any) => {
            if (isComponentMounted.current) {
              onError(err);
            }
          }
        });

        if (isComponentMounted.current && paypalButtonsRef.current) {
          await buttons.render(paypalButtonsRef.current);
        }
      } catch (error) {
        if (isComponentMounted.current) {
          onError(error);
        }
      }
    };

    // 使用setTimeout来确保DOM已经完全渲染
    const timeoutId = setTimeout(loadPayPalScript, 0);

    return () => {
      isComponentMounted.current = false;
      clearTimeout(timeoutId);
      if (paypalButtonsRef.current) {
        paypalButtonsRef.current.innerHTML = '';
      }
    };
  }, [amount, currency, onSuccess, onError, onCancel]);

  return <div ref={paypalButtonsRef} className="paypal-button-container" />;
};

export default PayPalButton;