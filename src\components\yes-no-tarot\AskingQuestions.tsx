import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { CdnLazyImage } from '../../components/CdnImageExport';

const AskingQuestions: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <div className="mt-24 sm:mt-32">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('yes_no_tarot.asking_questions.title')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
        <p className={`${
          theme === 'light' ? 'text-gray-700' : 'text-gray-300'
        } text-lg max-w-3xl mx-auto mb-6`}>
          {t('yes_no_tarot.asking_questions.description')}
        </p>
      </div>
      
      {/* 对话气泡形式展示问题对比 */}
      <div className="space-y-8 mb-4 max-w-4xl mx-auto">
        {/* 第一组问题 */}
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 mr-3 shadow-sm border border-gray-200 dark:border-gray-700">
              <CdnLazyImage 
                src="/images-optimized/yes-no-tarot/user-avatar.webp" 
                alt="User Avatar" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-red-500/30 hover:border-red-500/40 p-4 rounded-2xl rounded-tl-none max-w-md shadow-sm">
              <p className="text-red-600 dark:text-red-400 font-medium mb-1">{t('yes_no_tarot.asking_questions.bad_question')}</p>
              <p className="text-gray-700 dark:text-gray-300 italic">{t('yes_no_tarot.asking_questions.example_bad_1')}</p>
            </div>
          </div>
          
          <div className="flex items-start justify-end">
            <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-4 rounded-2xl rounded-tr-none max-w-md shadow-sm">
              <p className="text-purple-700 dark:text-purple-300 font-medium mb-1">{t('yes_no_tarot.asking_questions.good_question')}</p>
              <p className="text-gray-700 dark:text-gray-300 italic">{t('yes_no_tarot.asking_questions.example_good_1')}</p>
            </div>
            <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 ml-3 shadow-sm border border-gray-200 dark:border-gray-700">
              <CdnLazyImage 
                src="/images-optimized/yes-no-tarot/user-avatar2.webp" 
                alt="Tarot QA Logo" 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
        
        {/* 第二组问题 */}
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 mr-3 shadow-sm border border-gray-200 dark:border-gray-700">
              <CdnLazyImage 
                src="/images-optimized/yes-no-tarot/user-avatar.webp" 
                alt="User Avatar" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-red-500/30 hover:border-red-500/40 p-4 rounded-2xl rounded-tl-none max-w-md shadow-sm">
              <p className="text-red-600 dark:text-red-400 font-medium mb-1">{t('yes_no_tarot.asking_questions.bad_question')}</p>
              <p className="text-gray-700 dark:text-gray-300 italic">{t('yes_no_tarot.asking_questions.example_bad_2')}</p>
            </div>
          </div>
          
          <div className="flex items-start justify-end">
            <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-4 rounded-2xl rounded-tr-none max-w-md shadow-sm">
              <p className="text-purple-700 dark:text-purple-300 font-medium mb-1">{t('yes_no_tarot.asking_questions.good_question')}</p>
              <p className="text-gray-700 dark:text-gray-300 italic">{t('yes_no_tarot.asking_questions.example_good_2')}</p>
            </div>
            <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 ml-3 shadow-sm border border-gray-200 dark:border-gray-700">
              <CdnLazyImage 
                src="/images-optimized/yes-no-tarot/user-avatar2.webp" 
                alt="Tarot QA Logo" 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
        
        {/* 第三组问题 */}
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 mr-3 shadow-sm border border-gray-200 dark:border-gray-700">
              <CdnLazyImage 
                src="/images-optimized/yes-no-tarot/user-avatar.webp" 
                alt="User Avatar" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-red-500/30 hover:border-red-500/40 p-4 rounded-2xl rounded-tl-none max-w-md shadow-sm">
              <p className="text-red-600 dark:text-red-400 font-medium mb-1">{t('yes_no_tarot.asking_questions.bad_question')}</p>
              <p className="text-gray-700 dark:text-gray-300 italic">{t('yes_no_tarot.asking_questions.example_bad_3')}</p>
            </div>
          </div>
          
          <div className="flex items-start justify-end">
            <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-4 rounded-2xl rounded-tr-none max-w-md shadow-sm">
              <p className="text-purple-700 dark:text-purple-300 font-medium mb-1">{t('yes_no_tarot.asking_questions.good_question')}</p>
              <p className="text-gray-700 dark:text-gray-300 italic">{t('yes_no_tarot.asking_questions.example_good_3')}</p>
            </div>
            <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 ml-3 shadow-sm border border-gray-200 dark:border-gray-700">
              <CdnLazyImage 
                src="/images-optimized/yes-no-tarot/user-avatar2.webp" 
                alt="Tarot QA Logo" 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AskingQuestions; 