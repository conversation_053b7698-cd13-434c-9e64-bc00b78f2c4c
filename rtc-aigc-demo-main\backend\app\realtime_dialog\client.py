import websockets
import gzip
import json
import uuid
import asyncio
import sys
from typing import Dict, Any, Optional, Callable

from . import protocol


class RealtimeDialogClient:
    """实时对话客户端类，处理与火山引擎语音服务的WebSocket通信"""
    
    def __init__(self, config: Dict[str, Any], session_id: str, callback: Optional[Callable] = None):
        self.config = config
        self.logid = ""
        self.session_id = session_id
        self.ws = None
        self.is_connected = False
        self.callback = callback  # 用于回调处理接收到的音频数据
        self.task = None
    
    async def connect(self) -> bool:
        """建立WebSocket连接"""
        print(f"连接到语音服务: {self.config['base_url']}")
        try:
            # 添加连接ID到headers
            headers = self.config['headers'].copy()
            headers["X-Api-Connect-Id"] = str(uuid.uuid4())
            
            # 根据Python版本选择合适的连接参数
            if sys.version_info >= (3, 10):
                # Python 3.10及以上版本，不使用ping_interval
                self.ws = await websockets.connect(
                    self.config['base_url'],
                    extra_headers=headers
                )
            else:
                # 旧版本Python
                self.ws = await websockets.connect(
                    self.config['base_url'],
                    extra_headers=headers,
                    ping_interval=None
                )
                
            self.is_connected = True
            self.logid = self.ws.response_headers.get("X-Tt-Logid")
            print(f"连接成功，logid: {self.logid}")

            # StartConnection request
            start_connection_request = bytearray(protocol.generate_header())
            start_connection_request.extend(int(1).to_bytes(4, 'big'))
            payload_bytes = str.encode("{}")
            payload_bytes = gzip.compress(payload_bytes)
            start_connection_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            start_connection_request.extend(payload_bytes)
            await self.ws.send(start_connection_request)
            response = await self.ws.recv()
            print(f"连接初始化响应: {protocol.parse_response(response)}")
            
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            self.is_connected = False
            return False

    async def start_session(self, session_params: Dict[str, Any]) -> bool:
        """启动会话"""
        if not self.is_connected or not self.ws:
            print("WebSocket未连接，无法启动会话")
            return False
            
        try:
            # StartSession request
            payload_bytes = str.encode(json.dumps(session_params))
            payload_bytes = gzip.compress(payload_bytes)
            start_session_request = bytearray(protocol.generate_header())
            start_session_request.extend(int(100).to_bytes(4, 'big'))
            start_session_request.extend((len(self.session_id)).to_bytes(4, 'big'))
            start_session_request.extend(str.encode(self.session_id))
            start_session_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            start_session_request.extend(payload_bytes)
            await self.ws.send(start_session_request)
            response = await self.ws.recv()
            print(f"会话启动响应: {protocol.parse_response(response)}")
            
            # 启动接收任务
            self.task = asyncio.create_task(self._receive_loop())
            
            return True
        except Exception as e:
            print(f"启动会话失败: {e}")
            return False

    async def say_hello(self) -> None:
        """发送Hello消息"""
        if not self.is_connected or not self.ws:
            print("WebSocket未连接，无法发送Hello消息")
            return
            
        try:
            payload = {
                "content": "你好，有什么我可以帮助你的？",
            }
            hello_request = bytearray(protocol.generate_header())
            hello_request.extend(int(300).to_bytes(4, 'big'))
            payload_bytes = str.encode(json.dumps(payload))
            payload_bytes = gzip.compress(payload_bytes)
            hello_request.extend((len(self.session_id)).to_bytes(4, 'big'))
            hello_request.extend(str.encode(self.session_id))
            hello_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            hello_request.extend(payload_bytes)
            await self.ws.send(hello_request)
            print("Hello消息已发送")
        except Exception as e:
            print(f"发送Hello消息失败: {e}")

    async def send_text(self, content: str, start: bool = True, end: bool = False) -> None:
        """发送文本消息"""
        if not self.is_connected or not self.ws:
            print("WebSocket未连接，无法发送文本")
            return
            
        try:
            payload = {
                "start": start,
                "end": end,
                "content": content,
            }
            print(f"发送文本: {payload}")
            payload_bytes = str.encode(json.dumps(payload))
            payload_bytes = gzip.compress(payload_bytes)

            text_request = bytearray(protocol.generate_header())
            text_request.extend(int(500).to_bytes(4, 'big'))
            text_request.extend((len(self.session_id)).to_bytes(4, 'big'))
            text_request.extend(str.encode(self.session_id))
            text_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            text_request.extend(payload_bytes)
            await self.ws.send(text_request)
        except Exception as e:
            print(f"发送文本失败: {e}")

    async def send_audio(self, audio: bytes) -> None:
        """发送音频数据"""
        if not self.is_connected or not self.ws:
            print("WebSocket未连接，无法发送音频")
            return
            
        try:
            audio_request = bytearray(
                protocol.generate_header(message_type=protocol.CLIENT_AUDIO_ONLY_REQUEST,
                                         serial_method=protocol.NO_SERIALIZATION))
            audio_request.extend(int(200).to_bytes(4, 'big'))
            audio_request.extend((len(self.session_id)).to_bytes(4, 'big'))
            audio_request.extend(str.encode(self.session_id))
            payload_bytes = gzip.compress(audio)
            audio_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            audio_request.extend(payload_bytes)
            await self.ws.send(audio_request)
        except Exception as e:
            print(f"发送音频失败: {e}")

    async def _receive_loop(self) -> None:
        """接收消息循环"""
        try:
            while self.is_connected and self.ws:
                response = await self.ws.recv()
                data = protocol.parse_response(response)
                
                # 调用回调函数处理接收到的数据
                if self.callback:
                    # 检查回调函数是否需要异步调用
                    if asyncio.iscoroutinefunction(self.callback):
                        await self.callback(data)
                    else:
                        self.callback(data)
                
                # 检查是否是会话结束事件
                if 'event' in data and (data['event'] == 152 or data['event'] == 153):
                    print(f"接收到会话结束事件: {data['event']}")
                    break
        except asyncio.CancelledError:
            print("接收任务已取消")
        except Exception as e:
            print(f"接收消息错误: {e}")
            # 记录更详细的错误信息
            import traceback
            traceback.print_exc()

    async def finish_session(self):
        """结束会话"""
        if not self.is_connected or not self.ws:
            print("WebSocket未连接，无法结束会话")
            return
            
        try:
            finish_session_request = bytearray(protocol.generate_header())
            finish_session_request.extend(int(102).to_bytes(4, 'big'))
            payload_bytes = str.encode("{}")
            payload_bytes = gzip.compress(payload_bytes)
            finish_session_request.extend((len(self.session_id)).to_bytes(4, 'big'))
            finish_session_request.extend(str.encode(self.session_id))
            finish_session_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            finish_session_request.extend(payload_bytes)
            await self.ws.send(finish_session_request)
            print(f"会话 {self.session_id} 已结束")
            
            # 取消接收任务
            if self.task and not self.task.done():
                self.task.cancel()
                try:
                    await self.task
                except asyncio.CancelledError:
                    pass
        except Exception as e:
            print(f"结束会话失败: {e}")

    async def finish_connection(self):
        """结束连接"""
        if not self.is_connected or not self.ws:
            print("WebSocket未连接，无法结束连接")
            return
            
        try:
            finish_connection_request = bytearray(protocol.generate_header())
            finish_connection_request.extend(int(2).to_bytes(4, 'big'))
            payload_bytes = str.encode("{}")
            payload_bytes = gzip.compress(payload_bytes)
            finish_connection_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            finish_connection_request.extend(payload_bytes)
            await self.ws.send(finish_connection_request)
            response = await self.ws.recv()
            print(f"连接结束响应: {protocol.parse_response(response)}")
        except Exception as e:
            print(f"结束连接失败: {e}")

    async def close(self) -> None:
        """关闭WebSocket连接"""
        if self.ws:
            print(f"关闭WebSocket连接...")
            try:
                await self.finish_session()
                await self.finish_connection()
                await self.ws.close()
                self.is_connected = False
                print(f"WebSocket连接已关闭")
            except Exception as e:
                print(f"关闭连接失败: {e}")
                self.is_connected = False 