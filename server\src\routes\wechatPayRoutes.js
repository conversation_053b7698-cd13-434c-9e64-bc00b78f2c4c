const express = require('express');
const router = express.Router();
const wechatPayService = require('../services/wechatPayService');
const { authenticateToken } = require('../../middleware/auth');

// 生成订单号：时间戳（10位）+ 随机数（6位）
function generateOrderId() {
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
  return `${timestamp}${random}`;
}

// 创建支付订单
router.post('/create-order', authenticateToken, async (req, res) => {
  try {
    console.log('Received create order request:', req.body);
    const { amount, productId, productName } = req.body;
    const userId = req.user.userId;  // 从认证中间件获取用户ID

    // 参数验证
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '无效的支付金额'
      });
    }

    if (!productId || !productName) {
      return res.status(400).json({
        success: false,
        message: '商品信息不完整'
      });
    }

    const orderId = generateOrderId();
    console.log('Creating WeChat payment order...', { 
      amount, 
      productId,
      productName, 
      orderId,
      userId 
    });

    const payResult = await wechatPayService.createNativeOrder({
      amount,
      description: productName,
      orderId,
      userId,
      productId,
      productName
    });

    console.log('WeChat payment order created:', payResult);
    res.json({
      success: true,
      data: {
        orderId: orderId,
        qrCodeUrl: payResult.codeUrl,
        paymentMethod: 'wechat'
      }
    });
  } catch (error) {
    console.error('Create payment error:', error.response?.data || error);
    res.status(500).json({
      success: false,
      message: error.response?.data?.message || error.message || '创建支付订单失败'
    });
  }
});

// 查询订单状态
router.get('/order-status/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    // 参数验证
    if (!orderId || orderId.length !== 16) {
      return res.status(400).json({
        success: false,
        message: '无效的订单号'
      });
    }

    console.log('Querying order status:', orderId);
    const result = await wechatPayService.queryOrder(orderId);
    
    res.json({
      success: true,
      data: {
        orderId,
        tradeState: result.tradeState,
        tradeStateDesc: result.tradeStateDesc
      }
    });
  } catch (error) {
    console.error('Query order status error:', error.response?.data || error);
    res.status(500).json({
      success: false,
      message: error.response?.data?.message || error.message || '查询订单状态失败'
    });
  }
});

// 关闭订单
router.post('/close-order', async (req, res) => {
  const startTime = new Date();
  console.log('=== 收到关闭订单请求 ===');
  console.log('请求时间:', startTime.toISOString());
  console.log('请求参数:', req.body);
  
  try {
    const { orderId } = req.body;
    
    if (!orderId) {
      console.log('请求参数错误: 缺少订单ID');
      return res.status(400).json({ error: 'Order ID is required' });
    }

    const result = await wechatPayService.closeOrder(orderId);
    
    const endTime = new Date();
    const duration = endTime - startTime;
    console.log('订单关闭完成:', {
      orderId,
      success: result.success,
      duration: `${duration}ms`
    });
    
    res.json(result);
  } catch (error) {
    console.error('关闭订单失败:', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({ error: 'Failed to close order' });
  }
});

// 支付通知回调
router.post('/notify', express.raw({ type: '*/*' }), async (req, res) => {
  console.log('=== 接收到微信支付回调 ===');
  console.log('请求头:', req.headers);
  console.log('原始请求体:', req.body.toString());
  
  try {
    const result = await wechatPayService.handlePaymentNotification(req.headers, req.body);
    console.log('处理回调结果:', result);
    res.status(200).json({ code: 'SUCCESS', message: '成功' });
  } catch (error) {
    console.error('处理支付回调出错:', error);
    res.status(500).json({ 
      code: 'FAIL',
      message: error.message
    });
  }
});

module.exports = router; 