#!/usr/bin/env python3
# coding=utf-8
# 生成CDN预热URL列表并执行预热脚本

import os
import subprocess
import sys
import argparse
from pathlib import Path
import urllib.parse

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='生成CDN预热URL列表并执行预热脚本')
    parser.add_argument('--domain', '-d', default='https://cdn.tarotqa.com',
                      help='网站域名，默认为https://cdn.tarotqa.com')
    parser.add_argument('--output', '-o', default='urllist.txt',
                      help='输出文件名，默认为urllist.txt')
    parser.add_argument('--dir', default='public/images-optimized',
                      help='要收集的资源目录，默认为public/images-optimized')
    parser.add_argument('--execute', '-e', action='store_true',
                      help='自动执行CDN预热操作，不询问')
    parser.add_argument('--task', '-t', default='push', choices=['push', 'clear'],
                      help='CDN操作类型: push(预热)或clear(刷新)，默认为push')
    return parser.parse_args()

# 获取AccessKey和AccessKeySecret
def get_credentials():
    """获取阿里云访问凭证"""
    # 直接设置凭证
    access_key = "LTAI5tKfGxYfN1bkAxBo1arR"
    access_key_secret = "******************************"
    
    return access_key, access_key_secret

# 收集所有文件路径
def collect_files(directory):
    file_paths = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            file_paths.append(file_path)
    return file_paths

# 正确编码URL路径部分
def encode_url_path(path):
    # 分割路径的各个部分
    parts = path.split('/')
    # 对每个部分进行URL编码，但保留路径分隔符
    encoded_parts = [urllib.parse.quote(part) for part in parts]
    # 重新组合路径
    return '/'.join(encoded_parts)

# 生成URL列表
def generate_url_list(file_paths, base_dir, base_url):
    urls = []
    for file_path in file_paths:
        # 将Windows路径分隔符转换为URL路径分隔符
        relative_path = os.path.relpath(file_path, "public").replace("\\", "/")
        # 对路径部分进行URL编码，处理中文字符
        encoded_path = encode_url_path(relative_path)
        url = f"{base_url}/{encoded_path}"
        urls.append(url)
    return urls

# 保存URL列表到文件
def save_urls_to_file(urls, output_file):
    # 使用二进制模式写入，避免BOM
    with open(output_file, "wb") as f:
        for url in urls:
            f.write(f"{url}\n".encode('utf-8'))
    print(f"已生成URL列表文件: {output_file}")
    print(f"共包含 {len(urls)} 个URL")

# 执行CDN预热脚本
def run_refresh_script(output_file, access_key, access_key_secret, task_type="push"):
    script_path = os.path.join(os.path.dirname(__file__), "Refresh.py")
    
    if not os.path.exists(script_path):
        script_path = "scripts/Refresh.py"
        if not os.path.exists(script_path):
            print(f"错误: 找不到Refresh.py脚本")
            return False
    
    cmd = [
        "python", script_path,
        "-i", access_key,
        "-k", access_key_secret,
        "-r", output_file,
        "-t", task_type
    ]
    
    print(f"执行CDN {task_type}操作...")
    try:
        subprocess.run(cmd, check=True)
        print(f"CDN {task_type}操作已完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"执行CDN {task_type}操作时出错: {e}")
        return False

def main():
    # 解析命令行参数
    args = parse_arguments()
    
    # 获取阿里云访问凭证
    access_key, access_key_secret = get_credentials()
    
    # 设置基础参数
    base_url = args.domain.rstrip('/')
    base_dir = Path(args.dir)
    
    # 修改输出文件路径，使其保存在脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_file = os.path.join(script_dir, args.output)
    
    # 检查资源目录是否存在
    if not base_dir.exists():
        print(f"错误: {base_dir} 目录不存在")
        sys.exit(1)
    
    print(f"开始收集 {base_dir} 目录下的所有资源文件...")
    file_paths = collect_files(base_dir)
    print(f"找到 {len(file_paths)} 个文件")
    
    print("生成URL列表...")
    urls = generate_url_list(file_paths, base_dir, base_url)
    
    print(f"保存URL列表到 {output_file}...")
    save_urls_to_file(urls, output_file)
    
    # 只有在--execute参数被指定时才执行CDN预热
    if args.execute:
        run_refresh_script(output_file, access_key, access_key_secret, args.task)

if __name__ == "__main__":
    main() 