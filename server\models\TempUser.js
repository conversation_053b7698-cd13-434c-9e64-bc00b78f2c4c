const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');
const bcrypt = require('bcryptjs');

class TempUser {
  static async create({ email, password, username, verificationCode, language, fingerprint }) {
    const pool = await getConnection();
    const id = uuidv4();
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // 设置验证码过期时间（30分钟后）
    const expiryDate = new Date();
    expiryDate.setMinutes(expiryDate.getMinutes() + 30);
    
    console.log('创建临时用户，指纹:', fingerprint ? fingerprint.substring(0, 10) + '...' : '无');
    
    try {
      await pool.query(
        'INSERT INTO temp_users (id, email, password, username, verification_code, verification_code_expiry, language, fingerprint) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [id, email, hashedPassword, username, verificationCode, expiryDate, language || 'zh-CN', fingerprint || null]
      );
      
      // 返回临时用户ID
      const [rows] = await pool.query('SELECT * FROM temp_users WHERE id = ?', [id]);
      
      // console.log('临时用户创建详情:', {
      //   id: rows[0]?.id || id,
      //   email: rows[0]?.email || email,
      //   hasFingerprint: rows[0]?.fingerprint ? '是' : '否',
      //   fingerprint: rows[0]?.fingerprint ? rows[0]?.fingerprint.substring(0, 10) + '...' : '未保存'
      // });
      
      return rows[0];
    } catch (error) {
      console.error('创建临时用户失败:', error);
      throw error;
    }
  }

  static async findById(id) {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT * FROM temp_users WHERE id = ?', [id]);
    return rows[0];
  }

  static async findByEmail(email) {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT * FROM temp_users WHERE email = ?', [email]);
    return rows[0];
  }

  static async findByUsername(username) {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT * FROM temp_users WHERE username = ?', [username]);
    return rows[0];
  }

  static async update(id, updateData) {
    const pool = await getConnection();
    const setClause = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(updateData), id];
    
    await pool.query(
      `UPDATE temp_users SET ${setClause} WHERE id = ?`,
      values
    );
  }

  static async delete(id) {
    const pool = await getConnection();
    await pool.query('DELETE FROM temp_users WHERE id = ?', [id]);
  }
}

module.exports = { TempUser }; 