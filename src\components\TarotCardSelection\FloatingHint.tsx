import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface FloatingHintProps {
  message: string;
  isDark: boolean;
}

const FloatingHint: React.FC<FloatingHintProps> = ({ message, isDark }) => {
  if (!message) return null;
  
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="fixed top-[35%] left-0 right-0 flex items-center justify-center z-50 pointer-events-none"
      >
        <div className={`${isDark ? 'bg-gray-900/95 border-purple-500/30' : 'bg-white/95 border-purple-400/30'} backdrop-blur-md ${isDark ? 'text-white' : 'text-gray-800'} px-8 py-4 rounded-xl shadow-xl border`}>
          <div className="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <span className="text-base font-medium font-sans japanese">
              {message}
            </span>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default FloatingHint; 