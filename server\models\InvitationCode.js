const { getConnection } = require('../services/database');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

class InvitationCode {
  static async findById(id) {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT * FROM invitation_codes WHERE id = ?', [id]);
    return rows[0];
  }

  static async findByCode(code) {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT * FROM invitation_codes WHERE code = ?', [code]);
    return rows[0];
  }

  static async findBySalesId(salesId) {
    const pool = await getConnection();
    
    // 1. 获取该销售人员的所有邀请码
    const [rows] = await pool.query(`
      SELECT ic.*, u.username as used_by_username 
      FROM invitation_codes ic 
      LEFT JOIN users u ON ic.used_by = u.id
      WHERE ic.sales_id = ?
      ORDER BY ic.created_at DESC
    `, [salesId]);
    
    // 如果没有邀请码，直接返回空数组
    if (rows.length === 0) {
      return [];
    }
    
    // 2. 收集所有已使用的邀请码
    const usedCodes = rows.filter(code => code.is_used && code.code).map(code => code.code);
    
    // 如果没有已使用的邀请码，返回原始数据，没有支付金额
    if (usedCodes.length === 0) {
      return rows.map(code => ({ ...code, payment_amount: 0 }));
    }
    
    // 3. 批量查询所有邀请码的支付总额，并考虑货币转换
    const placeholders = usedCodes.map(() => '?').join(',');
    const [paymentData] = await pool.query(`
      SELECT 
        u.invitation_code as code, 
        po.currency,
        SUM(
          CASE 
            WHEN po.currency = 'USD' THEN po.amount * 7.2
            ELSE po.amount
          END
        ) as payment_amount
      FROM users u
      JOIN payment_orders po ON u.id = po.user_id
      WHERE u.invitation_code IN (${placeholders})
      AND po.status = 'success' 
      AND po.discount_applied IS NOT NULL 
      AND po.discount_applied > 0
      GROUP BY u.invitation_code, po.currency
    `, usedCodes);
    
    // 4. 创建邀请码与支付金额的映射
    const paymentMap = new Map();
    paymentData.forEach(item => {
      // 如果已经有该邀请码的记录，累加金额
      if (paymentMap.has(item.code)) {
        paymentMap.set(item.code, Number(paymentMap.get(item.code)) + Number(item.payment_amount));
      } else {
        paymentMap.set(item.code, Number(item.payment_amount));
      }
    });
    
    // 5. 合并数据
    return rows.map(code => ({
      ...code,
      payment_amount: code.is_used && code.code ? (paymentMap.get(code.code) || 0) : 0
    }));
  }

  static async create(salesId) {
    // 生成随机邀请码，确保唯一性
    let code = this.generateCode();
    let isUnique = false;
    const pool = await getConnection();
    
    // 检查邀请码是否唯一，如果不唯一则重新生成
    while (!isUnique) {
      const [existingCodes] = await pool.query('SELECT id FROM invitation_codes WHERE code = ?', [code]);
      if (existingCodes.length === 0) {
        isUnique = true;
      } else {
        code = this.generateCode();
      }
    }
    
    const [result] = await pool.query(
      'INSERT INTO invitation_codes (code, sales_id) VALUES (?, ?)',
      [code, salesId]
    );
    
    return { id: result.insertId, code, salesId, isUsed: false, createdAt: new Date() };
  }

  static generateCode() {
    // 生成8位随机字母数字组合的邀请码
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    const randomBytes = crypto.randomBytes(8);
    
    for (let i = 0; i < 8; i++) {
      const index = randomBytes[i] % chars.length;
      code += chars.charAt(index);
    }
    
    return code;
  }

  static async use(code, userId) {
    const pool = await getConnection();
    
    // 先检查用户是否已经有邀请码
    const [userRows] = await pool.query(
      'SELECT invitation_code, has_internal_privilege FROM users WHERE id = ?',
      [userId]
    );
    
    // 如果用户已经使用过邀请码，则不允许再次使用
    if (userRows[0] && userRows[0].invitation_code) {
      return { success: false, error: 'already_used_invitation' };
    }
    
    const [invitationCode] = await pool.query('SELECT * FROM invitation_codes WHERE code = ?', [code]);
    
    if (!invitationCode[0] || invitationCode[0].is_used) {
      return { success: false, error: 'code_invalid_or_used' };
    }
    
    // 使用事务确保数据一致性
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();
      
      // 更新邀请码状态
      await connection.query(
        `UPDATE invitation_codes 
         SET is_used = true, used_by = ?, used_at = NOW() 
         WHERE code = ?`,
        [userId, code]
      );
      
      // 更新用户状态 - 直接保存邀请码文本，而不是ID
      await connection.query(
        `UPDATE users 
         SET has_internal_privilege = true, 
             invitation_code = ?, 
             privilege_granted_at = NOW() 
         WHERE id = ?`,
        [code, userId]
      );
      
      await connection.commit();
      return { success: true, codeId: invitationCode[0].id };
    } catch (error) {
      await connection.rollback();
      console.error('Error using invitation code:', error);
      return { success: false, error: 'process_error' };
    } finally {
      connection.release();
    }
  }

  // 获取邀请码使用者的支付总金额
  static async getPaymentAmount(codeId) {
    const pool = await getConnection();
    try {
      // 首先获取对应的邀请码
      const [codeRows] = await pool.query('SELECT code FROM invitation_codes WHERE id = ?', [codeId]);
      
      if (!codeRows[0]) {
        return 0;
      }
      
      const code = codeRows[0].code;
      
      const [rows] = await pool.query(`
        SELECT SUM(
          CASE 
            WHEN po.currency = 'USD' THEN po.amount * 7.2
            ELSE po.amount
          END
        ) as total_amount
        FROM users u
        JOIN payment_orders po ON u.id = po.user_id
        WHERE u.invitation_code = ? 
        AND po.status = 'success'
        AND po.discount_applied IS NOT NULL 
        AND po.discount_applied > 0
      `, [code]);
      
      return rows[0]?.total_amount || 0;
    } catch (error) {
      console.error('获取邀请码支付金额失败:', error);
      return 0;
    }
  }
}

module.exports = { InvitationCode }; 