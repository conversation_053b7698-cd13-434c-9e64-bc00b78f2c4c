/**
 * VIP状态清理服务
 * 检查并更新过期的VIP用户状态
 */
const schedule = require('node-schedule');
const { getConnection } = require('./database');

// 用于跟踪服务是否已初始化
let isInitialized = false;
let currentJob = null;

/**
 * 检查并更新过期的VIP用户状态
 * 将过期的VIP用户状态重置为默认值
 */
const checkAndUpdateExpiredVipStatus = async () => {
  try {
    // console.log('[VipStatus] 开始检查过期的VIP用户状态...');
    
    const pool = await getConnection();
    
    // 获取过期VIP用户统计
    const [expiredVipUsers] = await pool.query(
      `SELECT COUNT(*) as count
       FROM users
       WHERE vip_status = 'active' AND vip_end_date < NOW()`
    );
    
    const expiredCount = expiredVipUsers[0]?.count || 0;
    // console.log(`[VipStatus] 发现 ${expiredCount} 个已过期但状态仍为active的VIP用户`);
    
    if (expiredCount > 0) {
      // 更新过期的VIP用户状态
      const [updateResult] = await pool.query(
        `UPDATE users
         SET vip_status = 'expired'
         WHERE vip_status = 'active' AND vip_end_date < NOW()`
      );
      
      const updatedCount = updateResult.affectedRows || 0;
      // console.log(`[VipStatus] 已将 ${updatedCount} 个过期VIP用户的状态更新为expired`);
      
      return updatedCount;
    }
    
    return 0;
  } catch (error) {
    console.error('[VipStatus] 检查并更新过期的VIP用户状态时出错:', error);
    return 0;
  }
};

/**
 * 初始化VIP状态清理服务
 * @param {string} [cronExpression='0 0 * * *'] - Cron表达式，默认每天执行一次（午夜）
 * @returns {Object|null} 调度任务对象或null
 */
const initVipStatusCleanup = (cronExpression = '0 0 * * *') => {
  try {
    // 单例模式：如果服务已经初始化，则取消之前的任务并重新调度
    if (isInitialized) {
      if (currentJob) {
        currentJob.cancel();
      }
    } else {
      isInitialized = true;
    }
    
    // 设置定时清理任务 - 检查并更新过期的VIP用户状态
    const job = schedule.scheduleJob(cronExpression, checkAndUpdateExpiredVipStatus);
    
    // 保存当前任务引用
    currentJob = job;
    
    // console.log(`[VipStatus] VIP状态清理服务已${isInitialized ? '更新' : '启动'}，调度表达式: ${cronExpression}`);
    // console.log(`[VipStatus] 下次执行时间: ${job.nextInvocation().toISOString()}`);
    
    // 服务启动时也执行一次检查
    checkAndUpdateExpiredVipStatus().then(count => {
      console.log(`[VipStatus] 初始化检查完成，更新了${count}个过期VIP用户的状态`);
    });
    
    return job;
  } catch (error) {
    console.error('[VipStatus] 初始化VIP状态清理服务失败:', error);
    return null;
  }
};

/**
 * 关闭VIP状态清理服务
 */
const shutdownVipStatusCleanup = () => {
  if (currentJob) {
    currentJob.cancel();
    console.log('[VipStatus] VIP状态清理服务已关闭');
    currentJob = null;
  }
  isInitialized = false;
};

module.exports = {
  initVipStatusCleanup,
  checkAndUpdateExpiredVipStatus,
  shutdownVipStatusCleanup
}; 