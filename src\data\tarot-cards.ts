export const CARD_DIMENSIONS = {
  width: 140,
  height: 240,
  margin: 10,
};

export interface TarotCard {
  id: number;
  name: string;
  nameEn: string;
  displayNameEn?: string; // 用于显示的英文名称，带有空格
}

export const TAROT_CARDS: TarotCard[] = [
  // 大阿卡纳牌组 (0-21)
  {
    id: 0,
    name: '愚者',
    nameEn: 'The_Fool',
  },
  {
    id: 1,
    name: '魔术师',
    nameEn: 'The_Magician',
  },
  {
    id: 2,
    name: '女祭司',
    nameEn: 'The_High_Priestess',
  },
  {
    id: 3,
    name: '女皇',
    nameEn: 'The_Empress',
  },
  {
    id: 4,
    name: '皇帝',
    nameEn: 'The_Emperor',
  },
  {
    id: 5,
    name: '教皇',
    nameEn: 'The_Hierophant',
  },
  {
    id: 6,
    name: '恋人',
    nameEn: 'The_Lovers',
  },
  {
    id: 7,
    name: '战车',
    nameEn: 'The_Chariot',
  },
  {
    id: 8,
    name: '力量',
    nameEn: 'Strength',
  },
  {
    id: 9,
    name: '隐士',
    nameEn: 'The_Hermit',
  },
  {
    id: 10,
    name: '命运之轮',
    nameEn: 'Wheel_of_Fortune',
  },
  {
    id: 11,
    name: '正义',
    nameEn: 'Justice',
  },
  {
    id: 12,
    name: '倒吊人',
    nameEn: 'The_Hanged_Man',
  },
  {
    id: 13,
    name: '死神',
    nameEn: 'Death',
  },
  {
    id: 14,
    name: '节制',
    nameEn: 'Temperance',
  },
  {
    id: 15,
    name: '恶魔',
    nameEn: 'The_Devil',
  },
  {
    id: 16,
    name: '塔',
    nameEn: 'The_Tower',
  },
  {
    id: 17,
    name: '星星',
    nameEn: 'The_Star',
  },
  {
    id: 18,
    name: '月亮',
    nameEn: 'The_Moon',
  },
  {
    id: 19,
    name: '太阳',
    nameEn: 'The_Sun',
  },
  {
    id: 20,
    name: '审判',
    nameEn: 'Judgement',
  },
  {
    id: 21,
    name: '世界',
    nameEn: 'The_World',
  },
  // 权杖牌组 (22-35)
  {
    id: 22,
    name: '权杖王牌',
    nameEn: 'Ace_of_Wands',
  },
  {
    id: 23,
    name: '权杖二',
    nameEn: 'Two_of_Wands'
  },
  {
    id: 24,
    name: '权杖三',
    nameEn: 'Three_of_Wands'
  },
  {
    id: 25,
    name: '权杖四',
    nameEn: 'Four_of_Wands'
  },
  {
    id: 26,
    name: '权杖五',
    nameEn: 'Five_of_Wands'
  },
  {
    id: 27,
    name: '权杖六',
    nameEn: 'Six_of_Wands'
  },
  {
    id: 28,
    name: '权杖七',
    nameEn: 'Seven_of_Wands'
  },
  {
    id: 29,
    name: '权杖八',
    nameEn: 'Eight_of_Wands'
  },
  {
    id: 30,
    name: '权杖九',
    nameEn: 'Nine_of_Wands'
  },
  {
    id: 31,
    name: '权杖十',
    nameEn: 'Ten_of_Wands'
  },
  {
    id: 32,
    name: '权杖侍者',
    nameEn: 'Page_of_Wands'
  },
  {
    id: 33,
    name: '权杖骑士',
    nameEn: 'Knight_of_Wands'
  },
  {
    id: 34,
    name: '权杖皇后',
    nameEn: 'Queen_of_Wands'
  },
  {
    id: 35,
    name: '权杖国王',
    nameEn: 'King_of_Wands'
  },
  // 圣杯牌组 (36-49)
  {
    id: 36,
    name: '圣杯王牌',
    nameEn: 'Ace_of_Cups'
  },
  {
    id: 37,
    name: '圣杯二',
    nameEn: 'Two_of_Cups'
  },
  {
    id: 38,
    name: '圣杯三',
    nameEn: 'Three_of_Cups'
  },
  {
    id: 39,
    name: '圣杯四',
    nameEn: 'Four_of_Cups'
  },
  {
    id: 40,
    name: '圣杯五',
    nameEn: 'Five_of_Cups'
  },
  {
    id: 41,
    name: '圣杯六',
    nameEn: 'Six_of_Cups'
  },
  {
    id: 42,
    name: '圣杯七',
    nameEn: 'Seven_of_Cups'
  },
  {
    id: 43,
    name: '圣杯八',
    nameEn: 'Eight_of_Cups'
  },
  {
    id: 44,
    name: '圣杯九',
    nameEn: 'Nine_of_Cups'
  },
  {
    id: 45,
    name: '圣杯十',
    nameEn: 'Ten_of_Cups'
  },
  {
    id: 46,
    name: '圣杯侍者',
    nameEn: 'Page_of_Cups'
  },
  {
    id: 47,
    name: '圣杯骑士',
    nameEn: 'Knight_of_Cups'
  },
  {
    id: 48,
    name: '圣杯皇后',
    nameEn: 'Queen_of_Cups'
  },
  {
    id: 49,
    name: '圣杯国王',
    nameEn: 'King_of_Cups'
  },
  // 宝剑牌组 (50-63)
  {
    id: 50,
    name: '宝剑王牌',
    nameEn: 'Ace_of_Swords'
  },
  {
    id: 51,
    name: '宝剑二',
    nameEn: 'Two_of_Swords'
  },
  {
    id: 52,
    name: '宝剑三',
    nameEn: 'Three_of_Swords'
  },
  {
    id: 53,
    name: '宝剑四',
    nameEn: 'Four_of_Swords'
  },
  {
    id: 54,
    name: '宝剑五',
    nameEn: 'Five_of_Swords'
  },
  {
    id: 55,
    name: '宝剑六',
    nameEn: 'Six_of_Swords'
  },
  {
    id: 56,
    name: '宝剑七',
    nameEn: 'Seven_of_Swords'
  },
  {
    id: 57,
    name: '宝剑八',
    nameEn: 'Eight_of_Swords'
  },
  {
    id: 58,
    name: '宝剑九',
    nameEn: 'Nine_of_Swords'
  },
  {
    id: 59,
    name: '宝剑十',
    nameEn: 'Ten_of_Swords'
  },
  {
    id: 60,
    name: '宝剑侍者',
    nameEn: 'Page_of_Swords'
  },
  {
    id: 61,
    name: '宝剑骑士',
    nameEn: 'Knight_of_Swords'
  },
  {
    id: 62,
    name: '宝剑皇后',
    nameEn: 'Queen_of_Swords'
  },
  {
    id: 63,
    name: '宝剑国王',
    nameEn: 'King_of_Swords'
  },
  // 钱币牌组 (64-77)
  {
    id: 64,
    name: '钱币王牌',
    nameEn: 'Ace_of_Pentacles'
  },
  {
    id: 65,
    name: '钱币二',
    nameEn: 'Two_of_Pentacles'
  },
  {
    id: 66,
    name: '钱币三',
    nameEn: 'Three_of_Pentacles'
  },
  {
    id: 67,
    name: '钱币四',
    nameEn: 'Four_of_Pentacles'
  },
  {
    id: 68,
    name: '钱币五',
    nameEn: 'Five_of_Pentacles'
  },
  {
    id: 69,
    name: '钱币六',
    nameEn: 'Six_of_Pentacles'
  },
  {
    id: 70,
    name: '钱币七',
    nameEn: 'Seven_of_Pentacles'
  },
  {
    id: 71,
    name: '钱币八',
    nameEn: 'Eight_of_Pentacles'
  },
  {
    id: 72,
    name: '钱币九',
    nameEn: 'Nine_of_Pentacles'
  },
  {
    id: 73,
    name: '钱币十',
    nameEn: 'Ten_of_Pentacles'
  },
  {
    id: 74,
    name: '钱币侍者',
    nameEn: 'Page_of_Pentacles'
  },
  {
    id: 75,
    name: '钱币骑士',
    nameEn: 'Knight_of_Pentacles'
  },
  {
    id: 76,
    name: '钱币皇后',
    nameEn: 'Queen_of_Pentacles'
  },
  {
    id: 77,
    name: '钱币国王',
    nameEn: 'King_of_Pentacles'
  }
]; 