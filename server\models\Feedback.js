const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');

class Feedback {
  /**
   * 创建用户反馈
   * @param {Object} feedbackData - 反馈数据
   * @param {string} [feedbackData.userId] - 用户ID（可选，未登录用户为null）
   * @param {string} feedbackData.content - 反馈内容
   * @param {string} feedbackData.type - 反馈类型(problem/suggestion/other)
   * @param {string} [feedbackData.email] - 用户邮箱（可选）
   * @param {string} [feedbackData.username] - 用户名（可选） 
   * @param {Object} [feedbackData.metadata] - 额外元数据（例如浏览器信息等）
   * @returns {Promise<Object>} 创建的反馈信息
   */
  static async create(feedbackData) {
    const pool = await getConnection();
    const id = uuidv4();
    const now = new Date();

    const [result] = await pool.query(
      `INSERT INTO user_feedback (
        id, user_id, content, type, email, username,
        metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        feedbackData.userId || null,
        feedbackData.content,
        feedbackData.type || 'other',
        feedbackData.email || null,
        feedbackData.username || null,
        feedbackData.metadata ? JSON.stringify(feedbackData.metadata) : null,
        now,
        now
      ]
    );

    return { id, ...feedbackData, created_at: now, updated_at: now };
  }

  /**
   * 获取单个反馈详情
   * @param {string} id - 反馈ID
   * @returns {Promise<Object|null>} 反馈信息
   */
  static async findById(id) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      `SELECT * FROM user_feedback WHERE id = ?`,
      [id]
    );

    if (rows.length === 0) {
      return null;
    }

    const feedback = rows[0];
    
    if (feedback.metadata) {
      try {
        feedback.metadata = JSON.parse(feedback.metadata);
      } catch (error) {
        console.error('Error parsing feedback metadata:', error);
        feedback.metadata = {};
      }
    }

    return feedback;
  }

  /**
   * 获取反馈列表
   * @param {Object} options - 查询选项
   * @param {number} [options.limit=20] - 每页数量
   * @param {number} [options.offset=0] - 偏移量
   * @param {string} [options.type] - 类型过滤
   * @param {string} [options.userId] - 用户ID过滤
   * @returns {Promise<Object>} 反馈列表和总数
   */
  static async findAll(options = {}) {
    const pool = await getConnection();
    const limit = options.limit || 20;
    const offset = options.offset || 0;
    
    let queryConditions = [];
    let queryParams = [];
    
    if (options.type) {
      queryConditions.push('type = ?');
      queryParams.push(options.type);
    }
    
    if (options.userId) {
      queryConditions.push('user_id = ?');
      queryParams.push(options.userId);
    }
    
    const whereClause = queryConditions.length > 0 
      ? `WHERE ${queryConditions.join(' AND ')}` 
      : '';
    
    // 查询总记录数
    const [countRows] = await pool.query(
      `SELECT COUNT(*) as total FROM user_feedback ${whereClause}`,
      queryParams
    );
    
    const total = countRows[0].total;
    
    // 查询数据
    const [dataRows] = await pool.query(
      `SELECT * FROM user_feedback ${whereClause} 
       ORDER BY created_at DESC LIMIT ? OFFSET ?`,
      [...queryParams, limit, offset]
    );
    
    // 处理metadata字段
    const feedbacks = dataRows.map(feedback => {
      if (feedback.metadata) {
        try {
          feedback.metadata = JSON.parse(feedback.metadata);
        } catch (error) {
          console.error('Error parsing feedback metadata:', error);
          feedback.metadata = {};
        }
      }
      return feedback;
    });
    
    return {
      data: feedbacks,
      total,
      limit,
      offset
    };
  }

  /**
   * 获取反馈统计数据
   * @param {Object} options - 查询选项
   * @param {Date} [options.startDate] - 开始日期
   * @param {Date} [options.endDate] - 结束日期
   * @returns {Promise<Object>} 统计数据
   */
  static async getStats(options = {}) {
    const pool = await getConnection();
    
    let whereClause = '';
    let queryParams = [];
    
    if (options.startDate && options.endDate) {
      whereClause = 'WHERE created_at BETWEEN ? AND ?';
      queryParams = [options.startDate, options.endDate];
    }
    
    // 获取总数
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM user_feedback ${whereClause}`,
      queryParams
    );
    
    // 获取按类型分类的数量
    const [countByType] = await pool.query(
      `SELECT 
        type,
        COUNT(*) as count
       FROM user_feedback ${whereClause}
       GROUP BY type`,
      queryParams
    );
    
    return {
      total: countResult[0].total,
      byType: countByType.reduce((acc, curr) => {
        acc[curr.type] = curr.count;
        return acc;
      }, {})
    };
  }
}

module.exports = Feedback; 