import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { updateSession } from '../services/sessionService';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import '../styles/SpreadSelection.css';
import { IoChevronBackOutline, IoChevronForwardOutline } from 'react-icons/io5';
import { useTranslation } from 'react-i18next';
import { SpreadOption, SPREAD_OPTIONS } from '../data/spreads';
import axiosInstance from '../utils/axios';
import SEO from '../components/SEO';
import { CdnLazyImage } from '../components/CdnImageExport';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
// 导入用户上下文和对话框组件
import { useUser } from '../contexts/UserContext';
import VipPromptDialog from '../components/VipPromptDialog';
import LoginPrompt from '../components/LoginPrompt';

const CATEGORIES = ['all', 'love_relationships', 'fortune_prediction', 'self-exploration', 'career_wealth', 'general'];

// 添加辅助函数来转换分类名称
const getCategoryInChinese = (key: string): string => {
  const categoryMap: Record<string, string> = {
    'all': '全部',
    'general': '通用',
    'love_relationships': '爱情人际',
    'fortune_prediction': '运势预测',
    'self-exploration': '自我探索',
    'career_wealth': '事业财富'
  };
  return categoryMap[key] || key;
};

const getCategoryKey = (chineseCategory: string): string => {
  const reverseCategoryMap: Record<string, string> = {
    '全部': 'all',
    '通用': 'general',
    '爱情人际': 'love_relationships',
    '运势预测': 'fortune_prediction',
    '自我探索': 'self-exploration',
    '事业财富': 'career_wealth'
  };
  return reverseCategoryMap[chineseCategory] || chineseCategory;
};

const SpreadCard = ({ spread, isRecommended, isSelected, onClick, isLoading, loadingProgress }: { 
  spread: SpreadOption; 
  isRecommended?: boolean; 
  isSelected?: boolean;
  onClick: () => void;
  isLoading?: boolean;
  loadingProgress: number;
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [currentPage, setCurrentPage] = useState(0);
  const positionsPerPage = 4;
  const totalPages = Math.ceil(spread.positions.length / positionsPerPage);

  const handlePrevPage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentPage(prev => (prev > 0 ? prev - 1 : prev));
  };

  const handleNextPage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentPage(prev => (prev < totalPages - 1 ? prev + 1 : prev));
  };

  // 获取翻译后的牌阵信息
  const getTranslatedSpreadInfo = () => {
    const spreadId = spread.id.replace(/-/g, '_'); // 将 id 中的连字符替换为下划线
    if (Object.keys(t('spreads', { returnObjects: true })).includes(spreadId)) {
      // 如果在翻译文件中存在该牌阵的翻译
      return {
        name: t(`spreads.${spreadId}.name`),
        description: t(`spreads.${spreadId}.description`),
        positions: spread.positions.map((_, index) => {
          const positionKeys = Object.keys(t(`spreads.${spreadId}.positions`, { returnObjects: true }));
          return t(`spreads.${spreadId}.positions.${positionKeys[index]}`);
        })
      };
    }
    // 如果没有翻译，返回原始值
    return {
      name: spread.name,
      description: spread.description,
      positions: spread.positions
    };
  };

  const { name: spreadName, description: spreadDescription, positions: spreadPositions } = getTranslatedSpreadInfo();

  const currentPositions = spreadPositions.slice(
    currentPage * positionsPerPage,
    (currentPage + 1) * positionsPerPage
  );

  if (isLoading) {
    return (
      <div className={`spread-card rounded-2xl p-6 relative h-[260px] border-2 ${isDark ? 'border-purple-500/20 bg-black/40' : 'border-purple-300/30 bg-[#F4F4F5]'} backdrop-blur-xl shadow-2xl flex items-center justify-center overflow-hidden`}>
        <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/10 rounded-full blur-3xl"></div>
        <div className="flex flex-col items-center gap-6 w-full max-w-[400px] relative z-10">
          {/* Loading Text */}
          <div className={`text-lg ${isDark ? 'text-gray-300' : 'text-gray-600'} font-medium font-['Inter']`}>
            {t('spread.recommending')}
          </div>
          
          {/* Progress Bar Container */}
          <div className={`w-full h-2 ${isDark ? 'bg-gray-800' : 'bg-gray-300'} rounded-full overflow-hidden`}>
            <div 
              className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transition-all duration-300"
              style={{ width: `${loadingProgress}%` }}
            ></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      id={`spread-${spread.id}`}
      className={`relative w-full overflow-hidden transition-all duration-300 ${
        isDark 
          ? 'bg-black/40 border-2 border-purple-500/20 hover:border-purple-500/40' 
          : 'bg-[#F4F4F5] border-2 border-purple-300/30 hover:border-purple-400/50'
      } backdrop-blur-xl rounded-2xl shadow-2xl ${
        isSelected ? isDark ? 'ring-2 ring-purple-500' : 'ring-2 ring-purple-400' : ''
      }`}
      onClick={onClick}
    >
      {isRecommended && (
        <div className="absolute top-0 right-0 bg-purple-600 text-white px-4 py-1.5 text-sm rounded-bl font-medium z-10" style={{ color: 'white' }}>
          {t('spread.ai_recommended')}
        </div>
      )}
      
      <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/10 rounded-full blur-3xl"></div>
      
      <div className="relative flex flex-col md:flex-row w-full">
        <div className="flex-1 p-4 md:p-6">
          <div className="flex flex-col h-full">
            <div className="mb-3 md:mb-4">
              <h3 className={`text-lg md:text-xl font-bold ${isDark ? 'text-white' : 'text-gray-800'} mb-1 md:mb-2`}>{spreadName}</h3>
              <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'} text-sm line-clamp-2`}>{spreadDescription}</p>
            </div>

            <div className="flex flex-wrap items-center gap-2 mb-2 md:mb-3">
              {spread.tags.map((tag) => {
                const tagText = t(`spread.tags.${tag}`, { defaultValue: tag });
                return (
                  <span
                    key={tag}
                    className={`px-2 py-0.5 ${isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-300 text-gray-700'} rounded text-xs`}
                  >
                    {tagText}
                  </span>
                );
              })}
              {totalPages > 1 && (
                <div className="flex items-center gap-1 ml-auto">
                  <button
                    onClick={handlePrevPage}
                    disabled={currentPage === 0}
                    className={`p-1 rounded ${
                      currentPage === 0 
                        ? isDark ? 'text-gray-600' : 'text-gray-400' 
                        : isDark ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-300'
                    }`}
                  >
                    <IoChevronBackOutline size={16} />
                  </button>
                  <button
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages - 1}
                    className={`p-1 rounded ${
                      currentPage === totalPages - 1 
                        ? isDark ? 'text-gray-600' : 'text-gray-400'
                        : isDark ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-300'
                    }`}
                  >
                    <IoChevronForwardOutline size={16} />
                  </button>
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-2 auto-rows-min content-start min-h-[80px]">
              {currentPositions.map((position, index) => (
                <div
                  key={currentPage * positionsPerPage + index}
                  className={`position-chip px-2 py-1.5 text-sm text-center rounded ${
                    isDark 
                      ? 'bg-gray-800 text-gray-200' 
                      : 'bg-gray-300 text-gray-700'
                  } truncate`}
                >
                  <span className="mr-1">{currentPage * positionsPerPage + index + 1}.</span>
                  {spread.id === 'time-flow' ? spreadPositions[currentPage * positionsPerPage + index] : position}
                </div>
              ))}
              {[...Array(positionsPerPage - currentPositions.length)].map((_, index) => (
                <div
                  key={`empty-${index}`}
                  className="invisible"
                />
              ))}
            </div>
          </div>
        </div>

        <div className={`w-full md:w-[200px] lg:w-[240px] p-4 md:pl-6 lg:pl-8 md:border-l border-t md:border-t-0 ${
          isDark ? 'border-gray-800' : 'border-gray-300'
        } flex items-center justify-center`}>
          <div className="w-[160px] md:w-[180px] lg:w-[200px] h-[140px] md:h-[160px] lg:h-[180px] flex items-center justify-center">
            <CdnLazyImage 
              src={spread.image}
              alt={spread.image.split('/').pop()?.split('.')[0] || spreadName}
              className="max-w-full max-h-full object-contain opacity-100"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const SpreadSelection: React.FC = () => {
  
  const { navigate } = useLanguageNavigate();
  const { t } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  // 添加用户上下文
  const { user } = useUser();
  const initialRequestMade = React.useRef(false);
  // 添加一个ref来追踪用户是否主动选择了分类
  const userSelectedCategory = React.useRef(false);
  const [selectedSpread, setSelectedSpread] = useState<SpreadOption | null>(null);
  const [recommendedSpreadId, setRecommendedSpreadId] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [isRequestInProgress, setIsRequestInProgress] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  // 添加对话框状态
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const apiResponseRef = React.useRef<any>(null);
  const isApiCalledGlobally = React.useRef(false);
  const isLoadingForced = React.useRef(false);
  const progressAnimationRef = React.useRef<{
    timer: NodeJS.Timeout | null;
    cancel: () => void;
  }>({
    timer: null,
    cancel: () => {
      if (progressAnimationRef.current.timer) {
        clearTimeout(progressAnimationRef.current.timer);
        progressAnimationRef.current.timer = null;
      }
    }
  });

  // 简化的进度条动画函数
  const startProgressAnimation = () => {
    // 如果API已经返回结果，直接完成进度条
    if (apiResponseRef.current || isLoadingForced.current) {
      setLoadingProgress(100);
      return;
    }

    // 定义进度点
    const points = [10, 25, 40, 55, 70, 85, 95];
    let currentIndex = 0;

    // 递归函数，逐步增加进度
    const animateToNextPoint = () => {
      // 如果API已经返回结果，直接到100%
      if (apiResponseRef.current || isLoadingForced.current) {
        setLoadingProgress(100);
        return;
      }

      // 如果已经到最后一个点，停止
      if (currentIndex >= points.length) {
        return;
      }

      // 设置当前进度
      setLoadingProgress(points[currentIndex]);
      
      // 随机延迟时间，使进度条看起来更自然
      const delay = 800 + Math.random() * 400;
      
      // 保存定时器引用，以便需要时取消
      progressAnimationRef.current.timer = setTimeout(() => {
        currentIndex++;
        animateToNextPoint();
      }, delay);
    };

    // 开始动画
    animateToNextPoint();
  };

  // 处理API返回后的操作
  const handleApiResponse = () => {
    // 清除正在进行的进度条动画
    progressAnimationRef.current.cancel();
    
    // 强制进度条到100%
    isLoadingForced.current = true;
    setLoadingProgress(100);
    
    // 等待进度条视觉效果后再显示结果
    setTimeout(() => {
      if (apiResponseRef.current) {
        const data = apiResponseRef.current;
        if (data.recommendation?.recommendedSpreadId) {
          setRecommendedSpreadId(data.recommendation.recommendedSpreadId);
        }
        // 只有当用户没有主动选择分类时，才应用API返回的分类
        if (data.category && !userSelectedCategory.current) {
          setSelectedCategory(data.category);
        }
      }
      // 最后关闭请求状态
      setIsRequestInProgress(false);
    }, 800);
  };

  // 启动进度条动画
  useEffect(() => {
    if (isRequestInProgress) {
      startProgressAnimation();
    }

    return () => {
      progressAnimationRef.current.cancel();
    };
  }, [isRequestInProgress]);

  // 监听API响应并处理
  useEffect(() => {
    if (apiResponseRef.current && isRequestInProgress) {
      handleApiResponse();
    }
  }, [apiResponseRef.current, isRequestInProgress]);

  // 当recommendedSpreadId变化时，自动选择推荐的牌阵，但不再自动滚动
  useEffect(() => {
    if (recommendedSpreadId) {
      const recommendedSpread = SPREAD_OPTIONS.find(s => s.id === recommendedSpreadId);
      if (recommendedSpread) {
        setSelectedSpread(recommendedSpread);
        // 删除自动滚动到推荐牌阵的代码
      }
    }
  }, [recommendedSpreadId]);

  // API请求逻辑
  useEffect(() => {
    const fetchRecommendation = async () => {
      // 避免重复调用
      if (initialRequestMade.current || isApiCalledGlobally.current) {
        return;
      }

      const userQuestion = localStorage.getItem('userQuestion');
      const token = localStorage.getItem('token');
      const sessionId = localStorage.getItem('sessionId'); // 获取sessionId

      // 清除缓存
      localStorage.removeItem('spreadRecommendation');
      localStorage.removeItem('recommendationQuestion');

      if (!userQuestion || !token) {
        setIsRequestInProgress(false);
        // console.error('缺少问题或token，无法推荐牌阵');
        return;
      }

      try {
        // 设置标记
        isApiCalledGlobally.current = true;
        initialRequestMade.current = true;
        setIsLoading(true);

        const response = await axiosInstance.post('/api/spread-recommendation/', { 
          question: userQuestion,
          language: localStorage.getItem('i18nextLng') || 'zh-CN',
          sessionId // 添加sessionId到请求
        });

        // 存储响应结果
        apiResponseRef.current = response.data;
        
        // 如果API返回得很快，等待进度条至少显示一段时间
        if (loadingProgress < 30) {
          setTimeout(() => {
            handleApiResponse();
          }, 1500);
        } else {
          // 否则直接处理API响应
          handleApiResponse();
        }

      } catch (error) {
        // console.error('推荐请求出错:', error);
        isLoadingForced.current = true;
        handleApiResponse();
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecommendation();
  }, []);

  // 组件挂载和卸载处理
  useEffect(() => {
    // 组件挂载时清除推荐缓存
    localStorage.removeItem('spreadRecommendation');
    localStorage.removeItem('recommendationQuestion');
    // 重置用户选择分类状态
    userSelectedCategory.current = false;
    
    // 组件卸载时清理
    return () => {
      initialRequestMade.current = false;
      progressAnimationRef.current.cancel();
      userSelectedCategory.current = false;
    };
  }, []);

  const filteredSpreads = SPREAD_OPTIONS.filter(spread => 
    selectedCategory === 'all' || spread.category.includes(getCategoryInChinese(selectedCategory))
  );

  // 查找推荐牌阵（无论它是否在当前分类中）
  const findRecommendedSpread = () => {
    if (!recommendedSpreadId) return null;
    
    // 在所有牌阵中查找推荐牌阵
    return SPREAD_OPTIONS.find(s => s.id === recommendedSpreadId);
  };

  // 创建一个占位的推荐牌阵
  const placeholderSpread: SpreadOption = {
    id: 'placeholder',
    name: '',
    description: '',
    cardCount: 0,
    positions: [],
    category: [''],
    tags: [],
    image: ''
  };

  // 修改显示逻辑
  const getDisplaySpreads = () => {
    // 对筛选后的牌阵进行排序的公共函数
    const sortByCategory = (spreads: SpreadOption[]) => {
      return [...spreads].sort((a, b) => {
        // 将多类别的牌阵放在后面
        if (a.category.length !== b.category.length) {
          return a.category.length - b.category.length;  // 类别数量少的排在前面
        }

        // 如果类别数量相同，按照类别顺序排序
        if (a.category[0] === b.category[0]) {
          return filteredSpreads.indexOf(a) - filteredSpreads.indexOf(b);
        }
        return CATEGORIES.indexOf(getCategoryKey(a.category[0])) - CATEGORIES.indexOf(getCategoryKey(b.category[0]));
      });
    };

    if (isRequestInProgress) {
      // 在加载状态，显示占位牌阵和排序后的牌阵
      const sortedSpreads = sortByCategory(filteredSpreads);
      return [placeholderSpread, ...sortedSpreads];
    } else {
      // 加载完成后，始终确保推荐牌阵显示在首位（如果有）
      const recommendedSpread = findRecommendedSpread();
      
      // 对筛选后的牌阵进行排序
      const sortedFilteredSpreads = sortByCategory(filteredSpreads);
      
      if (recommendedSpread) {
        // 过滤出不是推荐牌阵的其他牌阵
        const otherFilteredSpreads = sortedFilteredSpreads.filter(s => s.id !== recommendedSpreadId);
        return [recommendedSpread, ...otherFilteredSpreads];
      }
      return sortedFilteredSpreads;
    }
  };

  const displaySpreads = getDisplaySpreads();

  const handleSpreadSelect = (spread: SpreadOption) => {
    setSelectedSpread(spread);
  };

  // 添加用户权限检查函数
  const checkUserPermission = () => {
    if (!user) {
      setShowLoginPrompt(true);
      return false;
    }
    if (user.vipStatus === 'active') return true;
    if (user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return false;
    }
    return true;
  };

  // 修改handleConfirm函数，添加权限检查
  const handleConfirm = async () => {
    if (selectedSpread && !isLoading) {
      // 添加用户权限检查
      if (!checkUserPermission()) {
        return;
      }
      
      setIsLoading(true);
      try {
        // 保存选中的牌阵
        localStorage.setItem('selectedSpread', JSON.stringify(selectedSpread));
        
        // 预加载下一页所需资源，但不显示加载界面
        const img = new Image();
        img.src = '/images-optimized/back/001.webp'; // 预加载卡背图片
        
        // 更新会话，不阻塞导航
        const sessionId = localStorage.getItem('sessionId');
        if (sessionId) {
          await updateSession(sessionId, {
            selectedSpread: {
              id: selectedSpread.id,
              name: selectedSpread.name,
              cardCount: selectedSpread.cardCount,
              positions: selectedSpread.positions
            },
            status: 'spread_selected'
          }, false);
        }
        
        // 清除推荐相关缓存
        localStorage.removeItem('spreadRecommendation');
        localStorage.removeItem('recommendationQuestion');
        
        // 直接导航到下一页，不添加延迟
        navigate('/reading/shuffle', { replace: true });
      } catch (error) {
        // console.error('Error updating session with spread:', error);
        // 即使出错也清除缓存
        localStorage.removeItem('spreadRecommendation');
        localStorage.removeItem('recommendationQuestion');
        localStorage.removeItem('userQuestion');
        navigate('/reading/shuffle', { replace: true });
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col relative text-white">
      <SEO />
      <LandingBackground />
      
      {/* Main content */}
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8 pt-2 sm:pt-4">
          <div className="text-center mt-8 sm:mt-10">
            <h1 className="main-title mb-2 sm:mb-3">{t('spread.select_spread')}</h1>
            <p className="sub-title mb-4 sm:mb-6">{t('reader.subtitle')}</p>
          </div>

          {/* 分类导航 */}
          <div className="py-1 px-4 mb-4 sm:mb-6">
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:flex lg:flex-nowrap justify-center gap-2 md:gap-3">
              {CATEGORIES.map((category) => {
                const translationKey = 'spread.categories.' + category;
                return (
                  <button
                    key={category}
                    className={`w-full sm:min-w-[140px] lg:w-[180px] h-[44px] flex items-center justify-center whitespace-normal text-center px-3 md:px-4 rounded-full text-sm font-medium transition-colors duration-200 font-sans ${
                      selectedCategory === category
                      ? 'bg-purple-600 text-white'
                      : isDark 
                        ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        : 'bg-gray-200 text-white hover:bg-gray-300'
                    }`}
                    onClick={() => {
                      setSelectedCategory(category);
                      userSelectedCategory.current = true;
                    }}
                  >
                    <span className="line-clamp-2" style={{color: selectedCategory === category || isDark ? 'white' : '#4B5563'}}>
                      {t(translationKey)}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* 牌阵网格 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            {displaySpreads.map((spread) => (
              <div key={spread.id} id={`spread-container-${spread.id}`} className="relative group">
                <SpreadCard
                  spread={spread}
                  isRecommended={spread.id === recommendedSpreadId}
                  isSelected={selectedSpread?.id === spread.id}
                  onClick={() => handleSpreadSelect(spread)}
                  isLoading={spread.id === 'placeholder'}
                  loadingProgress={loadingProgress}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="relative z-10">
        <Footer />
      </div>
      {selectedSpread && (
        <motion.div 
          className="fixed bottom-4 md:bottom-8 left-0 right-0 z-50 flex justify-center pointer-events-auto px-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <motion.button
            className="w-full md:w-auto px-8 md:px-16 py-3 md:py-4 bg-purple-600 rounded-full text-white font-medium text-lg
                    shadow-lg hover:shadow-purple-500/30 hover:scale-102 transform transition-all duration-200 font-sans"
            onClick={handleConfirm}
            whileTap={{ scale: 0.98 }}
            disabled={isLoading}
            animate={isLoading ? { opacity: 0 } : { opacity: 1 }}
            transition={{ duration: 0.2 }}
          >
            {t('home.start_reading')}
          </motion.button>
        </motion.div>
      )}
      
      {/* 添加VIP提示对话框 */}
      <VipPromptDialog 
        isOpen={showVipPrompt} 
        onCancel={() => setShowVipPrompt(false)}
      />
      
      {/* 添加登录提示对话框 */}
      <LoginPrompt 
        isOpen={showLoginPrompt} 
        onClose={() => setShowLoginPrompt(false)}
      />
    </div>
  );
};

export default SpreadSelection;
// Add this CSS at the end of the file, before the last closing brace
const styles = document.createElement('style');
styles.textContent = `
  @keyframes loading {
    0% { width: 0%; }
    100% { width: 100%; }
  }

  .loading-progress {
    animation: loading 4s linear infinite;
  }

  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  .hide-scrollbar::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
`;
document.head.appendChild(styles);