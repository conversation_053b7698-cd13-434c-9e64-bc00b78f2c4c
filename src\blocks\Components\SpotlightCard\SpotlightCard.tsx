/*
	jsrepo 1.42.0
	Installed from https://reactbits.dev/ts/default/
	2025-3-7
*/

import React, { useRef } from "react";
import "./SpotlightCard.css";
import { useTheme } from "../../../contexts/ThemeContext";

interface Position {
  x: number;
  y: number;
}

interface SpotlightCardProps extends React.PropsWithChildren {
  className?: string;
  spotlightColor?: `rgba(${number}, ${number}, ${number}, ${number})`;
}

const SpotlightCard: React.FC<SpotlightCardProps> = ({
  children,
  className = "",
  spotlightColor = "rgba(255, 255, 255, 0.25)"
}) => {
  const divRef = useRef<HTMLDivElement>(null);
  const { theme } = useTheme();

  const handleMouseMove: React.MouseEventHandler<HTMLDivElement> = (e) => {
    if (!divRef.current) return;

    const rect = divRef.current.getBoundingClientRect();
    const position: Position = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    divRef.current.style.setProperty("--mouse-x", `${position.x}px`);
    divRef.current.style.setProperty("--mouse-y", `${position.y}px`);
    divRef.current.style.setProperty("--spotlight-color", spotlightColor);
  };

  return (
    <div
      ref={divRef}
      onMouseMove={handleMouseMove}
      className={`card-spotlight ${theme === 'light' ? 'card-spotlight-light' : ''} ${className}`}
    >
      {children}
    </div>
  );
};

export default SpotlightCard;
