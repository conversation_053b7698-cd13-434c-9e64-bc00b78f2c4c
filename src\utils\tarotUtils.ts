// 移除段落标识文本的辅助函数，用于清除如prologue:, answer:, analysis1:等前缀
export const removeSectionPrefix = (text: string): string => {
  // 匹配常见的段落前缀，如prologue:, answer:, analysis1:, summary:, advice1:等
  const prefixRegex = /^(prologue|answer|analysis\d*|summary|advice\d*|record):\s*/i;
  return text.replace(prefixRegex, '').trim();
};

// 辅助函数：清理高维建议内容，移除可能存在的标识符
export const cleanAdviceContent = (content: string): string => {
  // 去除可能出现在内容中的advice标记
  return content.replace(/\s*advice\d+\s*:\s*/gi, '').trim();
};

// 获取适合当前语言的字体类名
export const getFontClass = (lang: string): string => {
  switch (lang) {
    case 'ja':
      return 'font-sans japanese';
    case 'zh-CN':
    case 'zh-TW': // 确保繁体中文使用与简体中文相同的字体类
      return 'font-sans chinese';
    default:
      return 'font-sans';
  }
};

/**
 * 生成卡片的统一路径格式
 * 将卡片名称转换为URL友好的格式，使用连字符而不是下划线
 * @param cardName 卡片名称（可以是nameEn形式，如'The_High_Priestess'或'The High Priestess'）
 * @returns 处理后的URL友好路径，如'the-high-priestess'
 */
export const generateCardPath = (cardName: string): string => {
  return cardName
    .toLowerCase()
    .replace(/_/g, '-')  // 将下划线替换为连字符
    .replace(/\s+/g, '-'); // 将空格替换为连字符
}; 