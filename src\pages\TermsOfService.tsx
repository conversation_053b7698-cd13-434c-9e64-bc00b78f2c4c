import React from 'react';
import { useTranslation } from 'react-i18next';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import SEO from '../components/SEO';

const TermsOfService: React.FC = () => {
  const { t } = useTranslation();

  return (
    <>
      <SEO />
      <div className="min-h-screen flex flex-col relative">
        <LandingBackground />
        <div className="flex-grow relative z-10">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
            <div className="text-center mt-8 sm:mt-10">
              <h1 className="text-4xl font-bold dark:text-white text-gray-900 mb-8">{t('terms.title')}</h1>
            </div>

            <div className="space-y-6 dark:text-gray-300 text-gray-700">
              <p>{t('terms.welcome')}</p>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.service.title')}</h2>
                <p>{t('terms.service.description')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.usage.title')}</h2>
                <p>{t('terms.usage.description')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.ip.title')}</h2>
                <p>{t('terms.ip.description')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.conduct.title')}</h2>
                <p>{t('terms.conduct.description')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.payment.title')}</h2>
                <p>{t('terms.payment.description')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.disclaimer.title')}</h2>
                <p>{t('terms.disclaimer.description')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.termination.title')}</h2>
                <p>{t('terms.termination.description')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.modification.title')}</h2>
                <p>{t('terms.modification.description')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.law.title')}</h2>
                <p>{t('terms.law.description')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.language.title')}</h2>
                <p>{t('terms.language.description')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold dark:text-white text-gray-900">{t('terms.contact.title')}</h2>
                <p>{t('terms.contact.description')}</p>
              </section>

              <p>{t('terms.agreement')}</p>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
};

export default TermsOfService; 