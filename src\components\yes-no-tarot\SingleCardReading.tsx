import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { CdnLazyImage } from '../../components/CdnImageExport';
import YesNoQuestionForm from './YesNoQuestionForm';
import TarotCardDisplay from './TarotCardDisplay';
import ReadingResult from './ReadingResult';

interface SingleCardReadingProps {
  userQuestion: string;
  setUserQuestion: (question: string) => void;
  errorMessage: string;
  isSubmitting: boolean;
  showCard: boolean;
  flipped: boolean;
  cardBackImage: string;
  selectedCard: number | null;
  cardImage: string | null;
  cardOrientation: boolean;
  processingCard: boolean;
  readingResult: any;
  safetyIssue: boolean;
  safetyMessage: string;
  getFontClass: () => string;
  onSubmitQuestion: (e: React.FormEvent) => void;
  handleCardFlip: () => void;
  onBackToSelection: () => void;
  // 分享相关props
  onShare?: () => void;
  hasReceivedShareReward?: boolean;
}

const SingleCardReading: React.FC<SingleCardReadingProps> = ({
  userQuestion,
  setUserQuestion,
  errorMessage,
  isSubmitting,
  showCard,
  flipped,
  cardBackImage,
  selectedCard,
  cardImage,
  cardOrientation,
  processingCard,
  readingResult,
  safetyIssue,
  safetyMessage,
  getFontClass,
  onSubmitQuestion,
  handleCardFlip,
  onBackToSelection,
  onShare,
  hasReceivedShareReward
}) => {
  const { t } = useTranslation();
  
  // 添加问题展示区域的ref
  const questionDisplayRef = useRef<HTMLDivElement>(null);
  // 添加卡片区域的ref，这个ref会在主组件中使用
  const cardDisplayRef = useRef<HTMLDivElement>(null);

  // 将cardDisplayRef暴露给父组件
  React.useEffect(() => {
    // 如果父组件传入了ref，就将本地ref赋值给它
    if (window && cardDisplayRef.current) {
      // 将ref存储在window对象上，以便父组件可以访问
      (window as any).cardDisplayRef = cardDisplayRef;
    }
  }, [cardDisplayRef]);

  return (
    <div className="max-w-3xl mx-auto">
      {/* 单卡塔罗卡片 */}
      <div className="relative backdrop-blur-xl rounded-xl overflow-hidden dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 shadow-lg">
        {/* 顶部图片区域 - 仅在未提交问题时显示 */}
        {!showCard && (
          <div className="relative h-auto min-h-[200px] aspect-video overflow-hidden">
            <CdnLazyImage
              src="/images-optimized/yes-no-tarot/Yes-No-Single-Card-Spread.webp"
              alt="One Card Tarot Reading"
              className="w-full h-auto object-contain"
            />
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/60"></div>
            <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6">
              <h3 className="text-xl font-bold text-white">{t('yes_no_tarot.reading_options.single_card.title')}</h3>
              <p className="text-gray-300 mt-2 hidden sm:block text-sm">{t('yes_no_tarot.reading_options.single_card.description')}</p>
            </div>
          </div>
        )}
        
        {/* 内容区域 */}
        <div className="p-6">
          {/* 问题输入区域 - 只在未提交问题时显示 */}
          {!showCard ? (
            <YesNoQuestionForm
              userQuestion={userQuestion}
              setUserQuestion={setUserQuestion}
              errorMessage={errorMessage}
              isSubmitting={isSubmitting}
              onSubmit={onSubmitQuestion}
            />
          ) : (
            <div className="space-y-8">
              {/* 显示用户问题 */}
              <div className="text-left" ref={questionDisplayRef}>
                <h3 className="text-xl font-medium text-white mb-2">{t('yes_no_tarot.your_question')}</h3>
                <div className="mb-2">
                  <p className="text-lg text-purple-100">{userQuestion}</p>
                </div>
              </div>
              
              {/* 卡牌展示区域 */}
              <div ref={cardDisplayRef} id="card-display-area">
                <TarotCardDisplay
                  flipped={flipped}
                  cardBackImage={cardBackImage}
                  selectedCard={selectedCard}
                  cardImage={cardImage}
                  cardOrientation={cardOrientation}
                  processingCard={processingCard}
                  handleCardFlip={handleCardFlip}
                  getFontClass={getFontClass}
                />
              </div>
              
              {/* 解读结果显示区域 */}
              {flipped && (readingResult || safetyIssue) && (
                <ReadingResult
                  readingResult={readingResult}
                  safetyIssue={safetyIssue}
                  safetyMessage={safetyMessage}
                  onBackToSelection={onBackToSelection}
                  onShare={onShare}
                  hasReceivedShareReward={hasReceivedShareReward}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SingleCardReading; 