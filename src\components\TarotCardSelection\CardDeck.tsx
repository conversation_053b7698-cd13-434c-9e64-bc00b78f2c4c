import React, { MouseEvent, RefObject } from 'react';
import TarotCard from './TarotCard';

interface CardDeckProps {
  scrollRef: RefObject<HTMLDivElement>;
  isDark: boolean;
  randomCardIndices: number[];
  flippedCards: number[];
  processingCards: Set<number>;
  cardOrientations: Map<number, boolean>;
  cardBackImage: string;
  cardImages: Map<number, string>;
  currentLanguage: string;
  dragActive: boolean;
  animationDuration: {
    flip: number;
  };
  handleMouseDown: (e: MouseEvent<HTMLDivElement>) => void;
  handleMouseMove: (e: MouseEvent<HTMLDivElement>) => void;
  handleMouseUp: () => void;
  handleMouseLeave: () => void;
  handleCardClick: (index: number) => void;
  t: (key: string) => string;
}

const CardDeck: React.FC<CardDeckProps> = ({
  scrollRef,
  isDark,
  randomCardIndices,
  flippedCards,
  processingCards,
  cardOrientations,
  cardBackImage,
  cardImages,
  currentLanguage,
  dragActive,
  animationDuration,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  handleMouseLeave,
  handleCardClick,
  t
}) => {
  return (
    <div className="relative group mt-4">
      {/* Card deck */}
      <div 
        ref={scrollRef}
        className={`overflow-x-auto scrollbar-thin ${isDark ? 'scrollbar-thumb-purple-500/50 scrollbar-track-gray-800/30' : 'scrollbar-thumb-purple-500/40 scrollbar-track-gray-300/30'} pb-4 pt-2 mt-6 cursor-grab hide-scrollbar-default`}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
      >
        <div className="relative flex px-4 sm:px-8 md:px-12 py-4 min-w-max">
          {randomCardIndices.length === 78 ? 
            // 使用随机排列的卡牌数组
            randomCardIndices.map((cardId, displayIndex) => (
              <TarotCard
                key={displayIndex}
                cardId={cardId}
                displayIndex={displayIndex}
                isFirstCard={displayIndex === 0}
                isFlipped={flippedCards.includes(cardId)}
                isProcessing={processingCards.has(cardId)}
                dragActive={dragActive}
                orientation={cardOrientations.get(cardId) || false}
                cardBackImage={cardBackImage}
                cardFrontImage={cardImages.get(cardId) || ''}
                currentLanguage={currentLanguage}
                animationDuration={animationDuration}
                onClick={() => handleCardClick(cardId)}
              />
            ))
            : 
            // 如果随机卡牌数组还未准备好，使用默认顺序
            Array.from({ length: 78 }).map((_, index) => (
              <TarotCard
                key={index}
                cardId={index}
                displayIndex={index}
                isFirstCard={index === 0}
                isFlipped={flippedCards.includes(index)}
                isProcessing={processingCards.has(index)}
                dragActive={dragActive}
                orientation={cardOrientations.get(index) || false}
                cardBackImage={cardBackImage}
                cardFrontImage={cardImages.get(index) || ''}
                currentLanguage={currentLanguage}
                animationDuration={animationDuration}
                onClick={() => handleCardClick(index)}
              />
            ))
          }
        </div>
      </div>
      
      {/* Drag hint */}
      <div className={`absolute bottom-4 left-1/2 transform -translate-x-1/2 ${isDark ? 'bg-gray-900/70 text-gray-300' : 'bg-gray-100/70 text-gray-600'} text-xs px-3 py-1 rounded-t-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap`}>
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
          </svg>
          <span className="font-sans japanese">
            {t('reading.shuffle.drag_hint')}
          </span>
        </div>
      </div>
    </div>
  );
};

export default CardDeck; 