import axiosInstance from '../utils/axios';
import { getBrowserFingerprint } from '../utils/fingerprint';

export interface User {
  id: string;
  username: string;
  email: string;
  gender: string;
  age: number;
  remainingReads: number;
  vipStatus: string;
  vipType: 'none' | 'monthly' | 'yearly';
  vipEndDate: string | null;
  isEmailVerified: boolean;
  userProfile: string | null;
  whether_paypal: boolean;
  birthday: string | null;
  location: string | null;
  hasInternalPrivilege?: boolean;
  invitationCodeId?: number | null;
  privilegeGrantedAt?: string | null;
  isAdmin?: boolean;
}

export interface RegisterResponse {
  message: string;
  userId?: string;
  requireVerification?: boolean;
}

export interface LoginResponse {
  user: User;
  token: string;
}

export interface VerifyEmailResponse {
  success: boolean;
  message: string;
  code?: string;
  user?: User;
  token?: string;
}

export const register = async (userData: {
  email: string;
  password: string;
  username: string;
  language?: string;
  fingerprint?: string;
}): Promise<RegisterResponse> => {
  // 获取当前语言设置
  const currentLanguage = localStorage.getItem('i18nextLng') || 'zh-CN';
  
  // 确保指纹使用最新的格式
  // 无论是否提供指纹，都重新获取，确保使用最新的指纹格式
  userData.fingerprint = await getBrowserFingerprint();
  
  // 合并用户提供的语言或使用当前语言
  const dataWithLanguage = {
    ...userData,
    language: userData.language || currentLanguage
  };
  const response = await axiosInstance.post(`/api/auth/register`, dataWithLanguage);
  return response.data;
};

export const verifyEmail = async (userId: string, verificationCode: string): Promise<VerifyEmailResponse> => {
  const response = await axiosInstance.post(`/api/auth/verify-email`, {
    userId,
    verificationCode
  });
  return response.data;
};

export const login = async (email: string, password: string): Promise<LoginResponse> => {
  // 获取当前语言设置
  const currentLanguage = localStorage.getItem('i18nextLng') || 'zh-CN';
  
  // 获取浏览器指纹
  let fingerprint = '';
  fingerprint = await getBrowserFingerprint();

  
  const response = await axiosInstance.post(`/api/auth/login`, {
    email,
    password,
    language: currentLanguage,
    fingerprint
  });
  return response.data;
};

export const resendVerification = async (email: string, userId?: string, language?: string): Promise<{ message: string, userId?: string }> => {
  // 获取当前语言设置
  const currentLanguage = localStorage.getItem('i18nextLng') || 'zh-CN';
  
  try {
    // 获取浏览器指纹
    let fingerprint = '';
    fingerprint = await getBrowserFingerprint();

    
    // 尝试发送验证邮件
    try {
      const response = await axiosInstance.post(`/api/auth/resend-verification`, { 
        email,
        userId,
        language: language || currentLanguage,
        fingerprint
      });
      return response.data;
    } catch (apiError: any) {
      // 如果是404错误，尝试不带api前缀的路径
      if (apiError.response?.status === 404) {
        const altResponse = await axiosInstance.post(`/auth/resend-verification`, { 
          email,
          userId,
          language: language || currentLanguage,
          fingerprint
        });
        return altResponse.data;
      }
      throw apiError;
    }
  } catch (error: any) {
    // 如果是404错误(邮箱未注册到临时用户)，则先注册用户
    if (error.response?.status === 404) {
      try {
        // 自动从localStorage获取注册信息
        const registerData = JSON.parse(localStorage.getItem('registerData') || '{}');
        
        // 如果没有足够的信息，则无法继续
        if (!registerData.username || !registerData.password) {
          // console.error('注册信息不完整:', registerData);
          throw new Error('无法获取注册信息，请重新填写注册表单');
        }
        
        // 获取浏览器指纹
        let fingerprint = '';

        fingerprint = await getBrowserFingerprint();

        
        // 先注册临时用户
        const registerResponse = await register({
          email,
          username: registerData.username,
          password: registerData.password,
          language,
          fingerprint
        });
        
        // 注册接口本身已经发送了验证码，直接返回注册结果
        if (registerResponse.userId) {
          return {
            message: registerResponse.message,
            userId: registerResponse.userId
          };
        } else {
          // console.error('注册临时用户失败，未返回userId:', registerResponse);
          throw new Error('注册临时用户失败');
        }
      } catch (registerError: any) {
        // console.error('自动注册临时用户失败:', registerError);
        // 包装错误信息，传递回调用方
        const wrappedError = new Error(registerError.message || '验证码发送失败，请重试');
        wrappedError.name = 'RegistrationError';
        throw wrappedError;
      }
    }
    // 其他错误则直接抛出
    if (error.response?.data?.message !== '该邮箱已被注册') {
      // console.error('发送验证码失败:', error.response?.data || error.message);
    }
    throw error;
  }
};

export const getCurrentUser = async (): Promise<User> => {
  const response = await axiosInstance.get(`/api/auth/me`);
  return response.data;
};

export const updateRemainingReads = async (userId: string, newCount: number): Promise<void> => {
  await axiosInstance.put(`/auth/user/${userId}/readings`, { 
    remainingReads: newCount 
  });
};

// 发送重置密码邮件
export const sendResetPasswordEmail = async (email: string, language?: string) => {
  // 获取当前语言设置
  const currentLanguage = localStorage.getItem('i18nextLng') || 'zh-CN';
  const response = await axiosInstance.post('/api/auth/forgot-password', { 
    email,
    language: language || currentLanguage
  });
  return response.data;
};

// 发送重置密码验证码
export const sendResetCode = async (email: string, language?: string) => {
  // 获取当前语言设置
  const currentLanguage = localStorage.getItem('i18nextLng') || 'zh-CN';
  const response = await axiosInstance.post('/api/auth/send-reset-code', { 
    email,
    language: language || currentLanguage
  });
  return response.data;
};

// 使用验证码重置密码
export const resetPasswordWithCode = async (email: string, code: string, newPassword: string, language?: string) => {
  // 获取当前语言设置
  const currentLanguage = localStorage.getItem('i18nextLng') || 'zh-CN';
  const response = await axiosInstance.post('/api/auth/reset-password-with-code', {
    email,
    code,
    newPassword,
    language: language || currentLanguage
  });
  return response.data;
};

// 重置密码
export const resetPassword = async (token: string, newPassword: string, language?: string) => {
  // 获取当前语言设置
  const currentLanguage = localStorage.getItem('i18nextLng') || 'zh-CN';
  const response = await axiosInstance.post('/api/auth/reset-password', {
    token,
    newPassword,
    language: language || currentLanguage
  });
  return response.data;
};

// 验证重置密码验证码
export const verifyResetCode = async (email: string, code: string, language?: string) => {
  try {
    // 获取当前语言设置
    const currentLanguage = localStorage.getItem('i18nextLng') || 'zh-CN';
    const response = await axiosInstance.post('/api/auth/verify-reset-code', {
      email,
      code,
      language: language || currentLanguage
    });
    return response.data;
  } catch (error) {
    // console.error('Verify reset code error:', error);
    throw error;
  }
};

// 更新用户信息
export const updateUserInfo = async (userId: string, data: Partial<User>): Promise<User> => {
  const response = await axiosInstance.put(`/api/auth/user/${userId}`, data);
  return response.data;
};

// 发送测试邮件（仅用于开发环境）
export const sendTestEmail = async (email: string, language?: string) => {
  // 获取当前语言设置
  const currentLanguage = localStorage.getItem('i18nextLng') || 'zh-CN';
  const response = await axiosInstance.post('/api/auth/test-email', { 
    email,
    language: language || currentLanguage
  });
  return response.data;
};

// 检查用户是否已经使用过深度解析
export const checkUserDeepAnalysisUsage = async (): Promise<boolean> => {
  try {
    const response = await axiosInstance.get('/api/user/check-deep-analysis');
    return response.data.hasUsedDeepAnalysis;
  } catch (error) {
    // console.error('Error checking deep analysis usage:', error);
    // 出错时默认为已使用过，避免误授予权限
    return true;
  }
};

// 检查用户是否已经使用过追问功能
export const checkUserFollowupUsage = async (): Promise<boolean> => {
  try {
    const response = await axiosInstance.get('/api/user/check-followup-usage');
    return response.data.hasUsedFollowup;
  } catch (error) {
    // console.error('Error checking followup usage:', error);
    // 出错时默认为已使用过，避免误授予权限
    return true;
  }
};

export const checkRavenUsed = async (): Promise<boolean> => {
  try {
    const response = await axiosInstance.get('/api/user/check-raven-usage');
    return response.data.hasUsedRaven;
  } catch (error) {
    // console.error('Error checking raven usage:', error);
    // 出错时默认为已使用过，避免误授予权限
    return true;
  }
};

// 会话类型定义
export interface SessionTypes {
  basicReading: boolean;
  deepAnalysis: boolean;
  followup: boolean;
}

/**
 * 检查会话类型
 * @param sessionId 会话ID
 * @returns SessionTypes 会话类型对象
 */
export const checkSessionType = async (sessionId: string): Promise<SessionTypes> => {
  try {
    const response = await axiosInstance.get(`/api/session/types/${sessionId}`);
    return response.data.types;
  } catch (error) {
    // console.error('检查会话类型失败:', error);
    // 默认值
    return {
      basicReading: true,
      deepAnalysis: false,
      followup: false
    };
  }
};

/**
 * 提交解读反馈
 * @param feedbackData 反馈数据
 * @returns Promise
 */
export const submitReadingFeedback = async (feedbackData: {
  sessionId: string;
  rating: number;
  content: string;
  feedbackTypes?: SessionTypes;
}) => {
  try {
    const response = await axiosInstance.post('/api/reading-feedback', feedbackData);
    return response.data;
  } catch (error) {
    // console.error('提交反馈失败:', error);
    throw error;
  }
};
