/**
 * 分享截图持久化存储服务
 * 统一管理分享截图的存储路径和文件操作
 */
const fs = require('fs');
const path = require('path');

// 分享截图存储目录配置 - 固定使用持久化目录
const getShareScreenshotDir = () => {
  // 固定使用持久化目录，不再依赖环境变量
  return '/var/www/tarot/share-screenshots';
};

/**
 * 检查分享截图目录是否存在
 * @returns {boolean} 目录是否存在
 */
function ensureShareScreenshotDirExists() {
  try {
    const dir = getShareScreenshotDir();

    if (!fs.existsSync(dir)) {
      console.log(`分享截图目录不存在: ${dir}`);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`检查分享截图目录失败: ${error.message}`);
    return false;
  }
}

/**
 * 获取分享截图存储目录路径
 * @returns {string} 存储目录路径
 */
function getStorageDir() {
  return getShareScreenshotDir();
}

/**
 * 检查分享截图文件是否存在
 * @param {string} filename 文件名
 * @returns {boolean} 文件是否存在
 */
function checkFileExists(filename) {
  try {
    const filePath = path.join(getShareScreenshotDir(), filename);
    return fs.existsSync(filePath);
  } catch (error) {
    console.error(`检查文件存在性失败: ${error.message}`);
    return false;
  }
}

/**
 * 删除分享截图文件
 * @param {string} filename 文件名
 * @returns {boolean} 删除是否成功
 */
function deleteFile(filename) {
  try {
    const filePath = path.join(getShareScreenshotDir(), filename);
    
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`已删除分享截图文件: ${filename}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`删除分享截图文件失败: ${error.message}`);
    return false;
  }
}

/**
 * 获取分享截图目录的文件列表
 * @returns {string[]} 文件名列表
 */
function getFileList() {
  try {
    const dir = getShareScreenshotDir();
    
    if (!fs.existsSync(dir)) {
      return [];
    }
    
    return fs.readdirSync(dir).filter(file => {
      // 只返回图片文件
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
    });
  } catch (error) {
    console.error(`获取文件列表失败: ${error.message}`);
    return [];
  }
}

/**
 * 获取分享截图目录的统计信息
 * @returns {Object} 统计信息
 */
function getStorageStats() {
  try {
    const dir = getShareScreenshotDir();
    const files = getFileList();
    let totalSize = 0;
    
    files.forEach(file => {
      try {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        totalSize += stats.size;
      } catch (error) {
        console.error(`获取文件统计信息失败: ${file}`, error.message);
      }
    });
    
    return {
      directory: dir,
      fileCount: files.length,
      totalSize: totalSize,
      totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2)
    };
  } catch (error) {
    console.error(`获取存储统计信息失败: ${error.message}`);
    return {
      directory: getShareScreenshotDir(),
      fileCount: 0,
      totalSize: 0,
      totalSizeMB: '0.00'
    };
  }
}

/**
 * 清理过期的分享截图文件（可选功能）
 * @param {number} daysOld 保留天数，超过此天数的文件将被删除
 * @returns {Object} 清理结果
 */
function cleanupOldFiles(daysOld = 30) {
  try {
    const dir = getShareScreenshotDir();
    const files = getFileList();
    const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
    
    let deletedCount = 0;
    let deletedSize = 0;
    
    files.forEach(file => {
      try {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          deletedSize += stats.size;
          fs.unlinkSync(filePath);
          deletedCount++;
          console.log(`已删除过期文件: ${file}`);
        }
      } catch (error) {
        console.error(`删除过期文件失败: ${file}`, error.message);
      }
    });
    
    return {
      deletedCount,
      deletedSize,
      deletedSizeMB: (deletedSize / (1024 * 1024)).toFixed(2)
    };
  } catch (error) {
    console.error(`清理过期文件失败: ${error.message}`);
    return {
      deletedCount: 0,
      deletedSize: 0,
      deletedSizeMB: '0.00'
    };
  }
}

// 初始化时检查目录是否存在
ensureShareScreenshotDirExists();

module.exports = {
  getStorageDir,
  ensureShareScreenshotDirExists,
  checkFileExists,
  deleteFile,
  getFileList,
  getStorageStats,
  cleanupOldFiles
};
