-- 创建分享提交表
CREATE TABLE IF NOT EXISTS `share_submissions` (
  `id` VARCHAR(36) NOT NULL,
  `user_id` VARCHAR(36) NOT NULL,
  `share_url` TEXT NOT NULL,
  `image_url` TEXT NULL,
  `platform` VARCHAR(50) NOT NULL,
  `session_id` VARCHAR(36) NULL,
  `language` VARCHAR(10) NULL COMMENT '用户提交时使用的语言',
  `status` ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
  `reviewer_id` VARCHAR(36) NULL,
  `review_note` TEXT NULL,
  `reviewed_at` DATETIME NULL,
  `reward_granted` TINYINT(1) NOT NULL DEFAULT 0,
  `reward_granted_at` DATETIME NULL,
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_reward_granted` (`reward_granted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

