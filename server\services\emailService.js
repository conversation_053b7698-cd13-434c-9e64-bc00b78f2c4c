const nodemailer = require('nodemailer');
const StatsService = require('./statsService');
// 修改百度推送引入方式，使用try-catch避免依赖问题
let getLatestPushReport;
try {
  getLatestPushReport = require('../services/baiduUrlService').getLatestPushReport;
} catch (error) {
  console.warn('百度推送模块加载失败，将不显示百度推送数据:', error.message);
  getLatestPushReport = () => null;
}

class EmailService {
  // 存储所有可用的邮箱配置
  static emailConfigs = [];
  
  // 初始化邮箱配置
  static initEmailConfigs() {
    // 清空现有配置
    this.emailConfigs = [];
    
    // console.log('==== 初始化邮箱配置 ====');
    // console.log('检查环境变量...');
    
    // 获取所有EMAIL开头的环境变量
    const emailVars = Object.keys(process.env).filter(key => key.startsWith('EMAIL_'));
    
    // 找出所有可能的邮箱索引（包括无索引的默认邮箱）
    const indices = new Set();
    indices.add(''); // 添加默认邮箱（无索引）
    
    emailVars.forEach(key => {
      // 提取可能的索引，如 EMAIL_HOST_2 中的 "_2"
      const match = key.match(/_(\d+)$/);
      if (match) {
        indices.add(`_${match[1]}`);
      }
    });
    
    // 按索引处理每个邮箱配置
    for (const index of indices) {
      // 修改变量名格式，使用 EMAIL_HOST_2 格式而不是 EMAIL_2_HOST
      const hostKey = index ? `EMAIL_HOST${index}` : 'EMAIL_HOST';
      const portKey = index ? `EMAIL_PORT${index}` : 'EMAIL_PORT';
      const secureKey = index ? `EMAIL_SECURE${index}` : 'EMAIL_SECURE';
      const userKey = index ? `EMAIL_USER${index}` : 'EMAIL_USER';
      const passKey = index ? `EMAIL_PASS${index}` : 'EMAIL_PASS';
      const fromKey = index ? `EMAIL_FROM${index}` : 'EMAIL_FROM';
      
      const host = process.env[hostKey];
      const user = process.env[userKey];
      const pass = process.env[passKey];
      
      if (host && user && pass) {
        const config = {
          host: host,
          port: process.env[portKey],
          secure: process.env[secureKey] === 'true',
          auth: {
            user: user,
            pass: pass
          },
          from: process.env[fromKey] || user
        };
        
        this.emailConfigs.push(config);
        // console.log(`已加载邮箱配置 ${index || '(默认)'}: ${config.auth.user}`);
      } else {
        if (index === '') {
          console.warn('警告: 主邮箱配置不完整');
        } else {
          console.warn(`警告: 邮箱配置 ${index} 不完整`);
        }
        console.log(`${hostKey}:`, process.env[hostKey] ? '已设置' : '未设置');
        console.log(`${userKey}:`, process.env[userKey] ? '已设置' : '未设置');
        console.log(`${passKey}:`, process.env[passKey] ? '已设置' : '未设置');
      }
    }
    
    
    // 如果没有可用的邮箱配置，记录警告
    if (this.emailConfigs.length === 0) {
      console.warn('警告: 没有配置任何邮箱，邮件功能将不可用');
    } else {
      // console.log(`总共加载了 ${this.emailConfigs.length} 个邮箱配置`);
    }
  }
  
  // 从可用配置中随机选择一个邮箱配置，可排除已尝试过的邮箱
  static getRandomEmailConfig(excludeEmails = new Set()) {
    // 只有当配置为空时才初始化，避免重复初始化
    if (this.emailConfigs.length === 0) {
      console.log('邮箱配置为空，尝试初始化...');
      this.initEmailConfigs();
    }
    
    if (this.emailConfigs.length === 0) {
      throw new Error('没有可用的邮箱配置');
    }
    
    // 过滤出未尝试过的邮箱配置
    const availableConfigs = this.emailConfigs.filter(config => 
      !excludeEmails.has(config.auth.user)
    );
    
    // 如果所有邮箱都已尝试过，则重新使用所有邮箱
    if (availableConfigs.length === 0) {
      console.log('所有邮箱都已尝试过，重新使用所有邮箱');
      return this.emailConfigs[Math.floor(Math.random() * this.emailConfigs.length)];
    }
    
    // 如果只有一个可用配置，直接返回
    if (availableConfigs.length === 1) {
      console.log('只有一个未尝试的邮箱配置可用，使用该配置');
      return availableConfigs[0];
    }
    
    // 从未尝试过的邮箱中随机选择
    const randomIndex = Math.floor(Math.random() * availableConfigs.length);
    // console.log(`随机选择了第 ${randomIndex + 1} 个未尝试的邮箱配置，共 ${availableConfigs.length} 个可用配置`);
    
    return availableConfigs[randomIndex];
  }
  
  // 使用随机选择的邮箱配置创建传输器，可排除已尝试过的邮箱
  static createTransporter(excludeEmails = new Set()) {
    const emailConfig = this.getRandomEmailConfig(excludeEmails);
    
    return nodemailer.createTransport({
      host: emailConfig.host,
      port: emailConfig.port,
      secure: emailConfig.secure,
      auth: {
        user: emailConfig.auth.user,
        pass: emailConfig.auth.pass
      },
      // 163邮箱的优化配置
      connectionTimeout: 30000,    // 连接超时时间30秒
      greetingTimeout: 15000,     // 问候超时时间15秒
      socketTimeout: 30000,       // 套接字超时时间30秒
      debug: process.env.NODE_ENV === 'development', // 仅在开发环境启用调试
      tls: {
        rejectUnauthorized: true  // 验证SSL证书
      }
    });
  }

  static async sendMailWithRetry(mailOptions, maxRetries = 3) {
    // 初始化重试计数器
    let retries = 0;
    // 记录已尝试过的邮箱用户名，用于排除
    const triedEmails = new Set();
    
    console.log('==== 准备发送邮件 ====');
    console.log(`发送邮件至: ${mailOptions.to}`);
    console.log(`邮件主题: ${mailOptions.subject}`);
    // console.log(`可用邮箱列表: ${this.emailConfigs.map(config => config.auth.user).join(', ')}`);
    console.log('====================');
    
    // 随机选择一个邮箱配置
    const emailConfig = this.getRandomEmailConfig();
    
    // 创建邮件选项的副本
    const currentMailOptions = { ...mailOptions };
    
    // 确保发件人地址与授权用户相同（163邮箱要求）
    currentMailOptions.from = `"${currentMailOptions.from ? currentMailOptions.from.split('<')[0].trim() : 'TarotQA'}" <${emailConfig.auth.user}>`;
    
    // 增加详细日志输出
    console.log(`使用邮箱: ${emailConfig.auth.user}`);
    // console.log(`发件人设置为: ${currentMailOptions.from}`);
    
    // 使用当前选择的邮箱配置创建传输器
    const transporter = nodemailer.createTransport({
      host: emailConfig.host,
      port: emailConfig.port,
      secure: emailConfig.secure,
      auth: {
        user: emailConfig.auth.user,
        pass: emailConfig.auth.pass
      },
      // 163邮箱的优化配置
      connectionTimeout: 30000,    // 连接超时时间30秒
      greetingTimeout: 15000,     // 问候超时时间15秒
      socketTimeout: 30000,       // 套接字超时时间30秒
      debug: process.env.NODE_ENV === 'development', // 仅在开发环境启用调试
      tls: {
        rejectUnauthorized: true  // 验证SSL证书
      }
    });
    
    // console.log(`尝试使用邮箱 ${emailConfig.auth.user} 发送邮件...`);
    await transporter.verify();
    const info = await transporter.sendMail(currentMailOptions);
    console.log(`邮件发送成功(使用邮箱: ${emailConfig.auth.user}):`, info.messageId);
    return info;
  }

  static async sendStatsReport(recipientEmails) {
    try {
      // 如果传入的是字符串，则按逗号分隔转换为数组
      const recipients = Array.isArray(recipientEmails) 
        ? recipientEmails 
        : recipientEmails.split(',').map(email => email.trim());

      const stats = await StatsService.getDailyStats();
      
      // 获取百度URL推送报告，使用try-catch避免错误中断
      let baiduPushReport = null;
      try {
        baiduPushReport = getLatestPushReport();
      } catch (error) {
        console.error('获取百度推送报告失败:', error);
        // 出错时返回null，不影响邮件其他部分的生成
        baiduPushReport = null;
      }
      
      // 将毫秒转换为秒的辅助函数
      const msToSeconds = ms => (ms / 1000).toFixed(2);
      
      // 格式化金额的辅助函数
      const formatCurrency = amount => {
        return parseFloat(amount || 0).toFixed(2);
      };
      
      // 将支付方式转换为中文
      const getPaymentMethodName = method => {
        const methodMap = {
          'wechat': '微信',
          'alipay': '支付宝',
          'paypal': 'PayPal'
        };
        return methodMap[method] || method;
      };
      
      // 格式化百度推送报告HTML
      const baiduPushHtml = baiduPushReport ? `
        <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">百度搜索引擎URL推送统计</h3>
          <ul style="padding-left: 20px; line-height: 1.6;">
          <li>推送日期：${baiduPushReport.date}</li>
            <li>已推送URL总数：<span style="font-weight: bold;">${baiduPushReport.total}个</span></li>
          <li>成功推送数量：${baiduPushReport.success}个</li>
          <li>剩余推送配额：${baiduPushReport.remain}个</li>
        </ul>
        
        ${baiduPushReport.urls && baiduPushReport.urls.length > 0 ? `
            <h4 style="color: #2c3e50; margin-bottom: 10px; margin-top: 15px;">已推送URL列表</h4>
            <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 10px; background-color: white; margin-bottom: 15px;">
              <ol style="padding-left: 25px; margin: 0; line-height: 1.5;">
                ${baiduPushReport.urls.map(url => `<li style="padding: 3px 0; word-break: break-all; font-size: 13px;">${url}</li>`).join('')}
            </ol>
          </div>
          ` : '<p style="color: #7f8c8d; font-style: italic;">无URL推送记录</p>'}
        </div>
      ` : `
        <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">百度搜索引擎URL推送统计</h3>
          <p style="color: #7f8c8d; font-style: italic;">近期尚未进行百度URL推送</p>
        </div>
      `;

      // 格式化统计数据为 HTML
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; color: #333;">
          <h2 style="color: #2c3e50; border-bottom: 2px solid #ecf0f1; padding-bottom: 10px; margin-top: 25px;">每日数据统计 - ${stats.date}</h2>
        
        
          <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">用户数据</h3>
            <div style="display: flex; flex-wrap: wrap; justify-content: space-between;">
              <div style="flex: 1; min-width: 250px; margin-right: 15px;">
                <h4 style="color: #2c3e50; margin-bottom: 10px;">总用户数据</h4>
                <ul style="padding-left: 20px; line-height: 1.6;">
                  <li>总用户数：<span style="font-weight: bold;">${stats.users.total.total}</span></li>
          <li>普通用户数：${stats.users.total.normal}</li>
          <li>VIP用户数：${stats.users.total.vip}</li>
        </ul>
              </div>
              <div style="flex: 1; min-width: 250px;">
                <h4 style="color: #2c3e50; margin-bottom: 10px;">新增用户数据</h4>
                <ul style="padding-left: 20px; line-height: 1.6;">
                  <li>新增用户数：<span style="font-weight: bold;">${stats.users.new.total}</span></li>
          <li>新增普通用户数：${stats.users.new.normal}</li>
          <li>新增VIP用户数：${stats.users.new.vip}</li>
          <li>新增用户中羊毛用户数：<span style="color: #e74c3c;">${stats.users.new.zeroRemainingReads}</span></li>
        </ul>
              </div>
            </div>
          </div>
        
          <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">Sessions数据</h3>
        <div style="margin-left: 20px;">
              <p>总新增Sessions：<span style="font-weight: bold;">${stats.sessions.total}</span></p>
              <p>安全检测数 (基础解读)：<span style="color: #e74c3c; font-weight: bold;">${stats.sessions.byStatus.ethical_intervention || 0}</span></p>
              <p>安全检测数 (追问)：<span style="color: #e74c3c; font-weight: bold;">${stats.sessions.byStatus.ethical_intervention_follow || 0}</span></p>
              <p>潜在安全问题 (基础解读)：<span style="color: #e67e22; font-weight: bold;">${stats.sessions.byStatus.potential_ethical_issue || 0}</span></p>
              <p>潜在安全问题 (追问)：<span style="color: #e67e22; font-weight: bold;">${stats.sessions.byStatus.potential_ethical_issue_follow || 0}</span></p>
              <h5 style="color: #2c3e50; margin-bottom: 10px;">状态分布</h5>
              <ul style="padding-left: 20px; line-height: 1.6;">
                <li>初始状态 (pending)：${stats.sessions.byStatus.pending}</li>
                <li>已选择占卜师 (reader_selected)：${stats.sessions.byStatus.reader_selected || 0}</li>
                <li>已选择牌阵 (spread_selected)：${stats.sessions.byStatus.spread_selected || 0}</li>
                <li>已选择卡牌 (cards_selected)：${stats.sessions.byStatus.cards_selected || 0}</li>
                <li>完成状态 (completed)：${stats.sessions.byStatus.completed}</li>
                <li>失败状态 (failed)：${stats.sessions.byStatus.failed}</li>
          </ul>
            </div>
        </div>
        
          <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">基础解读 API 调用统计</h3>
        ${stats.sessions.llmStats.length > 0 ? `
              <table style="width: 100%; border-collapse: collapse; margin: 10px 0; background-color: white; border-radius: 4px; overflow: hidden;">
                <tr style="background-color: #e8f4fd;">
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">调用次数</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输入Token</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输出Token</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输入成本(¥)</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输出成本(¥)</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">总成本(¥)</th>
                </tr>
                ${stats.sessions.llmStats.map(stat => `
                  <tr>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.calls}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.inputTokens || 0}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.outputTokens || 0}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">${formatCurrency(stat.inputCost)}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">${formatCurrency(stat.outputCost)}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c; font-weight: bold;">${formatCurrency(stat.totalCost)}</td>
                  </tr>
                `).join('')}
                <tr style="background-color: #f8f9fa;">
                  <td colspan="3" style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-style: italic;">单价(每千Token)</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">¥0.0008</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">¥0.002</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;"></td>
                </tr>
              </table>
            ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有API调用记录</p>'}
            
            <h3 style="color: #3498db; margin-top: 15px; border-bottom: 1px solid #ddd; padding-bottom: 8px;">日运解读 API 调用统计</h3>
            ${stats.sessions.dailyFortuneStats && stats.sessions.dailyFortuneStats.length > 0 ? `
              <table style="width: 100%; border-collapse: collapse; margin: 10px 0; background-color: white; border-radius: 4px; overflow: hidden;">
                <tr style="background-color: #e8f4fd;">
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">调用次数</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输入Token</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输出Token</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输入成本(¥)</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输出成本(¥)</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">总成本(¥)</th>
                </tr>
                ${stats.sessions.dailyFortuneStats.map(stat => `
                  <tr>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.calls}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.inputTokens || 0}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.outputTokens || 0}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">${formatCurrency(stat.inputCost)}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">${formatCurrency(stat.outputCost)}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c; font-weight: bold;">${formatCurrency(stat.totalCost)}</td>
                  </tr>
                `).join('')}
                <tr style="background-color: #f8f9fa;">
                  <td colspan="3" style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-style: italic;">单价(每千Token)</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">¥0.0003</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">¥0.0006</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;"></td>
                </tr>
              </table>
            ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有日运解读API调用记录</p>'}
            
            <h3 style="color: #3498db; margin-top: 15px; border-bottom: 1px solid #ddd; padding-bottom: 8px;">安全检测 API 调用统计</h3>
            ${stats.sessions.ethicalStats && stats.sessions.ethicalStats.length > 0 ? `
              <table style="width: 100%; border-collapse: collapse; margin: 10px 0; background-color: white; border-radius: 4px; overflow: hidden;">
                <tr style="background-color: #e8f4fd;">
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">调用次数</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输入Token</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输出Token</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输入成本(¥)</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输出成本(¥)</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">总成本(¥)</th>
                </tr>
                ${stats.sessions.ethicalStats.map(stat => `
                  <tr>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.calls}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.inputTokens || 0}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.outputTokens || 0}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">${formatCurrency(stat.inputCost)}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">${formatCurrency(stat.outputCost)}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c; font-weight: bold;">${formatCurrency(stat.totalCost)}</td>
                  </tr>
                `).join('')}
                <tr style="background-color: #f8f9fa;">
                  <td colspan="3" style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-style: italic;">单价(每千Token)</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">¥0.0003</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">¥0.0006</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;"></td>
                </tr>
              </table>
            ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有安全检测API调用记录</p>'}
            
            <h3 style="color: #3498db; margin-top: 15px; border-bottom: 1px solid #ddd; padding-bottom: 8px;">牌阵推荐 API 调用统计</h3>
            ${stats.sessions.spreadRecommendationStats && stats.sessions.spreadRecommendationStats.length > 0 ? `
              <table style="width: 100%; border-collapse: collapse; margin: 10px 0; background-color: white; border-radius: 4px; overflow: hidden;">
                <tr style="background-color: #e8f4fd;">
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">调用次数</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输入Token</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输出Token</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输入成本(¥)</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输出成本(¥)</th>
                  <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">总成本(¥)</th>
                </tr>
                ${stats.sessions.spreadRecommendationStats.map(stat => `
                  <tr>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.calls}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.inputTokens || 0}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.outputTokens || 0}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">${formatCurrency(stat.inputCost)}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">${formatCurrency(stat.outputCost)}</td>
                    <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c; font-weight: bold;">${formatCurrency(stat.totalCost)}</td>
                  </tr>
                `).join('')}
                <tr style="background-color: #f8f9fa;">
                  <td colspan="3" style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-style: italic;">单价(每千Token)</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">¥0.0003</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">¥0.0006</td>
                  <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;"></td>
                </tr>
              </table>
            ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有牌阵推荐API调用记录</p>'}
          </div>
          
          <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">支付订单统计</h3>
        ${stats.orders && stats.orders.total ? `
              <p>总订单数：<span style="font-weight: bold;">${stats.orders.total.count}</span></p>
              <p>人民币订单：<span style="font-weight: bold;">${stats.orders.total.countCNY}</span> 单，总金额：<span style="font-weight: bold; color: #e74c3c;">¥${formatCurrency(stats.orders.total.amountCNY)}</span></p>
              <p>美元订单：<span style="font-weight: bold;">${stats.orders.total.countUSD}</span> 单，总金额：<span style="font-weight: bold; color: #e74c3c;">$${formatCurrency(stats.orders.total.amountUSD)}</span></p>
          
          ${stats.orders.byPaymentMethod && stats.orders.byPaymentMethod.length > 0 ? `
                <h4 style="color: #2c3e50; margin-bottom: 10px; margin-top: 15px;">按支付渠道分布</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0; background-color: white; border-radius: 4px; overflow: hidden;">
                  <tr style="background-color: #e8f4fd;">
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: left; font-weight: 600;">支付渠道</th>
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">总订单数</th>
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">人民币订单数</th>
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">人民币金额</th>
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">美元订单数</th>
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">美元金额</th>
              </tr>
              ${stats.orders.byPaymentMethod.map(stat => `
                <tr>
                      <td style="padding: 10px; border: 1px solid #d3e0ea;">${getPaymentMethodName(stat.method)}</td>
                      <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.count}</td>
                      <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.countCNY}</td>
                      <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">¥${formatCurrency(stat.amountCNY)}</td>
                      <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.countUSD}</td>
                      <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">$${formatCurrency(stat.amountUSD)}</td>
                </tr>
              `).join('')}
            </table>
              ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有支付渠道数据</p>'}
            ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有成功支付的订单</p>'}
          </div>
          
          <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">用户反馈统计</h3>
        ${stats.feedback && stats.feedback.total > 0 ? `
              <ul style="padding-left: 20px; line-height: 1.6;">
                <li>新增反馈总数：<span style="font-weight: bold;">${stats.feedback.total}</span></li>
            <li>按类型分类：
                  <ul style="padding-left: 20px; margin-top: 5px;">
                <li>问题反馈：${stats.feedback.byType.problem || 0}</li>
                <li>建议反馈：${stats.feedback.byType.suggestion || 0}</li>
                <li>其他反馈：${stats.feedback.byType.other || 0}</li>
              </ul>
            </li>
          </ul>
            ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有新增反馈</p>'}
          </div>
          
          <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">用户解读反馈统计</h3>
            ${stats.readingFeedback && stats.readingFeedback.total > 0 ? `
              <ul style="padding-left: 20px; line-height: 1.6;">
                <li>新增解读反馈总数：<span style="font-weight: bold;">${stats.readingFeedback.total}</span></li>
              </ul>
            ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有新增解读反馈</p>'}
          </div>
          
          <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">TTS语音缓存统计</h3>
        ${stats.ttsCache && stats.ttsCache.total ? `
              <ul style="padding-left: 20px; line-height: 1.6;">
                <li>总字符数：<span style="font-weight: bold;">${stats.ttsCache.total.totalCharacters.toLocaleString('zh-CN')}</span></li>
            <li>免费语音字符数：${stats.ttsCache.total.freeCharacters.toLocaleString('zh-CN')}</li>
            <li>付费语音字符数：${stats.ttsCache.total.paidCharacters.toLocaleString('zh-CN')}</li>
                <li>总成本：<span style="font-weight: bold; color: #e74c3c;">¥${formatCurrency(stats.ttsCache.total.totalCost)}</span></li>
          </ul>
          
          ${stats.ttsCache.byCategory && stats.ttsCache.byCategory.length > 0 ? `
                <h4 style="color: #2c3e50; margin-bottom: 10px; margin-top: 15px;">按内容分类</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0; background-color: white; border-radius: 4px; overflow: hidden;">
                  <tr style="background-color: #e8f4fd;">
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: left; font-weight: 600;">分类</th>
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">生成次数</th>
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">总字符数</th>
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">免费字符数</th>
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">付费字符数</th>
                    <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">成本</th>
              </tr>
              ${stats.ttsCache.byCategory.map(stat => `
                <tr>
                      <td style="padding: 10px; border: 1px solid #d3e0ea;">${stat.category}</td>
                      <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.count}</td>
                      <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.totalCharacters.toLocaleString('zh-CN')}</td>
                      <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.freeCharacters.toLocaleString('zh-CN')}</td>
                      <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${stat.paidCharacters.toLocaleString('zh-CN')}</td>
                      <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">¥${formatCurrency(stat.cost)}</td>
                </tr>
              `).join('')}
            </table>
              ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有分类数据</p>'}
            ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有语音生成数据</p>'}
          </div>
          
          <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">博客阅读统计</h3>
            ${stats.blogReadings && stats.blogReadings.total ? `
              <div style="margin-left: 20px;">
                <p>总阅读次数：<span style="font-weight: bold;">${stats.blogReadings.total.count}</span></p>
                <ul style="padding-left: 20px; line-height: 1.6;">
                  <li>总输入Token数：${stats.blogReadings.total.inputTokens.toLocaleString('zh-CN')}</li>
                  <li>总输出Token数：${stats.blogReadings.total.outputTokens.toLocaleString('zh-CN')}</li>
                  <li>总成本：<span style="font-weight: bold; color: #e74c3c;">¥${formatCurrency(stats.blogReadings.total.cost)}</span>
                    <small style="color: #7f8c8d;">（输入：¥${formatCurrency(stats.blogReadings.total.inputTokens / 1000 * 0.0003)}，输出：¥${formatCurrency(stats.blogReadings.total.outputTokens / 1000 * 0.0006)}）</small>
                  </li>
                </ul>
                
                ${stats.blogReadings.byBlog && stats.blogReadings.byBlog.length > 0 ? `
                  <h4 style="color: #2c3e50; margin-bottom: 10px; margin-top: 15px;">按博客ID分布</h4>
                  <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse; margin: 10px 0; background-color: white; border-radius: 4px; overflow: hidden;">
                      <tr style="background-color: #e8f4fd;">
                        <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: left; font-weight: 600;">博客ID</th>
                        <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">阅读次数</th>
                        <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输入Token</th>
                        <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">输出Token</th>
                        <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">成本</th>
                      </tr>
                      ${stats.blogReadings.byBlog.map(blog => `
                        <tr>
                          <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: left;">${blog.blogId}</td>
                          <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${blog.count}</td>
                          <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${blog.inputTokens.toLocaleString('zh-CN')}</td>
                          <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${blog.outputTokens.toLocaleString('zh-CN')}</td>
                          <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #e74c3c;">¥${formatCurrency(blog.cost)}</td>
                        </tr>
                      `).join('')}
                    </table>
                  </div>
                ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有博客阅读数据</p>'}
              </div>
            ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有博客阅读数据</p>'}
          </div>

          <div style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="color: #3498db; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px;">分享统计</h3>
            ${stats.shares && stats.shares.total ? `
              <div style="margin-left: 20px;">
                <p>总分享提交数：<span style="font-weight: bold;">${stats.shares.total.submissions}</span></p>
                <ul style="padding-left: 20px; line-height: 1.6;">
                  <li>待审核：<span style="color: #f39c12; font-weight: bold;">${stats.shares.total.pending}</span></li>
                  <li>已通过：<span style="color: #27ae60; font-weight: bold;">${stats.shares.total.approved}</span></li>
                  <li>已拒绝：<span style="color: #e74c3c; font-weight: bold;">${stats.shares.total.rejected}</span></li>
                  <li>已发放奖励：<span style="color: #8e44ad; font-weight: bold;">${stats.shares.total.rewardsGranted}</span></li>
                </ul>

                ${stats.shares.byPlatform && stats.shares.byPlatform.length > 0 ? `
                  <h4 style="color: #2c3e50; margin-bottom: 10px; margin-top: 15px;">按平台分布</h4>
                  <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse; margin: 10px 0; background-color: white; border-radius: 4px; overflow: hidden;">
                      <tr style="background-color: #e8f4fd;">
                        <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: left; font-weight: 600;">平台</th>
                        <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">提交数</th>
                        <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">通过数</th>
                        <th style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; font-weight: 600;">奖励数</th>
                      </tr>
                      ${stats.shares.byPlatform.map(platform => `
                        <tr>
                          <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: left;">${platform.platform}</td>
                          <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right;">${platform.submissions}</td>
                          <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #27ae60;">${platform.approved}</td>
                          <td style="padding: 10px; border: 1px solid #d3e0ea; text-align: right; color: #8e44ad;">${platform.rewards}</td>
                        </tr>
                      `).join('')}
                    </table>
                  </div>
                ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有平台分布数据</p>'}
              </div>
            ` : '<p style="color: #7f8c8d; font-style: italic;">该时间段内没有分享数据</p>'}
          </div>

          ${baiduPushHtml}
        
          <hr style="margin: 30px 0; border: 0; height: 1px; background-color: #ecf0f1;">
          <p style="color: #7f8c8d; font-size: 12px; text-align: center;">
          此邮件为系统自动发送，请勿直接回复。如需帮助，请联系技术支持。
        </p>
        </div>
      `;

      // 发送邮件给所有收件人
      await this.sendMailWithRetry({
        from: process.env.EMAIL_FROM,
        to: recipients.join(','),
        subject: `每日数据统计 - ${stats.date}`,
        html: html
      });

      return true;
    } catch (error) {
      console.error('Error sending daily stats email:', error);
      throw error;
    }
  }

  
  /**
   * 手动触发发送统计邮件
   * @param {string|Array<string>} emails 收件人邮箱，可以是单个邮箱或邮箱数组
   * @returns {Promise<boolean>} 发送结果
   */
  static async sendTestStatsEmail(emails) {
    try {
      if (!emails || (Array.isArray(emails) && emails.length === 0)) {
        throw new Error('收件人邮箱不能为空');
      }
      
      console.log('正在手动发送统计邮件测试...');
      const result = await this.sendStatsReport(emails);
      console.log('统计邮件发送成功!');
      return result;
    } catch (error) {
      console.error('发送测试统计邮件失败:', error);
      throw error;
    }
  }

  // 验证所有邮箱配置，但不随机选择
  static async verifyAllConfigs() {
    // console.log('开始验证所有邮箱配置...');
    
    if (this.emailConfigs.length === 0) {
      console.warn('没有可用的邮箱配置，无法验证');
      return;
    }
    
    for (let i = 0; i < this.emailConfigs.length; i++) {
      const config = this.emailConfigs[i];
      try {
        // console.log(`正在验证第 ${i + 1} 个邮箱配置: ${config.auth.user}`);
        
        const transporter = nodemailer.createTransport({
          host: config.host,
          port: config.port,
          secure: config.secure,
          auth: {
            user: config.auth.user,
            pass: config.auth.pass
          },
          connectionTimeout: 30000,
          greetingTimeout: 15000,
          socketTimeout: 30000,
          debug: process.env.NODE_ENV === 'development',
          tls: {
            rejectUnauthorized: true
          }
        });
        
        await transporter.verify();
        // console.log(`邮箱 ${config.auth.user} 验证成功`);
      } catch (error) {
        console.error(`邮箱 ${config.auth.user} 验证失败:`, error.message);
      }
    }
    
    console.log('所有邮箱配置验证完成');
  }
}

// 移除自动初始化代码，由server/index.js控制初始化时机
// EmailService.initEmailConfigs();

module.exports = EmailService; 