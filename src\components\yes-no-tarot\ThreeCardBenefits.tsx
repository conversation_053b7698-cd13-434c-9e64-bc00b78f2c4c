import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

const ThreeCardBenefits: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <div className="mt-24 sm:mt-32">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('yes_no_tarot.three_card_benefits.title', 'Benefits of Three Card Tarot Reading Yes No')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
      </div>
      
      {/* Benefits Grid */}
      <div className="max-w-5xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Complete Context */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">🔄</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.three_card_benefits.complete_context_title', 'Complete Context')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.three_card_benefits.complete_context_description', 
                'Unlike single card readings, three cards provide the full story behind your yes or no answer.')}
            </p>
          </div>
          
          {/* Strategic Insight */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">🧩</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.three_card_benefits.strategic_insight_title', 'Strategic Insight')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.three_card_benefits.strategic_insight_description', 
                'Understand what supports your goals and what challenges you\'ll face, allowing better preparation.')}
            </p>
          </div>
          
          {/* Timing Awareness */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">⏱️</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.three_card_benefits.timing_awareness_title', 'Timing Awareness')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.three_card_benefits.timing_awareness_description', 
                'Discover whether obstacles are temporary or if you need to wait for better cosmic alignment.')}
            </p>
          </div>
          
          {/* Action Guidance */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">🚀</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.three_card_benefits.action_guidance_title', 'Action Guidance')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.three_card_benefits.action_guidance_description', 
                'Learn specific steps to enhance positive outcomes or avoid negative consequences.')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThreeCardBenefits; 