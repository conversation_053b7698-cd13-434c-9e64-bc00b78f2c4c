{"app": {"name": "TarotQA"}, "auth": {"validation": {"invalidEmail": "Please enter a valid email address", "invalidPassword": "Password can only contain letters, numbers, and underscores", "emailExists": "This email has already been registered", "usernameExists": "This username is already in use", "emailNotExists": "This email is not registered", "invalidCredentials": "Incorrect email or password", "invalidCode": "Invalid or expired verification code", "invalidToken": "Reset link has expired, please request a new one", "userNotExists": "User does not exist", "unauthorized": "You are not authorized to update other users' information", "emailNotReceivable": "Email address does not exist or cannot receive emails, please check and try again", "provideEmail": "Please provide an email address", "userIdEmailMismatch": "User ID and email do not match"}, "success": {"codeSent": "Verification code has been sent to your email", "emailVerified": "Email verification successful", "passwordReset": "Password reset successful", "resetLinkSent": "Password reset link has been sent to your email", "resetCodeSent": "Password reset code has been sent to your email", "codeVerified": "Verification code verified successfully", "testEmailSent": "Test email has been sent"}, "error": {"registration": "Registration failed, please try again later", "verification": "Verification failed, please try again later", "login": "<PERSON><PERSON> failed, please try again later", "getUserInfo": "Failed to get user information", "sendResetEmail": "Failed to send password reset email, please try again later", "resetPassword": "Failed to reset password, please try again later", "sendCode": "Failed to send verification code, please try again later", "updateUserInfo": "Failed to update user information", "testEmailFailed": "Failed to send test email", "authFailed": "Authentication failed"}, "registration": {"expiredInfo": "Registration information has expired, please register again", "expiredCode": "Verification code has expired, please register again", "expiredVerification": "Verification code has expired, please register again", "incorrectCode": "Incorrect verification code, please try again"}, "email": {"registerSubject": "Verify Your Email", "registerTitle": "Welcome to TarotQA", "registerCodeMessage": "Your verification code is:", "registerCodeExpiry": "This code is valid for 30 minutes. If you did not request this, please ignore this email.", "resetPasswordSubject": "Reset Your Password", "resetPasswordTitle": "Password Reset", "resetPasswordGreeting": "Hello, we received a request to reset your password.", "resetPasswordInstruction": "Please click the link below to reset your password:", "resetPasswordExpiry": "This link is valid for 1 hour. If you did not request a password reset, please ignore this email.", "resetCodeSubject": "Password Reset Code", "resetCodeMessage": "Your verification code is: {{code}}", "resetCodeExpiry": "This code is valid for 30 minutes. If you did not request this, please ignore this email.", "testSubject": "Test Email", "testTitle": "Test Email", "testMessage": "This is a test email to verify that the email service is working properly.", "timestamp": "Sent at: {{time}}", "shareReviewApprovedSubject": "Your Share Has Been Approved", "shareReviewApprovedTitle": "Share Review Approved", "shareReviewApprovedGreeting": "Congratulations! Your submitted share content has been approved.", "shareReviewApprovedMessage": "Thank you for sharing your tarot experience with us. Your share will help more users understand and experience the charm of tarot divination.", "shareReviewApprovedReward": "As a token of our appreciation, we have granted you the corresponding reward. Please log in to your account to view the details.", "shareReviewApprovedNote": "Review Note: {{note}}", "shareReviewRejectedSubject": "Your Share Was Not Approved", "shareReviewRejectedTitle": "Share Review Result", "shareReviewRejectedGreeting": "We're sorry, but your submitted share content did not pass our review.", "shareReviewRejectedMessage": "We greatly appreciate your participation, but your share may not meet our community guidelines or quality standards.", "shareReviewRejectedEncouragement": "Please don't be discouraged. You can resubmit share content that meets our requirements. If you have any questions, please contact our customer service team: <EMAIL>.", "shareReviewRejectedNote": "Review Note: {{note}}", "footer": "This email was sent automatically. Please do not reply."}}, "session": {"userNotExists": "User does not exist", "noRemainingReads": "You have used all your free readings, please upgrade to VIP to continue"}, "stats": {"updateFailed": "Failed to update user statistics", "getFailed": "Failed to get user statistics", "getApiFailed": "Failed to get API statistics"}, "payment": {"invalidAmount": "Invalid payment amount", "incompleteInfo": "Incomplete product information", "invalidOrder": "Invalid order number", "success": "Success", "signatureVerificationFailed": "Signature verification failed", "invalidTradeStatus": "Invalid trade status", "invalidAmountFormat": "Invalid amount format", "orderProcessed": "Order has been processed"}, "middleware": {"unauthorized": "Unauthorized access", "invalidToken": "Invalid token"}, "reading": {"cards": {"major": {"0": "The Fool", "1": "The Magician", "2": "The High Priestess", "3": "The Empress", "4": "The Emperor", "5": "The Hierophant", "6": "The Lovers", "7": "The Chariot", "8": "Strength", "9": "The Hermit", "10": "Wheel of Fortune", "11": "Justice", "12": "The Hanged Man", "13": "Death", "14": "Temperance", "15": "The Devil", "16": "The Tower", "17": "The Star", "18": "The Moon", "19": "The Sun", "20": "Judgement", "21": "The World"}, "wands": {"ace": "Ace of Wands", "2": "Two of Wands", "3": "Three of Wands", "4": "Four of Wands", "5": "Five of Wands", "6": "Six of Wands", "7": "Seven of Wands", "8": "Eight of Wands", "9": "Nine of Wands", "10": "Ten of Wands", "page": "Page of Wands", "knight": "Knight of Wands", "queen": "Queen of Wands", "king": "King of Wands"}, "cups": {"ace": "Ace of Cups", "2": "Two of Cups", "3": "Three of Cups", "4": "Four of Cups", "5": "Five of Cups", "6": "Six of Cups", "7": "Seven of Cups", "8": "Eight of Cups", "9": "Nine of Cups", "10": "Ten of Cups", "page": "Page of Cups", "knight": "Knight of Cups", "queen": "Queen of Cups", "king": "King of Cups"}, "swords": {"ace": "Ace of Swords", "2": "Two of Swords", "3": "Three of Swords", "4": "Four of Swords", "5": "Five of Swords", "6": "Six of Swords", "7": "Seven of Swords", "8": "Eight of Swords", "9": "Nine of Swords", "10": "Ten of Swords", "page": "Page of Swords", "knight": "Knight of Swords", "queen": "Queen of Swords", "king": "King of Swords"}, "pentacles": {"ace": "Ace of Pentacles", "2": "Two of Pentacles", "3": "Three of Pentacles", "4": "Four of Pentacles", "5": "Five of Pentacles", "6": "Six of Pentacles", "7": "Seven of Pentacles", "8": "Eight of Pentacles", "9": "Nine of Pentacles", "10": "Ten of Pentacles", "page": "Page of Pentacles", "knight": "Knight of Pentacles", "queen": "Queen of Pentacles", "king": "King of Pentacles"}}}, "spreads": {"time_flow": {"name": "Time Flow Spread", "description": "A versatile divination spread that interprets events from a temporal dimension, suitable for single event interpretation and future prediction", "positions": {"past": "Past", "present": "Present", "future": "Future"}}, "single_card": {"name": "Single Card Reading", "description": "A beginner-friendly divination method, suitable for yes/no questions and daily fortune predictions", "positions": {"current": "Current Situation"}}, "yes_no": {"name": "Yes/No Spread", "description": "Determines 'yes', 'no', or 'neutral' based on each card's meaning. Three 'yes' cards indicate a positive answer, two 'yes' cards plus one 'no' or 'neutral' card suggests high possibility but needs time, all 'no' cards or a mix of 'no' and 'neutral' cards indicate a negative answer. Suitable for yes/no questions", "positions": {"answer": "Card One", "reason": "Card Two", "obstacle": "Card Three"}}, "problem_solving": {"name": "Problem Solving Spread", "description": "A beginner-friendly versatile spread, easy to use, suitable for single event interpretation and finding solutions", "positions": {"cause": "Problem's Root Cause", "current": "Problem's Current State", "solution": "Problem Solution Method"}}, "diamond": {"name": "Diamond Spread", "description": "A versatile predictive spread that interprets the cause and current state of events, predicting future developments, suitable for single event analysis and future prediction", "positions": {"cause": "Event's Root Cause", "current": "Event's Current State", "situation": "Current Situation", "future": "Future Outcome"}}, "core_insight": {"name": "Core Insight Spread", "description": "An advanced problem-solving spread that helps querent get to the core of the issue, suitable for deep problem interpretation and breakthrough", "positions": {"essence": "Problem Essence", "weakness": "Obstacles and Weaknesses", "solution": "Problem Solution", "strength": "Resources and Strengths"}}, "X_opportunity": {"name": "X Opportunity Spread", "description": "Helps querent understand timing and personal capabilities, aiding in decision-making, suitable for timing & on-the-spot decisions", "positions": {"mindset": "Current Mindset", "timing": "Present Timing", "probability": "Success Rate", "factors": "Impact Factors", "result": "Future Result"}}, "celtic_cross": {"name": "Celtic Cross Spread", "description": "Classic cross spread that thoroughly analyzes various aspects of the question", "positions": {"current": "Current Situation", "challenge": "Challenge", "past": "Past Foundation", "passing": "Passing Phase", "expectation": "Hopes", "future": "Future", "attitude": "Personal Attitude", "environment": "External Environment", "hopes_fears": "Hopes and Fears", "outcome": "Final Outcome"}}, "binary_choice": {"name": "Binary Choice Spread", "description": "Analyzes pros and cons of two choices and their final outcomes, helping querent make decisions in various fields, suitable for solving 'either/or' decision problems", "positions": {"current": "Current Problem State", "choice_a_short": "Choice A Short-term Development", "choice_b_short": "Choice B Short-term Development", "choice_a_final": "Choice A Final Outcome", "choice_b_final": "Choice B Final Outcome"}}, "triple_choice": {"name": "Triple Choice Spread", "description": "Analyzes the stakes and relationships of three different options, weighs pros and cons to help querent make the final choice. Suitable for three-way decisions", "positions": {"current": "Querent's Current State", "choice_a": "Choice A Development", "choice_b": "Choice B Development", "choice_c": "Choice C Development", "result_a": "Choice A Outcome", "result_b": "Choice B Outcome", "result_c": "Choice C Outcome"}}, "daily_tree": {"name": "Daily Fortune Spread", "description": "Predicts daily fortune and alerts querent to potential issues, suitable for daily fortune prediction", "positions": {"past": "Fortune Theme", "current": "Fortune Status", "advice": "Potential Issues", "result": "Action Advice"}}, "weekly_fortune": {"name": "Weekly Fortune Spread", "description": "Predicts fortune and themes for the coming week, alerting querent to potential issues to avoid trouble. Suitable for weekly fortune prediction", "positions": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "theme": "Weekly Theme"}}, "monthly_fortune": {"name": "Monthly Fortune Spread", "description": "Predicts fortune and themes for the coming month, alerting querent to potential issues to avoid trouble and seize opportunities. Suitable for monthly fortune prediction", "positions": {"week1": "First Week", "week2": "Second Week", "week3": "Third Week", "week4": "Fourth Week", "overview": "Monthly Overview"}}, "future_development": {"name": "Future Development Spread", "description": "Predicts querent's development in four aspects: love, career/study, wealth, and achievement for the coming year. Suitable for yearly fortune prediction", "positions": {"current": "Querent's Current State", "love": "Love Development", "career": "Career or Study Development", "wealth": "Wealth Development", "achievement": "Achievement", "result": "Final Result"}}, "body_mind_spirit": {"name": "Body Mind Spirit Spread", "description": "Helps querent seek answers within, deepen self-understanding and dispel confusion. Suitable for self-awareness and exploratory growth", "positions": {"body": "Body", "mind": "Mind", "spirit": "Spirit", "improvement": "Elements for Growth"}}, "four_elements_self": {"name": "Four Elements Self Spread", "description": "Helps querent thoroughly examine self from four aspects: action, material, rational, and emotional. Suitable for self-awareness", "positions": {"action": "Action & Confidence Aspect", "material": "Reality & Money Aspect", "rational": "Rationality & Decision Aspect", "emotional": "Emotion & Sensitivity Aspect"}}, "self_exploration": {"name": "Self Exploration Spread", "description": "Self-cultivation growth spread, helps querent know themselves, develop potential, break through bottlenecks, and continuously grow. Suitable for self-awareness and potential development", "positions": {"inner": "Inner Depths", "spiritual": "Spiritual Life", "knowledge": "Knowledge Domain", "emotional": "Emotional Life"}}, "self_breakthrough": {"name": "Self Breakthrough Spread", "description": "Helps querent seek breakthrough in recurring fixed patterns, suitable for self-breakthrough", "positions": {"pattern": "Your Pattern", "challenge": "Your Challenge", "breakthrough": "Your Breakthrough", "strength": "Your Strength", "future": "Your Future"}}, "future_lover": {"name": "Future Lover Spread", "description": "Predicts querent's destined partner, helps better identify the right person, and promotes the arrival of a beautiful relationship. Suitable for predicting destined relationships", "positions": {"personality": "What kind of person is your destined one", "appeared": "Have they appeared in your life", "sign": "What signs will indicate when you meet them", "attraction": "How to attract them to come closer", "challenge": "What challenges you'll face together", "gain": "What you'll gain from being with them", "future": "Future development trends"}}, "love_cross": {"name": "Love Cross Spread", "description": "Love divination spread, analyzes the mindset of both parties in love, indicates intimate relationship development trends, suitable for couple relationship analysis & intimate relationship management", "positions": {"self_state": "Your state and thoughts", "other_attitude": "Their attitude towards you", "relationship_status": "Current state of this relationship", "problems": "Existing problems", "future": "Future development"}}, "inspiration_corr": {"name": "Inspiration Correspondence Spread", "description": "Helps querent analyze both parties' true thoughts about the interpersonal relationship, suitable for analyzing interpersonal relationships", "positions": {"self_thoughts": "Your thoughts about them", "self_view": "Your view of this relationship", "self_expectation": "Your expectations for future development", "other_thoughts": "Their thoughts about you", "other_view": "Their view of this relationship", "other_expectation": "Their expectations for future development"}}, "venus": {"name": "Venus Love Spread", "description": "Classic love spread, more detailed analysis of the current state of both parties in love, predicts future changes for both parties, suitable for predicting love relationships & marriage trends", "positions": {"self_thoughts": "Your thoughts", "other_thoughts": "Their thoughts", "self_influence": "Your influence on them", "other_influence": "Their influence on you", "obstacles": "Obstacles you both face", "result": "Outcome of this relationship", "self_future": "Your future thoughts or situation", "other_future": "Their future thoughts or situation"}}, "love_repair": {"name": "Love Repair Spread", "description": "Provides relationship management advice for querent, repairs relationship rifts, promotes positive development of feelings between both parties. Suitable for guiding relationship repair & intimate relationship management", "positions": {"action": "Action advice", "communication": "Verbal communication strategies", "attitude": "Attitude to adopt", "material": "Material aspect handling methods"}}, "career_pyramid": {"name": "Career Pyramid Spread", "description": "Analyzes wealth fortune, clarifies personal pros and cons, helps break through career bottlenecks, stabilizes career development. Suitable for guiding career development and enhancing financial fortune", "positions": {"core": "Core competitiveness", "weakness": "Weaknesses", "strength": "Strengths", "achievement": "Future achievements"}}, "wealth_tree": {"name": "Wealth Tree Spread", "description": "Spread for analyzing wealth fortune, sorting out the foundation, energy, and obstacles of wealth growth, suitable for predicting career development and financial fortune", "positions": {"foundation": "Foundation of wealth growth", "energy": "Energy wealth development relies on", "obstacle": "Obstacles to wealth development", "danger": "Potential dangers to wealth development", "height": "Expected wealth height"}}, "hexagram": {"name": "Hexagram Spread", "description": "Helps querent clarify the ins and outs of matters, analyze the current situation, predict future trends, and provide coping strategies and suggestions, commonly used for career problem analysis and resolution", "positions": {"past": "Past situation of the problem", "present": "Current state of the problem", "future": "Future development of the problem", "strategy": "Strategies to adopt", "environment": "Surrounding environmental conditions", "attitude": "Your psychological attitude", "result": "Final outcome of the matter"}}, "job_interview": {"name": "Job Interview Spread", "description": "Job seeking spread, helps querent understand the other party's needs and what to pay attention to, effectively improving interview success rate. Suitable for job interviews", "positions": {"mindset": "Your mindset and thoughts", "preparation": "Things to note before the interview", "process": "Situations that will occur during the interview", "requirements": "The other party's requirements or questions", "result": "Final result"}}, "yearly": {"result_title": "Yearly Fortune Reading", "result_description": "Tarot cards reveal the energy and guidance for your coming year", "spread": {"name": "Yearly Fortune Spread", "description": "Predicts fortune for the coming year through 16 tarot cards", "question": "Please interpret my fortune for the coming year", "positions": {"q1_career": "Q1·Career/Study", "q1_love": "Q1·Love", "q1_health": "Q1·Health", "q1_achievement": "Q1·Achievement", "q2_career": "Q2·Career/Study", "q2_love": "Q2·Love", "q2_health": "Q2·Health", "q2_achievement": "Q2·Achievement", "q3_career": "Q3·Career/Study", "q3_love": "Q3·Love", "q3_health": "Q3·Health", "q3_achievement": "Q3·Achievement", "q4_career": "Q4·Career/Study", "q4_love": "Q4·Love", "q4_health": "Q4·Health", "q4_achievement": "Q4·Achievement"}}}}}