import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import CdnLazyImage from '../../components/CdnLazyImage';

interface StepsSectionProps {}

const StepsSection: React.FC<StepsSectionProps> = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  
  // 添加三步骤轮播所需的状态
  const [selectedDivinationStep, setSelectedDivinationStep] = useState('step1');
  const divinationStepRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [divinationStepCursorPosition, setDivinationStepCursorPosition] = useState({ top: 0, height: 0 });
  
  // 更新光标位置的函数
  const updateDivinationStepCursorPosition = () => {
    const stepIndex = selectedDivinationStep === 'step1' ? 0 : 
                 selectedDivinationStep === 'step2' ? 1 : 2;
    
    const currentOption = divinationStepRefs.current[stepIndex];
    if (currentOption) {
      const { offsetTop, clientHeight } = currentOption;
      setDivinationStepCursorPosition({
        top: offsetTop + 20, // 稍微向下偏移，使其更居中
        height: clientHeight - 40 // 稍微缩短高度
      });
    }
  };
  
  // 当占卜步骤选择改变或组件挂载时更新光标位置
  useEffect(() => {
    updateDivinationStepCursorPosition();
    
    // 添加窗口大小变化的监听，确保响应式布局下光标位置正确
    window.addEventListener('resize', updateDivinationStepCursorPosition);
    return () => window.removeEventListener('resize', updateDivinationStepCursorPosition);
  }, [selectedDivinationStep]);
  
  // 添加自动切换功能 - 仅在桌面端
  useEffect(() => {
    // 检查是否为移动端
    const isMobile = window.innerWidth < 768; // md断点通常是768px
    
    // 如果是移动端，不启用自动切换
    if (isMobile) return;
    
    const autoChangeInterval = setInterval(() => {
      // 循环切换三个选项
      setSelectedDivinationStep(prevStep => {
        if (prevStep === 'step1') return 'step2';
        if (prevStep === 'step2') return 'step3';
        return 'step1';
      });
    }, 5000); // 每5秒切换一次
    
    return () => clearInterval(autoChangeInterval); // 清理定时器
  }, []);

  return (
    <div className="section-spacing max-w-[95%] lg:max-w-5xl mx-auto mt-12 sm:mt-16 lg:mt-20 relative z-10">
      <h2 className="text-2xl sm:text-3xl font-bold text-center mb-6 dark:text-white text-gray-900">
        {t('daily.three_steps.title', '三步開啟您的今日運勢')}
      </h2>
      
      {/* 桌面端：左侧文案右侧图片的布局 */}
      <div className="hidden md:flex flex-row gap-12 items-center">
        {/* 左侧文案区域 */}
        <div className="w-1/2">
          <div className="space-y-10 relative">
            {/* 添加移动的光标指示器 */}
            <div 
              className="absolute left-0 w-1 bg-purple-500 transition-all duration-300 ease-in-out"
              style={{
                top: `${divinationStepCursorPosition.top}px`,
                height: `${divinationStepCursorPosition.height}px`,
              }}
            ></div>
            
            {/* 第一步 */}
            <div 
              ref={el => divinationStepRefs.current[0] = el}
              className={`p-4 cursor-pointer transition-all duration-300 pl-8 ${
                selectedDivinationStep === 'step1' 
                  ? `dark:text-purple-300 text-purple-700`
                  : `dark:hover:text-purple-300 hover:text-purple-700`
              }`}
              onClick={() => {
                setSelectedDivinationStep('step1');
              }}
            >
              <div>
                <h3 className={`text-lg font-semibold mb-1 ${
                  selectedDivinationStep === 'step1' ? 'dark:text-purple-300 text-purple-700' : 'dark:text-white text-gray-800'
                }`}>{t('daily.three_steps.step1.title', '第一步：校準您的星辰座標')}</h3>
                <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
                  {t('daily.three_steps.step1.description', '請輸入您的出生日期。它是解鎖您個人星盤、與生辰宇宙能量對頻的第一把鑰匙，也是我們進行精準運勢占卜的基礎。')}
                </p>
              </div>
            </div>

            {/* 第二步 */}
            <div 
              ref={el => divinationStepRefs.current[1] = el}
              className={`p-4 cursor-pointer transition-all duration-300 pl-8 ${
                selectedDivinationStep === 'step2' 
                  ? `dark:text-purple-300 text-purple-700`
                  : `dark:hover:text-purple-300 hover:text-purple-700`
              }`}
              onClick={() => {
                setSelectedDivinationStep('step2');
              }}
            >
              <div>
                <h3 className={`text-lg font-semibold mb-1 ${
                  selectedDivinationStep === 'step2' ? 'dark:text-purple-300 text-purple-700' : 'dark:text-white text-gray-800'
                }`}>{t('daily.three_steps.step2.title', '第二步：專注提問')}</h3>
                <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
                  {t('daily.three_steps.step2.description', '請閉上雙眼，進行一次深呼吸，靜心默想「我今天運勢如何？」，將您的意念專注於牌卡之上。')}
                </p>
              </div>
            </div>

            {/* 第三步 */}
            <div 
              ref={el => divinationStepRefs.current[2] = el}
              className={`p-4 cursor-pointer transition-all duration-300 pl-8 ${
                selectedDivinationStep === 'step3' 
                  ? `dark:text-purple-300 text-purple-700`
                  : `dark:hover:text-purple-300 hover:text-purple-700`
              }`}
              onClick={() => {
                setSelectedDivinationStep('step3');
              }}
            >
              <div>
                <h3 className={`text-lg font-semibold mb-1 ${
                  selectedDivinationStep === 'step3' ? 'dark:text-purple-300 text-purple-700' : 'dark:text-white text-gray-800'
                }`}>{t('daily.three_steps.step3.title', '第三步：抽取命運牌卡')}</h3>
                <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
                {t('daily.three_steps.step3.description', '憑直覺點擊下方牌卡，立即揭曉結合星座與塔羅的每日運勢解析。')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧图片区域 */}
        <div className="w-1/2 flex items-center justify-center">
          <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg aspect-square w-full">
            <div className="rounded-xl overflow-hidden relative h-full">
              {/* 第一步图片 */}
              <div className={`transition-opacity duration-500 ${selectedDivinationStep === 'step1' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                <CdnLazyImage 
                  src="/images-optimized/daily-fortune/step1-tarot-question3.webp"
                  alt="Birthday Information Input"
                  className="w-full h-full object-cover" 
                />
              </div>
              
              {/* 第二步图片 */}
              <div className={`transition-opacity duration-500 ${selectedDivinationStep === 'step2' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                <CdnLazyImage 
                  src="/images-optimized/daily-fortune/step2-focus-question.webp"
                  alt="Focus on Your Question"
                  className="w-full h-full object-cover" 
                />
              </div>
              
              {/* 第三步图片 */}
              <div className={`transition-opacity duration-500 ${selectedDivinationStep === 'step3' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                <CdnLazyImage 
                  src="/images-optimized/daily-fortune/step3-select-tarot.webp"
                  alt="Select Your Tarot Card"
                  className="w-full h-full object-cover" 
                />
              </div>
              
              {/* 添加进度指示器 */}
              <div className="absolute bottom-3 left-0 right-0 flex justify-center space-x-2">
                <div 
                  className={`h-2 w-8 rounded-full transition-all duration-300 ${selectedDivinationStep === 'step1' ? 'bg-white' : 'bg-white/30'}`}
                  onClick={() => {
                    setSelectedDivinationStep('step1');
                  }}
                  style={{ cursor: 'pointer' }}
                ></div>
                <div 
                  className={`h-2 w-8 rounded-full transition-all duration-300 ${selectedDivinationStep === 'step2' ? 'bg-white' : 'bg-white/30'}`}
                  onClick={() => {
                    setSelectedDivinationStep('step2');
                  }}
                  style={{ cursor: 'pointer' }}
                ></div>
                <div 
                  className={`h-2 w-8 rounded-full transition-all duration-300 ${selectedDivinationStep === 'step3' ? 'bg-white' : 'bg-white/30'}`}
                  onClick={() => {
                    setSelectedDivinationStep('step3');
                  }}
                  style={{ cursor: 'pointer' }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 移动端：上文下图排列 */}
      <div className="md:hidden space-y-10">
        {/* 第一步 */}
        <div className="space-y-2">
          <div className="px-4">
            <h3 className="text-lg font-semibold mb-1 dark:text-white text-gray-800">{t('daily.three_steps.step1.title', '第一步：校準您的星辰座標')}</h3>
            <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
              {t('daily.three_steps.step1.description', '請輸入您的出生日期。它是解鎖您個人星盤、與生辰宇宙能量對頻的第一把鑰匙，也是我們進行精準運勢占卜的基礎。')}
            </p>
          </div>
          <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
            <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
              <CdnLazyImage 
                src="/images-optimized/daily-fortune/step1-tarot-question3.webp"
                alt="Birthday Information Input"
                className="w-full h-full object-cover" 
              />
            </div>
          </div>
        </div>
        
        {/* 第二步 */}
        <div className="space-y-2">
          <div className="px-4">
            <h3 className="text-lg font-semibold mb-1 dark:text-white text-gray-800">{t('daily.three_steps.step2.title', '第二步：專注提問')}</h3>
            <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
              {t('daily.three_steps.step2.description', '請閉上雙眼，進行一次深呼吸，靜心默想「我今天運勢如何？」，將您的意念專注於牌卡之上。')}
            </p>
          </div>
          <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
            <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
              <CdnLazyImage 
                src="/images-optimized/daily-fortune/step2-focus-question.webp"
                alt="Focus on Your Question"
                className="w-full h-full object-cover" 
              />
            </div>
          </div>
        </div>
        
        {/* 第三步 */}
        <div className="space-y-2">
          <div className="px-4">
            <h3 className="text-lg font-semibold mb-1 dark:text-white text-gray-800">{t('daily.three_steps.step3.title', '第三步：抽取命運牌卡')}</h3>
            <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
              {t('daily.three_steps.step3.description', '憑直覺點擊下方牌卡，立即揭曉結合星座與塔羅的每日運勢解析。')}
            </p>
          </div>
          <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
            <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
              <CdnLazyImage 
                src="/images-optimized/daily-fortune/step3-select-tarot.webp"
                alt="Select Your Tarot Card"
                className="w-full h-full object-cover" 
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepsSection; 