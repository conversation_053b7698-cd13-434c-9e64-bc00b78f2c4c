import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import LanguageLink from '../components/LanguageLink';
import { TAROT_CARDS } from '../data/tarot-cards';
import { useTranslation } from 'react-i18next';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import SEO from '../components/SEO';
import { useTheme } from '../contexts/ThemeContext';
import CdnLazyImage from '../components/CdnLazyImage';
import { generateCardPath } from '../utils/tarotUtils';
import { getTarotGallerySEOConfig } from '../lib/SEOConfig';

const TarotGallery: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [_, setForceUpdate] = useState(0);
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // 当语言变化时强制组件更新
  useEffect(() => {
    setForceUpdate(prev => prev + 1);
  }, [i18n.language]);

  // 获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  // 获取卡牌翻译名称的函数
  const getCardTranslatedName = (card: typeof TAROT_CARDS[0]): string => {
    if (card.id <= 21) {
      // Major Arcana
      return t(`reading.cards.major.${card.id}`);
    } else {
      // Minor Arcana
      const nameEn = card.nameEn.toLowerCase();
      const suit = nameEn.includes('wands') ? 'wands' :
                  nameEn.includes('cups') ? 'cups' :
                  nameEn.includes('swords') ? 'swords' :
                  'pentacles';
      
      const rankMap: { [key: string]: string } = {
        'ace': 'ace',
        'two': '2',
        'three': '3',
        'four': '4',
        'five': '5',
        'six': '6',
        'seven': '7',
        'eight': '8',
        'nine': '9',
        'ten': '10',
        'page': 'page',
        'knight': 'knight',
        'queen': 'queen',
        'king': 'king'
      };
      
      const rank = Object.keys(rankMap).find(r => nameEn.toLowerCase().startsWith(r));
      return rank ? t(`reading.cards.${suit}.${rankMap[rank]}`) : card.name;
    }
  };

  // 根据选择的类别过滤卡牌
  const filteredCards = TAROT_CARDS.filter(card => {
    if (selectedCategory === 'all') return true;
    if (selectedCategory === 'major') return card.id <= 21;
    if (selectedCategory === 'wands') return card.id >= 22 && card.id <= 35;
    if (selectedCategory === 'cups') return card.id >= 36 && card.id <= 49;
    if (selectedCategory === 'swords') return card.id >= 50 && card.id <= 63;
    if (selectedCategory === 'pentacles') return card.id >= 64 && card.id <= 77;
    return true;
  });

  return (
    <div className="min-h-screen flex flex-col relative">
      <SEO {...getTarotGallerySEOConfig(i18n.language)} />
      <LandingBackground />
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8">
          <div className="text-center mt-8 sm:mt-10">
            <h1 className={`main-title mb-1 ${getFontClass()} ${isDark ? '' : 'text-gray-800'}`}>
              {t('gallery.title')}
            </h1>
            <p className={`sub-title mb-4 sm:mb-6 ${isDark ? '' : 'text-gray-600'}`}>
              {t('gallery.description')}
            </p>
          </div>

          {/* 分类选择器 */}
          <div className="py-1 px-4 mb-4 sm:mb-6">
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:flex lg:flex-nowrap justify-center gap-2 md:gap-3">
              {['all', 'major', 'wands', 'cups', 'swords', 'pentacles'].map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`w-full sm:min-w-[140px] lg:w-[180px] h-[44px] flex items-center justify-center whitespace-normal text-center px-3 md:px-4 rounded-full text-sm font-medium transition-colors duration-200 font-['Inter'] ${
                    selectedCategory === category
                      ? 'bg-purple-600 text-white'
                      : isDark 
                        ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        : 'bg-gray-200 hover:bg-gray-300'
                  }`}
                >
                  <span style={{color: selectedCategory === category || isDark ? 'white' : '#4B5563'}}>
                    {t(`gallery.categories.${category}`)}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* 卡牌网格 */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-5 sm:gap-6 md:gap-7 max-w-7xl mx-auto">
            {filteredCards.map(card => (
              <LanguageLink key={card.id} to={`/gallery/card/${generateCardPath(card.nameEn)}`} className="flex flex-col items-center gap-3 no-underline group">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  whileHover={{ 
                    scale: 1.05,
                    transition: { duration: 0.2 }
                  }}
                  transition={{ duration: 0.3 }}
                  className="flex flex-col items-center gap-3 w-full"
                >
                  <div className="aspect-[2/3] relative w-full">
                    <div className="absolute inset-0 bg-gradient-to-b from-purple-500/0 to-purple-500/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 rounded-xl" />
                    <CdnLazyImage
                      src={`/images-optimized/tarot/${card.nameEn.replace(/ /g, '_')}.webp`}
                      alt={`Tarot Card - ${card.nameEn}`}
                      className="w-full h-full object-contain rounded-xl transition-all duration-300 group-hover:shadow-[0_0_15px_rgba(168,85,247,0.5)]"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        if (!target.src.includes('fallback')) {
                          // console.error(`卡牌图片 ${target.src} 加载失败，使用备用图片`);
                          target.src = `/images/tarot/${card.name}.jpg`;
                        }
                      }}
                    />
                  </div>
                  <div className="text-center">
                    <div className={`text-sm font-medium ${isDark ? 'text-purple-200 group-hover:text-purple-100' : 'text-purple-700 group-hover:text-purple-800'} transition-colors duration-300 ${getFontClass()}`}>
                      {getCardTranslatedName(card)}
                    </div>
                    <div className={`text-xs ${isDark ? 'text-gray-400 group-hover:text-gray-300' : 'text-gray-600 group-hover:text-gray-700'} transition-colors duration-300`}>
                      {card.nameEn.replace(/_/g, ' ')}
                    </div>
                  </div>
                </motion.div>
              </LanguageLink>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default TarotGallery; 