import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useUser } from '../contexts/UserContext';
import { checkPrivilegeStatus } from '../services/invitationService';
import { subscribeNewsletter } from '../services/newsletterService';
import { FaFacebook, FaInstagram, FaYoutube, FaWhatsapp } from 'react-icons/fa';
import { FaTiktok, FaThreads, FaXTwitter } from 'react-icons/fa6';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import LanguageLink from './LanguageLink';
import CdnLazyImage from './CdnLazyImage';

interface FooterProps {
  onNavigate?: (e: React.MouseEvent<HTMLAnchorElement>, path: string) => void;
  showBeian?: boolean; // 新增属性：是否显示备案信息
}

const Footer: React.FC<FooterProps> = ({ onNavigate, showBeian = false }) => {
  const { t, i18n } = useTranslation();
  const [email, setEmail] = useState('');
  const [subscribeStatus, setSubscribeStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [isSubscribing, setIsSubscribing] = useState(false);

  const { navigate } = useLanguageNavigate();
  const { user } = useUser();

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    const path = e.currentTarget.getAttribute('href') || '/';
    
    // 使用自定义导航钩子，自动处理语言参数
    if (onNavigate) {
      onNavigate(e, path);
    } else {
      navigate(path);
    }
  };

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || isSubscribing) return;
    
    setIsSubscribing(true);
    
    try {
      const result = await subscribeNewsletter(email, i18n.language);
      
      if (result.success) {
        setSubscribeStatus('success');
        setEmail('');
        setTimeout(() => {
          setSubscribeStatus('idle');
        }, 3000);
      } else {
        setSubscribeStatus('error');
        setTimeout(() => {
          setSubscribeStatus('idle');
        }, 3000);
      }
    } catch (error) {
      setSubscribeStatus('error');
      setTimeout(() => {
        setSubscribeStatus('idle');
      }, 3000);
    } finally {
      setIsSubscribing(false);
    }
  };

  // 处理会员链接点击
  const handleMembershipClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    
    if (user) {
      try {
        const privilegeStatus = await checkPrivilegeStatus();
        if (privilegeStatus.hasInternalPrivilege) {
          // 有内部权限，跳转到内部会员页面
          navigate('/membership-inter');
        } else {
          // 无内部权限，跳转到普通会员页面
          navigate('/membership');
        }
      } catch (error) {
        // 出错时默认跳转到普通会员页面
        navigate('/membership');
      }
    } else {
      // 未登录用户直接跳转到普通会员页面
      navigate('/membership');
    }
  };

  // 统一的链接样式类
  const linkClass = "dark:text-white text-gray-700 dark:hover:text-purple-300 hover:text-purple-600 text-sm transition-colors font-['Inter']";

  return (
    <>
      {/* 主页脚导航 */}
      <footer className="w-full relative mt-12 sm:mt-16 md:mt-20 lg:mt-24">
        <div className="w-full">
          <div className="w-full border-t-2 dark:border-white/10 border-gray-300/80"></div>
          
          <div className="py-8 sm:py-12">
            <div className="max-w-[1000px] mx-auto px-4">
              {/* 订阅组件 - 显示在所有页面 */}
              <div className="w-full sm:w-[400px] lg:absolute lg:top-12 lg:left-8 lg:w-[320px] mb-12 lg:mb-0 mx-auto">
                <p className="dark:text-gray-400 text-gray-500 text-sm mb-2 font-['Inter']">{t('footer.copyright.year')}</p>
                <div className="w-full">
                  <form onSubmit={handleSubscribe} className="relative w-full">
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder={t('footer.subscribe.placeholder')}
                      className="w-full px-4 py-2 bg-white dark:bg-[#1a1a1a] border border-gray-300 dark:border-[#333] rounded-md text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:border-purple-300 dark:focus:border-[#444] font-['Inter'] pr-[120px]"
                      required
                      disabled={isSubscribing}
                    />
                    <button
                      type="submit"
                      style={{ height: 'calc(100% - 2px)', margin: '1px', right: '1px' }}
                      className={`absolute right-0 top-0 bottom-0 px-6 py-2 bg-gradient-to-r from-purple-600 to-purple-800 text-white rounded-r-[3px] font-medium hover:opacity-90 transition-opacity whitespace-nowrap font-['Inter'] ${isSubscribing ? 'opacity-70 cursor-not-allowed' : ''}`}
                      disabled={isSubscribing}
                    >
                      {isSubscribing ? t('footer.subscribe.sending') : t('footer.subscribe.button')}
                    </button>
                  </form>
                  {subscribeStatus === 'success' && (
                    <p className="text-green-500 text-sm mt-2 font-['Inter']">{t('footer.subscribe.success')}</p>
                  )}
                  {subscribeStatus === 'error' && (
                    <p className="text-red-500 text-sm mt-2 font-['Inter']">{t('footer.subscribe.error')}</p>
                  )}
                </div>
              </div>

              {/* 导航地图 - 移动端优化为2列布局，PC端保持3列 */}
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-8 sm:gap-10 lg:gap-6">
                {/* 第1列：关于我们 */}
                <div className="w-full">
                  <h3 className="dark:text-white text-gray-800 font-medium text-lg mb-4 font-['Inter'] text-center">{t('footer.sections.about.title')}</h3>
                  <ul className="grid grid-cols-1 gap-3">
                    <li className="flex justify-center">
                      <LanguageLink 
                        to="/privacy"
                        className={linkClass}
                      >
                        {t('footer.sections.about.privacy')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center">
                      <LanguageLink 
                        to="/terms"
                        className={linkClass}
                      >
                        {t('footer.sections.about.terms')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center">
                      <Link to="/feedback" onClick={handleClick} className={linkClass}>
                        {t('footer.sections.contact.feedback')}
                      </Link>
                    </li>
                    <li className="flex justify-center">
                      <a 
                        href="mailto:<EMAIL>?subject=来自TarotQA用户的咨询" 
                        className={linkClass}
                      >
                        {t('footer.sections.contact.email')}
                      </a>
                    </li>
                  </ul>
                </div>

                {/* 第2列：核心功能 */}
                <div className="w-full">
                  <h3 className="dark:text-white text-gray-800 font-medium text-lg mb-4 font-['Inter'] text-center">{t('footer.sections.features.title')}</h3>
                  <ul className="grid grid-cols-1 gap-3">
                    <li className="flex justify-center">
                      <LanguageLink to="/" className={linkClass}>
                        {t('footer.sections.navigation.landing')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center">
                      <LanguageLink to="/home" className={linkClass}>
                        {t('footer.sections.navigation.tarot')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center">
                      <LanguageLink to="/daily-fortune" className={linkClass}>
                        {t('footer.sections.navigation.daily_fortune')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center">
                      <LanguageLink to="/yes-no-tarot" className={linkClass}>
                        {t('footer.sections.navigation.yes_no_tarot')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center">
                      <LanguageLink to="/horoscope" className={linkClass}>
                        {t('blog.categories.horoscope')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center">
                      <LanguageLink to="/membership" onClick={handleMembershipClick} className={linkClass}>
                        {t('footer.sections.navigation.membership')}
                      </LanguageLink>
                    </li>
                  </ul>
                </div>

                {/* 第3列：部落格 - 在移动端时放在第2行，跨越2列 */}
                <div className="col-span-2 sm:col-span-2 lg:col-span-1 w-full">
                  <h3 className="dark:text-white text-gray-800 font-medium text-lg mb-4 font-['Inter'] text-center">{t('footer.sections.blog.title')}</h3>
                  <ul className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-1 gap-3">
                    <li className="flex justify-center">
                      <LanguageLink to="/gallery" className={linkClass}>
                        {t('nav.gallery')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center">
                      <LanguageLink to="/spreads" className={linkClass}>
                        {t('footer.sections.navigation.spreads')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center">
                      <LanguageLink to="/tarot-guide" className={linkClass}>
                        {t('blog.tarot_guide.heading')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center">
                      <LanguageLink to="/zodiac-traits" className={linkClass}>
                        {t('blog.categories.zodiac_traits')}
                      </LanguageLink>
                    </li>
                    <li className="flex justify-center sm:col-span-2 lg:col-span-1">
                      <LanguageLink to="/general-divination" className={linkClass}>
                        {t('blog.general_divination.heading')}
                      </LanguageLink>
                    </li>
                  </ul>
                </div>
              </div>

              {/* 社交媒体图标 */}
              <div className="w-full flex justify-center mt-6 mb-0">
                <div className="flex flex-wrap justify-center gap-6 py-1">
                  <a 
                    href="https://facebook.com/jwangfv1" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-300 transition-colors"
                    aria-label="Facebook"
                  >
                    <FaFacebook size={24} />
                  </a>
                  <a 
                    href="https://instagram.com/jwangfv1/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-300 transition-colors"
                    aria-label="Instagram"
                  >
                    <FaInstagram size={24} />
                  </a>
                  <a 
                    href="https://threads.net/@jwangfv1" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-300 transition-colors"
                    aria-label="Threads"
                  >
                    <FaThreads size={24} />
                  </a>
                  <a 
                    href="https://tiktok.com/@tarotqa7" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-300 transition-colors"
                    aria-label="TikTok"
                  >
                    <FaTiktok size={24} />
                  </a>
                  <a 
                    href="https://youtube.com/@TarotQA" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-300 transition-colors"
                    aria-label="YouTube"
                  >
                    <FaYoutube size={24} />
                  </a>
                  <a 
                    href="https://x.com/TarotQA" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-300 transition-colors"
                    aria-label="Twitter"
                  >
                    <FaXTwitter size={24} />
                  </a>
                  <a 
                    href="https://wa.me/8618310377116" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-300 transition-colors"
                    aria-label="WhatsApp"
                  >
                    <FaWhatsapp size={24} />
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* 底部备案信息 - 仅在指定页面显示 */}
          {showBeian && (
            <div className="py-0 -mt-2">
              <div className="flex flex-col sm:flex-row justify-center items-center space-y-1.5 sm:space-y-0 sm:space-x-4 dark:text-gray-400 text-gray-500 text-xs sm:text-sm font-['Inter']">
                <a 
                  href="https://beian.miit.gov.cn/" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="dark:text-gray-400 text-gray-500 dark:hover:text-gray-300 hover:text-gray-700 transition-colors font-['Inter'] whitespace-nowrap"
                >
                  {t('footer.copyright.icp')}
                </a>
                <div className="flex items-center whitespace-nowrap">
                  <CdnLazyImage 
                    src="/images-optimized/head-logo.webp" 
                    alt={t('footer.copyright.police_logo_alt')} 
                    className="w-4 sm:w-5 h-4 sm:h-5 mr-1"
                  />
                  <a 
                    href="https://beian.mps.gov.cn/#/query/webSearch?code=37072402371950" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="dark:text-gray-400 text-gray-500 dark:hover:text-gray-300 hover:text-gray-700 transition-colors font-['Inter']"
                  >
                    {t('footer.copyright.police')}
                  </a>
                </div>
              </div>
            </div>
          )}
          
          {/* 移动端额外底部空间 */}
          <div className="h-10 sm:h-2 md:h-0 lg:h-0"></div>
        </div>
      </footer>
    </>
  );
};

export default Footer; 