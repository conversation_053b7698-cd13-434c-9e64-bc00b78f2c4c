import React from 'react';

interface NumberInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  theme: string;
  t: (key: string) => string;
}

const NumberInput: React.FC<NumberInputProps> = ({ value, onChange, onSubmit, theme, t }) => {
  return (
    <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-3 w-full justify-center mt-6">
      <div className={`relative w-full sm:w-32 max-w-xs ${theme === 'dark' ? 'text-white' : 'text-gray-800'} mb-2 sm:mb-0`}>
        <input
          type="number"
          min="1"
          max="78"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              onSubmit();
            }
          }}
          className={`w-full py-2.5 px-4 ${theme === 'dark' ? 'bg-gray-800/90 border-gray-700 focus:bg-gray-800' : 'bg-white/90 border-gray-300 focus:bg-white'} border rounded-xl text-center text-sm font-medium focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 transition-all duration-200 shadow-sm`}
          placeholder={t('reading.shuffle.number_placeholder')}
        />
      </div>
      <button
        onClick={onSubmit}
        className={`w-full sm:w-auto py-2.5 px-5 bg-purple-600 hover:bg-purple-500 active:bg-purple-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md hover:shadow-purple-500/20 active:transform active:scale-95 max-w-xs`}
      >
        {t('reading.shuffle.confirm')}
      </button>
    </div>
  );
};

export default NumberInput; 