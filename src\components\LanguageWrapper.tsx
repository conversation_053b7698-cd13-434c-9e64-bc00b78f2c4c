import React, { useEffect } from 'react';
import { useParams, Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { availableLanguages } from '../i18n';

/**
 * 语言包装器组件
 * 用于处理带有语言参数的路由
 * 如 /zh-CN, /zh-CN/home, /en/home 等
 * 
 * 注意：此组件使用原生的useNavigate而非useLanguageNavigate
 * 因为这个组件本身就是负责处理语言路由的，不应该再次应用语言处理逻辑
 */
const LanguageWrapper: React.FC = () => {
  const { lang } = useParams<{ lang: string }>();
  const { i18n } = useTranslation();
  const navigate = useNavigate(); // 保留原生useNavigate，避免循环处理
  const location = useLocation();
  
  useEffect(() => {
    // 处理简化的语言代码映射
    let normalizedLang = lang;
    // 将简化的'zh'映射到'zh-CN'
    if (lang === 'zh') {
      normalizedLang = 'zh-CN';
    }
    
    // 如果URL中的语言参数有效，则切换到该语言
    if (normalizedLang && availableLanguages.includes(normalizedLang)) {
      if (i18n.language !== normalizedLang) {
        i18n.changeLanguage(normalizedLang);
      }
      
      // 如果使用了简化语言代码，重定向到规范化的URL
      if (lang !== normalizedLang) {
        const currentPath = location.pathname;
        const pathWithoutLang = currentPath.replace(/^\/[^\/]+/, '');
        const newPath = `/${normalizedLang}${pathWithoutLang}`;
        navigate(newPath, { replace: true });
      }
    } else {
      // 语言参数无效，重定向到默认语言
      const currentPath = location.pathname;
      const pathWithoutLang = currentPath.replace(/^\/[^\/]+/, '');
      // 如果没有子路径，则导航到语言根路径
      const newPath = `/${i18n.language}${pathWithoutLang || ''}`;
      navigate(newPath, { replace: true });
    }
  }, [lang, i18n, navigate, location.pathname]);
  
  return <Outlet />;
};

export default LanguageWrapper; 