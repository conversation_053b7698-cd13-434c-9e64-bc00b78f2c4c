import React, { Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';
import { motion } from 'framer-motion';

// 延迟加载SpotlightCard组件
const SpotlightCard = React.lazy(() => import('../../blocks/Components/SpotlightCard/SpotlightCard'));

interface SpotlightSectionProps {}

const SpotlightSection: React.FC<SpotlightSectionProps> = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { navigate } = useLanguageNavigate();

  return (
    <Suspense
      fallback={
        <div className="w-full h-[200px] bg-gray-900/20 rounded-lg animate-pulse" />
      }
    >
      <div className="py-32 md:py-48">
        <div className="max-w-3xl mx-auto px-4">
          <SpotlightCard
            className="custom-spotlight-card"
            spotlightColor="rgba(0, 229, 255, 0.2)"
          >
            <div className="p-8 text-center">
              <h3
                className="text-2xl md:text-3xl font-semibold mb-4"
                style={{
                  background: theme === 'light' 
                    ? "none" 
                    : "linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)",
                  WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                  WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                  color: theme === 'light' ? "#000" : "inherit"
                }}
              >
                {t('daily.explore_section.title', '探索塔羅奧秘')}
              </h3>
              <p className={`${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              } text-lg md:text-xl mb-6`}>
                {t('daily.explore_section.description', '想更深入探索今日的愛情/事業運勢？試試我們的AI塔羅牌陣，獲取針對性問題的詳細解讀。')}
              </p>
              <div className="flex justify-center">
                <motion.button
                  onClick={() => navigate('/')}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-6 py-3 rounded-full font-medium"
                  style={{
                    background:
                      theme === 'light'
                        ? "linear-gradient(135deg, rgba(124, 58, 237, 0.7), rgba(168, 85, 247, 0.7))"
                        : "linear-gradient(135deg, rgba(124, 58, 237, 0.8), rgba(168, 85, 247, 0.8))",
                    boxShadow: theme === 'light' 
                      ? "0 0 20px rgba(168, 85, 247, 0.4)"
                      : "0 0 20px rgba(168, 85, 247, 0.5)",
                    color: 'white',
                  }}
                >
                  {t('daily.explore_section.start_button', '開始探索')}
                </motion.button>
              </div>
            </div>
          </SpotlightCard>
        </div>
      </div>
    </Suspense>
  );
};

export default SpotlightSection; 