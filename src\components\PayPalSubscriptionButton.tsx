import React, { useEffect, useRef } from 'react';
import { loadScript } from '../utils/loadScript';

declare global {
  interface Window {
    paypal?: any;
  }
}

interface PayPalSubscriptionButtonProps {
  planId: string;
  onSuccess: (details: any) => void;
  onError: (error: any) => void;
  onCancel: () => void;
}

const PayPalSubscriptionButton: React.FC<PayPalSubscriptionButtonProps> = ({
  planId,
  onSuccess,
  onError,
  onCancel,
}) => {
  const paypalButtonsRef = useRef<HTMLDivElement>(null);
  const isComponentMounted = useRef(true);

  useEffect(() => {
    isComponentMounted.current = true;

    const loadPayPalScript = async () => {
      try {
        const clientId = import.meta.env.VITE_PAYPAL_CLIENT_ID;

        if (!clientId) {
          throw new Error('PayPal client ID is not configured');
        }

        // 确保组件仍然挂载
        if (!isComponentMounted.current || !paypalButtonsRef.current) {
          return;
        }

        // 清除已有的按钮
        paypalButtonsRef.current.innerHTML = '';

        // 加载PayPal SDK，添加vault=true和intent=subscription参数，并禁用 Apple Pay
        const sdkUrl = `https://www.paypal.com/sdk/js?client-id=${clientId}&vault=true&intent=subscription&disable-funding=applepay`;
        await loadScript(sdkUrl);

        // 再次确保组件仍然挂载
        if (!isComponentMounted.current || !paypalButtonsRef.current) {
          return;
        }

        // 确保PayPal对象可用
        if (!window.paypal) {
          throw new Error('PayPal SDK not initialized properly');
        }

        const buttons = window.paypal.Buttons({
          style: {
            shape: 'rect',
            color: 'gold',
            layout: 'vertical',
            label: 'subscribe'
          },
          createSubscription: (_data: any, actions: any) => {
            if (!isComponentMounted.current) {
              return Promise.reject(new Error('Component unmounted'));
            }
            return actions.subscription.create({
              plan_id: planId
            });
          },
          onApprove: async (data: any) => {
            if (!isComponentMounted.current) return;
            try {
              onSuccess(data);
            } catch (error) {
              if (isComponentMounted.current) {
                onError(error);
              }
            }
          },
          onCancel: () => {
            if (isComponentMounted.current) {
              onCancel();
            }
          },
          onError: (err: any) => {
            if (isComponentMounted.current) {
              onError(err);
            }
          }
        });

        if (isComponentMounted.current && paypalButtonsRef.current) {
          await buttons.render(paypalButtonsRef.current);
        }
      } catch (error) {
        if (isComponentMounted.current) {
          onError(error);
        }
      }
    };

    // 使用setTimeout来确保DOM已经完全渲染
    const timeoutId = setTimeout(loadPayPalScript, 0);

    return () => {
      isComponentMounted.current = false;
      clearTimeout(timeoutId);
      if (paypalButtonsRef.current) {
        paypalButtonsRef.current.innerHTML = '';
      }
    };
  }, [planId, onSuccess, onError, onCancel]);

  return <div ref={paypalButtonsRef} className="paypal-button-container" />;
};

export default PayPalSubscriptionButton; 