/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = temp_users   */
/******************************************/
CREATE TABLE `temp_users` (
  `id` varchar(36) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `verification_code` varchar(6) DEFAULT NULL,
  `verification_code_expiry` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `language` varchar(10) DEFAULT 'zh-CN',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `fingerprint` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3
;
