/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = comments   */
/******************************************/
CREATE TABLE `comments` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `session_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `parent_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '父评论ID，NULL表示顶级评论',
  `reply_to_user_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '回复的用户ID',
  `reply_count` int NOT NULL DEFAULT '0' COMMENT '回复数量',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数量',
  `user_like_counts` json DEFAULT NULL COMMENT '用户点赞次数记录 {userId: count}',
  `rating` tinyint DEFAULT NULL COMMENT '评分(1-5)，回复评论时可为NULL',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '评论内容',
  `page_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'yes-no-tarot' COMMENT '页面类型',
  `language` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'zh-CN' COMMENT '语言',
  `metadata` json DEFAULT NULL COMMENT '额外元数据',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_session_user_toplevel` (`session_id`,`user_id`,`parent_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_rating` (`rating`),
  KEY `idx_page_type` (`page_type`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_reply_to_user_id` (`reply_to_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户评论表'
;
