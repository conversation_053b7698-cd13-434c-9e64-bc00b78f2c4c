/**
 * 分享奖励服务
 * 处理用户分享提交和VIP奖励发放
 */
const { ShareSubmission } = require('../models/ShareSubmission');
const { User } = require('../models/User');
const { getConnection } = require('./database');

class ShareRewardService {
  /**
   * 创建分享提交
   * @param {Object} data 分享数据
   * @returns {Promise<Object>} 创建的分享提交
   */
  static async createSubmission(data) {
    return await ShareSubmission.create(data);
  }
  
  /**
   * 审核分享提交
   * @param {string} id 分享提交ID
   * @param {string} status 状态：'approved', 'rejected'
   * @param {string} reviewerId 审核人ID
   * @param {string} reviewNote 审核备注
   * @returns {Promise<boolean>} 审核是否成功
   */
  static async reviewSubmission(id, status, reviewerId, reviewNote = '') {
    const updated = await ShareSubmission.updateStatus(id, status, reviewerId, reviewNote);
    
    // 如果审核通过，自动发放奖励
    if (updated && status === 'approved') {
      const submission = await ShareSubmission.findById(id);
      if (submission && !submission.reward_granted) {
        await this.grantReward(submission.id, submission.user_id);
      }
    }
    
    return updated;
  }
  
  /**
   * 发放VIP奖励
   * @param {string} submissionId 分享提交ID
   * @param {string} userId 用户ID
   * @returns {Promise<boolean>} 奖励发放是否成功
   */
  static async grantReward(submissionId, userId) {
    const pool = await getConnection();
    
    try {
      // 开始事务
      await pool.query('START TRANSACTION');
      
      // 检查用户是否已经获得过奖励
      const hasReceived = await ShareSubmission.hasReceivedReward(userId);
      if (hasReceived) {
        // 如果已经获得过奖励，回滚并返回失败
        await pool.query('ROLLBACK');
        return false;
      }
      
      // 获取用户当前VIP状态
      const [userRows] = await pool.query(
        'SELECT vip_status, vip_type, vip_end_date FROM users WHERE id = ?',
        [userId]
      );
      
      if (!userRows.length) {
        await pool.query('ROLLBACK');
        return false;
      }
      
      const user = userRows[0];
      const now = new Date();
      let newEndDate;
      
      // 计算新的VIP到期日期
      // 奖励14天VIP
      const rewardDays = 14;
      
      if (user.vip_status === 'active' && user.vip_end_date) {
        // 如果用户已经是VIP，从当前到期日增加奖励天数
        const currentEndDate = new Date(user.vip_end_date);
        newEndDate = new Date(currentEndDate.setDate(currentEndDate.getDate() + rewardDays));
      } else {
        // 如果用户不是VIP，从当前日期开始计算
        newEndDate = new Date(now.setDate(now.getDate() + rewardDays));
      }
      
      // 格式化日期为MySQL datetime格式
      const formattedEndDate = newEndDate.toISOString().slice(0, 19).replace('T', ' ');
      
      // 更新用户VIP状态
      await pool.query(
        `UPDATE users 
         SET vip_status = 'active',
             vip_type = 'monthly',
             vip_start_date = CURRENT_TIMESTAMP,
             vip_end_date = ?
         WHERE id = ?`,
        [formattedEndDate, userId]
      );
      
      // 标记奖励已发放
      await ShareSubmission.markRewardGranted(submissionId);
      
      // 提交事务
      await pool.query('COMMIT');
      return true;
    } catch (error) {
      // 发生错误时回滚事务
      await pool.query('ROLLBACK');
      console.error('发放分享奖励失败:', error);
      return false;
    }
  }
  
  /**
   * 获取待审核的分享提交
   * @param {number} limit 限制数量
   * @param {number} offset 偏移量
   * @returns {Promise<Array>} 待审核的分享提交列表
   */
  static async getPendingSubmissions(limit = 10, offset = 0) {
    return await ShareSubmission.findPending(limit, offset);
  }
  
  /**
   * 获取用户的分享提交
   * @param {string} userId 用户ID
   * @returns {Promise<Array>} 用户的分享提交列表
   */
  static async getUserSubmissions(userId) {
    // 获取用户的所有分享提交
    const submissions = await ShareSubmission.findByUserId(userId);
    
    // 确保返回完整的数据
    return submissions.map(sub => ({
      id: sub.id,
      user_id: sub.user_id,
      share_url: sub.share_url,
      image_url: sub.image_url,
      platform: sub.platform,
      session_id: sub.session_id,
      language: sub.language,
      status: sub.status,
      reviewer_id: sub.reviewer_id,
      review_note: sub.review_note,
      reviewed_at: sub.reviewed_at,
      reward_granted: sub.reward_granted === 1,
      reward_granted_at: sub.reward_granted_at,
      created_at: sub.created_at,
      updated_at: sub.updated_at
    }));
  }
  
  /**
   * 检查用户是否已经获得过分享奖励
   * @param {string} userId 用户ID
   * @returns {Promise<boolean>} 是否已获得奖励
   */
  static async hasUserReceivedReward(userId) {
    return await ShareSubmission.hasReceivedReward(userId);
  }
}

module.exports = ShareRewardService; 