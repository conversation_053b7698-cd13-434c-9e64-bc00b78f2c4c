import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';
import TarotProgressCircle from '../TarotProgressCircle';

interface YesNoReadingModalProps {
  isOpen: boolean;
  progress: number;
}

const YesNoReadingModal: React.FC<YesNoReadingModalProps> = ({
  isOpen,
  progress
}) => {
  const { theme } = useTheme();
  
  // 确保弹窗在导航过程中不会消失
  useEffect(() => {
    // 检查是否准备导航
    const isReadyToNavigate = localStorage.getItem('readyToNavigate') === 'true';
    
    if (isReadyToNavigate) {
      // 在导航期间禁用关闭弹窗
      return () => {
        // 组件卸载时清理标志
        localStorage.removeItem('readyToNavigate');
      };
    }
  }, []);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className={`w-full max-w-md rounded-xl overflow-hidden shadow-xl ${
            theme === 'light' ? 'bg-white' : 'bg-gray-900'
          } flex flex-col items-center justify-center py-8`}
        >
          {/* 进度条和文字提示 */}
          <div className="flex flex-col items-center justify-center my-4">
            <TarotProgressCircle progress={progress} isGenerating={true} />
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default YesNoReadingModal; 