import React, { useEffect, useRef, useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { getResourceUrl } from '../utils/cdnImageUrl';

interface VideoSectionProps {
  videoSrc: string;
  title: string;
  description: string;
  isRightAligned?: boolean;
  delayLoop?: boolean;
}

const VideoSection: React.FC<VideoSectionProps> = ({
  videoSrc,
  title,
  description,
  isRightAligned = false,
  delayLoop = false
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const videoRef = useRef<HTMLVideoElement>(null);
  const videoContainerRef = useRef<HTMLDivElement>(null);
  const [videoLoaded, setVideoLoaded] = useState(false);
  
  // 处理CDN URL，使用getResourceUrl函数处理视频URL
  const processedVideoSrc = useMemo(() => getResourceUrl(videoSrc), [videoSrc]);

  useEffect(() => {
    // 使用IntersectionObserver检测视频容器是否进入视口
    const options = {
      root: null,
      rootMargin: '300px', // 提前300px开始加载视频
      threshold: 0.1
    };

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !videoLoaded && videoRef.current) {
          // 只有当视频进入视口且尚未加载时才加载视频
          videoRef.current.src = processedVideoSrc;
          videoRef.current.load();
          setVideoLoaded(true);
        }

        // 控制视频播放/暂停
        if (videoRef.current && videoLoaded) {
          if (entry.intersectionRatio >= 0.1 && videoRef.current.paused) {
            videoRef.current.play().catch(e => console.log(t('errors.video_play_failed'), e));
          } else if (entry.intersectionRatio < 0.1 && !videoRef.current.paused) {
            videoRef.current.pause();
          }
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersection, options);

    if (videoContainerRef.current) {
      observer.observe(videoContainerRef.current);
    }

    return () => {
      if (videoContainerRef.current) {
        observer.unobserve(videoContainerRef.current);
      }
    };
  }, [processedVideoSrc, t, videoLoaded]);

  useEffect(() => {
    if (!delayLoop || !videoLoaded) return;

    const handleVideoEnd = (video: HTMLVideoElement) => {
      setTimeout(() => {
        video.play().catch(e => console.log(t('errors.video_play_failed'), e));
      }, 2000);
    };

    if (videoRef.current) {
      videoRef.current.addEventListener('ended', () => handleVideoEnd(videoRef.current!));
    }

    return () => {
      if (videoRef.current) {
        videoRef.current.removeEventListener('ended', () => handleVideoEnd(videoRef.current!));
      }
    };
  }, [delayLoop, t, videoLoaded]);

  return (
    <div className="video-section" style={{ margin: '0', paddingTop: '4rem' }}>
      <div className="video-text-container space-y-32 lg:space-y-48" style={{ maxWidth: '1800px', margin: '0 auto', padding: '0 0.75rem sm:1rem' }}>
        <div className={`flex flex-col lg:flex-row ${isRightAligned ? 'lg:flex-row-reverse' : ''} items-start gap-8`}>
          <div className={`w-full lg:w-[30%] ${isRightAligned ? 'lg:pl-4' : 'lg:pr-4'} pt-2`}>
            <div>
              <h2 
                className="text-2xl sm:text-3xl font-bold mb-4 flex items-center pl-2"
                style={{
                  background: theme === 'light' ? 'none' : 'linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)',
                  WebkitBackgroundClip: theme === 'light' ? 'initial' : 'text',
                  WebkitTextFillColor: theme === 'light' ? 'black' : 'transparent',
                  color: theme === 'light' ? 'black' : 'inherit',
                }}
              >
                <span 
                  className="mr-3 opacity-90"
                  style={{
                    fontSize: '0.9em',
                    background: theme === 'light' ? 'none' : 'linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)',
                    WebkitBackgroundClip: theme === 'light' ? 'initial' : 'text',
                    WebkitTextFillColor: theme === 'light' ? 'black' : 'transparent',
                    color: theme === 'light' ? 'black' : 'inherit',
                  }}
                >
                  ✧
                </span>
                {title}
              </h2>
              <p 
                className="text-base sm:text-lg pl-2"
                style={{
                  maxWidth: '800px',
                  lineHeight: 1.8,
                  color: theme === 'light' ? '#000000' : '#818286',
                  textShadow: theme === 'light' ? 'none' : '0 0 15px rgba(168, 85, 247, 0.15)',
                }}
              >
                {description}
              </p>
            </div>
          </div>

          <div className="w-full sm:w-4/5 md:w-3/4 lg:w-[70%] mx-auto">
            <div 
              ref={videoContainerRef}
              className="video-wrapper w-full mx-auto" 
              style={{ 
                position: 'relative',
                overflow: 'hidden',
                cursor: 'pointer',
                background: 'rgba(0, 0, 0, 0.2)',
                aspectRatio: '1582 / 1080',
                borderRadius: '0.5rem',
                boxShadow: '0 0 30px rgba(168, 85, 247, 0.2)'
              }}
            >
              <video
                ref={videoRef}
                preload="none"
                playsInline
                muted
                loop={!delayLoop}
                controls={false}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  display: 'block'
                }}
              >
                {t('home.video_not_supported')}
              </video>
              {!videoLoaded && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                  <div className="w-10 h-10 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoSection; 