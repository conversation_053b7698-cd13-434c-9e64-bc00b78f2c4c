import { useEffect, useRef } from 'react';
import { getBrowserFingerprint } from '../utils/fingerprint';

interface BrowserFingerprintProps {
  onFingerprintReady: (fingerprint: string) => void;
}

const BrowserFingerprint: React.FC<BrowserFingerprintProps> = ({ onFingerprintReady }) => {
  // 使用ref保存最新的回调函数
  const callbackRef = useRef(onFingerprintReady);
  
  // 更新ref中的回调函数
  useEffect(() => {
    callbackRef.current = onFingerprintReady;
  }, [onFingerprintReady]);

  // 组件挂载时获取浏览器指纹
  useEffect(() => {
    // 获取增强的浏览器指纹
    const getFingerprint = async () => {
      try {
        const fingerprint = await getBrowserFingerprint();
        callbackRef.current(fingerprint);
      } catch (error) {
        // 如果出错，生成一个随机标识符作为后备方案
        const fallbackId = `fallback-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
        callbackRef.current(fallbackId);
      }
    };

    getFingerprint();
  }, []); // 只在组件挂载时执行一次

  // 这是一个无UI组件，不需要渲染任何内容
  return null;
};

export default BrowserFingerprint; 