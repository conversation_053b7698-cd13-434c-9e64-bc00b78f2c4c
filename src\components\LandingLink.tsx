import React, { useEffect } from 'react';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

const LandingLink: React.FC = () => {
  const { navigate } = useLanguageNavigate();

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 确保消息来源是我们的landing页面
      if (event.origin !== window.location.origin) return;
      
      if (event.data === 'navigateToHome') {
        navigate('/home');
      }
    };

    window.addEventListener('message', handleMessage);
    
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [navigate]);

  return null;
};

export default LandingLink; 