// 获取API URL，优先使用环境变量，然后尝试使用当前主机名或备用地址
const getApiUrl = () => {
  const isDevelopment = import.meta.env.MODE === 'development';
  
  if (!isDevelopment) {
    return import.meta.env.VITE_API_URL;
  }
  
  // 开发环境逻辑
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }
  
  // 尝试使用当前主机名
  const hostname = window.location.hostname;
  return `http://${hostname}:5000`;
};

export const API_URL = getApiUrl();

const config = {
  mongodb: {
    uri: process.env.MONGODB_URI || '',
  },
  jwt: {
    secret: process.env.JWT_SECRET || '',
  },
  email: {
    service: process.env.EMAIL_SERVICE || '',
    user: process.env.EMAIL_USER || '',
    pass: process.env.EMAIL_PASS || '',
    from: process.env.EMAIL_FROM || '',
  },
  verification: {
    codeExpiryMinutes: 5,
  },
};

export { config };
