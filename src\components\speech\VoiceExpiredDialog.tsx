import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { VipBadge } from '../VipBadge';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';

interface Props {
  isOpen: boolean;
  onCancel: () => void;
  isVip?: boolean;
}

const VoiceExpiredDialog: React.FC<Props> = ({ isOpen, onCancel, isVip = false }) => {
  const { navigate } = useLanguageNavigate();
  const { t, i18n } = useTranslation();

  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW': // 确保繁体中文使用与简体中文相同的字体类
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  // 按钮点击时调转到会员页面
  const handleUpgradeClick = useCallback(() => {
    // 关闭对话框
    onCancel();
    // 跳转到会员页面，使用自定义导航钩子自动处理语言参数
    navigate('/membership');
  }, [navigate, onCancel]);

  // 阻止事件冒泡，避免点击弹窗内容时触发背景点击事件
  const handleDialogClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  // 按钮点击处理函数
  const handleButtonClick = useCallback((e: React.MouseEvent, callback: () => void) => {
    e.stopPropagation();
    e.preventDefault();
    callback();
  }, []);

  if (!isOpen) return null;

  // VIP用户显示简化的提示
  if (isVip) {
    return (
      <>
        <div
          className="fixed inset-0 bg-black/60 dark:bg-black/60 backdrop-blur-sm z-[9999]"
          onClick={onCancel}
        />
        <div
          className="fixed inset-0 flex items-center justify-center px-4 z-[9999]"
          onClick={onCancel}
        >
          <div 
            className="w-full max-w-md bg-white/90 dark:bg-black/40 backdrop-blur-xl rounded-2xl border border-gray-200 dark:border-purple-500/20 p-4 shadow-2xl relative overflow-hidden"
            onClick={handleDialogClick}
          >
            <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-300/30 dark:bg-purple-500/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-300/30 dark:bg-pink-500/20 rounded-full blur-3xl"></div>
            
            <div className="relative text-center">
              <h3 className={`text-xl sm:text-2xl font-bold mt-2 mb-4 text-gray-800 dark:text-gray-200 ${getFontClass()}`}>
                {t('speech.voice_expired.vip_title', '解读时未使用语音解析，无保存记录')}
              </h3>
              
              <div 
                className="inline-block" 
                role="button" 
                tabIndex={0} 
                onClick={(e) => handleButtonClick(e, onCancel)}
                onTouchEnd={(e) => {
                  e.preventDefault();
                  onCancel();
                }}
              >
                <button
                  className={`px-8 py-2.5 rounded-xl bg-gray-100 dark:bg-white/10 border border-gray-300 dark:border-purple-500/20 backdrop-blur-sm
                           text-gray-700 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-purple-500/10 hover:border-purple-300 dark:hover:border-purple-500/40
                           transition-all duration-200 text-base z-[10000] relative ${getFontClass()} 
                           touch-manipulation active:bg-purple-200 dark:active:bg-purple-500/20 
                           sm:py-2.5 py-3 mt-2`}
                  style={{ WebkitTapHighlightColor: 'transparent' }}
                >
                  {t('common.confirm', '确认')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  // 非VIP用户显示升级会员的促销弹窗
  return (
    <>
      <div
        className="fixed inset-0 bg-black/60 dark:bg-black/60 backdrop-blur-sm z-[9999]"
        onClick={onCancel}
      />
      <div
        className="fixed inset-0 flex items-center justify-center px-4 z-[9999]"
        onClick={onCancel}
      >
        <div 
          className="w-full max-w-md bg-white/90 dark:bg-black/40 backdrop-blur-xl rounded-2xl border border-gray-200 dark:border-purple-500/20 p-4 sm:p-6 shadow-2xl relative overflow-hidden"
          onClick={handleDialogClick}
        >
          {/* 装饰性光效 */}
          <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-300/30 dark:bg-purple-500/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-300/30 dark:bg-pink-500/20 rounded-full blur-3xl"></div>
          
          <div className="relative text-center">
            <div className="flex justify-center">
              <VipBadge className="scale-150" />
            </div>
            <h3 className={`text-xl sm:text-2xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 dark:from-purple-400 dark:via-pink-400 dark:to-purple-400 bg-clip-text text-transparent mt-3 sm:mt-4 ${getFontClass()}`}>
              {t('speech.voice_expired.title', '语音解析记录已过期')}
            </h3>
            
            <p className={`text-center text-gray-700 dark:text-gray-300 mt-2 sm:mt-3 ${getFontClass()}`}>
              {t('speech.voice_expired.description', '升级会员解锁：')}
            </p>

            <ul className="space-y-1.5 mt-2 sm:mt-3 mb-4">
              <li className="flex items-center gap-2 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 py-1 rounded-xl transition-all duration-200">
                <span className="text-purple-500 dark:text-purple-400 text-base group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                <span className={`text-sm md:text-base text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                  {t('speech.voice_expired.benefits.unlimited_voice', '解锁5种vip占卜师音色')}
                </span>
              </li>
              <li className="flex items-center gap-2 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 py-1 rounded-xl transition-all duration-200">
                <span className="text-purple-500 dark:text-purple-400 text-base group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                <span className={`text-sm md:text-base text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                  {t('speech.voice_expired.benefits.permanent_storage', '语音解析永久保存')}
                </span>
              </li>
              <li className="flex items-center gap-2 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 py-1 rounded-xl transition-all duration-200">
                <span className="text-purple-500 dark:text-purple-400 text-base group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                <span className={`text-sm md:text-base text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                  {t('card_back_settings.vip_prompt.benefits.unlimited_tarot_readings')}
                </span>
              </li>
              <li className="flex items-center gap-2 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 py-1 rounded-xl transition-all duration-200">
                <span className="text-purple-500 dark:text-purple-400 text-base group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                <span className={`text-sm md:text-base text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                  {t('card_back_settings.vip_prompt.benefits.unlimited_followup_readings')}
                </span>
              </li>
            </ul>

            <div className="flex flex-col sm:flex-row justify-center gap-2 sm:gap-3">
              <div 
                className="w-full" 
                role="button" 
                tabIndex={0} 
                onClick={(e) => handleButtonClick(e, onCancel)}
                onTouchEnd={(e) => {
                  e.preventDefault();
                  onCancel();
                }}
              >
                <button
                  className={`w-full sm:min-w-[120px] px-4 py-2 rounded-xl bg-gray-100 dark:bg-white/5 border border-gray-300 dark:border-purple-500/20 backdrop-blur-sm
                         text-gray-700 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-purple-500/10 hover:border-purple-300 dark:hover:border-purple-500/40
                         transition-all duration-200 text-sm sm:text-base relative z-[10000] ${getFontClass()}
                         touch-manipulation active:bg-purple-200 dark:active:bg-purple-500/20
                         sm:py-2 py-3`}
                  style={{ WebkitTapHighlightColor: 'transparent' }}
                >
                  {t('card_back_settings.vip_prompt.later')}
                </button>
              </div>
              <div 
                className="w-full" 
                role="button" 
                tabIndex={0} 
                onClick={(e) => handleButtonClick(e, handleUpgradeClick)}
                onTouchEnd={(e) => {
                  e.preventDefault();
                  handleUpgradeClick();
                }}
              >
                <button
                  className="w-full sm:min-w-[120px] relative group z-[10000] touch-manipulation"
                  style={{ WebkitTapHighlightColor: 'transparent' }}
                >
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-pink-600 
                              rounded-xl blur opacity-60 group-hover:opacity-100 transition duration-200 group-active:opacity-100">
                  </div>
                  <div className="relative px-4 bg-white dark:bg-black rounded-xl leading-none flex items-center justify-center sm:py-2 py-3">
                    <span className={`text-purple-700 dark:text-white text-sm sm:text-base ${getFontClass()}`}>{t('card_back_settings.vip_prompt.upgrade')}</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default VoiceExpiredDialog; 