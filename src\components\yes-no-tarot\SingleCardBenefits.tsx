import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

const SingleCardBenefits: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <div className="mt-24 sm:mt-32">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('yes_no_tarot.single_card_benefits.title', 'Benefits of Single Card Yes No Tarot')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
      </div>
      
      {/* Benefits Grid */}
      <div className="max-w-5xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Instant Clarity */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">⚡</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.single_card_benefits.instant_clarity_title', 'Instant Clarity')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.single_card_benefits.instant_clarity_description', 
                'Perfect for urgent decisions when you need immediate guidance. No waiting, no complex interpretations - just pure, direct answers.')}
            </p>
          </div>
          
          {/* Focused Energy */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">🔍</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.single_card_benefits.focused_energy_title', 'Focused Energy')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.single_card_benefits.focused_energy_description', 
                'One card concentrates all the universe\'s wisdom into a single, powerful message specifically for your question.')}
            </p>
          </div>
          
          {/* Daily Guidance */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">📅</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.single_card_benefits.daily_guidance_title', 'Daily Guidance')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.single_card_benefits.daily_guidance_description', 
                'Ideal for morning affirmations or quick check-ins with your intuition throughout the day.')}
            </p>
          </div>
          
          {/* Decision Support */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex items-center mb-2">
              <span className="text-xl mr-2">🧠</span>
              <h4 className={`font-bold text-lg ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.single_card_benefits.decision_support_title', 'Decision Support')}
              </h4>
            </div>
            <p className={`${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.single_card_benefits.decision_support_description', 
                'Whether it\'s career moves, relationship choices, or life transitions, get the clarity you need to move forward confidently.')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SingleCardBenefits; 