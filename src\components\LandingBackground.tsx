import React from 'react';
import { useTheme } from '../contexts/ThemeContext';

const LandingBackground: React.FC = () => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <div className="RenderLoad" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      width: '100%',
      zIndex: -1
    }}>
      {isDark && (
        <>
          <picture>
            <source srcSet="https://cdn.tarotqa.com/images-optimized/bg.webp" type="image/webp" />
            <img 
              className="xfbg" 
              alt="Starry-sky-background"
              decoding="async"
              {...{ fetchpriority: "high" }}
              style={{
                position: 'absolute',
                left: 0,
                top: 0,
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                transformStyle: 'preserve-3d',
                perspective: '2000px',
                transformOrigin: 'center center',
                backfaceVisibility: 'hidden'
              }}
            />
          </picture>
          
          <div 
            className="meteor-1"
            style={{
              position: 'absolute',
              width: '103px',
              height: '2px',
              top: '143px',
              transform: 'rotate(-20deg)',
              opacity: 0.8,
              animation: '10s meteor-anim linear infinite',
              borderRadius: '50%',
              background: 'linear-gradient(45deg, rgba(255, 255, 255, 1) 0%, rgba(246, 246, 246, 0.5) 47%, rgba(237, 237, 237, 0) 100%)'
            }}
          ></div>
          <div 
            className="meteor-2"
            style={{
              position: 'absolute',
              top: '52px',
              left: '114px',
              width: '70px',
              height: '2px',
              transform: 'rotate(-12deg)',
              opacity: 0.6,
              animation: '10s meteor-anim linear infinite',
              borderRadius: '50%',
              background: 'linear-gradient(45deg, rgba(255, 255, 255, 1) 0%, rgba(246, 246, 246, 0.5) 47%, rgba(237, 237, 237, 0) 100%)'
            }}
          ></div>
          <div 
            className="meteor-3"
            style={{
              position: 'absolute',
              top: '124px',
              right: '264px',
              width: '121px',
              height: '2px',
              transform: 'rotate(-23deg)',
              animation: '10s meteor-anim linear infinite',
              borderRadius: '50%',
              background: 'linear-gradient(45deg, rgba(255, 255, 255, 1) 0%, rgba(246, 246, 246, 0.5) 47%, rgba(237, 237, 237, 0) 100%)'
            }}
          ></div>
          <div 
            className="meteor-4"
            style={{
              position: 'absolute',
              top: '65px',
              right: '264px',
              width: '43px',
              height: '2px',
              transform: 'rotate(-12deg)',
              opacity: 0.3,
              animation: '10s meteor-anim linear infinite',
              borderRadius: '50%',
              background: 'linear-gradient(45deg, rgba(255, 255, 255, 1) 0%, rgba(246, 246, 246, 0.5) 47%, rgba(237, 237, 237, 0) 100%)'
            }}
          ></div>
          <div 
            className="meteor-5"
            style={{
              position: 'absolute',
              top: '65px',
              left: '264px',
              width: '51px',
              height: '2px',
              transform: 'rotate(-12deg)',
              opacity: 0.5,
              animation: '10s meteor-anim linear infinite',
              borderRadius: '50%',
              background: 'linear-gradient(45deg, rgba(255, 255, 255, 1) 0%, rgba(246, 246, 246, 0.5) 47%, rgba(237, 237, 237, 0) 100%)'
            }}
          ></div>
          <div 
            className="meteor-6"
            style={{
              position: 'absolute',
              top: '65px',
              left: '525px',
              width: '86px',
              height: '2px',
              transform: 'rotate(-21deg)',
              opacity: 0.5,
              animation: '10s meteor-anim linear infinite',
              borderRadius: '50%',
              background: 'linear-gradient(45deg, rgba(255, 255, 255, 1) 0%, rgba(246, 246, 246, 0.5) 47%, rgba(237, 237, 237, 0) 100%)'
            }}
          ></div>
        </>
      )}
      
      {/* 浅色主题使用纯白色背景，只添加极其轻微的装饰效果 */}
      {!isDark && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: '#ffffff',
            zIndex: -1
          }}
        >
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: 'radial-gradient(circle at 25% 25%, rgba(168, 85, 247, 0.02), transparent 20%), radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.03), transparent 30%)'
            }}
          ></div>
        </div>
      )}
      
      {!isDark && (
        <style dangerouslySetInnerHTML={{__html: `
          .light-theme-gradient {
            background: #ffffff;
          }
        `}} />
      )}
    </div>
  );
};

export default LandingBackground; 