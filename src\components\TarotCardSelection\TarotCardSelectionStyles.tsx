import React from 'react';

interface StylesProps {
  isDark: boolean;
}

const TarotCardSelectionStyles: React.FC<StylesProps> = ({ isDark }) => {
  return (
    <style dangerouslySetInnerHTML={{ __html: `
      .perspective-1000 {
        perspective: 1000px;
      }
      .preserve-3d {
        transform-style: preserve-3d;
        will-change: transform;
      }
      .backface-hidden {
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
      }
      /* 移除数字输入框的上下箭头 */
      input[type="number"]::-webkit-outer-spin-button,
      input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      input[type="number"] {
        -moz-appearance: textfield;
        appearance: textfield;
      }
      .card-wrapper {
        transition: transform 150ms cubic-bezier(0.25, 1, 0.5, 1);
        will-change: transform, z-index;
        z-index: 1;
        transition-property: transform, z-index;
      }
      .hoverable-card:hover {
        transform: translateY(-25px);
        z-index: 100;
        transition: transform 150ms cubic-bezier(0.25, 1, 0.5, 1), z-index 0ms;
      }
      .hoverable-card {
        transition: transform 150ms cubic-bezier(0.25, 1, 0.5, 1), z-index 0ms;
        pointer-events: auto !important;
        cursor: pointer;
      }
      .flipped-card {
        transform: translateY(-25px);
        z-index: 200;
        transition: transform 150ms cubic-bezier(0.25, 1, 0.5, 1) !important;
      }
      .card-hover {
        pointer-events: auto;
      }
      .card-container {
        transform: translateZ(0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        will-change: transform;
      }
      .will-change-transform {
        will-change: transform;
      }
      .hide-scrollbar-default {
        scrollbar-width: thin;
        scrollbar-color: rgba(147, 51, 234, 0.5) ${isDark ? 'rgba(31, 41, 55, 0.3)' : 'rgba(229, 231, 235, 0.3)'};
      }
      .hide-scrollbar-default::-webkit-scrollbar {
        display: block;
        height: 8px;
      }
      .hide-scrollbar-default::-webkit-scrollbar-track {
        background: ${isDark ? 'rgba(31, 41, 55, 0.3)' : 'rgba(229, 231, 235, 0.3)'};
        border-radius: 4px;
      }
      .hide-scrollbar-default::-webkit-scrollbar-thumb {
        background: rgba(147, 51, 234, 0.5);
        border-radius: 4px;
      }
      .hide-scrollbar-default::-webkit-scrollbar-thumb:hover {
        background: rgba(147, 51, 234, 0.7);
      }
      @media (hover: none) {
        .hide-scrollbar-default {
          -webkit-overflow-scrolling: touch;
        }
        .hide-scrollbar-default::-webkit-scrollbar {
          height: 4px;
        }
      }
    `}} />
  );
};

export default TarotCardSelectionStyles; 