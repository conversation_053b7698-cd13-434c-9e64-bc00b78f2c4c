/* 导航栏特殊效果样式 */

/* 文本阴影效果 - 用于导航栏下拉菜单 */
.text-shadow-sm {
  text-shadow: 0 0 0.5px rgba(168, 85, 247, 0.2);
  transition: text-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 丝滑字体过渡效果 */
.blog-dropdown-item {
  position: relative;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.blog-dropdown-item:hover .text-shadow-sm,
.blog-dropdown-item:hover {
  text-shadow: 0 0 1px rgba(168, 85, 247, 0.4), 0 0 2px rgba(236, 72, 153, 0.2);
}

.blog-dropdown-item::after {
  content: '';
  position: absolute;
  width: 0;
  height: 1.5px;
  bottom: 0;
  left: 50%;
  background: linear-gradient(to right, #A78BFA, #EC4899);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateX(-50%);
  opacity: 0;
  border-radius: 2px;
}

.blog-dropdown-item:hover::after {
  width: 60%;
  opacity: 0.8;
}

/* 悬停时的特殊字体效果 */
.blog-dropdown-item:hover {
  letter-spacing: 0.01em;
  font-weight: 600;
  transform: translateY(-1px);
}

/* 平滑的字体粗细过渡 */
.blog-dropdown-item {
  font-variation-settings: 'wght' 400;
  transition: font-variation-settings 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              letter-spacing 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              text-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.blog-dropdown-item:hover {
  font-variation-settings: 'wght' 600;
} 