import React from 'react';
import { motion } from 'framer-motion';
import ActionButton from './ActionButton';
import SelectedCards from './SelectedCards';

interface SpreadContainerProps {
  isDark: boolean;
  t: (key: string, options?: any) => string;
  translatedSpreadInfo: any;
  requiredCards: number;
  flippedCards: number[];
  selectedCards: number[];
  cardOrientations: Map<number, boolean>;
  cardImages: Map<number, string>;
  currentLanguage: string;
  selectionMode: 'slide' | 'number' | 'custom';
  handleFormSpread: () => void;
  onSelectPosition?: (index: number) => void;
  onToggleOrientation?: (cardId: number) => void;
}

const SpreadContainer: React.FC<SpreadContainerProps> = ({
  isDark,
  t,
  translatedSpreadInfo,
  requiredCards,
  flippedCards,
  cardOrientations,
  cardImages,
  currentLanguage,
  selectionMode,
  handleFormSpread,
  onSelectPosition,
  onToggleOrientation
}) => {
  // 准备位置数据
  const positions = Array.from({ length: requiredCards }).map((_, index) => {
    const position = translatedSpreadInfo?.positions?.[index] || `位置 ${index + 1}`;
    const selectedCard = flippedCards[index];
    const orientation = cardOrientations.get(selectedCard) || false;
    
    return {
      card: selectedCard,
      position,
      orientation
    };
  });
  
  // 根据卡牌数量决定内边距大小
  const getPadding = () => {
    const cardCount = requiredCards;
    
    if (cardCount <= 3) {
      // 移动端优化：少量卡牌时可以用较大内边距
      return "p-4 xs:p-5 sm:p-6 md:p-10";
    } else if (cardCount <= 5) {
      // 移动端优化：中等数量卡牌时适中内边距
      return "p-3 xs:p-4 sm:p-5 md:p-8";
    } else {
      // 移动端优化：大量卡牌时减小内边距
      return "p-2 xs:p-3 sm:p-4 md:p-6";
    }
  };
  
  return (
    <motion.div 
      className={`${isDark ? 'bg-gradient-to-b from-gray-800/40 to-gray-900/40 backdrop-blur-md border-purple-500/10' : 'bg-gradient-to-b from-gray-100/80 to-white/80 backdrop-blur-md border-purple-300/20'} rounded-xl sm:rounded-2xl overflow-hidden shadow-lg sm:shadow-2xl border mt-4 sm:mt-6 mb-6 sm:mb-8`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      {/* Header with spread name */}
      <div className={`py-3 sm:py-5 px-4 sm:px-6 md:px-8 ${isDark ? 'border-gray-700/30' : 'border-gray-300/50'} border-b`}>
        <h2 className="main-title text-base sm:text-lg md:text-xl mb-0.5 sm:mb-1 text-center">
          {translatedSpreadInfo?.name || t('reading.shuffle.form_spread')}
        </h2>
        {translatedSpreadInfo?.description && (
          <p className={`text-xs sm:text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'} text-center mx-auto max-w-2xl line-clamp-2 sm:line-clamp-none`}>
            {translatedSpreadInfo.description}
          </p>
        )}
      </div>
      
      {/* Cards container */}
      <div className={getPadding()}>
        <SelectedCards 
          positions={positions}
          cardImages={cardImages}
          currentLanguage={currentLanguage}
          isDark={isDark}
          selectionMode={selectionMode}
          onSelectPosition={onSelectPosition}
          onToggleOrientation={onToggleOrientation}
          t={t}
        />
      </div>

      {/* Action button for custom mode */}
      {selectionMode === 'custom' && (
        <div className="px-4 pb-4 sm:px-6 sm:pb-6 md:px-8 md:pb-8">
          <ActionButton
            onClick={handleFormSpread}
            isEnabled={flippedCards.length === requiredCards}
            isDark={isDark}
            t={t}
            requiredCards={requiredCards}
            selectedCount={flippedCards.length}
          />
        </div>
      )}
    </motion.div>
  );
};

export default SpreadContainer; 