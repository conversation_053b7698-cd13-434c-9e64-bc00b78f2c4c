import axios from 'axios';

const isDevelopment = import.meta.env.MODE === 'development';

// 创建一个自定义事件用于处理未授权错误
export const authEvents = new EventTarget();
export const AUTH_ERROR_EVENT = 'authError';

// 在开发环境下设置多个可能的API地址
const getApiUrls = () => {
  if (!isDevelopment) {
    return [import.meta.env.VITE_API_URL];
  }

  // 首先尝试环境变量
  const urls = [];
  if (import.meta.env.VITE_API_URL) {
    urls.push(import.meta.env.VITE_API_URL);
  }

  // 添加当前主机名
  urls.push(`http://${window.location.hostname}:5000`);
  
  // 添加指定的局域网IP作为备选
  urls.push('http://***********:5000');
  
  // 添加localhost作为最后选项
  if (!urls.includes('http://localhost:5000')) {
    urls.push('http://localhost:5000');
  }
  
  return urls;
};

// 获取API基础URL
let currentUrlIndex = 0;
const apiUrls = getApiUrls();
let baseApiUrl = apiUrls[currentUrlIndex];

// 创建 axios 实例
const instance = axios.create({
  baseURL: baseApiUrl,
  timeout: 1800000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 从 localStorage 获取 token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 添加Accept-Language请求头，传递当前语言设置
    const currentLanguage = localStorage.getItem('i18nextLng') || 'zh-TW';
    config.headers['Accept-Language'] = currentLanguage;
    
    // 使用当前选择的API URL
    config.baseURL = baseApiUrl;
    return config;
  },
  (error) => {
    // 检查是否是取消请求的错误
    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }
    
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理请求取消的情况
    if (axios.isCancel(error)) {
      // 这是一个预期的行为，不是真正的错误
      return Promise.reject(error);
    }
    
    // 在开发环境下，如果是连接错误，尝试切换到下一个API URL
    if (isDevelopment && error.message && 
        (error.message.includes('Network Error') || 
         error.code === 'ECONNABORTED' || 
         !error.response)) {
      
      currentUrlIndex = (currentUrlIndex + 1) % apiUrls.length;
      baseApiUrl = apiUrls[currentUrlIndex];
      
      // 如果尚未尝试所有URL，则重试请求
      if (error.config && currentUrlIndex !== 0) {
        error.config.baseURL = baseApiUrl;
        return axios(error.config);
      }
    }
    
    if (error.response) {
      // 处理特定的错误状态码
      const status = error.response.status;
      const url = error.config?.url || 'unknown URL';
      
      // 需要忽略的常见客户端错误
      const shouldIgnoreError =
        // 忽略所有400状态码错误
        status === 400 ||
        // 忽略与邮箱验证相关的特定404错误
        (status === 404 &&
         url.includes('/api/auth/resend-verification') &&
         error.response.data?.message === '该邮箱未注册');

      // 如果是需要忽略的错误，直接返回
      if (shouldIgnoreError) {
        return Promise.reject(error);
      }
      
      switch (status) {
        case 401:
          // 触发未授权事件，让 UserContext 处理
          authEvents.dispatchEvent(new Event(AUTH_ERROR_EVENT));
          break;
        case 403:
          // 权限不足
          break;
        case 404:
          // 资源不存在
          break;
        case 500:
          // 服务器错误
          break;
        default:
          if (status !== 400) {  // 不显示 400 错误的通用消息
            // console.error('请求失败');
          }
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      // console.error('无法连接到服务器:', error.request);
    } else {
      // 请求配置出错，但不是取消请求
      if (!error.message.includes('canceled')) {
        // console.error('请求配置错误:', error.message);
      }
    }
    return Promise.reject(error);
  }
);

export default instance;
