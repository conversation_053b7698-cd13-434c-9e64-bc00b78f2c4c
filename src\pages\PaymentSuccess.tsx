import React, { useEffect } from 'react';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { toast } from 'react-hot-toast';
import { useUser } from '../contexts/UserContext';
import { getCurrentUser } from '../services/userService';
import { useTranslation } from 'react-i18next';
import { checkPrivilegeStatus } from '../services/invitationService';

const PaymentSuccess: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { setUser } = useUser();
  const { t } = useTranslation();

  useEffect(() => {
    const updateUserInfo = async () => {
      try {
        const updatedUser = await getCurrentUser();
        setUser(updatedUser);
        toast.success(t('membership.success.member_activated'));
        
        // 检查用户是否有内部折扣权限且未使用过折扣
        try {
          const privilegeStatus = await checkPrivilegeStatus();
          
          // 根据内部权限状态决定跳转到哪个会员页面
          const targetPage = (privilegeStatus.hasInternalPrivilege && 
                            privilegeStatus.privilegeInfo && 
                            !privilegeStatus.privilegeInfo.has_used_discount) 
                            ? '/membership-inter' : '/membership';
                            
          // 3秒后跳转到相应会员页面
          setTimeout(() => {
            navigate(targetPage);
          }, 3000);
        } catch (error) {
          // console.error('检查内部折扣权限失败:', error);
          // 出错时默认跳转到普通会员页面
          setTimeout(() => {
            navigate('/membership');
          }, 3000);
        }
      } catch (error) {
        // console.error('Failed to update user info:', error);
        toast.error(t('membership.errors.status_update_failed'));
        // 出错时也跳转到普通会员页面
        setTimeout(() => {
          navigate('/membership');
        }, 3000);
      }
    };

    updateUserInfo();
  }, [setUser, navigate, t]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="text-center">
        <div className="mb-8">
          <svg
            className="mx-auto h-24 w-24 text-green-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h2 className="text-3xl font-bold text-white mb-4">{t('payment.success.title')}</h2>
        <p className="text-gray-400 mb-8">{t('payment.success.message')}</p>
      </div>
    </div>
  );
};

export default PaymentSuccess; 