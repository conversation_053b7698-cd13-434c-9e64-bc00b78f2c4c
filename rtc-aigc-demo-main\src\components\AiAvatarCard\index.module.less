/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

.card {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24px;

  .avatar {
    position: relative;
    border-radius: 50%;
    width: 167.5px;
    height: 167.5px;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .aiStatus {
    position: absolute;
    border: 1px solid;
    border-image-source: linear-gradient(77.86deg, #e5f2ff -3.23%, #d9e5ff 51.11%, #f6e2ff 98.65%);
    box-shadow: 0px 2px 22px 0px #0000001a;
    width: 93px;
    height: 73px;
    border-radius: 24px;
    top: -28px;
    left: -56px;
    color: #635bff;
    font-weight: 500;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 8px;
    background: #ffffff;
  }

  .barContainer {
    display: flex;
    gap: 4px;
  }

  .bar {
    width: 11px;
    height: 16px;
    border-radius: 6px;
    animation: shake 1s ease infinite;
    background-color: #4f4fff;
  }

  .bar:nth-child(1) {
    animation-delay: -0.4s;
  }

  .bar:nth-child(2) {
    animation-delay: -0.2s;
  }

  @keyframes shake {
    0% {
      transform: scaleY(1);
    }
    50% {
      transform: scaleY(0.5);
    }
    100% {
      transform: scaleY(1);
    }
  }
}

.fullScreen {
  .avatar {
    width: 115px;
    height: 115px;
  }

  .aiStatus {
    width: 72px;
    height: 56px;
    top: -24px;
    left: 86px;
    font-size: 12px;
  }
}
