import { useTranslation } from 'react-i18next';

// 定义页面类型常量
export const PAGE_TYPES = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
  LOVE: 'love'
};

export interface HoroscopeSign {
  id: string;
  nameKey: string;
  defaultName: string;
  iconPath: string;
  dateRange: string;
}

export const horoscopeSigns: HoroscopeSign[] = [
  {
    id: 'aries',
    nameKey: 'zodiac.aries',
    defaultName: '白羊座',
    iconPath: '/images-optimized/horoscope/aries.webp',
    dateRange: '3.21-4.19'
  },
  {
    id: 'taurus',
    nameKey: 'zodiac.taurus',
    defaultName: '金牛座',
    iconPath: '/images-optimized/horoscope/taurus.webp',
    dateRange: '4.20-5.20'
  },
  {
    id: 'gemini',
    nameKey: 'zodiac.gemini',
    defaultName: '双子座',
    iconPath: '/images-optimized/horoscope/gemini.webp',
    dateRange: '5.21-6.21'
  },
  {
    id: 'cancer',
    nameKey: 'zodiac.cancer',
    defaultName: '巨蟹座',
    iconPath: '/images-optimized/horoscope/cancer.webp',
    dateRange: '6.22-7.22'
  },
  {
    id: 'leo',
    nameKey: 'zodiac.leo',
    defaultName: '狮子座',
    iconPath: '/images-optimized/horoscope/leo.webp',
    dateRange: '7.23-8.22'
  },
  {
    id: 'virgo',
    nameKey: 'zodiac.virgo',
    defaultName: '处女座',
    iconPath: '/images-optimized/horoscope/virgo.webp',
    dateRange: '8.23-9.22'
  },
  {
    id: 'libra',
    nameKey: 'zodiac.libra',
    defaultName: '天秤座',
    iconPath: '/images-optimized/horoscope/libra.webp',
    dateRange: '9.23-10.23'
  },
  {
    id: 'scorpio',
    nameKey: 'zodiac.scorpio',
    defaultName: '天蝎座',
    iconPath: '/images-optimized/horoscope/scorpio.webp',
    dateRange: '10.24-11.22'
  },
  {
    id: 'sagittarius',
    nameKey: 'zodiac.sagittarius',
    defaultName: '射手座',
    iconPath: '/images-optimized/horoscope/sagittarius.webp',
    dateRange: '11.23-12.21'
  },
  {
    id: 'capricorn',
    nameKey: 'zodiac.capricorn',
    defaultName: '摩羯座',
    iconPath: '/images-optimized/horoscope/capricorn.webp',
    dateRange: '12.22-1.19'
  },
  {
    id: 'aquarius',
    nameKey: 'zodiac.aquarius',
    defaultName: '水瓶座',
    iconPath: '/images-optimized/horoscope/aquarius.webp',
    dateRange: '1.20-2.18'
  },
  {
    id: 'pisces',
    nameKey: 'zodiac.pisces',
    defaultName: '双鱼座',
    iconPath: '/images-optimized/horoscope/pisces.webp',
    dateRange: '2.19-3.20'
  }
];

export const horoscopePeriods = [
  {
    id: 'daily',
    nameKey: 'horoscope.periods.daily',
    defaultName: '每日运势'
  },
  {
    id: 'weekly',
    nameKey: 'horoscope.periods.weekly',
    defaultName: '每周运势'
  },
  {
    id: 'yearly',
    nameKey: 'horoscope.periods.yearly',
    defaultName: '年度运势'
  }
];

export const dailyOptions = [
  {
    id: 'yesterday',
    nameKey: 'horoscope.daily.yesterday',
    defaultName: '昨日运势'
  },
  {
    id: 'today',
    nameKey: 'horoscope.daily.today',
    defaultName: '今日运势'
  },
  {
    id: 'tomorrow',
    nameKey: 'horoscope.daily.tomorrow',
    defaultName: '明日运势'
  }
];

export const useHoroscopeData = () => {
  const { t } = useTranslation();
  
  const getSignName = (signId: string) => {
    const sign = horoscopeSigns.find(s => s.id === signId);
    return sign ? t(sign.nameKey, sign.defaultName) : '';
  };
  
  const getPeriodName = (periodId: string) => {
    const period = horoscopePeriods.find(p => p.id === periodId);
    return period ? t(period.nameKey, period.defaultName) : '';
  };
  
  const getDailyOptionName = (optionId: string) => {
    const option = dailyOptions.find(o => o.id === optionId);
    return option ? t(option.nameKey, option.defaultName) : '';
  };
  
  return {
    signs: horoscopeSigns,
    periods: horoscopePeriods,
    dailyOptions,
    getSignName,
    getPeriodName,
    getDailyOptionName
  };
}; 