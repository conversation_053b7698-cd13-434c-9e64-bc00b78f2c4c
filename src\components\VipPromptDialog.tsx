import React from 'react';
import { useTranslation } from 'react-i18next';
import { VipBadge } from './VipBadge';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

interface Props {
  isOpen: boolean;
  onCancel: () => void;
}

const VipPromptDialog: React.FC<Props> = ({ isOpen, onCancel }) => {
  const { navigate } = useLanguageNavigate();
  const { t, i18n } = useTranslation();

  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW': // 确保繁体中文使用与简体中文相同的字体类
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  // 按钮点击时调转到会员页面
  const handleUpgradeClick = () => {
    // 关闭对话框
    onCancel();
    // 跳转到会员页面，使用自定义导航钩子自动处理语言参数
    navigate('/membership');
  };

  if (!isOpen) return null;

  return (
    <>
      <div
        className="fixed inset-0 bg-black/60 dark:bg-black/60 backdrop-blur-sm z-[9999]"
        onClick={onCancel}
      />
      <div
        className="fixed inset-0 flex items-center justify-center px-4 z-[9999]"
      >
        <div className="w-full max-w-md bg-white/90 dark:bg-black/40 backdrop-blur-xl rounded-2xl border border-gray-200 dark:border-purple-500/20 p-4 sm:p-6 md:p-8 shadow-2xl relative overflow-hidden">
          {/* 装饰性光效 */}
          <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-300/30 dark:bg-purple-500/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-300/30 dark:bg-pink-500/20 rounded-full blur-3xl"></div>
          
          <div className="relative text-center space-y-4 sm:space-y-6">
            <div>
              <div className="flex justify-center">
                <VipBadge className="scale-150" />
              </div>
              <h3 className={`text-xl sm:text-2xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 dark:from-purple-400 dark:via-pink-400 dark:to-purple-400 bg-clip-text text-transparent mt-4 sm:mt-6 ${getFontClass()}`}>
                {t('card_back_settings.vip_prompt.title')}
              </h3>
            </div>
            
            <div className="space-y-2">

              <ul className="space-y-2 sm:space-y-3 pt-2">
                <li className="flex items-center gap-2 sm:gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 sm:px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-base sm:text-lg group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                  <span className={`text-sm sm:text-base md:text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                    {t('card_back_settings.vip_prompt.benefits.unlimited_tarot_readings')}
                  </span>
                </li>
                <li className="flex items-center gap-2 sm:gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 sm:px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-base sm:text-lg group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                  <span className={`text-sm sm:text-base md:text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                    {t('card_back_settings.vip_prompt.benefits.yes_no_readings')}
                  </span>
                </li>
                <li className="flex items-center gap-2 sm:gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 sm:px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-base sm:text-lg group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                  <span className={`text-sm sm:text-base md:text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                    {t('card_back_settings.vip_prompt.benefits.all_readers')}
                  </span>
                </li>
                <li className="flex items-center gap-2 sm:gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 sm:px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-base sm:text-lg group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                  <span className={`text-sm sm:text-base md:text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                    {t('card_back_settings.vip_prompt.benefits.voice_readings')}
                  </span>
                </li>
                <li className="flex items-center gap-2 sm:gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 sm:px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-base sm:text-lg group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                  <span className={`text-sm sm:text-base md:text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                    {t('card_back_settings.vip_prompt.benefits.unlimited_followup_readings')}
                  </span>
                </li>
                <li className="flex items-center gap-2 sm:gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 sm:px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-base sm:text-lg group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                  <span className={`text-sm sm:text-base md:text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                    {t('card_back_settings.vip_prompt.benefits.voice_history')}
                  </span>
                </li>
                <li className="flex items-center gap-2 sm:gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 sm:px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-base sm:text-lg group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                  <span className={`text-sm sm:text-base md:text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                    {t('card_back_settings.vip_prompt.benefits.card_backs')}
                  </span>
                </li>
                <li className="flex items-center gap-2 sm:gap-3 group hover:bg-purple-100 dark:hover:bg-purple-500/10 px-2 sm:px-3 py-1.5 rounded-xl transition-all duration-200">
                  <span className="text-purple-500 dark:text-purple-400 text-base sm:text-lg group-hover:scale-110 transition-transform flex-shrink-0 inline-flex">✦</span>
                  <span className={`text-sm sm:text-base md:text-lg text-gray-700 dark:text-gray-300 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors text-left ${getFontClass()}`}>
                    {t('card_back_settings.vip_prompt.benefits.cancel_anytime')}
                  </span>
                </li>
              </ul>
            </div>

            <div className="mt-4 sm:mt-8 flex flex-col sm:flex-row justify-center gap-3 sm:gap-4">
              <button
                onClick={onCancel}
                className={`w-full sm:min-w-[120px] px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl bg-gray-100 dark:bg-white/5 border border-gray-300 dark:border-purple-500/20 backdrop-blur-sm
                         text-gray-700 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-purple-500/10 hover:border-purple-300 dark:hover:border-purple-500/40
                         transition-all duration-200 text-sm sm:text-base ${getFontClass()}`}
              >
                {t('card_back_settings.vip_prompt.later')}
              </button>
              <button
                onClick={handleUpgradeClick}
                className="w-full sm:min-w-[120px] relative group"
              >
                <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-pink-600 
                              rounded-xl blur opacity-60 group-hover:opacity-100 transition duration-200">
                </div>
                <div className="relative px-4 sm:px-6 py-2.5 sm:py-3 bg-white dark:bg-black rounded-xl leading-none flex items-center justify-center">
                  <span className={`text-purple-700 dark:text-white text-sm sm:text-base ${getFontClass()}`}>{t('card_back_settings.vip_prompt.upgrade')}</span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default VipPromptDialog; 