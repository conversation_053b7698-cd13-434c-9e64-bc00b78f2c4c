# TTS功能部署指南

此文档提供了在服务器上正确配置和部署TTS（文本转语音）功能的步骤。

## 1. 修改服务器环境变量

在服务器上创建或编辑`.env`文件，添加以下配置：

```bash
# Python解释器路径配置
# 指向虚拟环境中的Python解释器
PYTHON_EXEC_PATH=/opt/python-envs/edge-tts-env/bin/python
```

## 2. 创建Python虚拟环境

```bash
# 安装必要的包
sudo apt update
sudo apt install python3-venv python3-pip python3-dev build-essential

# 创建虚拟环境目录
sudo mkdir -p /opt/python-envs
cd /opt/python-envs

# 创建虚拟环境
python3 -m venv edge-tts-env

# 激活虚拟环境
source edge-tts-env/bin/activate

# 安装edge-tts和websockets包
pip install edge-tts websockets==10.4

# 验证安装
python -c "import edge_tts; import websockets; print(f'Edge TTS installed, websockets version: {websockets.__version__}')"

# 退出虚拟环境
deactivate
```

## 3. 设置权限

```bash
# 如果你的Node.js应用以特定用户运行（例如www-data），设置合适的权限
sudo chown -R www-data:www-data /opt/python-envs/edge-tts-env

# 或者简单地设置所有用户可访问的权限
sudo chmod -R 755 /opt/python-envs/edge-tts-env
```

## 4. 重启服务

```bash
# 如果使用PM2
pm2 restart tarot-backend

# 如果使用systemd
sudo systemctl restart tarot-service
```

## 5. 检查日志确认功能正常

```bash
# 如果使用PM2
pm2 logs tarot-backend

# 如果使用systemd
sudo journalctl -u tarot-service -f
```

## 备注

* 确保服务器环境中的Python版本兼容edge-tts库
* 如果遇到依赖问题，您可能需要在虚拟环境中安装额外的库
* 在本地开发环境中，代码将默认使用`python3`命令 