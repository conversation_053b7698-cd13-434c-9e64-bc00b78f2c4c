/*
	jsrepo 1.42.0
	Installed from https://reactbits.dev/ts/default/
	2025-3-7
*/

.card-spotlight {
  position: relative;
  border-radius: 1.5rem;
  border: 1px solid #222;
  background-color: #111;
  padding: 2rem;
  overflow: hidden;
  --mouse-x: 50%;
  --mouse-y: 50%;
  --spotlight-color: rgba(255, 255, 255, 0.05);
}

/* 浅色模式下的卡片样式 */
.card-spotlight-light {
  background-color: #ffffff;
  border: 1px solid #e2e2e2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
}

.card-spotlight::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at var(--mouse-x) var(--mouse-y), var(--spotlight-color), transparent 80%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.card-spotlight:hover::before,
.card-spotlight:focus-within::before {
  opacity: 0.6;
}
