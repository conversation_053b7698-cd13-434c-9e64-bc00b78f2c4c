# TarotQA Firefox Extension

这是TarotQA塔罗牌占卜工具的Firefox版本插件。

## 功能特点

- 🔮 随机抽取塔罗牌进行Yes/No占卜
- 🎴 包含22张大阿卡纳塔罗牌
- 🔄 支持正位和逆位解读
- ✨ 精美的3D翻牌动画效果
- 🎨 优雅的用户界面设计

## 安装方法

### 方法一：临时安装（开发测试）
1. 打开Firefox浏览器
2. 在地址栏输入 `about:debugging`
3. 点击左侧的"此Firefox"
4. 点击"临时载入附加组件"
5. 选择插件目录中的 `manifest.json` 文件
6. 插件将被临时安装，重启浏览器后会被移除

### 方法二：打包并签名（正式发布）
1. 将整个插件目录打包为 `.zip` 文件
2. 访问 [Firefox Add-on Developer Hub](https://addons.mozilla.org/developers/)
3. 登录你的Mozilla账户
4. 上传zip文件进行签名
5. 选择分发方式：
   - **官方商店发布**：插件会在addons.mozilla.org上公开
   - **自主分发**：获得签名的.xpi文件，可自行分发

## 使用方法

1. 安装插件后，点击浏览器工具栏中的TarotQA图标
2. 在弹出的窗口中点击"Draw a Card"按钮
3. 观看精美的翻牌动画
4. 查看抽到的塔罗牌和对应的Yes/No答案
5. 点击"Draw Another Card"可以继续抽牌

## 技术说明

### Chrome版本与Firefox版本的主要区别：

1. **Manifest版本**：
   - Chrome: Manifest V3
   - Firefox: Manifest V2（更稳定）

2. **API差异**：
   - Chrome: 使用 `action`
   - Firefox: 使用 `browser_action`

3. **权限设置**：
   - 添加了对外部图片资源的访问权限
   - 设置了内容安全策略

### 文件结构：
```
tarot-extension-firefox/
├── manifest.json          # 插件配置文件
├── popup.html            # 弹窗界面
├── popup.js              # 功能逻辑
├── images/               # 图标资源
│   ├── icon16.png
│   ├── icon48.png
│   ├── icon128.png
│   └── card-back.jpg
└── README.md             # 说明文档
```

## 注意事项

- 插件需要访问 `https://cdn.tarotqa.com` 来加载塔罗牌图片
- 确保网络连接正常以获得最佳体验
- 如需修改塔罗牌数据，请编辑 `popup.js` 中的 `tarotCards` 数组

## 支持

如有问题或建议，请访问 [TarotQA官网](https://tarotqa.com) 获取更多信息。
