import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import SEO from '../components/SEO';
import './HistoryDetail.css';
import { useTheme } from '../contexts/ThemeContext';
import { HistorySession, TranslatedSpread } from '../types/history';
import { getTranslatedSpreadInfo } from '../utils/historyDetailUtils';
import { getFontClass } from '../utils/fontUtils';
import CardDisplay from '../components/history/CardDisplay';
import MessageDisplay from '../components/history/MessageDisplay';
import { useMessageProcessor } from '../hooks/useMessageProcessor';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

const HistoryDetail = () => {
  const { id } = useParams();
  const { navigate } = useLanguageNavigate();
  const { t, i18n } = useTranslation();
  const [session, setSession] = useState<HistorySession | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // 使用自定义钩子处理消息
  const messages = useMessageProcessor(session);

  useEffect(() => {
    const fetchSessionDetail = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          navigate('/login');
          return;
        }

        const response = await axios.get(
          `${import.meta.env.VITE_API_URL}/api/session/${id}`,
          {
            headers: { Authorization: `Bearer ${token}` }
          }
        );

        if (response.data.success) {
          if (!response.data.session) {
            throw new Error(t('errors.session_not_exist'));
          }
          setSession(response.data.session);
        } else {
          throw new Error(response.data.message || '获取会话详情失败');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '获取会话详情失败';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchSessionDetail();
  }, [id]);

  // 获取当前语言的字体类
  const currentFontClass = () => getFontClass(i18n.language);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col relative">
        <SEO />
        <LandingBackground />
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="flex-grow px-2 lg:px-8 py-1"
        >
          <div className="max-w-6xl w-full mx-auto">
            <div className="history-detail-content">
              {/* 仅保留标题部分，删除返回按钮 */}
              <div className="w-full flex justify-center mb-4">
                <div className="text-center mt-8 sm:mt-10 px-4 max-w-full">
                  <h1 className={`main-title ${currentFontClass()}`}>{t('history.detail.title')}</h1>
                  <p className="history-detail-timestamp text-sm sm:text-base">
                    {new Date(session?.timestamp || '').toLocaleDateString(i18n.language, {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>

              {/* 加载状态显示 */}
              <div className="flex items-center justify-center py-12">
                <div className="text-purple-400 text-lg">{t('history.detail.loading')}</div>
              </div>
            </div>
          </div>
        </motion.div>
        <Footer />
      </div>
    );
  }

  if (error || !session) {
    return (
      <div className="min-h-screen flex flex-col relative">
        <SEO />
        <LandingBackground />
        <div className="flex-grow px-2 lg:px-8 py-1 flex items-center justify-center">
          <div className="text-red-500 text-lg">{error || t('history.detail.error')}</div>
        </div>
        <Footer />
      </div>
    );
  }

  // 获取翻译后的牌阵信息
  const translatedSpread: TranslatedSpread | null = getTranslatedSpreadInfo(session.selectedSpread, t);

  return (
    <div className="min-h-screen flex flex-col relative">
      <SEO />
      <LandingBackground />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
        className="flex-grow px-2 lg:px-8 py-1"
      >
        <div className="max-w-6xl w-full mx-auto">
          <div className="history-detail-content">
            {/* 仅保留标题部分，删除返回按钮 */}
            <div className="w-full flex justify-center mb-4">
              <div className="text-center mt-8 sm:mt-10 px-4 max-w-full">
                <h1 className={`main-title ${currentFontClass()}`}>{t('history.detail.title')}</h1>
                <p className={`history-detail-timestamp text-sm sm:text-base ${!isDark ? 'text-purple-700' : ''}`}>
                  {new Date(session?.timestamp || '').toLocaleDateString(i18n.language, {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>

            {/* Selected cards display */}
            <motion.div 
              className={`${isDark ? 'bg-gradient-to-b from-gray-800/40 to-gray-900/40' : 'bg-gradient-to-b from-gray-100/70 to-gray-200/70'} backdrop-blur-md rounded-xl sm:rounded-2xl overflow-hidden shadow-2xl ${isDark ? 'border border-purple-500/10' : 'border border-purple-300/30'} mt-6`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              {/* Header with spread name */}
              <div className={`py-4 sm:py-6 px-4 sm:px-8 ${isDark ? 'border-b border-gray-700/30' : 'border-b border-gray-300/50'}`}>
                <h2 className={`main-title text-center ${currentFontClass()}`}>
                  {translatedSpread?.name || '塔罗牌阵'}
                </h2>
              </div>
              
              {/* Cards container */}
              <div className="py-8 sm:py-10 md:py-12 px-4 sm:px-6 md:px-8">
                <CardDisplay 
                  cards={session.selectedCards} 
                  positions={translatedSpread?.positions || []} 
                  getFontClass={currentFontClass}
                />
              </div>
            </motion.div>

            <motion.div 
              className={`flex-1 ${isDark ? 'bg-gradient-to-b from-gray-800/40 to-gray-900/40' : 'bg-gradient-to-b from-gray-100/70 to-gray-200/70'} backdrop-blur-md rounded-xl sm:rounded-2xl overflow-hidden shadow-2xl ${isDark ? 'border border-purple-500/10' : 'border border-purple-300/30'} mt-6`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {/* Header with reader info */}
              <div className={`px-4 sm:px-8 py-2 sm:py-3 ${isDark ? 'border-b border-gray-700/30' : 'border-b border-gray-300/50'} flex justify-center`}>
                {session?.selectedReader && (
                  <div className="text-center space-y-0">
                    <h2 className={`text-lg font-bold ${isDark ? 'text-white' : 'text-gray-800'} ${currentFontClass()}`}>
                      {t(`reader.${session.selectedReader.id}.name`)}
                    </h2>
                    <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-600'} ${currentFontClass()}`}>
                      {t(`reader.${session.selectedReader.id}.type`)}
                    </p>
                  </div>
                )}
              </div>
              
              {/* Messages container */}
              <div 
                className="p-3 sm:p-4 md:p-6 lg:p-8 max-h-[60vh] overflow-y-auto scroll-smooth scrollbar-tarot"
                style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: isDark ? 'rgba(139, 92, 246, 0.3) rgba(17, 24, 39, 0.1)' : 'rgba(139, 92, 246, 0.5) rgba(243, 244, 246, 0.3)'
                }}
              >
                <MessageDisplay 
                  messages={messages} 
                  readerInfo={session.selectedReader} 
                  getFontClass={currentFontClass}
                  sessionId={id}
                  session={session}
                />
              </div>
            </motion.div>

            {/* Continue exploring button */}
            <motion.div 
              className="flex justify-center py-4 mt-10"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <motion.button
                className="px-8 py-4 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-500/30 transition-all duration-300 flex items-center space-x-2"
                onClick={() => navigate('/home')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className={`text-lg font-medium ${currentFontClass()}`} style={{color: 'white'}}>{t('reading.action.continue_explore')}</span>
              </motion.button>
            </motion.div>
          </div>
        </div>
      </motion.div>
      <Footer />
    </div>
  );
};

export default HistoryDetail;