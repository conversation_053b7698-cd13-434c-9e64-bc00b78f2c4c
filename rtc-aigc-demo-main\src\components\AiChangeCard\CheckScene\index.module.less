/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

.wrapper {
  position: relative;
  width: max-content;
  height: 50px;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #737a87;
  font-size: 14px;
  line-height: 22px;
  border: 1px solid#DDE2E9;
  margin-bottom: 16px;

  .content {
    width: 100%;
    height: 100%;
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
    gap: 4px;

    .icon {
      border-radius: 50%;
      width: 26px;
      height: 26px;
    }

    .checked-text {
      width: max-content;
      font-size: 13px;
      line-height: 22px;
    }
  }
}

.wrapper:hover {
  box-shadow: 0px 5px 6px 0px rgba(82, 102, 133, 0.15);
}

.active {
  border: 1px solid transparent;
  background: linear-gradient(77.86deg, #f1f9ff -3.23%, #edf3ff 51.11%, #faf4ff 98.65%) padding-box,
    linear-gradient(77.86deg, #3b91ff -3.23%, #0d5eff 51.11%, #c069ff 98.65%) border-box;

  .content {
    .checked-text {
      background: linear-gradient(90deg, #004fff 38.86%, #9865ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 13px;
      font-weight: 500;
      line-height: 22px;
    }
  }
}

.active:hover {
  box-shadow: 0px 5px 6px 0px rgba(82, 102, 133, 0.15);
}

.tag {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 3;
  font-size: 10px;
  font-weight: 500;
  line-height: 18px;
  transform: translate(20%, -50%);
  background: rgba(134, 123, 227, 1);
  padding: 0px 6px 0px 6px;
  border-radius: 20px 20px 20px 0px;
  color: white;
}
