/**
 * 百度URL推送定时任务
 */
const cron = require('node-cron');
const logger = require('../utils/logger');
const baiduUrlService = require('../services/baiduUrlService');
const crypto = require('crypto');

/**
 * 执行百度URL推送任务
 */
async function executeBaiduUrlPush() {
  try {
    logger.info('开始执行百度URL推送定时任务');
    
    // 执行完整推送流程
    const result = await baiduUrlService.executeFullPushProcess();
    
    if (result.error) {
      logger.error(`百度URL推送出错: ${result.error}`);
      return;
    }
    
    if (result.message) {
      logger.info(`推送状态: ${result.message}`);
      return;
    }
    
    logger.info(`推送完成: 成功 ${result.success}/${result.totalSubmitted} 个URL`);
    
    if (result.overQuota) {
      logger.info('百度API返回配额已用完，已停止推送剩余URL');
    }
    
    logger.info('百度URL推送定时任务完成');
  } catch (error) {
    logger.error(`百度URL推送任务执行出错: ${error.message}`);
  }
}

/**
 * 初始化百度URL推送定时任务
 * 默认每天凌晨2点执行
 */
function initBaiduUrlPushTask() {
  // 使用cron表达式设置定时任务: 分 时 日 月 周
  // 0 2 * * * 表示每天凌晨2点执行
  cron.schedule('0 2 * * *', async () => {
    await executeBaiduUrlPush();
  });
  
  // logger.info('百度URL推送定时任务已初始化，将在每天凌晨2点执行');
}

module.exports = {
  initBaiduUrlPushTask,
  executeBaiduUrlPush
}; 