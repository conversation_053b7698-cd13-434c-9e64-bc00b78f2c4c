import asyncio
import base64
import threading
import time
import traceback
from flask import request
from flask_socketio import emit, join_room, leave_room
from . import socketio
from .session_manager import SessionManager
from .logger import ws_logger, LogTimer

# 活动连接的用户映射，格式: {sid: {user_id: str, session_id: str}}
active_connections = {}

# 会话数据处理线程
session_data_threads = {}

def get_session_manager():
    """获取会话管理器实例"""
    return SessionManager.get_instance()

@socketio.on('connect')
def handle_connect():
    """处理客户端连接"""
    sid = request.sid
    ws_logger.info(f"客户端连接: {sid}")
    emit('connection_response', {'data': 'Connected'})

@socketio.on('disconnect')
def handle_disconnect():
    """处理客户端断开连接"""
    sid = request.sid
    ws_logger.info(f"客户端断开连接: {sid}")
    
    if sid in active_connections:
        # 停止数据处理线程
        if sid in session_data_threads:
            ws_logger.debug(f"停止会话数据处理线程: {sid}")
            session_data_threads[sid]['running'] = False
            if session_data_threads[sid].get('thread'):
                session_data_threads[sid]['thread'].join(timeout=2.0)
            del session_data_threads[sid]
        
        # 记录断开连接信息
        user_id = active_connections[sid]['user_id']
        session_id = active_connections[sid]['session_id']
        ws_logger.info(f"用户 {user_id} 断开会话 {session_id} 的连接")
        
        # 清理连接信息
        del active_connections[sid]

@socketio.on('join_session')
def handle_join_session(data):
    """处理客户端加入会话"""
    sid = request.sid
    user_id = data.get('user_id', 'anonymous')
    session_id = data.get('session_id')
    
    if not session_id:
        ws_logger.error(f"缺少session_id参数: {sid}")
        emit('error', {'message': '缺少session_id参数'})
        return
    
    ws_logger.info(f"用户 {user_id} 加入会话 {session_id}")
    
    # 记录用户连接信息
    active_connections[sid] = {
        'user_id': user_id,
        'session_id': session_id
    }
    
    # 加入房间
    join_room(session_id)
    emit('joined', {'session_id': session_id})
    
    # 启动会话数据处理线程
    if sid not in session_data_threads:
        ws_logger.debug(f"启动会话 {session_id} 的数据处理线程")
        session_data_threads[sid] = {
            'running': True,
            'thread': threading.Thread(target=session_data_worker, args=(sid, session_id))
        }
        session_data_threads[sid]['thread'].daemon = True
        session_data_threads[sid]['thread'].start()

@socketio.on('leave_session')
def handle_leave_session(data):
    """处理客户端离开会话"""
    sid = request.sid
    session_id = data.get('session_id')
    
    if not session_id:
        ws_logger.error(f"缺少session_id参数: {sid}")
        emit('error', {'message': '缺少session_id参数'})
        return
    
    if sid in active_connections:
        user_id = active_connections[sid]['user_id']
        ws_logger.info(f"用户 {user_id} 离开会话 {session_id}")
    else:
        ws_logger.info(f"连接 {sid} 离开会话 {session_id}")
    
    # 离开房间
    leave_room(session_id)
    emit('left', {'session_id': session_id})
    
    # 停止数据处理线程
    if sid in session_data_threads:
        ws_logger.debug(f"停止会话 {session_id} 的数据处理线程")
        session_data_threads[sid]['running'] = False
        if session_data_threads[sid].get('thread'):
            session_data_threads[sid]['thread'].join(timeout=2.0)
        del session_data_threads[sid]
    
    # 清理连接信息
    if sid in active_connections:
        del active_connections[sid]

@socketio.on('audio_data')
def handle_audio_data(data):
    """处理客户端发送的音频数据"""
    sid = request.sid
    session_id = data.get('session_id')
    audio_chunk = data.get('audio')
    
    if not session_id or not audio_chunk:
        ws_logger.error(f"缺少必要参数: {sid}")
        emit('error', {'message': '缺少必要参数'})
        return
    
    # 检查是否有权限发送到此会话
    if sid not in active_connections or active_connections[sid]['session_id'] != session_id:
        ws_logger.error(f"无权访问会话 {session_id}: {sid}")
        emit('error', {'message': '无权访问此会话'})
        return
    
    try:
        # 解码Base64音频数据
        audio_data = base64.b64decode(audio_chunk)
        
        with LogTimer(ws_logger, f"发送音频数据到会话 {session_id}"):
            # 发送到会话
            session_manager = get_session_manager()
            asyncio.run(session_manager.send_audio(session_id, audio_data))
        
        emit('audio_received', {'session_id': session_id, 'status': 'ok'})
    except Exception as e:
        ws_logger.error(f"处理音频数据出错: {str(e)}")
        ws_logger.error(traceback.format_exc())
        emit('error', {'message': f'处理音频数据出错: {str(e)}'})

def session_data_worker(sid, session_id):
    """会话数据处理工作线程，负责将会话数据转发给客户端
    
    Args:
        sid: Socket.IO连接ID
        session_id: 会话ID
    """
    session_manager = get_session_manager()
    
    ws_logger.info(f"会话 {session_id} 的数据处理线程已启动")
    
    try:
        while sid in session_data_threads and session_data_threads[sid]['running']:
            try:
                # 获取会话数据
                data = session_manager.get_session_data(session_id, timeout=0.1)
                
                if data:
                    # 记录数据类型
                    data_type = data.get('type', 'unknown')
                    ws_logger.debug(f"会话 {session_id} 收到 {data_type} 类型的数据")
                    
                    # 转发数据给客户端
                    socketio.emit('session_data', {
                        'session_id': session_id,
                        'data': data
                    }, room=session_id)
                
                time.sleep(0.01)  # 避免CPU过度使用
            except Exception as e:
                ws_logger.error(f"处理会话 {session_id} 数据出错: {str(e)}")
                ws_logger.error(traceback.format_exc())
                time.sleep(1.0)  # 出错后等待1秒再继续
    except Exception as e:
        ws_logger.error(f"会话 {session_id} 数据处理线程异常退出: {str(e)}")
        ws_logger.error(traceback.format_exc())
    finally:
        ws_logger.info(f"会话 {session_id} 的数据处理线程已结束") 