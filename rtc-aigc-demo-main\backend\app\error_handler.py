from flask import jsonify
from werkzeug.exceptions import HTTPException
import traceback

from .logger import api_logger

class APIError(Exception):
    """API错误基类"""
    
    def __init__(self, message, status_code=400, payload=None):
        super().__init__()
        self.message = message
        self.status_code = status_code
        self.payload = payload
    
    def to_dict(self):
        rv = dict(self.payload or {})
        rv['status'] = 'error'
        rv['message'] = self.message
        return rv


class SessionError(APIError):
    """会话相关错误"""
    pass


class AudioError(APIError):
    """音频处理错误"""
    pass


def register_error_handlers(app):
    """注册错误处理器
    
    Args:
        app: Flask应用实例
    """
    
    @app.errorhandler(APIError)
    def handle_api_error(error):
        """处理API错误"""
        api_logger.error(f"API错误: {error.message}")
        response = jsonify(error.to_dict())
        response.status_code = error.status_code
        return response
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(error):
        """处理HTTP异常"""
        api_logger.error(f"HTTP异常: {error}")
        response = jsonify({
            'status': 'error',
            'message': error.description,
            'code': error.code
        })
        response.status_code = error.code
        return response
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        """处理所有其他异常"""
        api_logger.error(f"未处理的异常: {str(error)}")
        api_logger.error(traceback.format_exc())
        response = jsonify({
            'status': 'error',
            'message': '服务器内部错误',
            'details': str(error) if app.config.get('DEBUG', False) else None
        })
        response.status_code = 500
        return response 