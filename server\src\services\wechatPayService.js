const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
require('dotenv').config();
const AesUtil = require('../utils/aesUtil');
const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../../services/database');

class WechatPayService {
  constructor() {
    // console.log('Initializing WechatPayService...');
    const keyPath = path.join(__dirname, '../..', 'certs/wechat/apiclient_key.pem');
    this.privateKey = fs.readFileSync(keyPath, 'utf8');
    
    this.appId = process.env.WECHAT_PAY_APP_ID;
    this.mchId = process.env.WECHAT_PAY_MCH_ID;
    this.serialNo = process.env.WECHAT_PAY_SERIAL_NO;
    this.apiV3Key = process.env.WECHAT_PAY_API_V3_KEY;
    this.baseUrl = 'https://api.mch.weixin.qq.com';
    
    // console.log('WechatPayService initialized with config:', {
    //   appId: this.appId,
    //   mchId: this.mchId,
    //   serialNo: this.serialNo
    // });

    this.aesUtil = new AesUtil(this.apiV3Key);
  }

  // 生成签名
  generateSignature(method, url, timestamp, nonceStr, body = '') {
    const signatureStr = `${method}\n${url}\n${timestamp}\n${nonceStr}\n${body}\n`;
    const sign = crypto.createSign('RSA-SHA256');
    sign.update(signatureStr);
    return sign.sign(this.privateKey, 'base64');
  }

  // 生成认证头
  generateAuthHeader(method, url, body = '') {
    const timestamp = Math.floor(Date.now() / 1000);
    const nonceStr = crypto.randomBytes(16).toString('hex');
    const signature = this.generateSignature(method, url, timestamp, nonceStr, body);

    return `WECHATPAY2-SHA256-RSA2048 mchid="${this.mchId}",nonce_str="${nonceStr}",signature="${signature}",timestamp="${timestamp}",serial_no="${this.serialNo}"`;
  }

  // 创建Native支付订单
  async createNativeOrder(orderData) {
    try {
      console.log('Creating native order:', orderData);
      const { amount, description, orderId, userId, productId, productName } = orderData;
      
      const url = '/v3/pay/transactions/native';
      const requestUrl = url;
      
      const body = {
        appid: this.appId,
        mchid: this.mchId,
        description,
        out_trade_no: orderId,
        notify_url: process.env.WECHAT_PAY_NOTIFY_URL || 'https://tarotqa.com/api/payment/wechat/notify',
        amount: {
          total: Math.round(amount * 100), // 转换为分
          currency: 'CNY'
        }
      };

      const bodyStr = JSON.stringify(body);
      const authorization = this.generateAuthHeader('POST', requestUrl, bodyStr);

      console.log('Sending request to WeChat Pay API:', {
        url: this.baseUrl + url,
        body,
        authorization
      });

      // 先记录订单信息
      try {
        const { getConnection } = require('../../services/database');
        const pool = await getConnection();
        await pool.query(
          `INSERT INTO payment_orders 
           (id, order_id, user_id, product_id, product_name, amount, status, payment_method) 
           VALUES (?, ?, ?, ?, ?, ?, 'pending', 'wechat')`,
          [uuidv4(), orderId, userId, productId, productName, amount]
        );
      } catch (error) {
        console.error('Failed to create order record:', error);
        throw error;
      }

      const response = await axios({
        method: 'POST',
        url: this.baseUrl + url,
        data: body,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': authorization
        }
      });

      console.log('Native order created:', response.data);
      return {
        codeUrl: response.data.code_url,
        orderId: orderId
      };
    } catch (error) {
      console.error('Create WeChatPay order failed:', error.response?.data || error);
      throw error;
    }
  }

  // 查询订单状态
  async queryOrder(orderId) {
    try {
      console.log('Querying order:', orderId);
      const url = `/v3/pay/transactions/out-trade-no/${orderId}?mchid=${this.mchId}`;
      const authorization = this.generateAuthHeader('GET', url);

      const response = await axios({
        method: 'GET',
        url: this.baseUrl + url,
        headers: {
          'Accept': 'application/json',
          'Authorization': authorization
        }
      });

      console.log('Order query result:', response.data);

      // 如果支付成功，更新订单和用户状态
      if (response.data.trade_state === 'SUCCESS') {
        try {
          const pool = await getConnection();
          
          // 查询订单信息
          const [orderRows] = await pool.query(
            'SELECT user_id, product_id, product_name, amount FROM payment_orders WHERE order_id = ?',
            [orderId]
          );

          if (orderRows.length > 0) {
            const { user_id, product_id, product_name, amount } = orderRows[0];
            console.log(`[微信支付查询] ${orderId} 订单信息:`, {
              user_id,
              product_id,
              product_name,
              amount
            });
            
            // 根据商品ID判断支付类型
            if (product_id.includes('reads') || product_id === 'pay_per_use') {
              console.log(`[微信支付查询] ${orderId} 检测到按次付费订单`);
              // 获取购买次数，从product_name中解析
              const readCount = parseInt(product_name.split('-').pop()) || 1;
              console.log(`[微信支付查询] ${orderId} 获取购买次数:`, {
                product_name,
                readCount
              });
              
              if (readCount > 0) {
                // 先查询用户当前的剩余次数
                const [userRows] = await pool.query(
                  'SELECT remaining_reads, has_internal_privilege, has_used_discount FROM users WHERE id = ?',
                  [user_id]
                );
                const currentReads = userRows[0]?.remaining_reads || 0;
                
                console.log(`[微信支付查询] ${orderId} 更新前用户信息:`, {
                  user_id,
                  currentReads,
                  readCount,
                  newTotal: currentReads + readCount
                });

                // 更新剩余次数
                await pool.query(
                  `UPDATE users 
                   SET remaining_reads = remaining_reads + ?,
                       vip_status = 'none',
                       vip_type = 'none',
                       vip_start_date = NULL,
                       vip_end_date = NULL
                   WHERE id = ?`,
                  [readCount, user_id]
                );

                // 验证更新是否成功
                const [verifyRows] = await pool.query(
                  'SELECT remaining_reads FROM users WHERE id = ?',
                  [user_id]
                );
                console.log(`[微信支付查询] ${orderId} 更新后用户信息:`, {
                  user_id,
                  newReads: verifyRows[0]?.remaining_reads
                });
                
                // 检查用户是否有内部折扣权限且未使用过
                const hasInternalPrivilege = userRows[0]?.has_internal_privilege === 1;
                const hasUsedDiscount = userRows[0]?.has_used_discount === 1;
                
                if (hasInternalPrivilege && !hasUsedDiscount) {
                  console.log(`[微信支付查询] ${orderId} 用户有内部折扣权限且未使用过，标记订单和用户`);
                  
                  // 更新订单的discount_applied字段
                  await pool.query(
                    `UPDATE payment_orders SET discount_applied = 1 WHERE order_id = ?`,
                    [orderId]
                  );
                  
                  // 更新用户的has_used_discount字段
                  await pool.query(
                    `UPDATE users SET has_used_discount = 1 WHERE id = ?`,
                    [user_id]
                  );
                  
                  console.log(`[微信支付查询] ${orderId} 内部折扣标记完成`);
                }
                
                // 更新订单状态
                await pool.query(
                  `UPDATE payment_orders 
                   SET status = 'success',
                       trade_no = ?,
                       trade_status = ?,
                       trade_state_desc = ?,
                       trade_type = ?,
                       bank_type = ?,
                       payer_openid = ?,
                       success_time = ?,
                       trade_time = NOW(),
                       currency = ?,
                       payer_currency = ?
                   WHERE order_id = ?`,
                  [
                    response.data.transaction_id,
                    response.data.trade_state,
                    response.data.trade_state_desc,
                    response.data.trade_type,
                    response.data.bank_type,
                    response.data.payer?.openid,
                    response.data.success_time,
                    response.data.amount?.currency,
                    response.data.amount?.payer_currency,
                    orderId
                  ]
                );
                
                console.log(`[微信支付查询] ${orderId} 按次付费订单状态更新成功`);
              }
            } else {
              console.log(`[微信支付查询] ${orderId} 检测到会员订单`);
              // 按月付费或按年付费
              let vipType = 'monthly';
              let membershipDays = 30;
              
              if (product_id.includes('year')) {
                vipType = 'yearly';
                membershipDays = 365;
              }

              console.log(`[微信支付查询] ${orderId} 会员订单信息:`, {
                vipType,
                membershipDays
              });

              // 先查询用户当前的会员状态
              const [userRows] = await pool.query(
                'SELECT vip_status, vip_type, vip_end_date, has_internal_privilege, has_used_discount FROM users WHERE id = ?',
                [user_id]
              );
              
              const currentUser = userRows[0];
              let newEndDate;
              
              if (currentUser && currentUser.vip_status === 'active' && currentUser.vip_end_date) {
                // 如果用户已经是活跃会员，从当前到期日开始计算
                const currentEndDate = new Date(currentUser.vip_end_date);
                
                // 如果是月度会员升级到年度会员，则从当前到期日增加一年
                if (currentUser.vip_type === 'monthly' && vipType === 'yearly') {
                  newEndDate = new Date(currentEndDate.setFullYear(currentEndDate.getFullYear() + 1));
                } else {
                  // 其他情况，从当前到期日增加相应天数
                  newEndDate = new Date(currentEndDate.setDate(currentEndDate.getDate() + membershipDays));
                }
              } else {
                // 如果用户不是活跃会员，从当前时间开始计算
                newEndDate = new Date();
                newEndDate.setDate(newEndDate.getDate() + membershipDays);
              }

              // 格式化日期为MySQL datetime格式
              const formattedEndDate = newEndDate.toISOString().slice(0, 19).replace('T', ' ');

              // 更新用户会员状态
              await pool.query(
                `UPDATE users 
                 SET vip_status = 'active',
                     vip_type = ?,
                     vip_start_date = CURRENT_TIMESTAMP,
                     vip_end_date = ?
                 WHERE id = ?`,
                [vipType, formattedEndDate, user_id]
              );

              // 验证更新是否成功
              const [verifyRows] = await pool.query(
                'SELECT vip_status, vip_type, vip_start_date, vip_end_date FROM users WHERE id = ?',
                [user_id]
              );
              console.log(`[微信支付查询] ${orderId} 更新后会员信息:`, verifyRows[0]);
              
              // 检查用户是否有内部折扣权限且未使用过
              const hasInternalPrivilege = currentUser?.has_internal_privilege === 1;
              const hasUsedDiscount = currentUser?.has_used_discount === 1;
              
              if (hasInternalPrivilege && !hasUsedDiscount) {
                console.log(`[微信支付查询] ${orderId} 用户有内部折扣权限且未使用过，标记订单和用户`);
                
                // 更新订单的discount_applied字段
                await pool.query(
                  `UPDATE payment_orders SET discount_applied = 1 WHERE order_id = ?`,
                  [orderId]
                );
                
                // 更新用户的has_used_discount字段
                await pool.query(
                  `UPDATE users SET has_used_discount = 1 WHERE id = ?`,
                  [user_id]
                );
                
                console.log(`[微信支付查询] ${orderId} 内部折扣标记完成`);
              }

              // 更新订单状态
              await pool.query(
                `UPDATE payment_orders 
                 SET status = 'success',
                     trade_no = ?,
                     trade_status = ?,
                     trade_state_desc = ?,
                     trade_type = ?,
                     bank_type = ?,
                     payer_openid = ?,
                     success_time = ?,
                     trade_time = NOW(),
                     currency = ?,
                     payer_currency = ?
                 WHERE order_id = ?`,
                [
                  response.data.transaction_id,
                  response.data.trade_state,
                  response.data.trade_state_desc,
                  response.data.trade_type,
                  response.data.bank_type,
                  response.data.payer?.openid,
                  response.data.success_time,
                  response.data.amount?.currency,
                  response.data.amount?.payer_currency,
                  orderId
                ]
              );

              console.log('会员状态更新成功:', {
                userId: user_id,
                vipType,
                membershipDays
              });
            }
          }
        } catch (error) {
          console.error('更新会员状态失败:', error);
          throw error;
        }
      }

      return {
        tradeState: response.data.trade_state,
        tradeStateDesc: response.data.trade_state_desc
      };
    } catch (error) {
      console.error('Query order failed:', error.response?.data || error);
      throw error;
    }
  }

  // 关闭订单
  async closeOrder(outTradeNo) {
    try {
      console.log('=== 开始关闭订单 ===');
      console.log('订单号:', outTradeNo);
      
      const url = `${this.baseUrl}/v3/pay/transactions/out-trade-no/${outTradeNo}/close`;
      const requestData = {
        mchid: this.mchId
      };

      console.log('请求微信支付API:', {
        url,
        method: 'POST',
        requestData
      });

      const response = await axios.post(url, requestData, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': await this.generateAuthHeader('POST', `/v3/pay/transactions/out-trade-no/${outTradeNo}/close`, JSON.stringify(requestData))
        }
      });

      console.log('微信支付API响应状态码:', response.status);

      // 204状态码表示成功关闭
      if (response.status === 204) {
        console.log('订单关闭成功');
        return { success: true };
      }

      console.log('订单关闭失败，意外的状态码:', response.status);
      return { 
        success: false, 
        error: `Unexpected status code: ${response.status}`
      };
    } catch (error) {
      console.error('关闭订单发生错误:', {
        orderId: outTradeNo,
        errorMessage: error.response?.data || error.message,
        errorStack: error.stack
      });
      throw error;
    }
  }

  // 处理支付回调通知
  async handlePaymentNotification(headers, body) {
    console.log('Received payment notification:', { headers, body: typeof body === 'object' ? JSON.stringify(body) : body });
    
    try {
      // 解析通知数据
      let notificationData;
      if (process.env.NODE_ENV?.toLowerCase() === 'development') {
        console.log('Development environment, skipping signature verification');
        notificationData = typeof body === 'object' ? body : JSON.parse(body);
      } else {
        // 验证签名和解密数据
        const decryptedData = await this.aesUtil.decryptToString(body.resource.ciphertext);
        console.log('Decrypted notification data:', decryptedData);
        notificationData = JSON.parse(decryptedData);
      }

      // 检查支付状态
      if (notificationData.trade_state === 'SUCCESS') {
        const pool = await getConnection();
        
        // 开始事务
        const connection = await pool.getConnection();
        await connection.beginTransaction();
        
        try {
          // 查询订单信息
          const [orderRows] = await connection.query(
            'SELECT user_id, product_id, product_name, amount, status FROM payment_orders WHERE order_id = ?',
            [notificationData.out_trade_no]
          );

          if (orderRows.length > 0) {
            const { user_id, product_id, product_name, amount, status } = orderRows[0];
            console.log(`[微信支付通知] ${notificationData.out_trade_no} 订单信息:`, {
              user_id,
              product_id,
              product_name,
              amount,
              status
            });
            
            // 如果订单已经是成功状态，直接返回成功
            if (status === 'success') {
              console.log('Order already processed:', notificationData.out_trade_no);
              await connection.commit();
              return { code: 'SUCCESS', message: '成功' };
            }

            // 根据商品ID判断支付类型
            if (product_id.includes('reads') || product_id === 'pay_per_use') {
              console.log(`[微信支付通知] ${notificationData.out_trade_no} 检测到按次付费订单`);
              // 获取购买次数，从product_name中解析
              const readCount = parseInt(product_name.split('-').pop()) || 1;
              console.log(`[微信支付通知] ${notificationData.out_trade_no} 获取购买次数:`, {
                product_name,
                readCount
              });
              
              if (readCount > 0) {
                // 先查询用户当前的剩余次数
                const [userRows] = await connection.query(
                  'SELECT remaining_reads, has_internal_privilege, has_used_discount FROM users WHERE id = ?',
                  [user_id]
                );
                const currentReads = userRows[0]?.remaining_reads || 0;
                
                console.log(`[微信支付通知] ${notificationData.out_trade_no} 更新前用户信息:`, {
                  user_id,
                  currentReads,
                  readCount,
                  newTotal: currentReads + readCount
                });

                // 更新剩余次数
                await connection.query(
                  `UPDATE users 
                   SET remaining_reads = remaining_reads + ?,
                       vip_status = 'none',
                       vip_type = 'none',
                       vip_start_date = NULL,
                       vip_end_date = NULL
                   WHERE id = ?`,
                  [readCount, user_id]
                );

                // 验证更新是否成功
                const [verifyRows] = await connection.query(
                  'SELECT remaining_reads FROM users WHERE id = ?',
                  [user_id]
                );
                console.log(`[微信支付通知] ${notificationData.out_trade_no} 更新后用户信息:`, {
                  user_id,
                  newReads: verifyRows[0]?.remaining_reads
                });
                
                // 检查用户是否有内部折扣权限且未使用过
                const hasInternalPrivilege = userRows[0]?.has_internal_privilege === 1;
                const hasUsedDiscount = userRows[0]?.has_used_discount === 1;
                
                if (hasInternalPrivilege && !hasUsedDiscount) {
                  console.log(`[微信支付通知] ${notificationData.out_trade_no} 用户有内部折扣权限且未使用过，标记订单和用户`);
                  
                  // 更新订单的discount_applied字段
                  await connection.query(
                    `UPDATE payment_orders SET discount_applied = 1 WHERE order_id = ?`,
                    [notificationData.out_trade_no]
                  );
                  
                  // 更新用户的has_used_discount字段
                  await connection.query(
                    `UPDATE users SET has_used_discount = 1 WHERE id = ?`,
                    [user_id]
                  );
                  
                  console.log(`[微信支付通知] ${notificationData.out_trade_no} 内部折扣标记完成`);
                }
              }
            } else {
              console.log(`[微信支付通知] ${notificationData.out_trade_no} 检测到会员订单`);
              // 按月付费或按年付费
              let vipType = 'monthly';
              let membershipDays = 30;
              
              if (product_id.includes('year')) {
                vipType = 'yearly';
                membershipDays = 365;
              }

              console.log(`[微信支付通知] ${notificationData.out_trade_no} 会员订单信息:`, {
                vipType,
                membershipDays
              });

              // 先查询用户当前的会员状态
              const [userRows] = await connection.query(
                'SELECT vip_status, vip_type, vip_end_date, has_internal_privilege, has_used_discount FROM users WHERE id = ?',
                [user_id]
              );
              
              const currentUser = userRows[0];
              let newEndDate;
              
              if (currentUser && currentUser.vip_status === 'active' && currentUser.vip_end_date) {
                // 如果用户已经是活跃会员，从当前到期日开始计算
                const currentEndDate = new Date(currentUser.vip_end_date);
                
                // 如果是月度会员升级到年度会员，则从当前到期日增加一年
                if (currentUser.vip_type === 'monthly' && vipType === 'yearly') {
                  newEndDate = new Date(currentEndDate.setFullYear(currentEndDate.getFullYear() + 1));
                } else {
                  // 其他情况，从当前到期日增加相应天数
                  newEndDate = new Date(currentEndDate.setDate(currentEndDate.getDate() + membershipDays));
                }
              } else {
                // 如果用户不是活跃会员，从当前时间开始计算
                newEndDate = new Date();
                newEndDate.setDate(newEndDate.getDate() + membershipDays);
              }

              // 格式化日期为MySQL datetime格式
              const formattedEndDate = newEndDate.toISOString().slice(0, 19).replace('T', ' ');

              // 更新用户会员状态
              await connection.query(
                `UPDATE users 
                 SET vip_status = 'active',
                     vip_type = ?,
                     vip_start_date = CURRENT_TIMESTAMP,
                     vip_end_date = ?
                 WHERE id = ?`,
                [vipType, formattedEndDate, user_id]
              );

              // 验证更新是否成功
              const [verifyRows] = await connection.query(
                'SELECT vip_status, vip_type, vip_start_date, vip_end_date FROM users WHERE id = ?',
                [user_id]
              );
              console.log(`[微信支付通知] ${notificationData.out_trade_no} 更新后会员信息:`, verifyRows[0]);
              
              // 检查用户是否有内部折扣权限且未使用过
              const hasInternalPrivilege = currentUser?.has_internal_privilege === 1;
              const hasUsedDiscount = currentUser?.has_used_discount === 1;
              
              if (hasInternalPrivilege && !hasUsedDiscount) {
                console.log(`[微信支付通知] ${notificationData.out_trade_no} 用户有内部折扣权限且未使用过，标记订单和用户`);
                
                // 更新订单的discount_applied字段
                await connection.query(
                  `UPDATE payment_orders SET discount_applied = 1 WHERE order_id = ?`,
                  [notificationData.out_trade_no]
                );
                
                // 更新用户的has_used_discount字段
                await connection.query(
                  `UPDATE users SET has_used_discount = 1 WHERE id = ?`,
                  [user_id]
                );
                
                console.log(`[微信支付通知] ${notificationData.out_trade_no} 内部折扣标记完成`);
              }

              // 更新订单状态和回调信息
              await connection.query(
                `UPDATE payment_orders 
                 SET status = 'success',
                     trade_no = ?,
                     trade_status = ?,
                     trade_state_desc = ?,
                     trade_type = ?,
                     bank_type = ?,
                     payer_openid = ?,
                     success_time = ?,
                     trade_time = NOW(),
                     currency = ?,
                     payer_currency = ?
                 WHERE order_id = ?`,
                [
                  notificationData.transaction_id,
                  notificationData.trade_state,
                  notificationData.trade_state_desc,
                  notificationData.trade_type,
                  notificationData.bank_type,
                  notificationData.payer?.openid,
                  notificationData.success_time,
                  notificationData.amount?.currency,
                  notificationData.amount?.payer_currency,
                  notificationData.out_trade_no
                ]
              );

              console.log('会员状态和订单更新成功:', {
                userId: user_id,
                orderId: notificationData.out_trade_no,
                vipType,
                membershipDays
              });

              // 提交事务
              await connection.commit();
            }
          }
        } catch (error) {
          // 回滚事务
          await connection.rollback();
          console.error('处理支付回调失败:', error);
          throw error;
        } finally {
          connection.release();
        }
      }

      return { code: 'SUCCESS', message: '成功' };
    } catch (error) {
      console.error('处理支付回调时发生错误:', error);
      throw error;
    }
  }
}

module.exports = new WechatPayService();