.loading-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #000;
  color: #fff;
}

.progress-wrapper {
  position: relative;
  width: 200px;
  height: 200px;
  margin-bottom: 2rem;
}

.progress-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 0 30px rgba(147, 51, 234, 0.3);
  transition: background 0.3s ease;
}

.progress-inner {
  width: 80%;
  height: 80%;
  background: #000;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-number {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 2.5rem;
  font-weight: 600;
  color: #9333EA;
  margin-bottom: 0.5rem;
  letter-spacing: -0.5px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  transform: translateZ(0);
  will-change: transform;
}

.progress-text {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 0.2px;
}

.loading-text {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin-top: 1rem;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 0.2px;
}

/* 添加脉冲动画效果 */
.progress-circle::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid #9333EA;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.4;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}
