import uuid
import asyncio
import threading
import json
import base64
import time
from queue import Queue
from typing import Dict, Any, Optional, List, Callable

from .client import RealtimeDialogClient


class DialogService:
    """对话服务类，管理实时语音对话会话"""
    
    def __init__(self, volcano_config: Dict[str, Any], tts_config: Dict[str, Any]):
        self.volcano_config = volcano_config
        self.tts_config = tts_config
        self.active_sessions: Dict[str, Dict[str, Any]] = {}  # 存储活动会话
        self.session_queues: Dict[str, Queue] = {}  # 每个会话的音频队列

    async def create_session(self, user_id: str) -> Dict[str, Any]:
        """创建新会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            包含会话信息的字典
        """
        session_id = str(uuid.uuid4())
        
        # 创建会话音频队列
        self.session_queues[session_id] = Queue()
        
        # 创建客户端 - 使用非异步方法作为回调
        client = RealtimeDialogClient(
            config=self.volcano_config,
            session_id=session_id,
            callback=lambda data: self._handle_response_sync(session_id, data)
        )
        
        # 记录会话信息
        self.active_sessions[session_id] = {
            'session_id': session_id,
            'user_id': user_id,
            'client': client,
            'status': 'initializing',
            'created_at': time.time(),  # 使用time.time()代替asyncio.get_event_loop().time()
        }
        
        # 连接到服务
        connected = await client.connect()
        if not connected:
            del self.active_sessions[session_id]
            if session_id in self.session_queues:
                del self.session_queues[session_id]
            return {'error': '无法连接到语音服务'}
        
        # 启动会话
        success = await client.start_session(self.tts_config)
        if not success:
            await client.close()
            del self.active_sessions[session_id]
            if session_id in self.session_queues:
                del self.session_queues[session_id]
            return {'error': '无法启动语音会话'}
        
        # 发送初始问候
        await client.say_hello()
        
        # 更新会话状态
        self.active_sessions[session_id]['status'] = 'active'
        
        return {
            'session_id': session_id,
            'status': 'active',
            'message': '会话已成功创建'
        }

    def _handle_response_sync(self, session_id: str, data: Dict[str, Any]) -> None:
        """处理服务器响应（同步版本）
        
        Args:
            session_id: 会话ID
            data: 服务器响应数据
        """
        if session_id not in self.active_sessions:
            print(f"收到未知会话 {session_id} 的响应")
            return

        try:
            if data['message_type'] == 'SERVER_ACK' and isinstance(data.get('payload_msg'), bytes):
                # 收到音频数据
                audio_data = data['payload_msg']
                
                # 将音频数据放入会话队列
                if session_id in self.session_queues:
                    self.session_queues[session_id].put({
                        'type': 'audio',
                        'data': base64.b64encode(audio_data).decode('utf-8')
                    })
                    
            elif data['message_type'] == 'SERVER_FULL_RESPONSE':
                # 收到完整响应
                event = data.get('event')
                payload_msg = data.get('payload_msg', {})
                
                # 根据事件类型处理
                if event == 350 and payload_msg.get("tts_type") == "chat_tts_text":
                    # 文本朗读事件
                    if session_id in self.session_queues:
                        self.session_queues[session_id].put({
                            'type': 'text',
                            'data': payload_msg
                        })
                
                if event == 459:
                    # 用户查询结束事件
                    if session_id in self.session_queues:
                        self.session_queues[session_id].put({
                            'type': 'event',
                            'data': {'event': 'query_end'}
                        })
                        
            elif data['message_type'] == 'SERVER_ERROR':
                # 服务器错误
                if session_id in self.session_queues:
                    self.session_queues[session_id].put({
                        'type': 'error',
                        'data': data['payload_msg']
                    })
        except Exception as e:
            print(f"处理响应出错: {e}")
            import traceback
            traceback.print_exc()

    # 保留原来的异步方法以便其他地方可能的调用
    async def _handle_response(self, session_id: str, data: Dict[str, Any]) -> None:
        """处理服务器响应（异步版本）
        
        Args:
            session_id: 会话ID
            data: 服务器响应数据
        """
        # 实际上调用同步版本
        self._handle_response_sync(session_id, data)

    def get_session_data(self, session_id: str, timeout: float = 0.1) -> Optional[Dict[str, Any]]:
        """获取会话数据
        
        Args:
            session_id: 会话ID
            timeout: 超时时间（秒）
            
        Returns:
            会话数据或None
        """
        if session_id not in self.session_queues:
            return None
            
        try:
            # 非阻塞方式获取数据
            return self.session_queues[session_id].get(block=True, timeout=timeout)
        except Exception:
            return None

    async def send_audio(self, session_id: str, audio_data: bytes) -> Dict[str, Any]:
        """发送音频数据到会话
        
        Args:
            session_id: 会话ID
            audio_data: 音频数据
            
        Returns:
            操作结果
        """
        if session_id not in self.active_sessions:
            return {'error': '会话不存在'}
            
        client = self.active_sessions[session_id]['client']
        await client.send_audio(audio_data)
        
        return {'status': 'sent'}

    async def send_text(self, session_id: str, text: str, start: bool = True, end: bool = False) -> Dict[str, Any]:
        """发送文本到会话
        
        Args:
            session_id: 会话ID
            text: 文本内容
            start: 是否是文本开始
            end: 是否是文本结束
            
        Returns:
            操作结果
        """
        if session_id not in self.active_sessions:
            return {'error': '会话不存在'}
            
        client = self.active_sessions[session_id]['client']
        await client.send_text(text, start, end)
        
        return {'status': 'sent'}

    async def close_session(self, session_id: str) -> Dict[str, Any]:
        """关闭会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            操作结果
        """
        if session_id not in self.active_sessions:
            return {'error': '会话不存在'}
            
        # 获取客户端并关闭连接
        client = self.active_sessions[session_id]['client']
        await client.close()
        
        # 清理资源
        del self.active_sessions[session_id]
        if session_id in self.session_queues:
            del self.session_queues[session_id]
        
        return {
            'status': 'closed',
            'message': f'会话 {session_id} 已关闭'
        }

    def get_active_sessions(self) -> List[Dict[str, Any]]:
        """获取活动会话列表
        
        Returns:
            活动会话列表
        """
        return [
            {
                'session_id': session_id,
                'user_id': info['user_id'],
                'status': info['status'],
                'created_at': info['created_at']
            }
            for session_id, info in self.active_sessions.items()
        ]

    async def cleanup_expired_sessions(self, max_age: float = 3600) -> None:
        """清理过期会话
        
        Args:
            max_age: 最大会话年龄（秒）
        """
        current_time = time.time()  # 使用time.time()
        expired_sessions = [
            session_id
            for session_id, info in self.active_sessions.items()
            if current_time - info['created_at'] > max_age
        ]
        
        for session_id in expired_sessions:
            print(f"清理过期会话: {session_id}")
            await self.close_session(session_id) 