import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { User, getCurrentUser } from '../services/userService';
import { authEvents, AUTH_ERROR_EVENT } from '../utils/axios';

interface UserContextType {
  user: User | null;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
  loading: boolean;
  logout: () => void;
  refreshUser: () => Promise<User | void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const cachedUser = localStorage.getItem('cachedUser');
  const [user, setUser] = useState<User | null>(cachedUser ? JSON.parse(cachedUser) : null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Combined effect for user initialization and caching
  useEffect(() => {
    const initializeUser = async () => {
      const token = localStorage.getItem('token');
      if (token) {
        try {
          const userData = await getCurrentUser();
          setUser(userData);
          localStorage.setItem('userId', userData.id);
          localStorage.setItem('cachedUser', JSON.stringify(userData));
        } catch (error) {
          // console.error('Failed to get user data:', error);
          localStorage.removeItem('token');
          localStorage.removeItem('userId');
          localStorage.removeItem('cachedUser');
          setUser(null);
        }
      } else {
        localStorage.removeItem('cachedUser');
        setUser(null);
      }
      setLoading(false);
    };

    initializeUser();
  }, []); // Only run on mount

  // 监听用户状态变化
  useEffect(() => {
    if (user) {
      localStorage.setItem('cachedUser', JSON.stringify(user));
    }
  }, [user]);

  useEffect(() => {
    const handleAuthError = () => {
      // 清除用户认证信息，但不自动重定向
      localStorage.removeItem('token');
      localStorage.removeItem('userId');
      localStorage.removeItem('cachedUser');
      setUser(null);
      // 删除自动重定向到登录页面的代码
    };

    // 监听未授权事件
    authEvents.addEventListener(AUTH_ERROR_EVENT, handleAuthError);

    return () => {
      authEvents.removeEventListener(AUTH_ERROR_EVENT, handleAuthError);
    };
  }, []);

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
    localStorage.removeItem('cachedUser');
    setUser(null);
    navigate('/login');
  };
  
  // 刷新用户信息
  const refreshUser = async () => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const userData = await getCurrentUser();
        setUser(userData);
        localStorage.setItem('userId', userData.id);
        localStorage.setItem('cachedUser', JSON.stringify(userData));
        return userData;
      } catch (error) {
        console.error('刷新用户信息失败:', error);
        throw error;
      }
    } else {
      throw new Error('用户未登录');
    }
  };

  return (
    <UserContext.Provider value={{ user, setUser, loading, logout, refreshUser }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
