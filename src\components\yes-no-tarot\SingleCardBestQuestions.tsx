import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { CdnLazyImage } from '../CdnImageExport';

const SingleCardBestQuestions: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  // 定义不同类别的问题
  const categories = [
    {
      title: t('yes_no_tarot.single_card_questions.love_title', 'Love & Relationships'),
      icon: '❤️',
      questions: [
        t('yes_no_tarot.single_card_questions.love_q1', '"Should I reach out to my ex?"'),
        t('yes_no_tarot.single_card_questions.love_q2', '"Is this person right for me?"'),
        t('yes_no_tarot.single_card_questions.love_q3', '"Will our relationship work out?"'),
        t('yes_no_tarot.single_card_questions.love_q4', '"Should I be open to new love?"')
      ]
    },
    {
      title: t('yes_no_tarot.single_card_questions.career_title', 'Career & Finance'),
      icon: '💼',
      questions: [
        t('yes_no_tarot.single_card_questions.career_q1', '"Is now the right time to change jobs?"'),
        t('yes_no_tarot.single_card_questions.career_q2', '"Should I accept this job offer?"'),
        t('yes_no_tarot.single_card_questions.career_q3', '"Will my business idea succeed?"'),
        t('yes_no_tarot.single_card_questions.career_q4', '"Should I make this investment?"')
      ]
    },
    {
      title: t('yes_no_tarot.single_card_questions.personal_title', 'Personal Growth'),
      icon: '🌱',
      questions: [
        t('yes_no_tarot.single_card_questions.personal_q1', '"Should I move to a new city?"'),
        t('yes_no_tarot.single_card_questions.personal_q2', '"Is this the right path for me?"'),
        t('yes_no_tarot.single_card_questions.personal_q3', '"Should I trust my instincts on this?"'),
        t('yes_no_tarot.single_card_questions.personal_q4', '"Will taking this risk pay off?"')
      ]
    }
  ];

  return (
    <div className="mt-24 sm:mt-32">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('yes_no_tarot.single_card_questions.title', 'Best Questions for One Card Tarot Yes/No')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
      </div>
      
      {/* 分类展示问题 */}
      <div className="max-w-5xl mx-auto space-y-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {categories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-8">
              {/* 类别标题 */}
              <div className="flex items-center justify-center mb-6">
                <span className="text-2xl mr-2">{category.icon}</span>
                <h3 className={`text-xl font-semibold ${
                  theme === 'light' ? 'text-purple-700' : 'text-purple-300'
                }`}>
                  {category.title}
                </h3>
              </div>
              
              {/* 问题列表 - 纵向排列，共用头像 */}
              <div className="flex">
                {/* 共用的头像 */}
                <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 mr-3 mt-1 shadow-sm border border-gray-200 dark:border-gray-700">
                  <CdnLazyImage 
                    src="/images-optimized/yes-no-tarot/user-avatar2.webp" 
                    alt="Tarot QA Logo" 
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* 问题列表 */}
                <div className="flex-grow space-y-3">
                  {category.questions.map((question, qIndex) => (
                    <div key={qIndex} className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-4 rounded-2xl rounded-tl-none shadow-sm transition-all duration-300 hover:border-purple-500/60 w-full h-[70px] flex items-center">
                      <p className="text-gray-700 dark:text-gray-300 italic line-clamp-2">{question}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SingleCardBestQuestions; 