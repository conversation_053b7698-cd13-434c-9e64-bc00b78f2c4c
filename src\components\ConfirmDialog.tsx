import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';

interface ConfirmDialogProps {
  isOpen: boolean;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  message,
  onConfirm,
  onCancel,
}) => {
  const { t } = useTranslation();

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-[9999]" style={{ zIndex: 9999 }}>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            onClick={onCancel}
          />
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative w-[90%] max-w-md mx-auto"
          >
            <div className="dark:bg-gray-900/90 bg-white/95 backdrop-blur-lg rounded-2xl border border-purple-500/20 p-6 shadow-xl">
              <div className="text-center mb-6">
                <p className="dark:text-gray-200 text-gray-800 text-lg font-['Noto_Sans_SC']">{message}</p>
              </div>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={onConfirm}
                  className="px-6 py-2 rounded-lg bg-purple-600 hover:bg-purple-700 text-white text-base font-medium 
                           transition-colors font-['Inter']"
                >
                  {t('common.confirm')}
                </button>
                <button
                  onClick={onCancel}
                  className="px-6 py-2 rounded-lg dark:bg-gray-800 bg-gray-200 dark:text-gray-300 text-gray-700 text-base font-medium 
                           dark:hover:bg-gray-700 hover:bg-gray-300 transition-colors border dark:border-gray-700 border-gray-300 font-['Inter']"
                >
                  {t('common.cancel')}
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default ConfirmDialog;
