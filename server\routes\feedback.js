const express = require('express');
const router = express.Router();
const Feedback = require('../models/Feedback');
const { authenticateToken, optionalAuthenticateToken } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

// 提交用户反馈 - 支持登录和未登录用户
router.post(
  '/',
  optionalAuthenticateToken, // 可选认证，支持匿名反馈
  [
    body('content').trim().isLength({ min: 5, max: 2000 }).withMessage('反馈内容长度必须在5-2000字符之间'),
    body('type').isIn(['problem', 'suggestion', 'other']).withMessage('反馈类型无效'),
    body('email').optional().isEmail().withMessage('邮箱格式不正确')
  ],
  async (req, res) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    try {
      const { content, type, email } = req.body;
      
      // 准备反馈数据
      const feedbackData = {
        content,
        type,
        email: email || null,
        userId: req.user?.id || null,
        username: req.user?.username || null,
        metadata: {
          userAgent: req.headers['user-agent'],
          platform: req.headers['sec-ch-ua-platform'],
          language: req.headers['accept-language'],
          ipAddress: req.ip,
          referrer: req.headers['referer'] || null
        }
      };
      
      // 创建反馈
      const feedback = await Feedback.create(feedbackData);
      
      res.status(201).json({
        success: true,
        message: '反馈提交成功，感谢您的宝贵意见',
        data: {
          id: feedback.id
        }
      });
    } catch (error) {
      // console.error('提交反馈失败:', error);
      res.status(500).json({ success: false, message: '提交反馈失败，请稍后重试' });
    }
  }
);

// 以下是管理员API，需要认证和授权

// 获取反馈列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    // TODO: 添加管理员权限检查
    
    const { limit = 20, offset = 0, type } = req.query;
    
    const feedbacks = await Feedback.findAll({
      limit: parseInt(limit),
      offset: parseInt(offset),
      type
    });
    
    res.json({
      success: true,
      data: feedbacks
    });
  } catch (error) {
    console.error('获取反馈列表失败:', error);
    res.status(500).json({ success: false, message: '获取反馈列表失败' });
  }
});

// 获取反馈详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    // TODO: 添加管理员权限检查
    
    const feedback = await Feedback.findById(req.params.id);
    
    if (!feedback) {
      return res.status(404).json({ success: false, message: '反馈不存在' });
    }
    
    res.json({
      success: true,
      data: feedback
    });
  } catch (error) {
    console.error('获取反馈详情失败:', error);
    res.status(500).json({ success: false, message: '获取反馈详情失败' });
  }
});

// 获取反馈统计数据
router.get('/stats/summary', authenticateToken, async (req, res) => {
  try {
    // TODO: 添加管理员权限检查
    
    // 获取今天的统计
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    
    const todayStats = await Feedback.getStats({
      startDate: todayStart,
      endDate: todayEnd
    });
    
    // 获取全部的统计
    const allStats = await Feedback.getStats();
    
    res.json({
      success: true,
      data: {
        today: todayStats,
        all: allStats
      }
    });
  } catch (error) {
    console.error('获取反馈统计失败:', error);
    res.status(500).json({ success: false, message: '获取反馈统计失败' });
  }
});

module.exports = router; 