export interface Card {
  name: string;
  position: string;
  isReversed: boolean;
}

export interface DialogMessage {
  type: string;
  content: string;
}

export interface ReadingResult {
  timestamp: string;
  prologue?: string;
  answer?: string;
  summary?: string;
  deep_analysis?: string;
  content?: string;
  repeatedWarning?: string;
  [key: string]: any; // 用于支持analysis1, advice1等动态字段
}

export interface SelectedSpread {
  id: string;
  name: string;
  cardCount: number;
}

export interface SelectedReader {
  id: string;
  name: string;
  type: string;
  nameEn?: string;
}

export interface HistorySession {
  question: string;
  selectedCards: Card[];
  readingResult: ReadingResult;
  dialogHistory?: DialogMessage[];
  timestamp: string;
  selectedSpread?: SelectedSpread;
  selectedReader?: SelectedReader;
  username?: string;
  deepAnalysis?: string;
  status?: string;
}

export interface TranslatedSpread {
  name: string;
  positions: string[];
}

export interface Message {
  type: string;
  content: string;
} 