import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

const ThreeCardUnderstanding: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <div className="mt-24 sm:mt-32">
      <div className="text-center mb-10">
        <h2 className={`text-2xl sm:text-3xl font-bold ${
          theme === 'light' ? 'text-purple-800' : 'text-white'
        }`}>
          {t('yes_no_tarot.three_card_understanding.title', 'Understanding the Three Card Yes No Tarot Spread')}
        </h2>
        <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
      </div>
      
      <div className="max-w-5xl mx-auto">
        {/* The Three Pillars of Guidance */}
        <div className="text-center mb-8">
          <h3 className={`text-xl sm:text-2xl font-semibold ${
            theme === 'light' ? 'text-purple-700' : 'text-purple-300'
          }`}>
            {t('yes_no_tarot.three_card_understanding.pillars_title', 'The Three Pillars of Guidance')}
          </h3>
        </div>
        
        {/* Cards Explanation */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Card 1 */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-green-500/30 hover:border-green-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex flex-col items-center mb-4">
              <div className="w-16 h-24 bg-gradient-to-br from-green-400 to-emerald-600 rounded-lg flex items-center justify-center mb-3 shadow-md">
                <span className="text-white font-bold text-2xl">1</span>
              </div>
              <h4 className={`font-bold text-lg text-center ${
                theme === 'light' ? 'text-green-700' : 'text-green-400'
              }`}>
                {t('yes_no_tarot.three_card_understanding.card1_title', 'Card 1: Supporting Forces (Yes Energy)')}
              </h4>
            </div>
            <p className={`text-center ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.three_card_understanding.card1_description', 
                'Reveals positive influences, opportunities, and reasons why your answer might be "yes"')}
            </p>
          </div>
          
          {/* Card 2 */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-red-500/30 hover:border-red-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex flex-col items-center mb-4">
              <div className="w-16 h-24 bg-gradient-to-br from-red-400 to-rose-600 rounded-lg flex items-center justify-center mb-3 shadow-md">
                <span className="text-white font-bold text-2xl">2</span>
              </div>
              <h4 className={`font-bold text-lg text-center ${
                theme === 'light' ? 'text-red-700' : 'text-red-400'
              }`}>
                {t('yes_no_tarot.three_card_understanding.card2_title', 'Card 2: Challenging Forces (No Energy)')}
              </h4>
            </div>
            <p className={`text-center ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.three_card_understanding.card2_description', 
                'Shows obstacles, warnings, and factors that suggest a "no" response')}
            </p>
          </div>
          
          {/* Card 3 */}
          <div className="backdrop-blur-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/30 hover:border-purple-500/40 p-6 rounded-2xl shadow-sm">
            <div className="flex flex-col items-center mb-4">
              <div className="w-16 h-24 bg-gradient-to-br from-purple-400 to-indigo-600 rounded-lg flex items-center justify-center mb-3 shadow-md">
                <span className="text-white font-bold text-2xl">3</span>
              </div>
              <h4 className={`font-bold text-lg text-center ${
                theme === 'light' ? 'text-purple-700' : 'text-purple-300'
              }`}>
                {t('yes_no_tarot.three_card_understanding.card3_title', 'Card 3: Most Likely Outcome')}
              </h4>
            </div>
            <p className={`text-center ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              {t('yes_no_tarot.three_card_understanding.card3_description', 
                'The universe\'s final verdict based on current energies and your potential actions')}
            </p>
          </div>
        </div>
        

      </div>
    </div>
  );
};

export default ThreeCardUnderstanding; 