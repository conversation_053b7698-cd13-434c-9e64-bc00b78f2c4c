import React, { useEffect, useState } from 'react';
import { motion, animate } from 'framer-motion';
import './LoadingProgress.css';

interface Props {
  onLoadingComplete: () => void;
}

const LoadingProgress: React.FC<Props> = ({ onLoadingComplete }) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const duration = 3000; // 3秒完成
    
    // 使用 framer-motion 的 animate 来实现平滑的进度动画
    const controls = animate(0, 100, {
      duration: duration / 1000, // 转换为秒
      ease: "easeOut",
      onUpdate: (latest) => {
        setProgress(latest);
      },
      onComplete: () => {
        setTimeout(onLoadingComplete, 500);
      }
    });

    return () => controls.stop();
  }, [onLoadingComplete]);

  return (
    <div className="loading-container">
      <div className="progress-wrapper">
        <motion.div 
          className="progress-circle"
          style={{
            background: `conic-gradient(from 0deg, #9333EA ${progress * 3.6}deg, rgba(147, 51, 234, 0.1) 0deg)`
          }}
        >
          <div className="progress-inner">
            <motion.span 
              className="progress-number"
              initial={{ scale: 1 }}
              animate={{ scale: 1 }}
            >
              {Math.round(progress)}%
            </motion.span>
            <span className="progress-text">AI正在解读</span>
          </div>
        </motion.div>
      </div>
      <div className="loading-text">正在分析塔罗牌的含义...</div>
    </div>
  );
};

export default LoadingProgress;
