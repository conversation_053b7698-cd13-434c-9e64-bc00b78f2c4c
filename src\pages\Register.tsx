import React, { useState, useEffect } from 'react';
import { register, resendVerification, verifyEmail } from '../services/userService';
import type { VerifyEmailResponse } from '../services/userService';
import { motion } from 'framer-motion';
import { useUser } from '../contexts/UserContext';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import { useTranslation } from 'react-i18next';
import { GoogleLogin } from '@react-oauth/google';
import axios from '../utils/axios';
import { toast } from 'react-hot-toast';
import SEO from '../components/SEO';
import { useTheme } from '../contexts/ThemeContext';
import LanguageLink from '../components/LanguageLink';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';

// 添加一个邮箱验证工具函数
const validateEmail = (email: string): { isValid: boolean; errorMessage?: string } => {
  // 基本格式验证
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, errorMessage: 'errors.invalid_email' };
  }
  
  // 提取域名部分
  const domain = email.split('@')[1].toLowerCase();
  
  // 检查是否是一次性邮箱域名列表（可以根据需要扩展）
  const disposableEmailDomains = [
    'guerrillamail.com', 'guerrillamailblock.com', 'sharklasers.com', 'grr.la', 'pokemail.net',
    'spam4.me', 'mailinator.com', 'trashmail.com', 'trashmail.net', 'mailnesia.com',
    'tempemail.net', 'tmpmail.org', 'temp-mail.org', 'tempmail.com', '10minutemail.com',
    'minutemail.com', 'tempinbox.com', 'yopmail.com', 'yopmail.fr', 'yopmail.net',
    'cool.fr.nf', 'jetable.fr.nf', 'nospam.ze.tc', 'nomail.xl.cx', 'mega.zik.dj',
    'speed.1s.fr', 'discardmail.com', 'throwawaymail.com', 'emailsensei.com', 'mailcatch.com'
  ];
  
  if (disposableEmailDomains.includes(domain)) {
    return { isValid: false, errorMessage: 'errors.disposable_email' };
  }
  
  // 一些常见的无效域名或错误拼写检查（可扩展）
  const invalidDomains = [
    // Gmail的常见错误拼写
    'gmail.con', 'gmail.co', 'gamil.com', 'gmal.com', 'gmial.com', 'gmil.com', 'gnail.com', 'gmail.cm',
    'gmail.comm', 'gmail.om', 'gmail.cpm', 'gmail.net', 'gmail.org', 'gmail.cn', 'gmai.com', 'gemail.com',
    'googlemail.co', 'goolemail.com', 'gmsil.com',
    
    // Hotmail的常见错误拼写
    'hotmail.con', 'hotmail.co', 'hotmai.com', 'hotmal.com', 'hotmall.com', 'hotmail.cm', 'hotmail.comm',
    'hotmail.cim', 'hotmial.com', 'hotmaill.com', 'hotnail.com', 'hotmali.com', 'hormail.com',
    
    // Yahoo的常见错误拼写
    'yaho.com', 'yahoo.co', 'yahoo.con', 'yhaoo.com', 'yahooo.com', 'yaoo.com', 'yaho.cm', 'yahoo.comm',
    'yaboo.com', 'yahocom', 'yayoo.com', 'yahoop.com', 'yahou.com',
    
    // 163的常见错误拼写
    '163.con', '136.com', '613.com', '361.com', '1363.com', '163.co', '163.cm', '163.om',
    
    // QQ的常见错误拼写
    'qq.con', 'ww.com', 'pp.com', 'qp.com', 'pq.com', 'q.com', 'qq.cm', 'qq.co', 'qq.met',
    
    // Outlook的常见错误拼写
    'outlook.con', 'outllok.com', 'outlock.com', 'outlook.co', 'outlook.cm', 'outloook.com', 
    'outlok.com', 'outook.com', 'oultlook.com',
    
    // iCloud的常见错误拼写
    'icloud.con', 'icould.com', 'icloud.co', 'icloude.com', 'iclouud.com', 'iclod.com', 'iclould.com',
    
    // 126的常见错误拼写
    '126.con', '162.com', '216.com', '261.com', '126.co', '126.cm',
    
    // 其他常见域名错误
    'foxmail.con', 'foxmail.co', 'formail.com', 'faxmail.com', 'foxmal.com',
    'sina.con', 'sina.co', 'sian.com', 'sina.cm', 'sohu.con', 'sohu.co',
    'protonmail.con', 'protonmail.co', 'protonmail.cm', 'protomail.com',
    'zoho.con', 'zoho.co'
  ];
  if (invalidDomains.includes(domain)) {
    return { isValid: false, errorMessage: 'errors.typo_email_domain' };
  }
  
  // MX记录检查需要在后端实现，前端无法直接查询DNS记录
  
  return { isValid: true };
};

// 添加用户名验证函数
const validateUsername = (username: string): { isValid: boolean; errorMessage?: string } => {
  // 检查是否包含表情符号和特殊Unicode字符
  const emojiRegex = /[\u{1F000}-\u{1FFFF}|\u{2600}-\u{27FF}|\u{2300}-\u{23FF}|\u{2B50}|\u{2614}|\u{3030}|\u{303D}|\u{3297}|\u{3299}|\u{FE00}-\u{FE0F}|\u{1F900}-\u{1F9FF}|\u{1F1E0}-\u{1F1FF}]/u;
  
  if (emojiRegex.test(username)) {
    return { isValid: false, errorMessage: 'errors.no_emoji_allowed' };
  }

  // 只检查控制字符和某些特殊字符，允许各国语言
  const invalidCharsRegex = /[\u0000-\u001F\u007F-\u009F\u2028\u2029\uFFF0-\uFFFF]/;
  if (invalidCharsRegex.test(username)) {
    return { isValid: false, errorMessage: 'errors.invalid_username_chars' };
  }
  
  return { isValid: true };
};

// 添加密码验证函数
const validatePassword = (password: string): { isValid: boolean; errorMessage?: string } => {
  // 检查密码是否只包含字母、数字和下划线
  const passwordRegex = /^[a-zA-Z0-9_]+$/;
  if (!passwordRegex.test(password)) {
    return { isValid: false, errorMessage: 'errors.invalid_password_format' };
  }
  
  return { isValid: true };
};

// 添加 CheckmarkAnimation 组件
const CheckmarkAnimation: React.FC<{
  size?: number;
  color?: string;
  strokeWidth?: number;
}> = ({
  size = 32,
  color = '#FFFFFF',
  strokeWidth = 4,
}) => {
  const circleVariants = {
    hidden: { opacity: 0, scale: 0.5 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  const checkVariants = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { duration: 0.5, ease: "easeOut" },
        opacity: { duration: 0.01 },
      },
    },
  };

  return (
    <div style={{ width: size, height: size }}>
      <motion.svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        initial="hidden"
        animate="visible"
      >
        <defs>
          <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgb(168, 85, 247)" />
            <stop offset="100%" stopColor="rgb(236, 72, 153)" />
          </linearGradient>
        </defs>
        <motion.circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          variants={circleVariants}
        />
        <motion.path
          d="M30,50 L45,65 L70,40"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
          variants={checkVariants}
        />
      </motion.svg>
    </div>
  );
};

const Register: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { t, i18n } = useTranslation();
  // const isZH = i18n?.language?.startsWith('zh');
  const { setUser } = useUser();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    verificationCode: ''
  });
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [userId, setUserId] = useState<string | null>(null);
  const [isDarkTheme, setIsDarkTheme] = useState(() => 
    document.documentElement.classList.contains('dark')
  );

  // 添加监听主题变化的逻辑
  useEffect(() => {
    const themeObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          setIsDarkTheme(document.documentElement.classList.contains('dark'));
        }
      });
    });
    
    themeObserver.observe(document.documentElement, { attributes: true });
    
    return () => {
      themeObserver.disconnect();
    };
  }, []);

  // 添加倒计时效果
  useEffect(() => {
    if (countdown > 0) {
      const timer = setInterval(() => {
        setCountdown(prev => {
          const newValue = prev - 1;
          if (newValue <= 0) {
            localStorage.removeItem('verifyEmailCountdownEnd');
          }
          return newValue;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [countdown]);

  // 在表单数据变化时，保存到localStorage
  useEffect(() => {
    // 只有当表单有值时才保存
    if (formData.email && formData.username && formData.password) {
      localStorage.setItem('registerData', JSON.stringify({
        email: formData.email,
        username: formData.username,
        password: formData.password
      }));
    }
  }, [formData.email, formData.username, formData.password]);

  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => {
      const newFormData = {
        ...prev,
        [name]: value
      };
      
      // 如果有必要的信息，立即更新localStorage
      if (newFormData.email && newFormData.username && newFormData.password) {
        localStorage.setItem('registerData', JSON.stringify({
          email: newFormData.email,
          username: newFormData.username,
          password: newFormData.password
        }));
      }
      
      return newFormData;
    });
    
    // 清除错误提示
    setError('');
  };

  // 添加邮箱输入框失去焦点时的验证函数
  const handleEmailBlur = () => {
    if (formData.email) {
      const validation = validateEmail(formData.email);
      if (!validation.isValid && validation.errorMessage) {
        setError(t(validation.errorMessage));
      }
    }
  };

  // 添加用户名输入框失去焦点时的验证函数
  const handleUsernameBlur = () => {
    if (formData.username) {
      const validation = validateUsername(formData.username);
      if (!validation.isValid && validation.errorMessage) {
        setError(t(validation.errorMessage));
      }
    }
  };

  // 添加密码输入框失去焦点时的验证函数
  const handlePasswordBlur = () => {
    if (formData.password) {
      const validation = validatePassword(formData.password);
      if (!validation.isValid && validation.errorMessage) {
        setError(t(validation.errorMessage));
      }
    }
  };

  // 添加确认密码输入框失去焦点时的验证函数
  const handleConfirmPasswordBlur = () => {
    if (formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword) {
      setError(t('errors.passwords_not_match'));
    }
  };

  const handleSendVerificationCode = async () => {
    if (!formData.email) {
      setError(t('errors.please_enter_email'));
      return;
    }

    // 验证邮箱格式
    const validation = validateEmail(formData.email);
    if (!validation.isValid) {
      setError(t(validation.errorMessage || 'errors.invalid_email'));
      return;
    }

    // 验证用户名
    const usernameValidation = validateUsername(formData.username);
    if (!usernameValidation.isValid) {
      setError(t(usernameValidation.errorMessage || 'errors.invalid_username'));
      return;
    }
    
    // 验证密码格式
    const passwordValidation = validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      setError(t(passwordValidation.errorMessage || 'errors.invalid_password_format'));
      return;
    }

    // 验证两次输入的密码是否一致
    if (formData.password !== formData.confirmPassword) {
      setError(t('errors.passwords_not_match'));
      return;
    }

    // 确保注册数据已经保存到localStorage
    if (formData.username && formData.password) {
      localStorage.setItem('registerData', JSON.stringify({
        email: formData.email,
        username: formData.username,
        password: formData.password
      }));
    } else {
      setError(t('errors.complete_form'));
      return;
    }

    try {
      setIsLoading(true);
      // 直接发送邮箱获取验证码，这步会获取到临时用户的userId
      const response = await resendVerification(formData.email);
      toast.success(t('auth.success.codeSent'), {
        style: {
          padding: '16px 24px',
          borderRadius: '16px',
          background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
          color: isDark ? '#FFFFFF' : '#1F2937',
          maxWidth: '90%',
          width: '420px',
          margin: '0 auto',
          boxShadow: isDark ? '0 8px 32px rgba(0, 0, 0, 0.4)' : '0 8px 32px rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(8px)',
          fontSize: '15px',
          lineHeight: '1.5',
          textAlign: 'center',
          zIndex: 9999,
          fontFamily: 'var(--font-sans)',
        },
      });
      setCountdown(60);
      const endTime = Date.now() + 60 * 1000;
      localStorage.setItem('verifyEmailCountdownEnd', endTime.toString());
      if (response.userId) {
        setUserId(response.userId);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message;
      
      if (error.name === 'RegistrationError') {
        // 处理注册过程中的特殊错误
        setError(error.message || t('errors.send_code_failed'));
      } else if (errorMessage && typeof errorMessage === 'string') {
        // 直接显示后端返回的错误信息，但检查是否包含未翻译的消息
        // 检查是否为邮箱已存在的错误
        if (error.response?.data?.code === 'EMAIL_EXISTS' || 
            errorMessage.includes('已被注册') || 
            errorMessage.includes('already registered') ||
            errorMessage.includes('already exists')) {
          setError(t('errors.email_exists'));
        } else {
          setError(errorMessage);
        }
      } else if (error.response?.status === 404) {
        // 处理未找到临时用户的特殊情况
        setError(t('errors.email_not_registered'));
      } else {
        setError(t('errors.send_code_failed'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setIsSuccess(false);

    // 验证邮箱格式
    const validation = validateEmail(formData.email);
    if (!validation.isValid) {
      setError(t(validation.errorMessage || 'errors.invalid_email'));
      setIsLoading(false);
      return;
    }
    
    // 验证用户名
    const usernameValidation = validateUsername(formData.username);
    if (!usernameValidation.isValid) {
      setError(t(usernameValidation.errorMessage || 'errors.invalid_username'));
      setIsLoading(false);
      return;
    }
    
    // 验证密码格式
    const passwordValidation = validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      setError(t(passwordValidation.errorMessage || 'errors.invalid_password_format'));
      setIsLoading(false);
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError(t('errors.passwords_not_match'));
      setIsLoading(false);
      return;
    }

    if (!formData.verificationCode) {
      setError(t('errors.please_enter_code'));
      setIsLoading(false);
      return;
    }

    // 检查是否先获取了验证码
    if (!userId) {
      setError(t('auth.registration.expiredCode'));
      setCountdown(0);
      localStorage.removeItem('verifyEmailCountdownEnd');
      setIsLoading(false);
      return;
    }

    try {
      if (!userId) {
        throw new Error('Missing userId');
      }
      
      // 验证邮箱验证码
      const verifyResponse: VerifyEmailResponse = await verifyEmail(userId, formData.verificationCode);
      
      if (!verifyResponse.success) {
        // 根据错误代码选择合适的翻译文本，而不是直接使用服务器返回的消息
        if (verifyResponse.code === 'INVALID_CODE') {
          setError(t('auth.registration.incorrectCode'));
        } else if (verifyResponse.code === 'CODE_EXPIRED') {
          setError(t('auth.registration.expiredCode'));
          setCountdown(0);
          localStorage.removeItem('verifyEmailCountdownEnd');
        } else {
          setError(t('errors.verification_failed'));
        }
        setIsLoading(false);
        return;
      }
      
      // 验证成功后，如果有token和user，说明后端已经完成了注册流程
      if (verifyResponse.token && verifyResponse.user) {
        localStorage.setItem('token', verifyResponse.token);
        localStorage.removeItem('verifyEmailCountdownEnd');
        setUser(verifyResponse.user);
        
        // 显示成功状态
        setIsLoading(false);
        setIsSuccess(true);
        
        // 延时跳转到首页
        setTimeout(() => {
          navigate('/home');
        }, 800);
        return;
      }
      
      // 如果没有token和user，则需要进行注册请求
      await register({
        username: formData.username,
        email: formData.email,
        password: formData.password,
        language: localStorage.getItem('i18nextLng') || 'zh-CN'
      });
      
      // 注册成功后显示成功状态
      setIsLoading(false);
      setIsSuccess(true);
      
      // 延时跳转到登录页面
      setTimeout(() => {
        navigate('/login');
      }, 800);
    } catch (error: any) {
      const errorMessage = error.response?.data?.message;
      const errorCode = error.response?.data?.code;
      
      // 专门处理已知的错误代码，确保正确翻译
      if (errorCode === 'EMAIL_EXISTS') {
        setError(t('errors.email_exists'));
      } else if (errorCode === 'USERNAME_EXISTS') {
        setError(t('errors.username_exists'));
      } else if (errorCode === 'INVALID_CODE') {
        // 对于验证码错误，使用翻译文本而不是服务器返回的消息
        setError(t('auth.registration.incorrectCode'));
      } else if (errorCode === 'CODE_EXPIRED') {
        setError(t('auth.registration.expiredCode'));
        setCountdown(0);
        localStorage.removeItem('verifyEmailCountdownEnd');
      } else if (errorMessage && typeof errorMessage === 'string') {
        // 检查通用错误消息是否包含邮箱已注册的信息
        if (errorMessage.includes('已被注册') || 
            errorMessage.includes('already registered') ||
            errorMessage.includes('already exists')) {
          setError(t('errors.email_exists'));
        } else if (errorMessage.includes('验证码错误') || 
                  errorMessage.includes('验证码無效') || 
                  errorMessage.includes('incorrect code') || 
                  errorMessage.includes('invalid code')) {
          // 处理验证码错误消息
          setError(t('auth.registration.incorrectCode'));
        } else if (errorMessage.includes('验证码已过期') || 
                  errorMessage.includes('code expired') || 
                  errorMessage.includes('verification expired')) {
          // 处理验证码过期消息
          setError(t('auth.registration.expiredCode'));
          setCountdown(0);
          localStorage.removeItem('verifyEmailCountdownEnd');
        } else {
          // 对于其他未知错误，仍然显示服务器返回的消息
          setError(errorMessage);
        }
      } else {
        setError(t('errors.register_failed'));
      }
      setIsLoading(false);
    }
  };

  const handleGoogleSuccess = async (credentialResponse: any) => {
    try {
      // 获取浏览器指纹
      let fingerprint = '';

      // 确保使用最新格式的指纹
      const { getBrowserFingerprint } = await import('../utils/fingerprint');
      fingerprint = await getBrowserFingerprint();
 

      const response = await axios.post('/api/auth/google', {
        credential: credentialResponse.credential,
        fingerprint // 添加指纹信息
      });
      
      if (response.data.token) {
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('userId', response.data.user.id);
        setUser(response.data.user);
        navigate('/home');
      } else if (response.data.requireVerification) {
        // 不再跳转到verify-email页面，而是在当前页面处理
        setUserId(response.data.userId);
        setFormData(prev => ({
          ...prev,
          email: response.data.email
        }));
        toast.success(t('auth.please_verify_email'), {
          style: {
            padding: '16px 24px',
            borderRadius: '16px',
            background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
            color: isDark ? '#FFFFFF' : '#1F2937',
            maxWidth: '90%',
            width: '420px',
            margin: '0 auto',
            boxShadow: isDark ? '0 8px 32px rgba(0, 0, 0, 0.4)' : '0 8px 32px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(8px)',
            fontSize: '15px',
            lineHeight: '1.5',
            textAlign: 'center',
            zIndex: 9999,
            fontFamily: 'var(--font-sans)',
          },
        });
        // 自动触发发送验证码
        setTimeout(() => {
          handleSendVerificationCode();
        }, 500);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message;
      if (errorMessage && typeof errorMessage === 'string') {
        // 直接显示后端返回的错误信息
        toast.error(errorMessage, {
          style: {
            padding: '16px 24px',
            borderRadius: '16px',
            background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
            color: isDark ? '#FFFFFF' : '#1F2937',
            maxWidth: '90%',
            width: '420px',
            margin: '0 auto',
            boxShadow: isDark ? '0 8px 32px rgba(0, 0, 0, 0.4)' : '0 8px 32px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(8px)',
            fontSize: '15px',
            lineHeight: '1.5',
            textAlign: 'center',
            zIndex: 9999,
            fontFamily: 'var(--font-sans)',
          },
        });
      } else {
        toast.error(errorMessage || t('login.errors.google_login_failed'), {
          style: {
            padding: '16px 24px',
            borderRadius: '16px',
            background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
            color: isDark ? '#FFFFFF' : '#1F2937',
            maxWidth: '90%',
            width: '420px',
            margin: '0 auto',
            boxShadow: isDark ? '0 8px 32px rgba(0, 0, 0, 0.4)' : '0 8px 32px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(8px)',
            fontSize: '15px',
            lineHeight: '1.5',
            textAlign: 'center',
            zIndex: 9999,
            fontFamily: 'var(--font-sans)',
          },
        });
      }
    }
  };

  const handleGoogleError = () => {
    toast.error(t('login.errors.google_login_failed'), {
      style: {
        padding: '16px 24px',
        borderRadius: '16px',
        background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
        border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
        color: isDark ? '#FFFFFF' : '#1F2937',
        maxWidth: '90%',
        width: '420px',
        margin: '0 auto',
        boxShadow: isDark ? '0 8px 32px rgba(0, 0, 0, 0.4)' : '0 8px 32px rgba(0, 0, 0, 0.1)',
        backdropFilter: 'blur(8px)',
        fontSize: '15px',
        lineHeight: '1.5',
        textAlign: 'center',
        zIndex: 9999,
        fontFamily: 'var(--font-sans)',
      },
    });
  };

  // 切换密码显示/隐藏状态
  const togglePasswordVisibility = () => {
    setShowPassword(prevState => !prevState);
  };

  // 在组件卸载时清理localStorage中的注册数据
  useEffect(() => {
    return () => {
      // 只有在成功注册或离开页面时才清理
      if (isSuccess) {
        localStorage.removeItem('registerData');
      }
    };
  }, [isSuccess]);

  return (
    <div className="min-h-screen flex flex-col relative font-['Inter']">
      <SEO 
      />
      <LandingBackground />
      <div className="flex-grow flex items-center justify-center relative z-10 mt-8 sm:mt-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-2 sm:mb-8">
            <h1 className={`main-title mb-1 ${getFontClass()}`}>{t('auth.register')}</h1>
            <p className="sub-title"></p>
          </div>
          <div className="flex items-center justify-center">
            <div className="w-full max-w-md mx-auto">
              <div className="login-card p-5 sm:p-8 rounded-2xl shadow-2xl relative overflow-hidden">
                {/* 装饰性光效 */}
                <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/20 rounded-full blur-3xl"></div>
                
                <div className="relative">
                  {error && (
                    <div className="mb-3 sm:mb-4 p-2.5 sm:p-3 rounded-lg bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
                      <p className="text-red-400 text-xs sm:text-sm font-['Inter']">{error}</p>
                    </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-5 sm:space-y-6">
                    <div>
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <input
                          type="text"
                          name="username"
                          value={formData.username}
                          onChange={handleChange}
                          onBlur={handleUsernameBlur}
                          className="input-field login-input block w-full px-3 py-2 sm:py-2.5 rounded-xl
                                  dark:text-white text-gray-800 text-[15px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                  placeholder-gray-400 backdrop-blur-sm pl-10 font-['Inter']"
                          placeholder={t('auth.enter_username')}
                          required
                          autoComplete="username"
                        />
                      </div>
                    </div>

                    <div>
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 8L10.8906 13.2604C11.5624 13.7083 12.4376 13.7083 13.1094 13.2604L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          onBlur={handleEmailBlur}
                          className="input-field login-input block w-full px-3 py-2 sm:py-2.5 rounded-xl
                                  dark:text-white text-gray-800 text-[15px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                  placeholder-gray-400 backdrop-blur-sm pl-10 font-['Inter']"
                          placeholder={t('auth.enter_email')}
                          required
                          autoComplete="email"
                        />
                      </div>
                    </div>

                    <div>
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 15V17M6 21H18C19.1046 21 20 20.1046 20 19V13C20 11.8954 19.1046 11 18 11H6C4.89543 11 4 11.8954 4 13V19C4 20.1046 4.89543 21 6 21ZM16 11V7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7V11H16Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <input
                          type={showPassword ? "text" : "password"}
                          name="password"
                          value={formData.password}
                          onChange={handleChange}
                          onBlur={handlePasswordBlur}
                          className="input-field login-input block w-full px-3 py-2 sm:py-2.5 rounded-xl
                                  dark:text-white text-gray-800 text-[15px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                  placeholder-gray-400 backdrop-blur-sm pl-10 pr-12 font-['Inter']"
                          placeholder={t('auth.enter_password')}
                          required
                          autoComplete="new-password"
                        />
                        <button
                          type="button"
                          onClick={togglePasswordVisibility}
                          className="absolute right-2 top-1/2 -translate-y-1/2
                                  text-gray-300 hover:text-purple-400 transition-colors
                                  focus:outline-none focus:text-purple-400 z-20
                                  w-8 h-8 flex items-center justify-center"
                          aria-label={t('auth.password_visibility', { show: showPassword })}
                        >
                          {showPassword ? (
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                            </svg>
                          ) : (
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>

                    <div>
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 15V17M6 21H18C19.1046 21 20 20.1046 20 19V13C20 11.8954 19.1046 11 18 11H6C4.89543 11 4 11.8954 4 13V19C4 20.1046 4.89543 21 6 21ZM16 11V7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7V11H16Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <input
                          type={showPassword ? "text" : "password"}
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleChange}
                          onBlur={handleConfirmPasswordBlur}
                          className="input-field login-input block w-full px-3 py-2 sm:py-2.5 rounded-xl
                                  dark:text-white text-gray-800 text-[15px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                  placeholder-gray-400 backdrop-blur-sm pl-10 pr-12 font-['Inter']"
                          placeholder={t('auth.enter_confirm_password')}
                          required
                          autoComplete="new-password"
                        />
                        <button
                          type="button"
                          onClick={togglePasswordVisibility}
                          className="absolute right-2 top-1/2 -translate-y-1/2
                                  text-gray-300 hover:text-purple-400 transition-colors
                                  focus:outline-none focus:text-purple-400 z-20
                                  w-8 h-8 flex items-center justify-center"
                          aria-label={t('auth.password_visibility', { show: showPassword })}
                        >
                          {showPassword ? (
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                            </svg>
                          ) : (
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>

                    <div>
                      <div className="flex space-x-3">
                        <div className="relative flex-grow">
                          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 z-10">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M9 12H15M9 16H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L18.7071 8.70711C18.8946 8.89464 19 9.149 19 9.41421V19C19 20.1046 18.1046 21 17 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </div>
                          <input
                            id="verificationCode"
                            name="verificationCode"
                            type="text"
                            value={formData.verificationCode}
                            onChange={handleChange}
                            maxLength={6}
                            className="input-field login-input block w-full px-3 py-2 sm:py-2.5 rounded-xl
                                    dark:text-white text-gray-800 text-[15px] sm:text-[16px] leading-[20px] sm:leading-[24px] 
                                    placeholder-gray-400 backdrop-blur-sm pl-10 font-['Inter']"
                            required
                            placeholder={t('auth.enter_verification_code')}
                          />
                        </div>
                        <button
                          type="button"
                          onClick={handleSendVerificationCode}
                          disabled={countdown > 0 || isLoading}
                          className={`whitespace-nowrap px-4 py-2 rounded-xl text-sm font-medium
                                    bg-transparent border border-[rgba(255,255,255,0.1)] 
                                    transition-all duration-200
                                    ${countdown > 0 || isLoading
                                      ? 'text-gray-500 cursor-not-allowed'
                                      : 'text-blue-500 hover:text-blue-600 hover:border-blue-300 hover:shadow-[0_0_0_2px_rgba(59,130,246,0.2)]'}`}
                          style={countdown > 0 || isLoading ? {} : { color: '#3b82f6' }}
                        >
                          {countdown > 0 ? `${countdown}s` : t('auth.send_code')}
                        </button>
                      </div>
                    </div>

                    <div className="pt-2 sm:pt-3">
                      <button
                        type="submit"
                        disabled={isLoading || isSuccess}
                        className="w-full relative bg-purple-600 rounded-xl 
                                 hover:bg-purple-500 active:bg-purple-700 
                                 transition-colors duration-200 disabled:opacity-50"
                      >
                        <div className="relative px-3 sm:px-4 h-[34px] sm:h-[40px] flex items-center justify-center">
                          <div className="flex items-center justify-center w-[24px] sm:w-[28px] h-[24px] sm:h-[28px]">
                            {isLoading ? (
                              <div className="w-4 sm:w-5 h-4 sm:h-5 border-2 border-white/20 border-t-white rounded-full animate-spin"></div>
                            ) : isSuccess ? (
                              <CheckmarkAnimation size={24} strokeWidth={4} />
                            ) : (
                              <span className="text-white text-sm sm:text-base font-medium whitespace-nowrap font-['Inter']">{t('auth.register')}</span>
                            )}
                          </div>
                        </div>
                      </button>
                    </div>
                  </form>

                  <div className="mt-2.5 sm:mt-4 text-center">
                    <span className="text-gray-400 text-sm font-['Inter']">{t('auth.have_account')}</span>
                    <LanguageLink 
                      to="/login"
                      className="text-blue-500 hover:text-blue-600 ml-1 text-sm font-medium
                               transition-all duration-200 font-['Inter'] !important"
                      style={{ color: '#3b82f6' }}
                    >
                      {t('auth.login_now')}
                    </LanguageLink>
                  </div>

                  <div className="mt-2.5 sm:mt-4">
                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t dark:border-gray-600 border-gray-300"></div>
                      </div>
                      <div className="relative flex justify-center text-sm">
                        <span className="px-2 dark:bg-[#0D0C0F] bg-[#F4F4F5] dark:text-gray-400 text-gray-500 text-sm font-['Inter']">{t('login.or')}</span>
                      </div>
                    </div>

                    <div className="mt-2.5 sm:mt-4 flex justify-center">
                      <GoogleLogin
                        onSuccess={handleGoogleSuccess}
                        onError={handleGoogleError}
                        theme={isDarkTheme ? "filled_black" : "outline"}
                        shape="pill"
                        text="signin_with"
                        locale={i18n.language}
                        useOneTap={true}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* 服务条款和隐私政策文本 - 卡片外部，紧贴下方边框 */}
              <div className="text-center text-xs text-gray-400 mt-2 sm:mt-8 mb-10 sm:mb-4">
                <span>{t('auth.terms_agreement')} </span>
                <LanguageLink 
                  to="/terms" 
                  className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 
                         hover:from-purple-500 hover:to-pink-500 transition-all duration-200"
                >
                  {t('auth.terms_of_service')}
                </LanguageLink>
                <span> {t('auth.and')} </span>
                <LanguageLink 
                  to="/privacy"
                  className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 
                         hover:from-purple-500 hover:to-pink-500 transition-all duration-200"
                >
                  {t('auth.privacy_policy')}
                </LanguageLink>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="relative z-10">
        <Footer />
      </div>
      <style>
        {`
          .input-field {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.2s ease-in-out;
            z-index: 1;
            position: relative;
          }
          
          /* 确保按钮在浅色主题下文字为白色 */
          body:not(.dark) .login-card button[type="submit"],
          html:not(.dark) .login-card button[type="submit"],
          :not(.dark) .login-card button[type="submit"] {
            color: white !important;
          }
          
          /* 确保按钮内的所有文字元素在浅色主题下都是白色 */
          body:not(.dark) .login-card button[type="submit"] *,
          html:not(.dark) .login-card button[type="submit"] *,
          :not(.dark) .login-card button[type="submit"] span,
          :not(.dark) .login-card button[type="submit"] div {
            color: white !important;
          }
          
          /* 确保发送验证码按钮在浅色主题下文字为白色 */
          body:not(.dark) .login-card button.send-code-btn,
          html:not(.dark) .login-card button.send-code-btn,
          :not(.dark) .login-card button.send-code-btn,
          body:not(.dark) .login-card button.send-code-btn *,
          html:not(.dark) .login-card button.send-code-btn *,
          :not(.dark) .login-card button.send-code-btn span,
          :not(.dark) .login-card button.send-code-btn div {
            color: white !important;
          }
          
          /* CSS变量 - 主题相关颜色 */
          :root, html, body {
            --input-bg-dark: rgba(255, 255, 255, 0.05);
            --input-border-dark: rgba(255, 255, 255, 0.1);
            --input-text-dark: rgba(255, 255, 255, 0.9);
            --input-bg-light: #F4F4F5;
            --input-border-light: rgba(168, 85, 247, 0.2);
            --input-text-light: #1F2937;
          }
          
          /* 彻底覆盖输入框样式 */
          body:not(.dark) .login-card input, 
          body:not(.dark) .login-card textarea,
          html:not(.dark) .login-card input, 
          html:not(.dark) .login-card textarea,
          div:not(.dark) .login-card input,
          div:not(.dark) .login-card textarea,
          :not(.dark) .input-field[type="email"],
          :not(.dark) .input-field[type="password"],
          :not(.dark) .input-field[type="text"] {
            background-color: #F4F4F5 !important;
            color: #1F2937 !important;
            border: 1px solid rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 输入框聚焦状态 */
          body:not(.dark) .login-card input:focus, 
          html:not(.dark) .login-card input:focus,
          :not(.dark) .input-field[type="email"]:focus,
          :not(.dark) .input-field[type="password"]:focus,
          :not(.dark) .input-field[type="text"]:focus {
            background-color: #FFFFFF !important;
            color: #1F2937 !important;
            border: 1px solid rgba(168, 85, 247, 0.5) !important;
            box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 占位符文本样式 */
          body:not(.dark) .login-card input::placeholder,
          html:not(.dark) .login-card input::placeholder,
          :not(.dark) .input-field::placeholder {
            color: #6B7280 !important;
          }
          
          /* 深色主题输入框样式 */
          body.dark .login-card input, 
          html.dark .login-card input,
          .dark .input-field[type="email"],
          .dark .input-field[type="password"],
          .dark .input-field[type="text"] {
            background-color: #13111C !important;
            color: #FFFFFF !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
          }
          
          /* 深色主题输入框聚焦状态 */
          body.dark .login-card input:focus, 
          html.dark .login-card input:focus,
          .dark .input-field[type="email"]:focus,
          .dark .input-field[type="password"]:focus,
          .dark .input-field[type="text"]:focus {
            background-color: #13111C !important;
            color: #FFFFFF !important;
            border: 1px solid rgba(168, 85, 247, 0.5) !important;
            box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
          }
          
          /* 深色主题下的登录输入框样式 */
          .dark .login-input {
            background-color: #13111C !important;
            color: rgba(255, 255, 255, 0.9) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
          }
          
          /* 修复自动填充状态下的输入框样式 */
          body:not(.dark) .login-card input:-webkit-autofill,
          body:not(.dark) .login-card input:-webkit-autofill:hover,
          body:not(.dark) .login-card input:-webkit-autofill:focus,
          body:not(.dark) .login-card input:-webkit-autofill:active,
          html:not(.dark) .login-card input:-webkit-autofill,
          html:not(.dark) .login-card input:-webkit-autofill:hover,
          html:not(.dark) .login-card input:-webkit-autofill:focus,
          html:not(.dark) .login-card input:-webkit-autofill:active,
          :not(.dark) .login-card input:-webkit-autofill,
          :not(.dark) .login-card input:-webkit-autofill:hover,
          :not(.dark) .login-card input:-webkit-autofill:focus,
          :not(.dark) .login-card input:-webkit-autofill:active {
            -webkit-text-fill-color: #1F2937 !important;
            -webkit-box-shadow: 0 0 0px 1000px #F4F4F5 inset !important;
            box-shadow: 0 0 0px 1000px #F4F4F5 inset !important;
            background-color: #F4F4F5 !important;
            border: 1px solid rgba(168, 85, 247, 0.2) !important;
            caret-color: #1F2937 !important;
            transition: background-color 5000s ease-in-out 0s !important;
          }
          
          /* 深色主题下自动填充状态的输入框样式 */
          body.dark .login-card input:-webkit-autofill,
          body.dark .login-card input:-webkit-autofill:hover,
          body.dark .login-card input:-webkit-autofill:focus,
          body.dark .login-card input:-webkit-autofill:active,
          html.dark .login-card input:-webkit-autofill,
          html.dark .login-card input:-webkit-autofill:hover,
          html.dark .login-card input:-webkit-autofill:focus,
          html.dark .login-card input:-webkit-autofill:active,
          .dark .login-card input:-webkit-autofill,
          .dark .login-card input:-webkit-autofill:hover,
          .dark .login-card input:-webkit-autofill:focus,
          .dark .login-card input:-webkit-autofill:active {
            -webkit-text-fill-color: #FFFFFF !important;
            -webkit-box-shadow: 0 0 0px 1000px #13111C inset !important;
            box-shadow: 0 0 0px 1000px #13111C inset !important;
            background-color: #13111C !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            caret-color: #FFFFFF !important;
            transition: background-color 5000s ease-in-out 0s !important;
          }
          
          .login-card {
            position: relative;
            background: rgba(13, 12, 15, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(236, 72, 153, 0.3);
            box-shadow: 
              0 0 0 1px rgba(168, 85, 247, 0.2),
              0 0 15px rgba(168, 85, 247, 0.15),
              0 0 30px rgba(236, 72, 153, 0.15),
              inset 0 0 15px rgba(168, 85, 247, 0.1);
          }
          /* 深色主题下的样式保持不变 */
          :root.dark .login-card, 
          html.dark .login-card, 
          .dark .login-card {
            background: rgba(13, 12, 15, 0.95);
          }
          /* 浅色主题下的卡片背景颜色 */
          :root:not(.dark) .login-card, 
          html:not(.dark) .login-card, 
          :not(.dark) .login-card {
            background: #F4F4F5;
          }
          .login-card::before {
            content: '';
            position: absolute;
            inset: -1px;
            padding: 1px;
            background: linear-gradient(
              135deg,
              rgba(168, 85, 247, 0.5),
              rgba(236, 72, 153, 0.5)
            );
            -webkit-mask: 
              linear-gradient(#fff 0 0) content-box, 
              linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
          }
          @media (max-width: 640px) {
            .login-card {
              margin: 1rem;
            }
          }
          
          /* 浅色主题下图标颜色 */
          body:not(.dark) .relative .absolute.left-3 svg path {
            stroke: #6B7280 !important;
          }
          
          /* 深色主题下图标颜色 */
          .dark .relative .absolute.left-3 svg path {
            stroke: rgba(255, 255, 255, 0.7) !important;
          }
          
          /* 图标位置调整 */
          .relative .absolute.left-3 {
            z-index: 20 !important; 
            pointer-events: none !important;
          }
          
          /* 确保左侧有足够空间放置图标 */
          .input-field {
            padding-left: 40px !important;
          }
          
          /* 登录输入框特殊样式 */
          .login-input {
            background-color: var(--input-bg-light) !important;
            color: var(--input-text-light) !important;
            border-color: var(--input-border-light) !important;
          }
          
          .dark .login-input {
            background-color: var(--input-bg-dark) !important;
            color: var(--input-text-dark) !important;
            border-color: var(--input-border-dark) !important;
          }
          
          /* 浅色主题下确保文字和图标不重叠 */
          body:not(.dark) .login-input {
            color: #1F2937 !important;
            background-color: #F4F4F5 !important;
          }
          
          body:not(.dark) .login-input::placeholder {
            color: #6B7280 !important;
          }
        `}
      </style>
    </div>
  );
};

export default Register;