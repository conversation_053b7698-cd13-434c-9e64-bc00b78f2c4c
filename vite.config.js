import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
// https://vitejs.dev/config/
export default defineConfig(function (_a) {
    var command = _a.command, mode = _a.mode;
    return ({
        plugins: [react()],
        assetsInclude: ['**/*.glb'],
        resolve: {
            alias: {
                '@': path.resolve(__dirname, './src'),
            },
        },
        server: {
            host: '0.0.0.0',
            proxy: {
                '/api': {
                    target: mode === 'development' ? 'http://localhost:5000' : 'http://**************:5000',
                    changeOrigin: true,
                    rewrite: function (path) { return path; }
                }
            },
            headers: {
                'Cross-Origin-Opener-Policy': 'unsafe-none',
                'Cross-Origin-Embedder-Policy': 'unsafe-none'
            }
        },
        define: {
            'process.env': process.env
        },
        build: {
            rollupOptions: {
                onwarn: function (warning, warn) {
                    // 忽略 COOP 相关的警告
                    if (warning.message.includes('Cross-Origin-Opener-Policy')) {
                        return;
                    }
                    warn(warning);
                }
            },
            // 确保 public/landing 目录被复制到构建输出
            copyPublicDir: true,
            assetsDir: 'assets',
            outDir: 'dist'
        },
        publicDir: 'public'
    });
});
