import React from 'react';

interface SlideControlsProps {
  onScroll: (direction: 'left' | 'right') => void;
  isDark: boolean;
  t: (key: string) => string;
}

const SlideControls: React.FC<SlideControlsProps> = ({ onScroll, isDark, t }) => {
  return (
    <div className="flex justify-center items-center gap-4 w-full mt-6 h-10">
      <button 
        onClick={() => onScroll('left')}
        className={`${isDark ? 'bg-gray-800/60 hover:bg-gray-700/80' : 'bg-gray-200/80 hover:bg-gray-300/90'} ${isDark ? 'text-white' : 'text-gray-700'} rounded-full p-2 transition-colors duration-300 hover:shadow-purple-500/20`}
        aria-label={t('reading.shuffle.slide_prev')}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'} font-sans japanese`}>{t('reading.shuffle.slide_browse')}</div>
      <button 
        onClick={() => onScroll('right')}
        className={`${isDark ? 'bg-gray-800/60 hover:bg-gray-700/80' : 'bg-gray-200/80 hover:bg-gray-300/90'} ${isDark ? 'text-white' : 'text-gray-700'} rounded-full p-2 transition-colors duration-300 hover:shadow-purple-500/20`}
        aria-label={t('reading.shuffle.slide_next')}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  );
};

export default SlideControls; 