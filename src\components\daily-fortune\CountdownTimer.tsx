import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

interface CountdownTimerProps {
  countdown: string;
  getFontClass: () => string;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ countdown, getFontClass }) => {
  const { t } = useTranslation();

  return (
    <motion.div 
      className="text-center mt-4 mb-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="bg-purple-900/30 backdrop-blur-sm rounded-lg p-4 max-w-md mx-auto">
        <p className={`text-purple-200 mb-2 ${getFontClass()}`}>
          {t('daily.already_predicted', '您今天已经进行过每日运势预测')}
        </p>
        <div className="flex items-center justify-center space-x-2">
          <span className={`text-gray-300 ${getFontClass()}`}>
            {t('daily.next_prediction', '下次预测倒计时')}:
          </span>
          <span className="font-mono text-lg text-white font-medium tabular-nums">
            {countdown}
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default CountdownTimer; 