const { getConnection } = require('../services/database');
const { v4: uuidv4 } = require('uuid');

/**
 * 分享提交模型
 * 用于存储用户分享的截图和链接，并进行审核
 */
class ShareSubmission {
  /**
   * 创建分享提交
   * @param {Object} data 分享数据
   * @param {string} data.userId 用户ID
   * @param {string} data.shareUrl 分享链接
   * @param {string} data.imageUrl 分享截图URL（可选）
   * @param {string} data.platform 分享平台
   * @param {string} data.sessionId 关联的会话ID（可选）
   * @param {string} data.language 用户使用的语言（可选）
   * @returns {Promise<Object>} 创建的分享提交
   */
  static async create(data) {
    const pool = await getConnection();
    const id = uuidv4();
    const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
    
    const [result] = await pool.query(
      `INSERT INTO share_submissions (
        id, user_id, share_url, image_url, platform, 
        session_id, language, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?)`,
      [
        id, 
        data.userId, 
        data.shareUrl || '', // 使用空字符串作为默认值，而不是 null
        data.imageUrl || null, 
        data.platform, 
        data.sessionId || null,
        data.language || null,
        now, 
        now
      ]
    );
    
    if (result.affectedRows > 0) {
      return this.findById(id);
    }
    
    return null;
  }
  
  /**
   * 通过ID查找分享提交
   * @param {string} id 分享提交ID
   * @returns {Promise<Object|null>} 分享提交对象
   */
  static async findById(id) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      `SELECT * FROM share_submissions WHERE id = ?`,
      [id]
    );
    
    return rows[0] || null;
  }
  
  /**
   * 获取用户的所有分享提交
   * @param {string} userId 用户ID
   * @returns {Promise<Array>} 分享提交列表
   */
  static async findByUserId(userId) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      `SELECT * FROM share_submissions WHERE user_id = ? ORDER BY created_at DESC`,
      [userId]
    );
    
    return rows;
  }
  
  /**
   * 获取待审核的分享提交
   * @param {number} limit 限制数量
   * @param {number} offset 偏移量
   * @returns {Promise<Array>} 待审核的分享提交列表
   */
  static async findPending(limit = 10, offset = 0) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      `SELECT s.*, u.username, u.email 
       FROM share_submissions s
       JOIN users u ON s.user_id = u.id
       WHERE s.status = 'pending'
       ORDER BY s.created_at ASC
       LIMIT ? OFFSET ?`,
      [limit, offset]
    );
    
    return rows;
  }
  
  /**
   * 更新分享提交状态
   * @param {string} id 分享提交ID
   * @param {string} status 状态：'approved', 'rejected'
   * @param {string} reviewerId 审核人ID
   * @param {string} reviewNote 审核备注
   * @returns {Promise<boolean>} 更新是否成功
   */
  static async updateStatus(id, status, reviewerId, reviewNote = '') {
    const pool = await getConnection();
    const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
    
    const [result] = await pool.query(
      `UPDATE share_submissions 
       SET status = ?, reviewer_id = ?, review_note = ?, reviewed_at = ?, updated_at = ?
       WHERE id = ?`,
      [status, reviewerId, reviewNote, now, now, id]
    );
    
    return result.affectedRows > 0;
  }
  
  /**
   * 检查用户是否已经获得过分享奖励
   * @param {string} userId 用户ID
   * @returns {Promise<boolean>} 是否已获得奖励
   */
  static async hasReceivedReward(userId) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      `SELECT COUNT(*) as count FROM share_submissions 
       WHERE user_id = ? AND status = 'approved' AND reward_granted = 1`,
      [userId]
    );
    
    return rows[0].count > 0;
  }
  
  /**
   * 标记已发放奖励
   * @param {string} id 分享提交ID
   * @returns {Promise<boolean>} 更新是否成功
   */
  static async markRewardGranted(id) {
    const pool = await getConnection();
    const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
    
    const [result] = await pool.query(
      `UPDATE share_submissions 
       SET reward_granted = 1, reward_granted_at = ?, updated_at = ?
       WHERE id = ?`,
      [now, now, id]
    );
    
    return result.affectedRows > 0;
  }
}

module.exports = { ShareSubmission }; 