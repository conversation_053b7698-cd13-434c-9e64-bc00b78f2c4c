import React, { ReactNode } from 'react';
import HoverableText from './HoverableText';

interface HoroscopeFeatureCardProps {
  title: string;
  description: string;
  icon: ReactNode;
  onClick: () => void;
  isFullWidth?: boolean;
}

/**
 * 星座运势特性卡片组件，提供统一的卡片样式和悬停效果
 */
const HoroscopeFeatureCard: React.FC<HoroscopeFeatureCardProps> = ({
  title,
  description,
  icon,
  onClick,
  isFullWidth = false
}) => {
  return (
    <div 
      className={`feature-card flex flex-col items-start gap-4 p-6 sm:p-8 dark:bg-[#0D0C0F]/95 bg-[#F4F4F5] backdrop-blur-xl rounded-xl ${isFullWidth ? 'md:col-span-2' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-center gap-4 w-full">
        <div className="flex-shrink-0 w-16 h-16 rounded-full flex items-center justify-center border-2 border-amber-600">
          {icon}
        </div>
        <HoverableText
          as="h3"
          isTitle={true}
          className="text-xl"
        >
          {title}
        </HoverableText>
      </div>
      <HoverableText
        className="w-full text-left mt-2"
      >
        {description}
      </HoverableText>
    </div>
  );
};

export default HoroscopeFeatureCard; 