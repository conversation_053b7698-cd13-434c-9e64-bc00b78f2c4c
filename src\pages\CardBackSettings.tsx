import React, { useState, useEffect, useCallback } from 'react';
import { useUser } from '../contexts/UserContext';
import { useTranslation } from 'react-i18next';
// import { motion } from 'framer-motion';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import VipPromptDialog from '../components/VipPromptDialog';
import '../styles/CardBackSettings.css';
import SEO from '../components/SEO';
import { useTheme } from '../contexts/ThemeContext';
import CdnLazyImage from '../components/CdnLazyImage';

interface CardBack {
  id: string;
  image: string;
  isVip: boolean;
  name: string;
  description?: string;
}

const CARD_BACKS: CardBack[] = [
  {
    id: '001',
    image: '/images-optimized/back/001.webp',
    isVip: false,
    name: '暗夜荆棘',
  },
  {
    id: '002',
    image: '/images-optimized/back/002.webp',
    isVip: true,
    name: '月光魔典'
  },
  {
    id: '003',
    image: '/images-optimized/back/003.webp',
    isVip: true,
    name: '星辰织梦'
  },
  {
    id: '004',
    image: '/images-optimized/back/004.webp',
    isVip: true,
    name: '命运之门'
  },
  {
    id: '005',
    image: '/images-optimized/back/005.webp',
    isVip: true,
    name: '秘法典籍'
  },
  {
    id: '006',
    image: '/images-optimized/back/006.webp',
    isVip: true,
    name: '魔法印记'
  },
  {
    id: '007',
    image: '/images-optimized/back/007.webp',
    isVip: true,
    name: '神圣符文'
  },
  {
    id: '008',
    image: '/images-optimized/back/008.webp',
    isVip: true,
    name: '预言之书'
  },
  {
    id: '009',
    image: '/images-optimized/back/009.webp',
    isVip: true,
    name: '灵魂之镜'
  },
  {
    id: '010',
    image: '/images-optimized/back/010.webp',
    isVip: true,
    name: '命运织网'
  },
  {
    id: '011',
    image: '/images-optimized/back/011.webp',
    isVip: true,
    name: '星光罗盘'
  },
  {
    id: '012',
    image: '/images-optimized/back/012.webp',
    isVip: true,
    name: '智慧之眼'
  }
];

const CardBackSettings: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { user } = useUser();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [selectedCardBack, setSelectedCardBack] = useState<string>(() => {
    return localStorage.getItem('selectedCardBack') || '001';
  });
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [showVipPrompt, setShowVipPrompt] = useState(false);

  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  useEffect(() => {
    localStorage.setItem('selectedCardBack', selectedCardBack);
  }, [selectedCardBack]);

  const handleCardBackSelect = (cardBack: CardBack) => {
    if (cardBack.isVip && (!user?.vipStatus || user.vipStatus === 'none')) {
      setShowVipPrompt(true);
      return;
    }
    setSelectedCardBack(cardBack.id);
  };

  const handleImageLoad = useCallback((id: string) => {
    setLoadedImages(prev => new Set(prev).add(id));
  }, []);

  return (
    <>
      <SEO 
      />
      
      <div className="min-h-screen flex flex-col relative">
        <LandingBackground />
        
        <div className="flex-grow relative z-10">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8">
            <div className="text-center mt-8 sm:mt-10">
              <h1 className={`main-title mb-1 ${getFontClass()}`}>{t('card_back_settings.title')}</h1>
              <p className={`sub-title mb-4 sm:mb-6 ${getFontClass()}`}>{t('card_back_settings.subtitle')}</p>
            </div>

            <div className="card-grid">
              {CARD_BACKS.map((cardBack) => (
                <div
                  key={cardBack.id}
                  className={`card-item ${selectedCardBack === cardBack.id ? 'selected' : ''} 
                            ${cardBack.isVip ? 'vip' : ''}`}
                  onClick={() => handleCardBackSelect(cardBack)}
                  style={{
                    background: isDark ? 'rgba(30, 30, 30, 0.5)' : 'rgba(240, 240, 245, 0.7)',
                    border: isDark 
                      ? '1px solid rgba(255, 255, 255, 0.1)' 
                      : '1px solid rgba(180, 180, 200, 0.3)'
                  }}
                >
                  <div className="card-content">
                    {!loadedImages.has(cardBack.id) && (
                      <div className="card-placeholder" 
                        style={{
                          background: isDark 
                            ? 'linear-gradient(110deg, #2a2a2a 8%, #383838 18%, #2a2a2a 33%)' 
                            : 'linear-gradient(110deg, #e5e5e5 8%, #f0f0f0 18%, #e5e5e5 33%)'
                        }}
                      />
                    )}
                    <CdnLazyImage
                      src={cardBack.image}
                      alt={`Card Back ${cardBack.id}`}
                      className={`card-image ${loadedImages.has(cardBack.id) ? 'loaded' : ''}`}
                      onLoad={() => handleImageLoad(cardBack.id)}
                    />
                    {selectedCardBack === cardBack.id && (
                      <div className="selected-indicator">
                        <svg className="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    )}
                    <div className="card-info" 
                      style={{
                        background: isDark 
                          ? 'linear-gradient(to top, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.8) 50%, transparent)'
                          : 'linear-gradient(to top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8) 50%, transparent)'
                      }}
                    >
                      <div className="card-text font-['Inter']">
                        {/* 移除卡背名称显示 */}
                      </div>
                      {cardBack.isVip && (
                        <span className="vip-badge font-['Inter']" style={{ color: '#000' }}>
                          {t('card_back_settings.vip_badge')}
                        </span>
                      )}
                      {!cardBack.isVip && (
                        <span className="free-badge font-['Inter']">{t('card_back_settings.free_badge')}</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <Footer />
        <VipPromptDialog 
          isOpen={showVipPrompt} 
          onCancel={() => setShowVipPrompt(false)} 
        />
      </div>
    </>
  );
};

export default CardBackSettings;
