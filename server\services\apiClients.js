/**
 * 集中管理所有API客户端的配置文件
 */
const axios = require('axios');

// 创建用于调用 Zhipu API (智谱AI GLM) 的 axios 实例
const zhipuAPI = axios.create({
  baseURL: "https://open.bigmodel.cn/api/paas/v4/",
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${process.env.ZHIPU_API_KEY}`
  }
});

// 创建用于调用 Qwen API 的 axios 实例
const qwenAPI = axios.create({
  baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
  timeout: 500000,
  headers: {
    'Authorization': `Bearer ${process.env.QWEN_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

const DoubaoAPI = axios.create({
  baseURL: "https://ark.cn-beijing.volces.com/api/v3",
  timeout: 500000,
  headers: {
    'Authorization': `Bearer ${process.env.DOUBAO_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

module.exports = {
  zhipuAPI,
  qwenAPI,
  DoubaoAPI
}; 