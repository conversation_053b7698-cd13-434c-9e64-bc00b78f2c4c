/**
 * 根据当前语言返回适合的字体类
 * 这个函数应该在需要应用特定于语言的字体样式时使用
 * 
 * @param language 当前语言，通常从i18n.language获取
 * @returns 适合当前语言的CSS类名
 */
export const getFontClass = (language: string): string => {
  switch (language) {
    case 'en':
      return 'font-sans text-en';
    case 'ja':
      return 'font-sans japanese';
    case 'zh-CN':
      return 'font-sans chinese';
    case 'zh-TW':
      return 'font-sans chinese-trad';
    default:
      return 'font-sans';
  }
}; 