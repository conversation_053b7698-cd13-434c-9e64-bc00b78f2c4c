const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { getConnection } = require('../services/database');
const { User } = require('../models/User');

// 检查用户是否使用过深度解析
router.get('/check-deep-analysis', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 获取数据库连接
    const pool = await getConnection();
    
    // 查询用户是否有包含deep_analysis的会话记录
    const [records] = await pool.query(`
      SELECT COUNT(*) as count
      FROM sessions
      WHERE user_id = ? AND deep_analysis IS NOT NULL AND deep_analysis != ''
    `, [userId]);
    
    // 如果count > 0，表示用户已经使用过深度解析
    const hasUsedDeepAnalysis = records[0].count > 0;
    
    res.json({ hasUsedDeepAnalysis });
  } catch (error) {
    console.error('Error checking deep analysis usage:', error);
    res.status(500).json({ error: '检查深度解析使用情况失败' });
  }
});

// 检查用户是否使用过追问功能
router.get('/check-followup-usage', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 获取数据库连接
    const pool = await getConnection();
    
    // 查询用户是否有包含dialog_history的会话记录
    const [records] = await pool.query(`
      SELECT COUNT(*) as count
      FROM sessions
      WHERE user_id = ? AND dialog_history IS NOT NULL AND dialog_history != 'null' AND dialog_history != '[]'
    `, [userId]);
    
    // 如果count > 0，表示用户已经使用过追问功能
    const hasUsedFollowup = records[0].count > 0;
    
    res.json({ hasUsedFollowup });
  } catch (error) {
    console.error('Error checking followup usage:', error);
    res.status(500).json({ error: '检查追问使用情况失败' });
  }
});

// 检查会话的阅读类型（基础解读/深度解析/追问）
router.get('/check-session-type/:sessionId', authenticateToken, async (req, res) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user.userId;
    
    // 获取数据库连接
    const pool = await getConnection();
    
    // 查询会话信息
    const [sessions] = await pool.query(`
      SELECT 
        deep_analysis,
        dialog_history
      FROM sessions
      WHERE id = ? AND user_id = ?
    `, [sessionId, userId]);
    
    if (!sessions || sessions.length === 0) {
      return res.status(404).json({ error: '找不到相关会话' });
    }
    
    const session = sessions[0];
    
    // 判断会话类型
    const hasDeepAnalysis = session.deep_analysis && session.deep_analysis !== '' && session.deep_analysis !== 'null';
    const hasFollowup = session.dialog_history && session.dialog_history !== '[]' && session.dialog_history !== 'null';
    
    res.json({ 
      sessionTypes: {
        basicReading: true, // 基础解读总是存在
        deepAnalysis: hasDeepAnalysis,
        followup: hasFollowup
      }
    });
  } catch (error) {
    console.error('Error checking session type:', error);
    res.status(500).json({ error: '检查会话类型失败' });
  }
});

// 检查用户是否使用过渡鸦占卜师
router.get('/check-raven-usage', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 获取数据库连接
    const pool = await getConnection();
    
    // 查询数据库，看用户是否使用过渡鸦或Raven占卜师
    const [sessions] = await pool.query(`
      SELECT COUNT(*) as count
      FROM sessions 
      WHERE user_id = ? 
      AND (reader_id = 'raven' OR reader_name = '渡鸦')
    `, [userId]);
    
    // 如果count > 0，表示用户已经使用过渡鸦占卜师
    const hasUsedRaven = sessions[0].count > 0;
    
    res.json({ hasUsedRaven });
  } catch (error) {
    console.error('Error checking raven usage:', error);
    res.status(500).json({ error: '检查渡鸦占卜师使用情况失败' });
  }
});

module.exports = router; 