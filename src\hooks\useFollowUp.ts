import { useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { generateFollowUpReading } from '../services/deepseekService';

// 定义Message接口
interface Message {
  type: 'user' | 'reader';
  content: string;
  className?: string;
  isParagraph?: boolean;
  id?: string;
}

interface UseFollowUpProps {
  user: any;
  hasUsedFollowup: boolean;
  isSubmitting: boolean;
  followUpQuestion: string;
  setFollowUpQuestion: (question: string) => void;
  setIsSubmitting: (isSubmitting: boolean) => void;
  setShowVipPrompt: (show: boolean) => void;
  setMessages: (updater: React.SetStateAction<Message[]>) => void;
  messagesContainerRef: React.RefObject<HTMLDivElement>;
  t: any; // i18n translate function
  startProgress: () => void;
  markApiReceived: () => void;
  completeProgress: (callback?: () => void) => void;
  setHasUsedFollowup: (hasUsed: boolean) => void;
}

export const useFollowUp = (props: UseFollowUpProps) => {
  const {
    user,
    hasUsedFollowup,
    isSubmitting,
    followUpQuestion,
    setFollowUpQuestion,
    setIsSubmitting,
    setShowVipPrompt,
    setMessages,
    messagesContainerRef,
    t,
    startProgress,
    markApiReceived,
    completeProgress,
    setHasUsedFollowup
  } = props;

  const navigate = useNavigate();
  const followUpController = useRef<AbortController | null>(null);

  return useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!followUpQuestion.trim() || isSubmitting) {
      return;
    }

    if (!user) {
      navigate('/login');
      return;
    }

    // 检查非VIP用户是否已使用过追问
    if (user.vipStatus !== 'active' && hasUsedFollowup) {
      setShowVipPrompt(true);
      return;
    }

    try {
      if (followUpController.current) {
        followUpController.current.abort();
      }

      followUpController.current = new AbortController();
      
      setIsSubmitting(true);
      const userMessage = { type: 'user' as const, content: followUpQuestion };
      setMessages(prev => [...prev, userMessage]);
      
      // 在发送请求前清空输入框
      setFollowUpQuestion('');
      
      // 在添加用户消息后滚动到用户消息位置
      setTimeout(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
      }, 100);
      
      const reading = await generateFollowUpReading(
        followUpQuestion, 
        followUpController.current.signal
      );
      
      // 检查是否为错误响应
      try {
        const parsedResponse = JSON.parse(reading);
        if (parsedResponse.error && parsedResponse.errorType === 'NO_FREE_FOLLOWUP') {
          // 用户已使用过免费追问，显示VIP提示
          setShowVipPrompt(true);
          
          // 显示错误消息
          setMessages(prev => [
            ...prev,
            {
              type: 'reader' as const,
              content: parsedResponse.message,
              className: 'font-sans japanese'
            }
          ]);
          
          // 更新追问使用状态
          setHasUsedFollowup(true);
          return;
        }
      } catch (parseError) {
        // 不是JSON格式，继续处理正常的响应
      }
      
      let newMessages: Message[] = [];
      
      // 检查是否为伦理干预消息
      if (typeof reading === 'string' && reading.startsWith('[ETHICAL_INTERVENTION]')) {
        // 移除标记并处理干预消息
        const interventionMessage = reading.replace('[ETHICAL_INTERVENTION]', '');
        newMessages = [{
          type: 'reader' as const,
          content: interventionMessage
        }];
      } else if (reading.includes('\n\n')) {
        newMessages = reading.split('\n\n')
          .filter((text: string) => text.trim())
          .map((text: string) => ({
            type: 'reader' as const,
            content: text.trim()
          }));
      } else {
        newMessages = [{
          type: 'reader' as const,
          content: reading.trim()
        }];
      }
      
      if (newMessages.length > 0) {
        setMessages(prev => [...prev, ...newMessages]);
        
        const sessionId = localStorage.getItem('sessionId');
        if (sessionId) {
          // 获取当前消息并添加新消息
          const existingMessages = sessionId ? 
            JSON.parse(localStorage.getItem(`${sessionId}_messages`) || '[]') : [];
          const updatedMessages = [...existingMessages, userMessage, ...newMessages];
          localStorage.setItem(`${sessionId}_messages`, JSON.stringify(updatedMessages));
        }
        
        // 已使用过追问，更新状态
        setHasUsedFollowup(true);
      }
    } catch (error: any) {
      if (error?.name === 'CanceledError' || error?.name === 'AbortError' || axios.isCancel(error)) {
        return;
      }
      
      // console.error('Error generating follow-up reading:', error);
      setMessages(prev => [
        ...prev,
        { 
          type: 'reader', 
          content: t('reading.error.generate_failed'),
          className: 'font-sans japanese'
        }
      ]);
    } finally {
      setIsSubmitting(false);
    }
  }, [
    user,
    hasUsedFollowup,
    isSubmitting,
    followUpQuestion,
    setFollowUpQuestion,
    setIsSubmitting,
    setShowVipPrompt,
    setMessages,
    messagesContainerRef,
    t,
    startProgress,
    markApiReceived,
    completeProgress,
    setHasUsedFollowup,
    navigate
  ]);
}; 