import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { SPREAD_OPTIONS } from '../data/spreads';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import { useTranslation } from 'react-i18next';
import SEO from '../components/SEO';
import { useTheme } from '../contexts/ThemeContext';
import CdnLazyImage from '../components/CdnLazyImage';
import MULTI_LINGUAL_SPREAD_DETAILS from '../data/spreadDetails';
import LanguageLink from '../components/LanguageLink';
import HoverableText from '../components/horoscope/HoverableText';

const SpreadDetail: React.FC = () => {
  const { spreadId } = useParams<{ spreadId: string }>();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [spread, setSpread] = useState<typeof SPREAD_OPTIONS[0] | null>(null);
  const [relatedSpreads, setRelatedSpreads] = useState<typeof SPREAD_OPTIONS[0][]>([]);

  useEffect(() => {
    // 查找对应的牌阵
    const foundSpread = SPREAD_OPTIONS.find(s => s.id === spreadId);
    if (foundSpread) {
      setSpread(foundSpread);
      
      // 获取相关牌阵
      if (spreadId) {
        // 根据当前语言获取对应的牌阵详情
        const currentLang = i18n.language as keyof typeof MULTI_LINGUAL_SPREAD_DETAILS;
        const langDetails = MULTI_LINGUAL_SPREAD_DETAILS[currentLang] || MULTI_LINGUAL_SPREAD_DETAILS['zh-CN'];
        
        if (langDetails[spreadId]?.relatedSpreads) {
          const related = SPREAD_OPTIONS.filter(s => 
            spreadId && langDetails[spreadId].relatedSpreads.includes(s.id)
          );
          setRelatedSpreads(related);
        }
      }
    } else {
      // 如果没有找到对应的牌阵，跳转回牌阵列表页
      navigate('/spreads');
    }
  }, [spreadId, navigate, i18n.language]);

  // 获取翻译后的牌阵信息
  const getTranslatedSpreadInfo = () => {
    if (!spread) return { name: '', description: '', positions: [] };
    
    const spreadId = spread.id.replace(/-/g, '_');
    if (Object.keys(t('spreads', { returnObjects: true })).includes(spreadId)) {
      return {
        name: t(`spreads.${spreadId}.name`),
        description: t(`spreads.${spreadId}.description`),
        positions: spread.positions.map((_, index) => {
          const positionKeys = Object.keys(t(`spreads.${spreadId}.positions`, { returnObjects: true }));
          return t(`spreads.${spreadId}.positions.${positionKeys[index]}`);
        })
      };
    }
    return {
      name: spread.name,
      description: spread.description,
      positions: spread.positions
    };
  };

  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  // 获取板块标题的翻译
  const getSectionTitle = (key: string, defaultTitle: string) => {
    // 尝试获取翻译，如果没有则使用默认值
    return t(`spread.${key}`, defaultTitle);
  };

  if (!spread) {
    return (
      <div className="min-h-screen flex flex-col relative">
        <LandingBackground />
        <div className="flex-grow flex items-center justify-center relative z-10">
          <div className={`text-center ${isDark ? 'text-white' : 'text-gray-800'}`}>
            {t('common.loading')}...
          </div>
        </div>
      </div>
    );
  }

  const { name: spreadName, description: spreadDescription, positions: spreadPositions } = getTranslatedSpreadInfo();
  
  // 获取翻译后的牌阵详情信息
  const getTranslatedSpreadDetails = () => {
    if (!spreadId) return undefined;
    
    // 获取当前语言
    const currentLang = i18n.language as keyof typeof MULTI_LINGUAL_SPREAD_DETAILS;
    
    // 检查当前语言是否有对应的牌阵详情
    if (MULTI_LINGUAL_SPREAD_DETAILS[currentLang] && 
        MULTI_LINGUAL_SPREAD_DETAILS[currentLang][spreadId]) {
      return MULTI_LINGUAL_SPREAD_DETAILS[currentLang][spreadId];
    }
    
    // 如果当前语言没有对应的牌阵详情，回退到中文
    return MULTI_LINGUAL_SPREAD_DETAILS['zh-CN'][spreadId];
  };
  
  // 获取翻译后的牌阵详情
  const spreadDetail = getTranslatedSpreadDetails();

  // 优化后的卡片组件
  const InfoCard: React.FC<{title: string; children: React.ReactNode; className?: string; accentColor?: string; icon?: string}> = 
    ({ title, children, className = '', accentColor = '', icon }) => {
    // 确定默认和自定义颜色
    const defaultAccent = isDark ? 'from-purple-600/20 to-pink-600/20' : 'from-purple-200 to-pink-100';
    const accentGradient = accentColor || defaultAccent;
    
    return (
      <div className={`mb-10 relative ${className}`}>
        {/* 卡片标题 - 带装饰效果 */}
        <div className="relative z-10 flex items-center mb-5 pl-4">
          {icon && (
            <span className="mr-3 text-2xl">
              {icon}
            </span>
          )}
          <h2 className={`text-xl font-semibold inline-flex items-center ${isDark ? 'text-white' : 'text-gray-800'}`}>
            {title}
          </h2>
          {/* 删除下划装饰线 */}
        </div>
        
        {/* 卡片内容 - 增强阴影和视觉层次 */}
        <div className={`
          relative rounded-2xl overflow-hidden 
          border ${isDark ? 'border-gray-700/50' : 'border-gray-200/80'}
          shadow-lg
        `}>
          {/* 渐变背景装饰 */}
          <div className={`absolute inset-0 bg-gradient-to-br ${accentGradient} opacity-10`}></div>
          
          {/* 内容容器 */}
          <div className={`
            relative z-10 p-6 
            ${isDark ? 'bg-gray-800/80' : 'bg-white/90'} 
            backdrop-blur-sm
          `}>
            {children}
          </div>
        </div>
      </div>
    );
  };

  // 构建SEO信息
  const generateSEOInfo = () => {
    if (!spread || !spreadDetail) return {
      title: `${spreadName} - ${t('spread.title')}`,
      description: spreadDescription
    };
    
    // 根据当前语言选择适当的关键词
    let keywords = '';
    switch(i18n.language) {
      case 'en':
        keywords = `tarot spread, ${spreadName}, ${spread.cardCount} card spread, ${spread.tags.join(', ')}, ${spread.category.join(', ')} tarot`;
        break;
      case 'ja':
        keywords = `タロットスプレッド, ${spreadName}, ${spread.cardCount}枚スプレッド, ${spread.tags.join(', ')}, ${spread.category.join(', ')}タロット`;
        break;
      case 'zh-TW':
        keywords = `塔羅牌陣, ${spreadName}, ${spread.cardCount}張牌陣, ${spread.tags.join(', ')}, ${spread.category.join(', ')}塔羅`;
        break;
      default:
        keywords = `塔罗牌阵, ${spreadName}, ${spread.cardCount}张牌阵, ${spread.tags.join(', ')}, ${spread.category.join(', ')}塔罗`;
    }
    
    // 增强描述信息，结合牌阵详情
    const enhancedDescription = `${spreadDescription}。${spreadDetail.history.substring(0, 100)}...`;
    
    // 构建更有针对性的标题
    let enhancedTitle = `${spreadName} - ${t('spread.title')}`;
    if (spread.cardCount) {
      switch(i18n.language) {
        case 'en':
          enhancedTitle = `${spreadName} - ${spread.cardCount} Card Tarot Spread Guide | TarotQA`;
          break;
        case 'ja':
          enhancedTitle = `${spreadName} - ${spread.cardCount}枚タロットスプレッドガイド | TarotQA`;
          break;
        case 'zh-TW':
          enhancedTitle = `${spreadName} - ${spread.cardCount}張塔羅牌陣指南 | TarotQA`;
          break;
        default:
          enhancedTitle = `${spreadName} - ${spread.cardCount}张塔罗牌阵详解 | TarotQA`;
      }
    }
    
    return {
      title: enhancedTitle,
      description: enhancedDescription,
      keywords: keywords,
      ogType: 'article' as 'article' | 'website'
    };
  };
  
  const seoInfo = generateSEOInfo();
  
  return (
    <>
      <SEO 
        title={seoInfo.title}
        description={seoInfo.description}
        keywords={seoInfo.keywords}
        ogType={seoInfo.ogType}
        spreadName={spreadName}
        spreadDescription={spreadDescription}
      />
      
      <div className="min-h-screen flex flex-col relative">
        <LandingBackground />
        <div className="flex-grow relative z-10">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-16 pt-0 sm:pt-1 -mt-16">
            
            {/* 标题区域 - 简化标题样式，移除分割线 */}
            <div className="text-center mb-8 sm:mb-10 mt-8 sm:mt-10 md:mt-12">
              <h1 className={`main-title inline-block px-6 ${isDark ? 'bg-black/30' : 'bg-white/30'} backdrop-blur-sm ${getFontClass()} ${isDark ? 'text-white' : 'text-gray-900'}`}>
                {spreadName}
              </h1>
            </div>

            {/* 主信息卡片 - 增强视觉效果 */}
            <div className={`max-w-[95%] lg:max-w-5xl mx-auto ${
              isDark 
                ? 'bg-black/40 backdrop-blur-xl border-purple-500/20' 
                : 'bg-white/80 backdrop-blur-xl border-purple-300/40'
              } rounded-2xl shadow-2xl border overflow-hidden`}>
              
              <div className="relative p-8">
                {/* 装饰性渐变光晕 */}
                <div className={`absolute -top-32 -right-32 w-80 h-80 ${
                  isDark ? 'bg-purple-500/10' : 'bg-purple-300/20'
                } rounded-full blur-3xl`}></div>
                <div className={`absolute -bottom-32 -left-32 w-80 h-80 ${
                  isDark ? 'bg-pink-500/10' : 'bg-pink-300/20'
                } rounded-full blur-3xl`}></div>

                <div className="relative">
                  <div className="flex flex-col md:flex-row items-start gap-8">
                    {/* 移动端：文上图下，桌面端：图左文右 */}
                    <div className="order-2 md:order-1 w-full md:w-1/3 lg:w-1/4 flex justify-center mt-6 md:mt-0">
                      <div className="w-full max-w-[300px]">
                        <CdnLazyImage 
                          src={spread.image}
                          alt={`${spreadName} Layout`}
                          className="w-full h-auto object-contain"
                        />
                      </div>
                    </div>
                    
                    <div className="order-1 md:order-2 w-full md:w-2/3 lg:w-3/4">
                      
                      <div className="flex flex-wrap items-center gap-2 mb-5">
                        {spread.tags.map((tag) => {
                          const tagText = t(`spread.tags.${tag}`, { defaultValue: tag });
                          return (
                            <span
                              key={tag}
                              className={`px-3 py-1 ${
                                isDark ? 'bg-gray-700/70 text-gray-300' : 'bg-gray-200/70 text-gray-700'
                              } rounded-full text-xs font-medium`}
                            >
                              {tagText}
                            </span>
                          );
                        })}
                        {spread.category.map((cat) => {
                          // 使用更灵活的映射方式
                          const categoryKeyMap: Record<string, string> = {
                            '通用': 'general',
                            '爱情人际': 'love_relationships',
                            '运势预测': 'fortune_prediction',
                            '自我探索': 'self-exploration',
                            '事业财富': 'career_wealth'
                          };
                          
                          const categoryKey = categoryKeyMap[cat] || cat.toLowerCase().replace(/\s+/g, '_');
                          const categoryText = t(`spread.categories.${categoryKey}`, { defaultValue: cat });
                          
                          return (
                            <span
                              key={cat}
                              className={`px-3 py-1 ${
                                isDark ? 'bg-purple-900/60 text-purple-200' : 'bg-purple-200/70 text-purple-800'
                              } rounded-full text-xs font-medium`}
                            >
                              {categoryText}
                            </span>
                          );
                        })}
                      </div>
                      
                      <div className={`mb-7 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                        <p className="text-lg leading-relaxed">{spreadDescription}</p>
                      </div>
                      
                      <div className="mb-8">
                        <h2 className={`text-xl font-semibold mb-4 pb-2 border-b ${isDark ? 'border-gray-700 text-white' : 'border-gray-200 text-gray-800'}`}>
                          {t('spread.description')}
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                          {spreadPositions.map((position, index) => (
                            <div
                              key={index}
                              className={`position-card p-4 rounded-lg ${
                                isDark ? 'bg-gray-800/80 text-gray-200' : 'bg-gray-100/80 text-gray-700'
                              }`}
                            >
                              <div className="font-medium flex items-center">
                                <span className={`flex items-center justify-center w-7 h-7 rounded-full mr-2 text-center text-sm ${
                                  isDark ? 'bg-purple-900/60 text-purple-200' : 'bg-purple-200/70 text-purple-800'
                                }`}>
                                  {index + 1}
                                </span>
                                <span>{position}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div className="mt-8">
                        <h2 className={`text-xl font-semibold mb-4 pb-2 border-b ${isDark ? 'border-gray-700 text-white' : 'border-gray-200 text-gray-800'}`}>
                          {t('spread.suitable_for')}
                        </h2>
                        <div className={`${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                          <p className="leading-relaxed">{spreadDescription}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 添加额外的牌阵详细信息 */}
            {spreadDetail && (
              <div className="max-w-[95%] lg:max-w-5xl mx-auto mt-12 space-y-6">
                {/* 历史背景 */}
                <InfoCard 
                  title={getSectionTitle('history', '历史背景与起源')} 
                  accentColor={isDark ? 'from-blue-600/20 to-indigo-600/20' : 'from-blue-200 to-indigo-100'}
                  icon="📜"
                >
                  <p className={`${isDark ? 'text-gray-300' : 'text-gray-700'} leading-relaxed`}>
                    {spreadDetail.history}
                  </p>
                </InfoCard>

                {/* 使用建议和技巧 */}
                <InfoCard 
                  title={getSectionTitle('techniques', '使用建议与解读技巧')}
                  accentColor={isDark ? 'from-teal-600/20 to-emerald-600/20' : 'from-teal-100 to-emerald-100'}
                  icon="💡"
                >
                  <ul className={`list-disc pl-6 ${isDark ? 'text-gray-300' : 'text-gray-700'} space-y-3 marker:text-teal-500`}>
                    {spreadDetail.techniques.map((technique: string, index: number) => (
                      <li key={index} className="leading-relaxed">{technique}</li>
                    ))}
                  </ul>
                </InfoCard>

                {/* 优势与局限性 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <InfoCard 
                    title={getSectionTitle('advantages', '优势特点')}
                    accentColor={isDark ? 'from-green-600/20 to-teal-600/20' : 'from-green-100 to-teal-100'}
                    icon="✅"
                  >
                    <ul className={`list-disc pl-6 ${isDark ? 'text-gray-300' : 'text-gray-700'} space-y-3 marker:text-green-500`}>
                      {spreadDetail.advantages.map((advantage: string, index: number) => (
                        <li key={index} className="leading-relaxed">{advantage}</li>
                      ))}
                    </ul>
                  </InfoCard>
                  
                  <InfoCard 
                    title={getSectionTitle('limitations', '局限性')}
                    accentColor={isDark ? 'from-amber-600/20 to-orange-600/20' : 'from-amber-100 to-orange-100'}
                    icon="⚠️"
                  >
                    <ul className={`list-disc pl-6 ${isDark ? 'text-gray-300' : 'text-gray-700'} space-y-3 marker:text-amber-500`}>
                      {spreadDetail.limitations.map((limitation: string, index: number) => (
                        <li key={index} className="leading-relaxed">{limitation}</li>
                      ))}
                    </ul>
                  </InfoCard>
                </div>

                {/* 适用案例 */}
                <InfoCard 
                  title={getSectionTitle('examples', '适用场景案例')}
                  accentColor={isDark ? 'from-violet-600/20 to-purple-600/20' : 'from-violet-100 to-purple-100'}
                  icon="🔮"
                >
                  <div className="space-y-5">
                    {spreadDetail.examples.map((example: {title: string; description: string}, index: number) => (
                      <div 
                        key={index} 
                        className={`mb-5 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}
                      >
                        <h3 className={`font-medium text-lg mb-2 ${isDark ? 'text-purple-300' : 'text-purple-700'}`}>
                          {example.title}
                        </h3>
                        <p className="leading-relaxed">
                          {example.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </InfoCard>

                {/* 相关牌阵 */}
                {relatedSpreads.length > 0 && (
                  <InfoCard 
                    title={getSectionTitle('related', '相关牌阵推荐')}
                    accentColor={isDark ? 'from-pink-600/20 to-rose-600/20' : 'from-pink-100 to-rose-100'}
                    icon="🔀"
                  >
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                      {relatedSpreads.map((relatedSpread) => {
                        // 获取翻译后的牌阵名称
                        const relatedSpreadId = relatedSpread.id.replace(/-/g, '_');
                        const translatedName = Object.keys(t('spreads', { returnObjects: true })).includes(relatedSpreadId)
                          ? t(`spreads.${relatedSpreadId}.name`)
                          : relatedSpread.name;
                        
                        return (
                          <div 
                            key={relatedSpread.id}
                            onClick={() => navigate(`/spreads/${relatedSpread.id}`)}
                            className={`
                              cursor-pointer p-4 rounded-lg 
                              border ${isDark 
                                ? 'border-purple-500/20 bg-gray-800/30' 
                                : 'border-purple-300/30 bg-white/50'
                              } flex items-center gap-3
                            `}
                          >
                            <div className="w-14 h-14 shrink-0 relative">
                              {/* 图片装饰元素 */}
                              <div className={`absolute -inset-1 rounded-md blur-sm opacity-40 ${
                                isDark ? 'bg-purple-500/30' : 'bg-purple-200/50'
                              }`}></div>
                              <div className="relative rounded overflow-hidden">
                                <CdnLazyImage 
                                  src={relatedSpread.image}
                                  alt={translatedName}
                                  className="w-full h-full object-contain"
                                />
                              </div>
                            </div>
                            <div>
                              <HoverableText as="h3" className="font-medium">
                                {translatedName}
                              </HoverableText>
                              <div className="flex items-center mt-1">
                                <span className={`inline-block w-2 h-2 rounded-full mr-1.5 ${
                                  isDark ? 'bg-purple-400' : 'bg-purple-500'
                                }`}></span>
                                <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                                  {(() => {
                                    switch(i18n.language) {
                                      case 'en':
                                        return `${relatedSpread.cardCount} Cards`;
                                      case 'ja':
                                        return `${relatedSpread.cardCount}枚`;
                                      case 'zh-TW':
                                        return `${relatedSpread.cardCount}張牌`;
                                      default:
                                        return `${relatedSpread.cardCount}张牌`;
                                    }
                                  })()}
                                </p>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </InfoCard>
                )}

                {/* 更多塔罗资源和链接 */}
                <InfoCard 
                  title={getSectionTitle('more_resources', '更多塔罗资源')}
                  accentColor={isDark ? 'from-cyan-600/20 to-blue-600/20' : 'from-cyan-100 to-blue-100'}
                  icon="📚"
                >
                  <div className={`${isDark ? 'text-gray-300' : 'text-gray-700'} space-y-5`}>
                    <p className="leading-relaxed">
                      {(() => {
                        switch(i18n.language) {
                          case 'en':
                            return 'Want to learn more about tarot cards and techniques? Check out our related articles:';
                          case 'ja':
                            return 'タロットカードやテクニックについてもっと学びたいですか？関連記事をご覧ください：';
                          case 'zh-TW':
                            return '想要了解更多關於塔羅牌的知識和技巧？查看我們的相關文章：';
                          default:
                            return '想要了解更多关于塔罗牌的知识和技巧？查看我们的相关文章：';
                        }
                      })()}
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                      <div className={`
                        p-4 rounded-lg border relative overflow-hidden
                        ${isDark 
                          ? 'border-gray-700 bg-gray-800/50' 
                          : 'border-gray-200 bg-white/70'
                        }
                      `}>
                        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-cyan-400"></div>
                        <h3 className="font-medium mb-2">
                          <LanguageLink to="/blog/5-powerful-tarot-spreads">
                            <HoverableText>
                              {(() => {
                                switch(i18n.language) {
                                  case 'en':
                                    return '5 Essential Tarot Spreads for Beginners';
                                  case 'ja':
                                    return '初心者に必須の5つのタロットスプレッド';
                                  case 'zh-TW':
                                    return '初學者必學的5種塔羅牌陣';
                                  default:
                                    return '初学者必学的5种塔罗牌阵';
                                }
                              })()}
                            </HoverableText>
                          </LanguageLink>
                        </h3>
                        <p className="text-sm opacity-80">
                          {(() => {
                            switch(i18n.language) {
                              case 'en':
                                return 'Learn about the most suitable tarot spreads for beginners, progressing from simple to complex.';
                              case 'ja':
                                return '初心者に最適なタロットスプレッドについて学び、シンプルなものから複雑なものへと段階的に進みます。';
                              case 'zh-TW':
                                return '了解最適合初學者的塔羅牌陣，從簡單到複雜循序漸進。';
                              default:
                                return '了解最适合初学者的塔罗牌阵，从简单到复杂循序渐进。';
                            }
                          })()}
                        </p>
                    </div>

                      <div className={`
                        p-4 rounded-lg border relative overflow-hidden
                        ${isDark 
                          ? 'border-gray-700 bg-gray-800/50' 
                          : 'border-gray-200 bg-white/70'
                        }
                      `}>
                        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-400 to-indigo-400"></div>
                        <h3 className="font-medium mb-2">
                          <LanguageLink to="/blog/complete-tarot-guide">
                            <HoverableText>
                              {(() => {
                                switch(i18n.language) {
                                  case 'en':
                                    return 'Complete Tarot Guide: From Basics to Mastery';
                                  case 'ja':
                                    return 'タロット完全ガイド：基礎から習熟まで';
                                  case 'zh-TW':
                                    return '塔羅牌完全指南：從基礎到精通';
                                  default:
                                    return '塔罗牌完全指南：从基础到精通';
                                }
                              })()}
                            </HoverableText>
                          </LanguageLink>
                        </h3>
                        <p className="text-sm opacity-80">
                          {(() => {
                            switch(i18n.language) {
                              case 'en':
                                return 'Comprehensive tarot interpretation and application guide, suitable for learners of all levels.';
                              case 'ja':
                                return 'あらゆるレベルの学習者に適した、包括的なタロット解釈と応用ガイド。';
                              case 'zh-TW':
                                return '全面的塔羅牌解讀和應用指南，適合各個水平的學習者。';
                              default:
                                return '全面的塔罗牌解读和应用指南，适合各个水平的学习者。';
                            }
                          })()}
                        </p>
                      </div>
                      
                      <div className={`
                        p-4 rounded-lg border relative overflow-hidden
                        ${isDark 
                          ? 'border-gray-700 bg-gray-800/50' 
                          : 'border-gray-200 bg-white/70'
                        }
                      `}>
                        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-pink-400 to-rose-400"></div>
                        <h3 className="font-medium mb-2">
                          <LanguageLink to="/blog/complete-love-tarot-guide">
                            <HoverableText>
                              {(() => {
                                switch(i18n.language) {
                                  case 'en':
                                    return 'Complete Love Tarot Guide: Secrets to Finding True Love';
                                  case 'ja':
                                    return '恋愛タロット完全ガイド：真実の愛を見つける秘訣';
                                  case 'zh-TW':
                                    return '愛情塔羅全指南：找到真愛的秘密';
                                  default:
                                    return '爱情塔罗全指南：找到真爱的秘密';
                                }
                              })()}
                            </HoverableText>
                          </LanguageLink>
                        </h3>
                        <p className="text-sm opacity-80">
                          {(() => {
                            switch(i18n.language) {
                              case 'en':
                                return 'Tarot guide focused on relationship interpretation, helping you gain insights into your love life.';
                              case 'ja':
                                return '恋愛関係の解釈に焦点を当てたタロットガイド。あなたの恋愛生活への洞察を得るのに役立ちます。';
                              case 'zh-TW':
                                return '專注於愛情關係解讀的塔羅指南，幫助你在感情中獲得洞見。';
                              default:
                                return '专注于爱情关系解读的塔罗指南，帮助你在感情中获得洞见。';
                            }
                          })()}
                        </p>
                      </div>
                    </div>
                  </div>
                </InfoCard>
              </div>
            )}
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
};

export default SpreadDetail; 