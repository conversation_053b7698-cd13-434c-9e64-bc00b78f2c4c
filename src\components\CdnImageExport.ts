/**
 * CDN图片组件和工具导出文件
 */
import CdnImage from './CdnImage';
import CdnLazyImage from './CdnLazyImage';
import useCdnImage, { useCdnImages } from '../hooks/useCdnImage';
import { getImageUrl, toCdnUrl, getResourceUrl } from '../utils/cdnImageUrl';

// 导出组件
export { 
  CdnImage, 
  CdnLazyImage,
};

// 导出自定义Hook
export { 
  useCdnImage, 
  useCdnImages 
};

// 导出工具函数
export { 
  getImageUrl, 
  getResourceUrl,
  toCdnUrl 
};

// 默认导出基础CDN图片组件
export default CdnImage; 