import React from 'react';
import { useTranslation } from 'react-i18next';

interface YesNoQuestionFormProps {
  userQuestion: string;
  setUserQuestion: (question: string) => void;
  errorMessage: string;
  isSubmitting: boolean;
  onSubmit: (e: React.FormEvent) => void;
  buttonType?: 'single' | 'three';
}

const YesNoQuestionForm: React.FC<YesNoQuestionFormProps> = ({
  userQuestion,
  setUserQuestion,
  errorMessage,
  isSubmitting,
  onSubmit,
}) => {
  const { t } = useTranslation();

  const getButtonText = () => {
    return t('yes_no_tarot.question_input.submit_button');
  };


  return (
    <form onSubmit={onSubmit}>
      <div className="mb-4">
        <div className="relative">
          <textarea
            id="question-input"
            value={userQuestion}
            onChange={(e) => {
              const newValue = e.target.value;
              if (newValue.length <= 200) {
                setUserQuestion(newValue);
              }
            }}
            maxLength={200}
            placeholder={t('yes_no_tarot.question_input.placeholder')}
            className="body-text block w-full px-4 sm:px-5 py-3 sm:py-4 rounded-xl dark:bg-black/20 bg-gray-50/80 
                     dark:placeholder-gray-400 placeholder-gray-500 backdrop-blur-sm
                     outline-none resize-none
                     transition-all duration-300 ease-in-out
                     border dark:border-purple-500/20 border-purple-500/30
                     focus:border-purple-500 focus:ring focus:ring-purple-500/30
                     [&:not(:focus)]:hover:border-purple-500/30"
            disabled={isSubmitting}
            rows={3}
          />
          <div className="absolute bottom-[0.5rem] right-3 text-sm dark:text-gray-400/80 text-gray-500/80 pointer-events-none select-none font-medium tracking-wide tabular-nums">
            <span className="font-numeric">{userQuestion.length}</span>
            <span className="opacity-60">/</span>
            <span className="font-numeric">200</span>
          </div>
        </div>
        
        {errorMessage && (
          <div className="mt-3 p-3 rounded-lg bg-red-500/10 border border-red-500/20">
            <p className="body-text text-red-400">{errorMessage}</p>
          </div>
        )}
      </div>
      
      <button
        type="submit"
        className={`w-full px-4 py-2 bg-purple-600 hover:bg-purple-500 active:bg-purple-700 text-white font-medium rounded-xl transition-colors ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
        disabled={isSubmitting || !userQuestion.trim()}
      >
        {isSubmitting ? (
          <div className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {t('common.loading')}
          </div>
        ) : getButtonText()}
      </button>
    </form>
  );
};

export default YesNoQuestionForm; 