import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '../contexts/UserContext';
import { 
  getInvitationCodes, 
  generateInvitationCode, 
  getSalesStats,
  checkIsSalesPerson,
  InvitationCode,
  StatsInfo
} from '../services/invitationService';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import { toast } from 'react-hot-toast';
import { PlusCircle, Copy, Check, DownloadCloud, ChevronLeft, ChevronRight } from 'lucide-react';
import { Navigate, useNavigate } from 'react-router-dom';

const InvitationCodeManagement: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useUser();
  const navigate = useNavigate();
  const [invitationCodes, setInvitationCodes] = useState<InvitationCode[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [stats, setStats] = useState<StatsInfo>({ totalCodes: 0, usedCodes: 0, unusedCodes: 0, totalPayment: 0 });
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [hasSalesPermission, setHasSalesPermission] = useState(false);
  const [isCheckingPermission, setIsCheckingPermission] = useState(true);
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // 检查用户是否有销售人员权限
  useEffect(() => {
    if (!user) {
      setIsCheckingPermission(false);
      return;
    }

    const checkSalesPermission = async () => {
      try {
        // 检查用户ID是否在sales_persons表中
        const isSalesPerson = await checkIsSalesPerson(user.id);
        setHasSalesPermission(isSalesPerson);
      } catch (error) {
        toast.error('权限不足');
        setHasSalesPermission(false);
        // 3秒后重定向到首页
        setTimeout(() => {
          navigate('/');
        }, 3000);
      } finally {
        setIsCheckingPermission(false);
      }
    };

    checkSalesPermission();
  }, [user, navigate]);

  useEffect(() => {
    if (!user || !hasSalesPermission) return;

    const fetchInvitationCodes = async () => {
      setIsLoading(true);
      try {
        // 使用用户ID查询邀请码
        const codes = await getInvitationCodes(user.id);
        setInvitationCodes(codes);

        // 获取统计数据
        const statsData = await getSalesStats(user.id);
        setStats(statsData);
      } catch (error) {
        toast.error(t('invitation.fetch_failed'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvitationCodes();
  }, [user, t, hasSalesPermission]);

  const handleGenerateCode = async () => {
    if (!user) return;

    setIsGenerating(true);
    try {
      // 生成新邀请码
      const newCode = await generateInvitationCode(user.id);
      
      // 确保创建时间是有效的日期格式
      const currentDate = new Date();
      const formattedCode = {
        ...newCode,
        created_at: newCode.created_at || currentDate.toISOString()
      };
      
      setInvitationCodes(prev => [formattedCode, ...prev]);
      
      // 更新统计数据
      setStats(prev => ({
        ...prev,
        totalCodes: prev.totalCodes + 1,
        unusedCodes: prev.unusedCodes + 1
      }));

      // 当添加新邀请码时，确保我们在第一页
      setCurrentPage(1);

      toast.success(t('invitation.code_generated'));
    } catch (error) {
      toast.error(t('invitation.generate_failed'));
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code)
      .then(() => {
        setCopiedCode(code);
        toast.success(t('invitation.code_copied'));
        setTimeout(() => setCopiedCode(null), 2000);
      })
      .catch(() => {
        toast.error(t('invitation.copy_failed'));
      });
  };

  const exportToCSV = () => {
    // 创建CSV内容
    const csvContent = [
      // CSV 头部
      ['编号', '邀请码', '创建时间', '状态', '使用者', '使用时间', '支付金额'].join(','),
      // CSV 数据行
      ...invitationCodes.map((code, index) => [
        index + 1,
        code.code,
        formatDate(code.created_at),
        code.is_used ? '已使用' : '未使用',
        code.used_by_username || '-',
        formatDate(code.used_at),
        code.is_used && code.payment_amount ? `¥${Number(code.payment_amount).toFixed(2)}` : '-'
      ].join(','))
    ].join('\n');

    // 添加UTF-8 BOM标记，解决中文乱码问题
    const BOM = '\uFEFF';
    const csvContentWithBOM = BOM + csvContent;

    // 创建Blob对象，指定正确的编码
    const blob = new Blob([csvContentWithBOM], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    // 创建下载链接
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `邀请码列表_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 添加一个格式化日期的辅助函数
  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '-';
      }
      return date.toLocaleString();
    } catch (error) {
      return '-';
    }
  };

  // 计算当前页应显示的邀请码
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = invitationCodes.slice(indexOfFirstItem, indexOfLastItem);
  
  // 计算总页数
  const totalPages = Math.ceil(invitationCodes.length / itemsPerPage);

  // 页码变更处理
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // 每页显示数量变更处理
  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1); // 重置为第一页
  };

  // 权限检查
  if (isCheckingPermission) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-black">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!hasSalesPermission) {
    return <Navigate to="/" replace />;
  }

  return (
    <main className="relative min-h-screen">
      <LandingBackground />
      <div className="container mx-auto px-4 py-8 relative z-10">
        <h1 className="text-3xl font-bold text-center text-white mb-6">
          {t('invitation.management_title')}
        </h1>

        <div className="bg-gray-900/80 backdrop-blur-sm border border-purple-500/30 rounded-xl p-6 mb-8 shadow-lg">
          <h2 className="text-xl font-semibold text-white mb-4">{t('invitation.stats_title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-purple-900/50 rounded-lg p-4 border border-purple-500/20">
              <p className="text-purple-300 text-sm">{t('invitation.total_codes')}</p>
              <p className="text-3xl font-bold text-white font-['Inter'] tabular-nums">{stats.totalCodes}</p>
            </div>
            <div className="bg-green-900/50 rounded-lg p-4 border border-green-500/20">
              <p className="text-green-300 text-sm">{t('invitation.used_codes')}</p>
              <p className="text-3xl font-bold text-white font-['Inter'] tabular-nums">{stats.usedCodes}</p>
            </div>
            <div className="bg-blue-900/50 rounded-lg p-4 border border-blue-500/20">
              <p className="text-blue-300 text-sm">{t('invitation.unused_codes')}</p>
              <p className="text-3xl font-bold text-white font-['Inter'] tabular-nums">{stats.unusedCodes}</p>
            </div>
            <div className="bg-amber-900/50 rounded-lg p-4 border border-amber-500/20">
              <p className="text-amber-300 text-sm">{t('invitation.total_payment')}</p>
              <p className="text-3xl font-bold text-white font-['Inter'] tabular-nums">
                {stats.totalPayment 
                  ? `¥${Number(stats.totalPayment).toFixed(2)}` 
                  : '¥0.00'}
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-white">{t('invitation.codes_list')}</h2>
          <div className="flex space-x-2">
            <button
              onClick={exportToCSV}
              className="py-2 px-4 bg-gray-700 hover:bg-gray-600 text-white rounded-lg flex items-center space-x-2 transition-colors"
            >
              <DownloadCloud size={18} />
              <span>{t('invitation.export_csv')}</span>
            </button>
            <button
              onClick={handleGenerateCode}
              disabled={isGenerating}
              className="py-2 px-4 bg-purple-700 hover:bg-purple-600 text-white rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <PlusCircle size={18} />
              <span>{isGenerating ? t('invitation.generating') : t('invitation.generate_code')}</span>
            </button>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center my-12">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        ) : (
          <div className="bg-gray-900/80 backdrop-blur-sm border border-purple-500/30 rounded-xl overflow-hidden shadow-lg">
            <div className="overflow-x-auto">
              <table className="w-full table-auto text-base">
                <thead>
                  <tr className="bg-gray-800/90 text-left">
                    <th className="py-4 px-4 text-purple-300 w-[8%] text-center">#</th>
                    <th className="py-4 px-4 text-purple-300 w-[14%]">
                      {t('invitation.code')}
                    </th>
                    <th className="py-4 px-4 text-purple-300 w-[14%]">
                      {t('invitation.created_at')}
                    </th>
                    <th className="py-4 px-4 text-purple-300 w-[10%]">
                      {t('invitation.status')}
                    </th>
                    <th className="py-4 px-4 text-purple-300 w-[14%]">
                      {t('invitation.used_by')}
                    </th>
                    <th className="py-4 px-4 text-purple-300 w-[14%]">
                      {t('invitation.used_at')}
                    </th>
                    <th className="py-4 px-4 text-purple-300 w-[14%]">
                      {t('invitation.payment_amount')}
                    </th>
                    <th className="py-4 px-4 text-purple-300 w-[12%] text-center">
                      {t('common.actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {currentItems.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="py-8 px-4 text-center text-gray-400 text-base">
                        {t('invitation.no_codes')}
                      </td>
                    </tr>
                  ) : (
                    currentItems.map((code, index) => (
                      <tr key={code.id} className="hover:bg-gray-800/50 transition-colors">
                        <td className="py-3 px-4 text-gray-300 text-base text-center">
                          {indexOfFirstItem + index + 1}
                        </td>
                        <td className="py-3 px-4 text-white font-mono text-base">
                          {code.code}
                        </td>
                        <td className="py-3 px-4 text-gray-300 font-['Inter'] tabular-nums text-base">
                          {formatDate(code.created_at)}
                        </td>
                        <td className="py-3 px-4">
                          <span
                            className={`inline-block px-2 py-1 rounded-full text-xs font-medium
                              ${code.is_used ? 'bg-green-900/50 text-green-300' : 'bg-blue-900/50 text-blue-300'}`}
                          >
                            {code.is_used ? t('invitation.used') : t('invitation.unused')}
                          </span>
                        </td>
                        <td className="py-3 px-4 text-gray-300 text-base truncate">
                          {code.used_by_username || '-'}
                        </td>
                        <td className="py-3 px-4 text-gray-300 font-['Inter'] tabular-nums text-base">
                          {formatDate(code.used_at)}
                        </td>
                        <td className="py-3 px-4 text-gray-300 font-['Inter'] tabular-nums text-base">
                          {code.is_used && code.payment_amount ? 
                            <span className="text-green-400">¥{Number(code.payment_amount).toFixed(2)}</span> : 
                            <span className="text-gray-500">-</span>
                          }
                        </td>
                        <td className="py-3 px-4 text-center">
                          <button
                            onClick={() => handleCopyCode(code.code)}
                            disabled={code.is_used}
                            className={`p-2 rounded-lg text-white transition-colors
                              ${code.is_used 
                                ? 'bg-gray-700 opacity-50 cursor-not-allowed' 
                                : 'bg-purple-700 hover:bg-purple-600'}`}
                            title={t('invitation.copy_code')}
                          >
                            {copiedCode === code.code ? <Check size={16} /> : <Copy size={16} />}
                          </button>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
            
            {/* 分页控件 */}
            {invitationCodes.length > 0 && (
              <div className="flex items-center justify-between p-4 border-t border-gray-700 text-base">
                <div className="flex items-center">
                  <span className="text-gray-400 mr-2 text-base">每页显示:</span>
                  <select
                    value={itemsPerPage}
                    onChange={handleItemsPerPageChange}
                    className="bg-gray-800 text-white border border-gray-700 rounded-md px-2 py-1 text-base"
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                  </select>
                </div>
                
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="p-2 rounded-md text-white transition-colors
                              disabled:opacity-50 disabled:cursor-not-allowed
                              bg-gray-800 hover:bg-gray-700"
                  >
                    <ChevronLeft size={16} />
                  </button>
                  
                  <div className="flex items-center">
                    {/* 页码显示 */}
                    <div className="px-4 py-1 text-gray-300 font-['Inter'] tabular-nums text-base">
                      <span className="font-['Inter']">{currentPage}</span> / <span className="font-['Inter']">{totalPages}</span>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages || totalPages === 0}
                    className="p-2 rounded-md text-white transition-colors
                              disabled:opacity-50 disabled:cursor-not-allowed
                              bg-gray-800 hover:bg-gray-700"
                  >
                    <ChevronRight size={16} />
                  </button>
                </div>
                
                <div className="text-gray-400 text-sm font-['Inter']">
                  共 <span className="tabular-nums font-['Inter']">{invitationCodes.length}</span> 条记录
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      <Footer />
    </main>
  );
};

export default InvitationCodeManagement; 