import React from 'react';
import { useTranslation } from 'react-i18next';

interface DateInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
}

export const DateInput: React.FC<DateInputProps> = ({ label, value, onChange }) => {
  const { t } = useTranslation();
  const [year, month, day] = value ? value.split('-') : ['', '', ''];

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newYear = e.target.value;
    // 只有当年、月、日都有值时才生成完整的日期字符串
    if (newYear && month && day) {
      onChange(`${newYear}-${month}-${day}`);
    } else {
      // 如果任何一个字段为空，则清空整个日期
      onChange('');
    }
  };

  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMonth = e.target.value;
    // 只有当年、月、日都有值时才生成完整的日期字符串
    if (year && newMonth && day) {
      onChange(`${year}-${newMonth}-${day}`);
    } else {
      // 如果任何一个字段为空，则清空整个日期
      onChange('');
    }
  };

  const handleDayChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newDay = e.target.value;
    // 只有当年、月、日都有值时才生成完整的日期字符串
    if (year && month && newDay) {
      onChange(`${year}-${month}-${newDay}`);
    } else {
      // 如果任何一个字段为空，则清空整个日期
      onChange('');
    }
  };

  const years = Array.from({ length: 100 }, (_, i) => {
    const year = new Date().getFullYear() - i;
    return { value: year.toString(), label: year.toString() };
  });

  const months = Array.from({ length: 12 }, (_, i) => {
    const month = (i + 1).toString().padStart(2, '0');
    return { value: month, label: month };
  });

  const getDaysInMonth = (year: string, month: string) => {
    if (!year || !month) return 31;
    const date = new Date(parseInt(year), parseInt(month), 0);
    return date.getDate();
  };

  const days = Array.from({ length: getDaysInMonth(year, month) }, (_, i) => {
    const day = (i + 1).toString().padStart(2, '0');
    return { value: day, label: day };
  });

  const renderSelect = (
    selectLabel: string,
    value: string,
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void,
    options: { value: string; label: string }[]
  ) => {
    return (
      <div className="relative">
        <select
          value={value}
          onChange={onChange}
          className="w-full p-3 bg-black/20 text-white border border-purple-500/20 rounded-xl text-base 
            focus:outline-none focus:border-purple-500/40 focus:ring-0 focus:outline-0
            hover:border-purple-500/30 transition-all duration-300 appearance-none"
        >
          <option value="" disabled>
            {selectLabel}
          </option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <svg
            className="h-5 w-5 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-2">
      <label className="block text-white text-base">{label}</label>
      <div className="grid grid-cols-3 gap-4">
        {renderSelect(t('date.year'), year, handleYearChange, years)}
        {renderSelect(t('date.month'), month, handleMonthChange, months)}
        {renderSelect(t('date.day'), day, handleDayChange, days)}
      </div>
    </div>
  );
};