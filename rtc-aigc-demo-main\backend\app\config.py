"""
应用配置文件
"""

# Flask应用配置
class Config:
    SECRET_KEY = 'your-secret-key'
    DEBUG = True
    
# 火山引擎语音对话服务配置
VOLCANO_CONFIG = {
    "base_url": "wss://openspeech.bytedance.com/api/v3/realtime/dialogue",
    "headers": {
        "X-Api-App-ID": "7120470259",
        "X-Api-Access-Key": "ZjLnjw_gedm_HYuGRK06SP3RVKhOhiO-",
        "X-Api-Resource-Id": "volc.speech.dialog",  # 固定值
        "X-Api-App-Key": "PlgvMymc7f3tQnJ6",  # 固定值
    }
}

# 语音会话配置
TTS_CONFIG = {
    "tts": {
        "audio_config": {
            "channel": 1,
            "format": "pcm",
            "sample_rate": 24000
        },
    },
    "dialog": {
        "bot_name": "语音助手",
        "system_role": "你是一个活泼灵动的AI助手，性格开朗，热爱生活。",
        "speaking_style": "你的说话风格简洁明了，语速适中，语调自然。",
        "extra": {
            "strict_audit": False
        }
    }
}

# 音频输入配置
AUDIO_INPUT_CONFIG = {
    "chunk": 3200,
    "format": "pcm",
    "channels": 1,
    "sample_rate": 16000,
}

# 音频输出配置
AUDIO_OUTPUT_CONFIG = {
    "chunk": 3200,
    "format": "pcm",
    "channels": 1,
    "sample_rate": 24000,
} 