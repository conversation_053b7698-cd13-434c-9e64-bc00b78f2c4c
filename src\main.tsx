import ReactDOM from 'react-dom/client'
import App from './App'
import './styles/globals.css'  
import './styles/fonts.css';
import './index.css'
import { initI18n } from './i18n'  // 导入i18n配置

// 禁用开发环境提示
const devToolsMessage = /Download the React DevTools/i;
const shouldUpdateMessage = /should update/i;
const layoutDebugMessage = /\[Layout Debug\]/i;
const originalConsoleLog = console.log;
console.log = (...args) => {
  if (typeof args[0] === 'string' && (
    devToolsMessage.test(args[0]) || 
    shouldUpdateMessage.test(args[0]) ||
    layoutDebugMessage.test(args[0])
  )) return;
  originalConsoleLog(...args);
};

// 禁用 COOP 错误日志
const originalConsoleError = console.error;
console.error = (...args) => {
  if (
    typeof args[0] === 'string' && 
    (args[0].includes('Cross-Origin-Opener-Policy') || 
     args[0].includes('window.postMessage') ||
     // 过滤掉邮箱验证相关错误
     (args[0].includes('[Axios] Response error from /api/auth/resend-verification') && 
      (args[1]?.message === '该邮箱未注册' || args[1]?.message === '该邮箱已被注册')))
  ) {
    return;
  }
  originalConsoleError.apply(console, args);
};

// 全局错误处理
window.onerror = (message, source, lineno, colno, error) => {
  // 记录错误信息
  console.error('Global error:', { message, source, lineno, colno, error });
};

// 处理未捕获的Promise错误
window.onunhandledrejection = (event) => {
  // 检查是否是 axios 错误响应
  if (event.reason?.response?.status === 400) {
    // 不记录 400 错误
    event.preventDefault();
    return;
  }
  
  // 记录其他未处理的Promise错误
  console.error('Unhandled promise rejection:', event.reason);
};

// 设置移动端软键盘事件监听
if ('ontouchstart' in window) {
  const handleFocusIn = (e: FocusEvent) => {
    const target = e.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT') {
      document.body.classList.add('keyboard-open');
    }
  };

  const handleFocusOut = (e: FocusEvent) => {
    const target = e.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT') {
      setTimeout(() => {
        document.body.classList.remove('keyboard-open');
      }, 150);
    }
  };

  document.addEventListener('focusin', handleFocusIn);
  document.addEventListener('focusout', handleFocusOut);
}

// 等待i18n初始化完成后再渲染应用
const init = async () => {
  await initI18n();
  
  ReactDOM.createRoot(document.getElementById('root')!).render(
    <App />
  );
};

init().catch(console.error);