import React, { useState } from 'react';
import { verifyEmail, resendVerification } from '../services/userService';

interface EmailVerificationProps {
  email: string;
  userId?: string;
  onVerificationSuccess: () => void;
  onUserIdUpdate?: (newUserId: string) => void;
}

const EmailVerification: React.FC<EmailVerificationProps> = ({ 
  email, 
  userId, 
  onVerificationSuccess,
  onUserIdUpdate 
}) => {
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [isResending, setIsResending] = useState(false);
  const [countdown, setCountdown] = useState(0);

  const handleVerification = async () => {
    if (!verificationCode) {
      setError('请输入验证码');
      return;
    }

    const result = await verifyEmail(email, verificationCode);
    if (result.success) {
      setMessage(result.message);
      onVerificationSuccess();
    } else {
      setError(result.message);
    }
  };

  const handleResendCode = async () => {
    if (countdown > 0) return;
    
    setIsResending(true);
    setError('');
    try {
      const response = await resendVerification(email, userId);
      setMessage(response.message);
      
      // 如果服务器返回了新的userId，通知父组件
      if (response.userId && onUserIdUpdate) {
        onUserIdUpdate(response.userId);
      }
      
      // 开始60秒倒计时
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      setError(error instanceof Error ? error.message : '发送失败，请稍后重试');
    }
    setIsResending(false);
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-black/50 backdrop-blur-lg rounded-lg border border-purple-500/20">
      <h2 className="text-xl font-semibold text-purple-400 mb-4">邮箱验证</h2>
      <p className="text-gray-400 mb-6">
        验证码已发送至您的邮箱：{email}
      </p>
      
      <div className="space-y-4">
        <div>
          <input
            type="text"
            maxLength={6}
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
            placeholder="请输入6位验证码"
            className="w-full px-4 py-2 bg-black/30 border border-purple-500/20 rounded-lg 
                     text-white placeholder-gray-500 focus:outline-none focus:border-purple-500"
          />
        </div>

        {error && (
          <p className="text-red-500 text-sm">{error}</p>
        )}
        
        {message && (
          <p className="text-green-500 text-sm">{message}</p>
        )}

        <div className="flex space-x-4">
          <button
            onClick={handleVerification}
            className="flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 
                     text-white rounded-lg transition-colors duration-200"
          >
            验证
          </button>
          
          <button
            onClick={handleResendCode}
            disabled={countdown > 0 || isResending}
            className={`px-4 py-2 border border-purple-500/20 rounded-lg transition-colors duration-200
                      ${countdown > 0 || isResending
                        ? 'text-gray-500 cursor-not-allowed'
                        : 'text-purple-400 hover:bg-purple-500/20'
                      }`}
          >
            {countdown > 0 ? `重新发送(${countdown}s)` : '重新发送'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmailVerification;
