{"name": "tarot-server", "version": "1.0.0", "description": "Tarot reading server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "init-db": "node scripts/initDatabase.js", "migrate": "node apply_migrations.js", "migrate:feedback": "node scripts/apply_reading_feedback_migration.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@paypal/checkout-server-sdk": "^1.0.3", "@types/uuid": "^10.0.0", "alipay-sdk": "^3.6.1", "axios": "^1.4.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "form-data": "^4.0.0", "geoip-lite": "^1.4.10", "google-auth-library": "^9.15.0", "jsonwebtoken": "^9.0.0", "jwt-decode": "^4.0.0", "lodash.isboolean": "^3.0.3", "mongoose": "^8.13.2", "multer": "^2.0.1", "mysql2": "^3.3.1", "node-cron": "^3.0.3", "node-fetch": "2", "node-schedule": "^2.1.1", "nodemailer": "^6.9.3", "openai": "^4.77.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "slugify": "^1.6.6", "uuid": "^9.0.0", "ws": "^8.13.0"}, "devDependencies": {"nodemon": "^3.0.2"}}