import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';

interface HoroscopeNavigationProps {
  currentType: string; // 'daily', 'weekly', 'monthly', 'yearly', 'love', 'yesterday', 'tomorrow', 'lastweek', 'nextweek', 'lastmonth', 'nextmonth', 'lastyear', 'nextyear', 'lastlove', 'nextlove'
  currentDate?: Date;
  signId?: string;
  onTypeChange?: (type: string) => void;
  onDateChange?: (direction: 'prev' | 'current' | 'next') => void;
}

const HoroscopeNavigation: React.FC<HoroscopeNavigationProps> = ({
  currentType,
  currentDate = new Date(),
  signId,
  onTypeChange,
  onDateChange
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { navigate } = useLanguageNavigate();
  const isDark = theme === 'dark';
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // 关闭下拉菜单的点击外部事件处理
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 获取当前类型的显示名称
  const getTypeDisplayName = (type: string) => {
    switch (type) {
      case 'daily':
      case 'yesterday':
      case 'tomorrow':
        return t('horoscope.daily_title', '每日运势');
      case 'weekly':
      case 'lastweek':
      case 'nextweek':
        return t('horoscope.weekly_title', '每周运势');
      case 'monthly':
      case 'lastmonth':
      case 'nextmonth':
        return t('horoscope.monthly_title', '每月运势');
      case 'yearly':
      case 'lastyear':
      case 'nextyear':
        return t('horoscope.yearly_title', '年度运势');
      case 'love':
      case 'lastlove':
      case 'nextlove':
        return t('horoscope.love_title', '爱情运势');
      default:
        return t('horoscope.title_short', '星座运势');
    }
  };

  // 处理类型变更
  const handleTypeChange = (type: string) => {
    setDropdownOpen(false);
    if (onTypeChange) {
      onTypeChange(type);
    } else {
      // 默认导航行为
      const baseUrl = signId 
        ? `/horoscope/${signId}-${type}-horoscope` 
        : `/horoscope/${type}-horoscope`;
      navigate(baseUrl);
    }
  };

  // 处理日期变更
  const handleDateChange = (direction: 'prev' | 'current' | 'next') => {
    if (onDateChange) {
      onDateChange(direction);
    } else {
      // 默认导航行为
      if (signId) {
        // 处理daily、yesterday和tomorrow页面的导航
        if (currentType === 'daily' || currentType === 'yesterday' || currentType === 'tomorrow') {
          if (direction === 'prev') {
            navigate(`/horoscope/${signId}-yesterday-horoscope`);
          } else if (direction === 'next') {
            navigate(`/horoscope/${signId}-tomorrow-horoscope`);
          } else {
            navigate(`/horoscope/${signId}-daily-horoscope`);
          }
        } 
        // 处理weekly、lastweek和nextweek页面的导航
        else if (currentType === 'weekly' || currentType === 'lastweek' || currentType === 'nextweek') {
          if (direction === 'prev') {
            navigate(`/horoscope/${signId}-lastweek-horoscope`);
          } else if (direction === 'next') {
            navigate(`/horoscope/${signId}-nextweek-horoscope`);
          } else {
            navigate(`/horoscope/${signId}-weekly-horoscope`);
          }
        }
        // 处理monthly、lastmonth和nextmonth页面的导航
        else if (currentType === 'monthly' || currentType === 'lastmonth' || currentType === 'nextmonth') {
          if (direction === 'prev') {
            navigate(`/horoscope/${signId}-lastmonth-horoscope`);
          } else if (direction === 'next') {
            navigate(`/horoscope/${signId}-nextmonth-horoscope`);
          } else {
            navigate(`/horoscope/${signId}-monthly-horoscope`);
          }
        }
        // 处理yearly、lastyear和nextyear页面的导航
        else if (currentType === 'yearly' || currentType === 'lastyear' || currentType === 'nextyear') {
          if (direction === 'prev') {
            navigate(`/horoscope/${signId}-lastyear-horoscope`);
          } else if (direction === 'next') {
            navigate(`/horoscope/${signId}-nextyear-horoscope`);
          } else {
            navigate(`/horoscope/${signId}-yearly-horoscope`);
          }
        }
        // 处理love、lastlove和nextlove页面的导航
        else if (currentType === 'love' || currentType === 'lastlove' || currentType === 'nextlove') {
          if (direction === 'prev') {
            navigate(`/horoscope/${signId}-lastlove-horoscope`);
          } else if (direction === 'next') {
            navigate(`/horoscope/${signId}-nextlove-horoscope`);
          } else {
            navigate(`/horoscope/${signId}-love-horoscope`);
          }
        }
        // 其他类型的默认行为可以在这里添加
      }
    }
  };

  // 获取一周的开始日期（周一）
  const getStartOfWeek = (date: Date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一为一周的第一天
    return new Date(d.setDate(diff));
  };

  // 获取一周的结束日期（周日）
  const getEndOfWeek = (date: Date) => {
    const startOfWeek = getStartOfWeek(date);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(endOfWeek.getDate() + 6);
    return endOfWeek;
  };

  // 获取时间周期标签
  const getTimePeriodLabels = () => {
    switch (currentType) {
      case 'daily':
      case 'yesterday':
      case 'tomorrow':
        return {
          prev: t('horoscope.nav.yesterday', '昨日'),
          current: t('horoscope.nav.today', '今日'),
          next: t('horoscope.nav.tomorrow', '明日')
        };
      case 'weekly':
      case 'lastweek':
      case 'nextweek':
        return {
          prev: t('horoscope.nav.last_week', '上周'),
          current: t('horoscope.nav.this_week', '本周'),
          next: t('horoscope.nav.next_week', '下周')
        };
      case 'monthly':
      case 'lastmonth':
      case 'nextmonth':
        return {
          prev: t('horoscope.nav.last_month', '上月'),
          current: t('horoscope.nav.this_month', '本月'),
          next: t('horoscope.nav.next_month', '下月')
        };
      case 'yearly':
      case 'lastyear':
      case 'nextyear':
        return {
          prev: t('horoscope.nav.last_year', '去年'),
          current: t('horoscope.nav.this_year', '今年'),
          next: t('horoscope.nav.next_year', '明年')
        };
      case 'love':
      case 'lastlove':
      case 'nextlove':
        return {
          prev: t('horoscope.nav.last_love', '上月'),
          current: t('horoscope.nav.this_love', '本月'),
          next: t('horoscope.nav.next_love', '下月')
        };
      default:
        return {
          prev: t('common.previous', '上一个'),
          current: t('common.current', '当前'),
          next: t('common.next', '下一个')
        };
    }
  };

  // 获取日期显示
  const getDateDisplay = () => {
    if (!currentDate) return '';
    
    // 根据页面类型决定显示的日期
    let displayDate = new Date(currentDate);
    
    // 获取当前语言
    const { i18n } = useTranslation();
    const currentLanguage = i18n.language;
    
    // 根据语言选择日期格式
    let dateFormatOptions: Intl.DateTimeFormatOptions = {};
    let dateLocale = 'en-US';
    
    // 根据语言设置日期格式和区域
    switch (currentLanguage) {
      case 'zh-CN':
        dateLocale = 'zh-CN';
        break;
      case 'zh-TW':
        dateLocale = 'zh-TW';
        break;
      case 'ja':
        dateLocale = 'ja-JP';
        break;
      default:
        dateLocale = 'en-US';
        break;
    }
    
    // 根据页面类型决定日期的格式
    switch (currentType) {
      case 'daily':
      case 'yesterday':
      case 'tomorrow':
        dateFormatOptions = { 
          month: 'long', 
          day: 'numeric',
          year: 'numeric' 
        };
        return new Intl.DateTimeFormat(dateLocale, dateFormatOptions).format(displayDate);
      case 'weekly':
      case 'lastweek':
      case 'nextweek':
        // 显示周的开始和结束日期范围
        const startOfWeek = getStartOfWeek(displayDate);
        const endOfWeek = getEndOfWeek(displayDate);
        
        // 根据语言格式化日期范围
        if (currentLanguage === 'zh-CN' || currentLanguage === 'zh-TW') {
          const startYear = startOfWeek.getFullYear();
          const startMonth = startOfWeek.getMonth() + 1;
          const startDay = startOfWeek.getDate();
          const endYear = endOfWeek.getFullYear();
          const endMonth = endOfWeek.getMonth() + 1;
          const endDay = endOfWeek.getDate();
          
          // 中文日期格式：YYYY年MM月DD日-MM月DD日
          if (startYear === endYear && startMonth === endMonth) {
            return `${startYear}年${startMonth}月${startDay}日-${endDay}日`;
          } else if (startYear === endYear) {
            return `${startYear}年${startMonth}月${startDay}日-${endMonth}月${endDay}日`;
          } else {
            return `${startYear}年${startMonth}月${startDay}日-${endYear}年${endMonth}月${endDay}日`;
          }
        } else if (currentLanguage === 'ja') {
          const startYear = startOfWeek.getFullYear();
          const startMonth = startOfWeek.getMonth() + 1;
          const startDay = startOfWeek.getDate();
          const endYear = endOfWeek.getFullYear();
          const endMonth = endOfWeek.getMonth() + 1;
          const endDay = endOfWeek.getDate();
          
          // 日语日期格式：YYYY年MM月DD日-MM月DD日
          if (startYear === endYear && startMonth === endMonth) {
            return `${startYear}年${startMonth}月${startDay}日～${endDay}日`;
          } else if (startYear === endYear) {
            return `${startYear}年${startMonth}月${startDay}日～${endMonth}月${endDay}日`;
          } else {
            return `${startYear}年${startMonth}月${startDay}日～${endYear}年${endMonth}月${endDay}日`;
          }
        } else {
          // 英文日期格式
          const startMonth = startOfWeek.toLocaleString(dateLocale, { month: 'long' });
          const startDay = startOfWeek.getDate();
          const endMonth = endOfWeek.toLocaleString(dateLocale, { month: 'long' });
          const endDay = endOfWeek.getDate();
          const endYear = endOfWeek.getFullYear();
          
          // 如果开始和结束日期在同一个月
          if (startMonth === endMonth) {
            return `${startMonth} ${startDay} - ${endDay}, ${endYear}`;
          } else {
            // 如果开始和结束日期在不同月份
            return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${endYear}`;
          }
        }
      case 'monthly':
      case 'lastmonth':
      case 'nextmonth':
      case 'love':
      case 'lastlove':
      case 'nextlove':
        dateFormatOptions = { 
          month: 'long', 
          year: 'numeric' 
        };
        return new Intl.DateTimeFormat(dateLocale, dateFormatOptions).format(displayDate);
      case 'yearly':
      case 'lastyear':
      case 'nextyear':
        return displayDate.getFullYear().toString();
      default:
        dateFormatOptions = { 
          month: 'long', 
          day: 'numeric',
          year: 'numeric' 
        };
        return new Intl.DateTimeFormat(dateLocale, dateFormatOptions).format(displayDate);
    }
  };

  const timePeriodLabels = getTimePeriodLabels();
  
  // 确定哪个按钮应该有下划线
  const getActiveTimeButton = () => {
    // 确保currentType已定义
    if (!currentType) return 'current';
    
    if (currentType === 'yesterday' || currentType === 'lastweek' || currentType === 'lastmonth' || currentType === 'lastyear' || currentType === 'lastlove') return 'prev';
    if (currentType === 'tomorrow' || currentType === 'nextweek' || currentType === 'nextmonth' || currentType === 'nextyear' || currentType === 'nextlove') return 'next';
    return 'current';
  };
  
  // 使用useMemo缓存结果，避免不必要的重新计算
  const activeTimeButton = React.useMemo(() => getActiveTimeButton(), [currentType]);

  // 自定义样式
  const dropdownButtonStyle = {
    background: isDark 
      ? 'linear-gradient(145deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9))'
      : 'linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9))',
    boxShadow: isDark
      ? 'inset 0 1px 1px rgba(255, 255, 255, 0.1), 0 2px 8px rgba(0, 0, 0, 0.3)'
      : 'inset 0 1px 1px rgba(255, 255, 255, 0.8), 0 2px 8px rgba(0, 0, 0, 0.05)',
    backdropFilter: 'blur(8px)',
    WebkitBackdropFilter: 'blur(8px)',
  };

  const dropdownMenuStyle = {
    background: isDark 
      ? 'linear-gradient(160deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.95))'
      : 'linear-gradient(160deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.98))',
    boxShadow: isDark
      ? '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 0 0 1px rgba(148, 85, 255, 0.2)'
      : '0 8px 32px rgba(0, 0, 0, 0.08), inset 0 0 0 1px rgba(148, 85, 255, 0.15)',
    backdropFilter: 'blur(16px)',
    WebkitBackdropFilter: 'blur(16px)',
  };

  return (
    <div className="w-full flex flex-col items-center space-y-6 transition-opacity duration-300 ease-in-out opacity-100 animate-fadeIn">
      {/* 顶部下拉菜单 */}
      <div className="relative w-full max-w-sm mx-auto" ref={dropdownRef}>
        <button
          onClick={() => setDropdownOpen(!dropdownOpen)}
          className={`
            w-full flex items-center justify-between px-5 py-3 rounded-full
            ${isDark 
              ? 'text-purple-200 border-purple-500/40' 
              : 'text-purple-800 border-purple-400/30'
            } 
            border transition-all duration-300
            hover:border-purple-500/70 hover:shadow-lg
          `}
          style={dropdownButtonStyle}
        >
          <div className="flex items-center justify-center">
            {(currentType === 'daily' || currentType === 'yesterday' || currentType === 'tomorrow') && (
              <svg className="w-6 h-6" fill="none" stroke="#9333EA" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            )}
            {(currentType === 'weekly' || currentType === 'lastweek' || currentType === 'nextweek') && (
              <svg className="w-6 h-6" fill="none" stroke="#9333EA" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            )}
            {(currentType === 'monthly' || currentType === 'lastmonth' || currentType === 'nextmonth') && (
              <svg className="w-6 h-6" fill="none" stroke="#9333EA" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
              </svg>
            )}
            {(currentType === 'yearly' || currentType === 'lastyear' || currentType === 'nextyear') && (
              <div className={`w-6 h-6 flex items-center justify-center ${isDark ? 'text-purple-300' : 'text-purple-800'} font-bold text-sm`}>
                365
              </div>
            )}
            {(currentType === 'love' || currentType === 'lastlove' || currentType === 'nextlove') && (
              <svg className="w-6 h-6" fill="#9333EA" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            )}
          </div>
          
          <div className="flex-1 text-center mx-2">
            <span className="font-medium text-xl">{getTypeDisplayName(currentType)}</span>
          </div>
          
          <div className={`
            w-9 h-9 rounded-full flex items-center justify-center
            ${isDark ? 'bg-purple-900/30' : 'bg-purple-50/80'}
            transition-transform duration-300 ${dropdownOpen ? 'rotate-180' : ''}
          `}>
            <svg 
              className="w-5 h-5" 
              fill="none" 
              stroke="#9333EA"
              viewBox="0 0 24 24" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>

        {/* 下拉菜单内容 */}
        {dropdownOpen && (
          <div 
            className={`
              absolute top-full left-1/2 transform -translate-x-1/2 w-64 mt-2 z-50 rounded-xl border overflow-hidden
              ${isDark 
                ? 'border-purple-500/30' 
                : 'border-purple-300/30'
              } 
            `}
            style={dropdownMenuStyle}
          >
            {['daily', 'weekly', 'monthly', 'yearly', 'love'].map((type, index) => (
              <button
                key={type}
                onClick={() => handleTypeChange(type)}
                className={`
                  w-full text-center px-3 py-2.5 transition-all duration-200 relative
                  ${index !== 4 ? (isDark ? 'border-b border-purple-700/20' : 'border-b border-purple-200/50') : ''}
                  ${(currentType === type || 
                     (type === 'daily' && (currentType === 'yesterday' || currentType === 'tomorrow')) ||
                     (type === 'weekly' && (currentType === 'lastweek' || currentType === 'nextweek')) ||
                     (type === 'monthly' && (currentType === 'lastmonth' || currentType === 'nextmonth')) ||
                     (type === 'yearly' && (currentType === 'lastyear' || currentType === 'nextyear')) ||
                     (type === 'love' && (currentType === 'lastlove' || currentType === 'nextlove')))
                    ? isDark 
                      ? 'bg-purple-900/30 text-purple-100 font-medium' 
                      : 'bg-purple-50/70 text-purple-800 font-medium'
                    : isDark 
                      ? 'text-gray-300 hover:text-purple-200' 
                      : 'text-gray-700 hover:text-purple-800'
                  }
                  hover:bg-gradient-to-r ${isDark 
                    ? 'hover:from-purple-900/20 hover:to-purple-800/10' 
                    : 'hover:from-purple-50/80 hover:to-purple-100/40'
                  }
                `}
              >
                <div className="flex items-center w-full">
                  {/* 菜单项图标 */}
                  <div className="flex items-center justify-center mr-2">
                    {type === 'daily' && (
                      <svg className="w-5 h-5" fill="none" stroke={currentType === type || currentType === 'yesterday' || currentType === 'tomorrow' ? "#9333EA" : "#64748B"} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                      </svg>
                    )}
                    {type === 'weekly' && (
                      <svg className="w-5 h-5" fill="none" stroke={currentType === type || currentType === 'lastweek' || currentType === 'nextweek' ? "#9333EA" : "#64748B"} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                      </svg>
                    )}
                    {type === 'monthly' && (
                      <svg className="w-5 h-5" fill="none" stroke={currentType === type || currentType === 'lastmonth' || currentType === 'nextmonth' ? "#9333EA" : "#64748B"} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                      </svg>
                    )}
                    {type === 'yearly' && (
                      <div className={`w-5 h-5 flex items-center justify-center ${currentType === type ? (isDark ? 'text-purple-300' : 'text-purple-800') : (isDark ? 'text-gray-400' : 'text-gray-500')} font-bold text-xs`}>
                        365
                      </div>
                    )}
                    {type === 'love' && (
                      <svg className="w-5 h-5" fill={currentType === type || currentType === 'lastlove' || currentType === 'nextlove' ? "#9333EA" : "none"} stroke={currentType === type || currentType === 'lastlove' || currentType === 'nextlove' ? "#9333EA" : "#64748B"} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                      </svg>
                    )}
                  </div>
                  <span className="text-base flex-1 text-center">{getTypeDisplayName(type)}</span>
                </div>
                
                {/* 选中指示器 */}
                {(currentType === type || 
                  (type === 'daily' && (currentType === 'yesterday' || currentType === 'tomorrow')) ||
                  (type === 'weekly' && (currentType === 'lastweek' || currentType === 'nextweek')) ||
                  (type === 'monthly' && (currentType === 'lastmonth' || currentType === 'nextmonth')) ||
                  (type === 'yearly' && (currentType === 'lastyear' || currentType === 'nextyear')) ||
                  (type === 'love' && (currentType === 'lastlove' || currentType === 'nextlove'))) && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <svg className="w-3.5 h-3.5" fill="none" stroke="#9333EA" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                )}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* 时间周期切换 */}
      <div className="flex items-center justify-center space-x-6 text-xl">
        <button 
          onClick={() => handleDateChange('prev')}
          className={`font-medium transition-all duration-200 hover:opacity-80 ${
            activeTimeButton === 'prev' 
              ? (isDark 
                ? 'text-purple-300 border-b-2 border-purple-500' 
                : 'text-purple-700 border-b-2 border-purple-600')
              : (isDark ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-gray-900')
          }`}
          key={`prev-${currentType}`}
        >
          {timePeriodLabels.prev}
        </button>
        <span className={`font-medium ${isDark ? 'text-gray-500' : 'text-gray-400'}`}>|</span>
        <button 
          onClick={() => handleDateChange('current')}
          className={`font-medium transition-all duration-200 ${
            activeTimeButton === 'current'
              ? (isDark 
                ? 'text-purple-300 border-b-2 border-purple-500' 
                : 'text-purple-700 border-b-2 border-purple-600')
              : (isDark ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-gray-900')
          }`}
          key={`current-${currentType}`}
        >
          {timePeriodLabels.current}
        </button>
        <span className={`font-medium ${isDark ? 'text-gray-500' : 'text-gray-400'}`}>|</span>
        <button 
          onClick={() => handleDateChange('next')}
          className={`font-medium transition-all duration-200 hover:opacity-80 ${
            activeTimeButton === 'next'
              ? (isDark 
                ? 'text-purple-300 border-b-2 border-purple-500' 
                : 'text-purple-700 border-b-2 border-purple-600')
              : (isDark ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-gray-900')
          }`}
          key={`next-${currentType}`}
        >
          {timePeriodLabels.next}
        </button>
      </div>

      {/* 日期显示 */}
      <div className={`text-2xl font-medium ${
        isDark ? 'text-purple-200' : 'text-purple-800'
      }`}>
        {getDateDisplay()}
      </div>
    </div>
  );
};

export default HoroscopeNavigation; 