import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TAROT_CARDS } from '../../data/tarot-cards';
import CdnLazyImage from '../CdnLazyImage';

interface TarotCardProps {
  cardId: number;
  displayIndex: number;
  isFirstCard: boolean;
  isFlipped: boolean;
  isProcessing: boolean;
  dragActive: boolean;
  orientation: boolean;
  cardBackImage: string;
  cardFrontImage: string;
  currentLanguage: string;
  animationDuration: {
    flip: number;
  };
  onClick: () => void;
}

const TarotCard: React.FC<TarotCardProps> = ({ 
  cardId,
  displayIndex,
  isFirstCard,
  isFlipped,
  isProcessing,
  dragActive,
  orientation,
  cardBackImage,
  cardFrontImage,
  animationDuration,
  onClick
}) => {
  return (
    <div
      className={`relative perspective-1000 card-wrapper ${!dragActive && !isFlipped ? 'hoverable-card' : ''} ${isFlipped ? 'flipped-card' : ''}`}
      style={{ 
        marginLeft: isFirstCard ? '4px' : '-90px',
        zIndex: isFlipped ? displayIndex : displayIndex
      }}
      onClick={onClick}
    >
      <motion.div 
        className={`card-container w-36 h-52 cursor-pointer transition-all duration-300 preserve-3d 
          ${isFlipped ? 'pointer-events-none' : ''}
          ${isProcessing ? 'scale-110 z-50 shadow-xl shadow-purple-500/30' : ''}`}
      >
        <AnimatePresence>
          {/* 卡背 */}
          <motion.div 
            key={`back-${displayIndex}`}
            className={`absolute inset-0 backface-hidden rounded-lg border-0 shadow-none flex items-center justify-center overflow-hidden bg-transparent`}
            initial={false}
            animate={isFlipped ? 
              { rotateY: 180, transition: { duration: animationDuration.flip, ease: "easeInOut" } } : 
              { rotateY: 0, transition: { duration: animationDuration.flip, ease: "easeInOut" } }
            }
          >
            <CdnLazyImage 
              src={cardBackImage}
              alt="Card Back"
              className="h-[95%] w-auto object-contain"
              style={{ imageRendering: 'crisp-edges' }}
              draggable="false"
            />
          </motion.div>

          {/* 卡面 */}
          <motion.div 
            key={`face-${displayIndex}`}
            className={`absolute inset-0 backface-hidden rounded-lg border-0 shadow-none flex items-center justify-center overflow-hidden bg-transparent`}
            initial={{ rotateY: 180 }}
            animate={isFlipped ? 
              { rotateY: 0, transition: { duration: animationDuration.flip, ease: "easeOut" } } : 
              { rotateY: 180, transition: { duration: animationDuration.flip, ease: "easeIn" } }
            }
          >
            <div 
              className="h-full w-auto flex items-center justify-center will-change-transform"
              style={{ 
                transform: orientation ? 'rotate(180deg)' : 'rotate(0deg)'
              }}
            >
              <CdnLazyImage 
                src={cardFrontImage} 
                alt={TAROT_CARDS[cardId]?.nameEn.replace(/_/g, ' ') || 'Tarot Card'}
                className="h-[95%] w-auto object-contain"
                style={{ imageRendering: 'crisp-edges' }}
                draggable="false"
              />
            </div>
          </motion.div>
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default TarotCard; 