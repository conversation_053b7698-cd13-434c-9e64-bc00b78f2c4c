import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { ShareSubmission, submitShare } from '../services/shareService';
import { X, Upload, Image as ImageIcon, ChevronDown } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { socialPlatforms, getCurrentPageUrl } from '../utils/shareUtils';
import axiosInstance from '../utils/axios';

interface ResubmitModalProps {
  isOpen: boolean;
  onClose: () => void;
  submission: ShareSubmission;
  onSuccess: () => void;
}

const ResubmitModal: React.FC<ResubmitModalProps> = ({
  isOpen,
  onClose,
  submission,
  onSuccess
}) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  const [screenshot, setScreenshot] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const [selectedPlatform, setSelectedPlatform] = useState<string>(submission.platform);
  const [customPlatform, setCustomPlatform] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [originalReadingUrl, setOriginalReadingUrl] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 在组件打开时获取原始解读URL
  useEffect(() => {
    if (isOpen && submission.sessionId) {
      const fetchOriginalUrl = async () => {
        try {
          const url = await getOriginalReadingUrl(submission.sessionId!);
          setOriginalReadingUrl(url);
        } catch (error) {
          console.error('获取原始解读URL失败:', error);
          setOriginalReadingUrl('');
        }
      };
      fetchOriginalUrl();
    } else {
      setOriginalReadingUrl('');
    }
  }, [isOpen, submission.sessionId]);

  if (!isOpen) return null;

  // 根据sessionId生成原始解读URL
  const getOriginalReadingUrl = async (sessionId: string): Promise<string> => {
    if (!sessionId) return '';

    try {
      // 查询会话信息以确定页面类型
      const response = await axiosInstance.get(`/api/session/types/${sessionId}`);
      const sessionData = response.data;

      // 使用shareUtils中的getCurrentPageUrl函数，强制使用生产环境URL
      const currentUrl = getCurrentPageUrl(false, undefined, true);
      const url = new URL(currentUrl);

      // 根据spread_id确定页面路径和参数
      if (sessionData.spreadId === 'yes-no-single-card') {
        // Yes/No单卡占卜页面
        url.pathname = '/yes-no-tarot/single-card/result';
        url.search = `?session=${sessionId}`;
      } else if (sessionData.spreadId === 'daily-fortune') {
        // 每日运势页面
        url.pathname = '/daily-fortune-result';
        url.search = `?sessionId=${sessionId}`;
      } else {
        // 默认塔罗解读页面
        url.pathname = '/tarot-result';
        url.search = `?session=${sessionId}`;
      }

      return url.toString();
    } catch (error) {
      console.error('获取会话信息失败:', error);

      // 如果查询失败，回退到默认的tarot-result页面
      const currentUrl = getCurrentPageUrl(false, undefined, true);
      const url = new URL(currentUrl);
      url.pathname = '/tarot-result';
      url.search = `?session=${sessionId}`;
      return url.toString();
    }
  };

  // 处理文件选择
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setScreenshot(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  // 处理提交
  const handleSubmit = async () => {
    if (!selectedPlatform) {
      toast.error(t('share.error.platform_required', '请选择分享平台'));
      return;
    }

    if (selectedPlatform === 'other' && !customPlatform.trim()) {
      toast.error(t('share.error.custom_platform_required', '请输入自定义平台名称'));
      return;
    }

    if (!screenshot) {
      toast.error(t('share.error.screenshot_required', '请上传分享截图'));
      return;
    }

    setIsSubmitting(true);
    try {
      const formData = new FormData();
      formData.append('screenshot', screenshot);
      // 如果选择了"其他平台"，使用自定义平台名称
      const platformName = selectedPlatform === 'other' ? customPlatform.trim() : selectedPlatform;
      formData.append('platform', platformName);

      if (submission.sessionId) {
        formData.append('sessionId', submission.sessionId);
      }

      // 添加当前用户使用的语言
      formData.append('language', i18n.language);

      await submitShare(formData);
      
      toast.success(t('share.resubmit_success', '重新提交成功！我们会尽快审核您的分享。'));
      onSuccess();
    } catch (error: any) {
      console.error('重新提交分享失败:', error);
      toast.error(error.response?.data?.message || t('share.error.submit_failed', '提交失败，请重试'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 清理预览URL
  const clearPreview = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    setScreenshot(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className={`relative w-full max-w-md p-6 rounded-lg shadow-lg ${
        isDark ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
      }`}>
        {/* 关闭按钮 */}
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
        >
          <X className="w-5 h-5" />
        </button>
        
        {/* 标题 */}
        <h3 className="text-lg font-medium mb-4">
          {t('share.resubmit_title', '重新提交分享')}
        </h3>
        
        {/* 原申请信息和拒绝原因 */}
        {submission.reviewNote && (
          <div className={`mb-4 p-3 rounded-lg ${
            isDark ? 'bg-red-900/20 border border-red-800/30' : 'bg-red-50 border border-red-200'
          }`}>
            <p className="text-sm">
              <span className="font-medium text-red-600 dark:text-red-400">{t('share.rejection_reason', '拒绝原因')}:</span>
              <span className="text-red-700 dark:text-red-300 ml-1">"{submission.reviewNote}"</span>
            </p>
          </div>
        )}

        {/* 原始解读链接 */}
        {submission.sessionId && (
          <div className={`mb-4 p-3 rounded-lg ${
            isDark ? 'bg-blue-900/20 border border-blue-800/30' : 'bg-blue-50 border border-blue-200'
          }`}>
            <p className="text-sm mb-2">
              <span className="font-medium text-blue-600 dark:text-blue-400">{t('share.original_reading_url', '原始解读链接')}:</span>
            </p>
            {originalReadingUrl ? (
              <a
                href={originalReadingUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 break-all underline"
              >
                {originalReadingUrl}
              </a>
            ) : (
              <span className="text-xs text-gray-500">
                {t('share.loading_url', '正在加载链接...')}
              </span>
            )}
          </div>
        )}

        {/* 平台选择 */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">
            {t('share.reward.platform', '分享平台')}
          </label>
          <div className="relative">
            <select
              value={selectedPlatform}
              onChange={(e) => setSelectedPlatform(e.target.value)}
              className={`w-full p-3 rounded-lg border appearance-none ${
                isDark
                  ? 'bg-gray-700 border-gray-600 text-gray-200'
                  : 'bg-white border-gray-300 text-gray-800'
              } focus:outline-none focus:ring-2 focus:ring-purple-500`}
            >
              <option value="">{t('share.reward.select_platform', '选择分享平台')}</option>
              {socialPlatforms.map((platform) => (
                <option key={platform.id} value={platform.id}>
                  {platform.name}
                </option>
              ))}
              <option value="other">{t('share.reward.other_platform', '其他平台')}</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>

          {/* 自定义平台输入框 */}
          {selectedPlatform === 'other' && (
            <div className="mt-2">
              <input
                type="text"
                value={customPlatform}
                onChange={(e) => setCustomPlatform(e.target.value)}
                placeholder={t('share.custom_platform_placeholder', '请输入平台名称')}
                className={`w-full p-2 rounded-lg border ${
                  isDark
                    ? 'bg-gray-700 border-gray-600 text-gray-200'
                    : 'bg-white border-gray-300 text-gray-800'
                } focus:outline-none focus:ring-2 focus:ring-purple-500`}
              />
            </div>
          )}

          {submission.platform !== selectedPlatform && selectedPlatform !== 'other' && (
            <p className="text-xs text-gray-500 mt-1">
              {t('share.original_platform', '原分享平台')}: {submission.platform}
            </p>
          )}
        </div>
        

        {/* 文件上传 */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">
            {t('share.reward.screenshot', '分享截图')}
          </label>
          
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="image/*"
            className="hidden"
          />
          
          {previewUrl ? (
            <div className="relative">
              <img 
                src={previewUrl} 
                alt="Preview" 
                className="w-full h-auto rounded-lg border border-gray-300 dark:border-gray-600" 
              />
              <button
                onClick={clearPreview}
                className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ) : (
            <button
              onClick={() => fileInputRef.current?.click()}
              className={`w-full p-4 border-2 border-dashed rounded-lg transition-colors ${
                isDark 
                  ? 'border-gray-600 hover:border-gray-500 bg-gray-700' 
                  : 'border-gray-300 hover:border-gray-400 bg-gray-50'
              }`}
            >
              <div className="flex flex-col items-center">
                <ImageIcon className="w-8 h-8 mb-2 text-gray-400" />
                <span className="text-sm text-gray-500">
                  {t('share.reward.upload_screenshot', '上传分享截图')}
                </span>
              </div>
            </button>
          )}
        </div>
        
        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className={`px-4 py-2 rounded-lg transition-colors ${
              isDark 
                ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' 
                : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
            }`}
          >
            {t('common.cancel', '取消')}
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || !selectedPlatform || (selectedPlatform === 'other' && !customPlatform.trim()) || !screenshot}
            className={`px-4 py-2 rounded-lg transition-colors flex items-center ${
              isSubmitting || !selectedPlatform || (selectedPlatform === 'other' && !customPlatform.trim()) || !screenshot
                ? 'bg-gray-400 cursor-not-allowed text-gray-600'
                : 'bg-purple-600 hover:bg-purple-700 text-white'
            }`}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                {t('share.submitting', '提交中...')}
              </>
            ) : (
              <>
                <Upload className="w-4 h-4 mr-2" />
                {t('share.resubmit', '重新提交')}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResubmitModal;
