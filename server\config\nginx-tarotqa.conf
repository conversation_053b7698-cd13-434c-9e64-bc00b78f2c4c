# HTTP 服务器配置
server {
    listen 80;
    server_name tarotqa.com www.tarotqa.com;

    # 强制跳转HTTPS
    return 301 https://$server_name$request_uri;
}

# CDN HTTP 配置 - 强制 HTTPS
server {
    listen 80;
    server_name cdn.tarotqa.com;

    # 强制跳转HTTPS
    return 301 https://$server_name$request_uri;
}

# CDN HTTPS 配置
server {
    listen 443 ssl http2; # 添加http2支持
    server_name cdn.tarotqa.com;

    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/tarotqa.com.pem;
    ssl_certificate_key /etc/nginx/ssl/tarotqa.com.key;
    ssl_session_timeout 5m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # 视频文件目录
    location /public/ {
        alias /var/www/tarot/public/;

        # 正确的文件 MIME 类型
        types {
            video/webm webm;
            image/webp webp;
        }

        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";

        # 缓存设置
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 开启 sendfile
        sendfile on;
        tcp_nopush on;

        # 图片和视频的压缩设置
        gzip on;
        gzip_types image/webp;
        gzip_min_length 256;
        gzip_comp_level 6;
        gzip_vary on;

        # 大文件优化
        proxy_max_temp_file_size 0;
        proxy_buffering off;

        # 针对图片的特殊优化
        location ~* \.webp$ {
            add_header Vary Accept;
            access_log off;
            log_not_found off;
            expires max;
        }
    }

    # 视频文件目录
    location /dist/ {
        alias /var/www/tarot/dist/;

        # 正确的文件 MIME 类型
        types {
            video/webm webm;
            image/webp webp;
        }

        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";

        # 缓存设置
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 开启 sendfile
        sendfile on;
        tcp_nopush on;

        # 图片和视频的压缩设置
        gzip on;
        gzip_types image/webp;
        gzip_min_length 256;
        gzip_comp_level 6;
        gzip_vary on;

        # 大文件优化
        proxy_max_temp_file_size 0;
        proxy_buffering off;

        # 针对图片的特殊优化
        location ~* \.webp$ {
            add_header Vary Accept;
            access_log off;
            log_not_found off;
            expires max;
        }
    }

}
# HTTPS www 重定向配置
server {
    listen 443 ssl http2; # 添加http2支持
    server_name www.tarotqa.com;

    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/tarotqa.com.pem;
    ssl_certificate_key /etc/nginx/ssl/tarotqa.com.key;

    # 重定向到非www域名
    return 301 https://tarotqa.com$request_uri;
}

# WebP 支持检测
map $http_accept $webp_suffix {
    default   "";
    "~*webp"  ".webp";
}

# HTTPS 主服务器配置
server {
    listen 443 ssl http2; # 添加http2支持
    server_name tarotqa.com;

    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/tarotqa.com.pem;
    ssl_certificate_key /etc/nginx/ssl/tarotqa.com.key;
    ssl_session_timeout 5m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # Gzip 压缩配置
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;
    gzip_min_length 1000;
    gzip_comp_level 6;
    gzip_vary on;
    gzip_proxied any;
    gzip_disable "MSIE [1-6]\.";
    gzip_static on;

    # 日志配置
    access_log /var/log/nginx/tarot_access.log;
    error_log /var/log/nginx/tarot_error.log debug;

    # 前端静态文件
    root /var/www/tarot/dist;
    index index.html;

    # 添加 /public 目录的特殊处理
    location /public/ {
        alias /var/www/tarot/public/;  # 确保这个路径是正确的

        # 正确的文件 MIME 类型
        types {
            video/webm webm;
            image/webp webp;
        }

        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";

        # 缓存设置
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 开启 sendfile
        sendfile on;
        tcp_nopush on;

        # 图片和视频的压缩设置
        gzip on;
        gzip_types image/webp;
        gzip_min_length 256;
        gzip_comp_level 6;
        gzip_vary on;

        # 大文件优化
        proxy_max_temp_file_size 0;
        proxy_buffering off;

        # 针对图片的特殊优化
        location ~* \.webp$ {
            add_header Vary Accept;
            access_log off;
            log_not_found off;
            expires max;
        }
    }


    # 添加 /dist 目录的特殊处理
    location /dist/ {
        alias /var/www/tarot/dist/;  # 确保这个路径是正确的

        # 正确的文件 MIME 类型
        types {
            video/webm webm;
            image/webp webp;
        }

        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";

        # 缓存设置
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 开启 sendfile
        sendfile on;
        tcp_nopush on;

        # 图片和视频的压缩设置
        gzip on;
        gzip_types image/webp;
        gzip_min_length 256;
        gzip_comp_level 6;
        gzip_vary on;

        # 大文件优化
        proxy_max_temp_file_size 0;
        proxy_buffering off;

        # 针对图片的特殊优化
        location ~* \.webp$ {
            add_header Vary Accept;
            access_log off;
            log_not_found off;
            expires max;
        }
    }

    # 分享截图静态文件服务
    location /share-screenshots/ {
        alias /var/www/tarot/share-screenshots/;

        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";

        # 缓存设置
        expires 7d;
        add_header Cache-Control "public, no-transform";

        # 安全设置 - 只允许图片文件
        location ~* \.(jpg|jpeg|png|gif|webp)$ {
            try_files $uri =404;
        }

        # 拒绝其他文件类型
        location ~ {
            return 403;
        }
    }

    # 删除静态隐私政策HTML文件特殊处理和重定向
    # React应用路由 - 需要在通用匹配之前
    location /home {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires 0;
    }

    # 图片资源处理（支持 WebP）
    location ~* \.(png|jpe?g)$ {
        add_header Vary Accept;
        try_files $uri$webp_suffix $uri =404;
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 开启 gzip 压缩
        gzip_static on;
    }

    # WebP 图片专门处理
    location ~* \.webp$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri =404;
    }

    # 其他静态资源缓存 - 移到 /landing/ 配置后面
    location ~* \.(js|css|ico|svg)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri =404;

        # 开启 gzip 压缩
        gzip_static on;
    }

    # 字体文件处理
    location ~* \.(woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        add_header Access-Control-Allow-Origin "*";
        try_files $uri =404;
    }

    # 支付回调专用处理
    location = /api/payment/wechat/notify {
        proxy_pass http://127.0.0.1:5000/api/payment/wechat/notify;

        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 不处理 CORS，因为这是服务器间的通信
        proxy_pass_request_headers on;
    }

    # 支付宝回调专用处理
    location = /api/payment/alipay/notify {
        proxy_pass http://127.0.0.1:5000/api/payment/alipay/notify;

        # 添加调试日志
        access_log /var/log/nginx/alipay_notify_access.log combined buffer=512k;
        error_log /var/log/nginx/alipay_notify_error.log debug;

        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 增加超时时间
        proxy_connect_timeout 60;
        proxy_send_timeout 60;
        proxy_read_timeout 60;

        # 移除所有 CORS 相关头部
        proxy_hide_header 'Access-Control-Allow-Origin';
        proxy_hide_header 'Access-Control-Allow-Methods';
        proxy_hide_header 'Access-Control-Allow-Headers';
        proxy_hide_header 'Access-Control-Expose-Headers';
        proxy_hide_header 'Access-Control-Max-Age';

        # 确保请求体能够正确传递
        client_max_body_size 8m;
        client_body_buffer_size 128k;
    }

    # API 反向代理
    location /api/ {
        proxy_pass http://127.0.0.1:5000/;

        # 添加这两行以支持流式响应
        proxy_buffering off;
        proxy_max_temp_file_size 0;

        # 增加超时时间以支持长时间连接
        proxy_read_timeout 300s;

        # 支持文件上传 - 设置最大请求体大小为10MB
        client_max_body_size 10m;
        client_body_buffer_size 128k;
        client_body_timeout 60s;


        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 移除后端设置的 CORS 头
        proxy_hide_header 'Access-Control-Allow-Origin';
        proxy_hide_header 'Access-Control-Allow-Methods';
        proxy_hide_header 'Access-Control-Allow-Headers';
        proxy_hide_header 'Access-Control-Expose-Headers';
        proxy_hide_header 'Access-Control-Max-Age';

        # 设置允许的域名
        set $cors_origin "";
        if ($http_origin = "https://tarotqa.com") {
            set $cors_origin "https://tarotqa.com";
        }

        # CORS 预检请求处理
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' $cors_origin always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE, PATCH' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8' always;
            add_header 'Content-Length' 0 always;
            return 204;
        }

        # 正常请求的 CORS 处理
        add_header 'Access-Control-Allow-Origin' $cors_origin always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
    }

    # 通用路由处理 - 放在最后
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires 0;
    }
}
