import React from 'react';

// 塔罗牌阵类型定义
export interface TarotSpread {
  id: string;
  name: string;
  description: string;
  cardCount: number;
  positions: string[];
  layout: string;
  defaultQuestion?: string; // 默认占卜问题
}

// 博客文章类型定义
export interface BlogPost {
  id: string;
  title: string;
  coverImage: string;
  date: string;
  slug: string;
  description?: string; // 新增：可选的文章描述字段，用于直接指定文章描述
  category: string; // 新增：文章类别，用于分类筛选
  useNewUrlFormat?: boolean; // 新增：标识是否使用新的URL格式（/category/slug 而不是 /blog/slug）
  tarotSpread?: TarotSpread; // 可选的牌阵信息
  disableTarotDrawing?: boolean; // 控制是否禁用抽牌和解读组件
  showOnlyH2Headings?: boolean; // 控制是否只显示h2标题，不显示h3标题
  content: React.ReactNode;
} 