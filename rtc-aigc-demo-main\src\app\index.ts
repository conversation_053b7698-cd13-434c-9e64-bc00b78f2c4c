/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

import { AigcAPIs, BasicAPIs } from './api';
import { generateAPIs } from './base';
import voiceApi from './voiceApi';

const VoiceChat = {
  // 保留原有的API接口
  ...generateAPIs(AigcAPIs),
  
  // 添加新的语音API接口
  startVoiceChat: async (userId: string = 'anonymous') => {
    voiceApi.init({ userId });
    const result = await voiceApi.startSession();
    if (result.success) {
      await voiceApi.connect();
      await voiceApi.joinSession();
    }
    return result;
  },
  
  stopVoiceChat: async () => {
    return await voiceApi.stopSession();
  },
  
  sendAudio: async (audioData: string) => {
    return await voiceApi.sendAudio(audioData);
  },
  
  addAudioListener: (callback: (data: any) => void) => {
    voiceApi.addEventListener('audio', callback);
  },
  
  removeAudioListener: (callback: (data: any) => void) => {
    voiceApi.removeEventListener('audio', callback);
  },
  
  addTextListener: (callback: (data: any) => void) => {
    voiceApi.addEventListener('text', callback);
  },
  
  removeTextListener: (callback: (data: any) => void) => {
    voiceApi.removeEventListener('text', callback);
  },
  
  addEventListener: (callback: (data: any) => void) => {
    voiceApi.addEventListener('event', callback);
  },
  
  removeEventListener: (callback: (data: any) => void) => {
    voiceApi.removeEventListener('event', callback);
  },
  
  addErrorListener: (callback: (data: any) => void) => {
    voiceApi.addEventListener('error', callback);
  },
  
  removeErrorListener: (callback: (data: any) => void) => {
    voiceApi.removeEventListener('error', callback);
  }
};

const Basic = generateAPIs(BasicAPIs);

export default {
  VoiceChat,
  Basic,
};
