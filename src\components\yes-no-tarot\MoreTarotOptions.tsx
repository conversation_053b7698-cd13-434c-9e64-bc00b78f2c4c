import React from 'react';
import { useTranslation } from 'react-i18next';
import { CdnLazyImage } from '../../components/CdnImageExport';

interface MoreTarotOptionsProps {
  onNavigate: (path: string) => void;
  pageType?: 'yesno' | 'single' | 'three'; // 新增页面类型参数
}

const MoreTarotOptions: React.FC<MoreTarotOptionsProps> = ({ onNavigate, pageType = 'yesno' }) => {
  const { t } = useTranslation();

  // 根据页面类型返回第一个卡片的配置
  const getFirstCardConfig = () => {
    switch (pageType) {
      case 'yesno':
        return {
          title: t('yes_no_tarot.more_options.horoscope_tarot.title', 'Horoscope Reading'),
          description: t('yes_no_tarot.more_options.horoscope_tarot.description', 'Professional guidance on your love life and relationships'),
          image: '/images-optimized/home/<USER>',
          path: '/horoscope/love-horoscope'
        };
      case 'single':
        return {
          title: t('yes_no_tarot.more_options.three_card.title', 'Three Card Yes/No Tarot'),
          description: t('yes_no_tarot.more_options.three_card.description', 'Comprehensive analysis with three cards for deeper understanding'),
          image: '/images-optimized/yes-no-tarot/Yes-No-Three-Card-Spread.webp',
          path: '/yes-no-tarot/three-cards'
        };
      case 'three':
        return {
          title: t('yes_no_tarot.more_options.single_card.title', 'One Card Yes/No Tarot'),
          description: t('yes_no_tarot.more_options.single_card.description', 'Quick answers with a single card for immediate guidance'),
          image: '/images-optimized/yes-no-tarot/Yes-No-Single-Card-Spread.webp',
          path: '/yes-no-tarot/single-card'
        };
      default:
        return {
          title: t('home.more_tarot_options.love_tarot.title', 'Love Tarot Reading'),
          description: t('home.more_tarot_options.love_tarot.description', 'Discover insights about your romantic relationships and future love prospects'),
          image: '/images-optimized/home/<USER>',
          path: '/love-tarot'
        };
    }
  };

  const firstCard = getFirstCardConfig();

  return (
    <div className="section-spacing max-w-[95%] lg:max-w-5xl mx-auto mt-10 sm:mt-12 lg:mt-16">
      <h2 className="main-title mb-6 dark:text-white text-gray-900">
        {t('home.more_tarot_options.title')}
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
        {/* 动态第一卡片 */}
        <div 
          className="relative backdrop-blur-xl p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col cursor-pointer"
          onClick={() => onNavigate(firstCard.path)}
        >
          <div className="h-[100px]">  {/* 固定高度的文本区域 */}
            <h3 className="font-medium text-lg sm:text-xl dark:text-white text-gray-800 mb-2">
              {firstCard.title}
            </h3>
            <p className="body-text dark:text-gray-300 text-gray-600">
              {firstCard.description}
            </p>
          </div>
          <div className="mt-auto relative rounded-lg overflow-hidden flex-grow" style={{ aspectRatio: '1/1' }}>
            <CdnLazyImage 
              src={firstCard.image}
              alt={firstCard.title}
              className="w-full h-full object-cover"  
            />
          </div>
        </div>
        
        {/* 每日运势查询卡片 */}
        <div 
          className="relative backdrop-blur-xl p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col cursor-pointer"
          onClick={() => onNavigate('/daily-fortune')}
        >
          <div className="h-[100px]">  {/* 固定高度的文本区域 */}
            <h3 className="font-medium text-lg sm:text-xl dark:text-white text-gray-800 mb-2">{t('home.more_tarot_options.daily_tarot.title')}</h3>
            <p className="body-text dark:text-gray-300 text-gray-600">{t('home.more_tarot_options.daily_tarot.description')}</p>
          </div>
          <div className="mt-auto relative rounded-lg overflow-hidden flex-grow" style={{ aspectRatio: '1/1' }}>
            <CdnLazyImage 
              src="/images-optimized/home/<USER>" 
              alt={t('home.more_tarot_options.daily_tarot.title')}
              className="w-full h-full object-cover"  
            />
          </div>
        </div>
        
        {/* 塔罗学习指南卡片 */}
        <div 
          className="relative backdrop-blur-xl p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col cursor-pointer"
          onClick={() => onNavigate('/blog/complete-tarot-guide')}
        >
          <div className="h-[100px]">  {/* 固定高度的文本区域 */}
            <h3 className="font-medium text-lg sm:text-xl dark:text-white text-gray-800 mb-2">{t('home.more_tarot_options.tarot_learning.title')}</h3>
            <p className="body-text dark:text-gray-300 text-gray-600">{t('home.more_tarot_options.tarot_learning.description')}</p>
          </div>
          <div className="mt-auto relative rounded-lg overflow-hidden flex-grow" style={{ aspectRatio: '1/1' }}>
            <CdnLazyImage 
              src="/images-optimized/home/<USER>" 
              alt={t('home.more_tarot_options.tarot_learning.title')}
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MoreTarotOptions; 