// 会员价格配置文件
// 这个文件集中管理所有会员价格相关的常量，方便统一修改

// PayPal 订阅 ID 配置
export const PAYPAL_CONFIG = {
  // 沙盒环境
  SANDBOX: {
    MONTHLY_PLAN_ID: 'P-73T246948X854701UNA7MXRA',
    YEARLY_PLAN_ID: 'P-7DT945232F4259120NA7MXWY',
  },
  // 生产环境
  PRODUCTION: {
    MONTHLY_PLAN_ID: 'P-4AE86991PT549580ENBAPPSY', // 9.9$
    YEARLY_PLAN_ID: 'P-7Y036774DN444024CNBAPP4Q',
  }
};

// 内部会员 PayPal 订阅 ID 配置
export const INTERNAL_PAYPAL_CONFIG = {
  // 沙盒环境
  SANDBOX: {
    MONTHLY_PLAN_ID: 'P-1NE989578A8428222NA7MYFI',
    YEARLY_PLAN_ID: 'P-4MW60430DE4043443NA7MYKA',
  },
  // 生产环境
  PRODUCTION: {
    MONTHLY_PLAN_ID: 'P-6RX964851L724345RNBAPSRQ', // 7.92$
    YEARLY_PLAN_ID: 'P-5A3495901H5699508NBAPSYI',
  }
};

// 普通会员价格配置（USD）
export const PRICING_USD = {
  MONTHLY: {
    PRICE: 9.9,
    DISPLAY_PRICE: '9.9',
  },
  YEARLY: {
    PRICE: 89.9,
    DISPLAY_PRICE: '89.90',
    MONTHLY_EQUIVALENT: 7.49, // 每月等价
  },
  PAY_PER_USE: {
    PRICE_PER_USE: 1.38, // 每次的价格
  }
};

// 人民币价格配置（CNY）
export const PRICING_CNY = {
  MONTHLY: {
    PRICE: 71.28,
    DISPLAY_PRICE: '71.28',
  },
  YEARLY: {
    PRICE: 647.28,
    DISPLAY_PRICE: '647.28',
    MONTHLY_EQUIVALENT: 53.94, // 每月等价
  },
  PAY_PER_USE: {
    PRICE_PER_USE: 9.9, // 每次的价格
  }
};

// 内部折扣会员价格配置（USD，通常用于具有折扣权限的用户）
export const INTERNAL_PRICING_USD = {
  MONTHLY: {
    PRICE: 7.92,
    DISPLAY_PRICE: '7.92',
    ORIGINAL_PRICE: '9.90',
    DISCOUNT_PERCENT: 20,
  },
  YEARLY: {
    PRICE: 71.92,
    DISPLAY_PRICE: '71.92',
    ORIGINAL_PRICE: '89.90',
    DISCOUNT_PERCENT: 20,
    MONTHLY_EQUIVALENT: 5.99, // 折扣后每月等价
  },
  PAY_PER_USE: {
    PRICE_PER_USE: 1.10, // 折扣后每次的价格
    ORIGINAL_PRICE_PER_USE: 1.38, // 原始每次价格
    DISCOUNT_PERCENT: 20,
  }
};

// 内部折扣人民币价格配置（CNY）
export const INTERNAL_PRICING_CNY = {
  MONTHLY: {
    PRICE: 57.02,
    DISPLAY_PRICE: '57.02',
    ORIGINAL_PRICE: '71.28',
    DISCOUNT_PERCENT: 20,
  },
  YEARLY: {
    PRICE: 517.82,
    DISPLAY_PRICE: '517.82',
    ORIGINAL_PRICE: '647.28',
    DISCOUNT_PERCENT: 20,
    MONTHLY_EQUIVALENT: 43.15, // 折扣后每月等价
  },
  PAY_PER_USE: {
    PRICE_PER_USE: 7.92, // 折扣后每次的价格
    ORIGINAL_PRICE_PER_USE: 9.9, // 原始每次价格
    DISCOUNT_PERCENT: 20,
  }
}; 