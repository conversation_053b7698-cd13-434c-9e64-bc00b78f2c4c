import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Card } from '../../types/history';
import { getCardImagePath, getTranslatedCardName, getCardEnglishName } from '../../utils/historyDetailUtils';
import { useTheme } from '../../contexts/ThemeContext';
import CdnLazyImage from '../../components/CdnLazyImage';

interface CardDisplayProps {
  cards: Card[];
  positions: string[];
  getFontClass: () => string;
}

const CardDisplay: React.FC<CardDisplayProps> = ({ cards, positions, getFontClass }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 sm:gap-8 md:gap-10 justify-center place-items-center">
      {cards.map((card, index) => {
        const position = positions[index] || `位置 ${index + 1}`;
        
        return (
          <motion.div 
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.5 }}
            className="flex flex-col items-center w-[110px] sm:w-[140px] md:w-[160px] lg:w-[180px] mb-4"
          >
            <div className="relative mb-3 w-full">
              <div className="w-full aspect-[2/3] rounded-lg overflow-hidden border-0 transition-all duration-300 flex items-center justify-center bg-transparent"
                style={{ 
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }}
              >
                <motion.div 
                  className="h-full w-full flex items-center justify-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                  style={{ 
                    transform: card.isReversed ? 'rotate(180deg)' : 'rotate(0deg)'
                  }}
                >
                  <CdnLazyImage 
                    src={getCardImagePath(card.name)}
                    alt={getCardEnglishName(card.name)}
                    className="h-full w-full object-contain"
                    style={{ imageRendering: 'crisp-edges' }}
                    draggable="false"
                  />
                </motion.div>
              </div>
            </div>
            
            <div className="text-center mt-4 space-y-2">
              <h3 className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-800'} ${getFontClass()}`}>
                {getTranslatedCardName(card.name, t)}
              </h3>
              <p className={`text-sm ${isDark ? 'text-purple-500' : 'text-purple-700'} ${getFontClass()}`}>
                {card.isReversed ? t('reading.result.reversed') : t('reading.result.upright')}
              </p>
              <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'} h-10 flex items-start justify-center pt-1 ${getFontClass()}`}>
                {position}
              </p>
            </div>
          </motion.div>
        );
      })}
    </div>
  );
};

export default CardDisplay; 