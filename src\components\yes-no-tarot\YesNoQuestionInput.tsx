import React from 'react';
import { useTranslation } from 'react-i18next';
import { CdnLazyImage } from '../../components/CdnImageExport';
import YesNoQuestionForm from './YesNoQuestionForm';

interface YesNoQuestionInputProps {
  userQuestion: string;
  setUserQuestion: (question: string) => void;
  errorMessage: string;
  isSubmitting: boolean;
  onSubmitQuestion: (e: React.FormEvent) => void;
}

const YesNoQuestionInput: React.FC<YesNoQuestionInputProps> = ({
  userQuestion,
  setUserQuestion,
  errorMessage,
  isSubmitting,
  onSubmitQuestion
}) => {
  const { t } = useTranslation();

  return (
    <div className="max-w-3xl mx-auto">
      {/* 单卡塔罗卡片 */}
      <div className="relative backdrop-blur-xl rounded-xl overflow-hidden dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 shadow-lg">
        {/* 顶部图片区域 */}
        <div className="relative h-auto min-h-[200px] aspect-video overflow-hidden">
          <CdnLazyImage
            src="/images-optimized/yes-no-tarot/Yes-No-Single-Card-Spread.webp"
            alt="One Card Tarot Reading"
            className="w-full h-auto object-contain"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/60"></div>
          <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6">
            <h3 className="text-xl font-bold text-white">{t('yes_no_tarot.reading_options.single_card.title')}</h3>
            <p className="text-gray-300 mt-2 hidden sm:block text-sm">{t('yes_no_tarot.reading_options.single_card.description')}</p>
          </div>
        </div>
        
        {/* 内容区域 */}
        <div className="p-6">
          {/* 问题输入区域 */}
          <YesNoQuestionForm
            userQuestion={userQuestion}
            setUserQuestion={setUserQuestion}
            errorMessage={errorMessage}
            isSubmitting={isSubmitting}
            onSubmit={onSubmitQuestion}
          />
        </div>
      </div>
    </div>
  );
};

export default YesNoQuestionInput;
