/* 基础样式 */
.tag {
  white-space: nowrap;
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(135deg, #A855F7 0%, #EC4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 隐藏滚动条但保持可滚动 */
.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* 响应式布局 */
@media (max-width: 768px) {
  .spread-card {
    height: auto;
  }
  
  .spread-card .flex {
    flex-direction: column;
  }
  
  .spread-card > div > div[class*="w-[240px]"] {
    width: 100%;
    padding-left: 0;
    padding-top: 1rem;
    border-left: none;
    border-top: 1px solid rgb(31, 41, 55);
  }
} 