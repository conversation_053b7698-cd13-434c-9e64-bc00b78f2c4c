import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { horoscopeSigns } from '../../data/horoscopes';
import { useTheme } from '../../contexts/ThemeContext';
import { getFontClass } from '../../utils/fontUtils';
import LandingBackground from '../../components/LandingBackground';
import Footer from '../../components/Footer';
import SEO from '../../components/SEO';
import CdnLazyImage from '../../components/CdnLazyImage';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';
import SpotlightCard from '../../blocks/Components/SpotlightCard/SpotlightCard';
import ZodiacSignsGrid from '../../components/horoscope/ZodiacSignsGrid';
import HoroscopeFeatureCard from '../../components/horoscope/HoroscopeFeatureCard';
import HoverableText from '../../components/horoscope/HoverableText';

const Horoscope: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // 默认选择第一个星座
  const defaultSelectedSign = horoscopeSigns[0].id;
  // 跟踪用户选择的星座
  const [selectedSign, setSelectedSign] = useState<string>(defaultSelectedSign);

  // 修改处理星座点击的函数，不干扰ZodiacSignsGrid组件的内部状态
  const handleSelectedSignChange = (signId: string) => {
    setSelectedSign(signId);
    // 保存选择的星座到localStorage
    try {
      localStorage.setItem('selectedZodiacSign', signId);
    } catch (error) {
    }
  };

  // 从localStorage读取用户之前选择的星座
  useEffect(() => {
    try {
      const savedSign = localStorage.getItem('selectedZodiacSign');
      if (savedSign && horoscopeSigns.some(sign => sign.id === savedSign)) {
        setSelectedSign(savedSign);
      }
    } catch (error) {
    }
  }, []);

  const handleStartReading = () => {
    navigate('/');
  };

  return (
    <div className={`min-h-screen flex flex-col relative antialiased ${isDark ? 'text-white' : 'text-gray-800'}`}>
      <SEO />
      <LandingBackground />
      
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-16">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
            <h1 className={`main-title mb-1 sm:mb-2 ${getFontClass(i18n.language)} dark:text-white text-gray-900`}>
              <span className="block">{t('horoscope.heading', 'Free Horoscopes for Every Timeline')}</span>
            </h1>
            <h2 className={`text-base sm:text-lg ${isDark ? 'text-purple-300' : 'text-purple-600'} italic ${getFontClass(i18n.language)} font-normal`}>
              {t('horoscope.subheading', 'Navigate your life with hyper-personalized horoscopes, powered by real astrological data and AI-driven insights')}
            </h2>
          </div>

          {/* 星座运势网格 - 使用ZodiacSignsGrid组件，并设置默认选中的星座 */}
          <ZodiacSignsGrid 
            containerClassName="" 
            defaultSelectedSign={selectedSign} 
            horoscopeType="daily" 
            onSignClick={handleSelectedSignChange}
            showPreview={true}
            date={new Date()}
          />
            
          {/* 星座运势介绍区域 - SEO优化版本 */}
          <div className="mt-12 max-w-6xl mx-auto px-4 sm:px-6">
            
            <style>
              {`
                .feature-card {
                  position: relative;
                  border: 1px solid rgba(236, 72, 153, 0.3);
                  box-shadow: 
                    0 0 0 1px rgba(168, 85, 247, 0.2),
                    0 0 15px rgba(168, 85, 247, 0.15),
                    0 0 30px rgba(236, 72, 153, 0.15),
                    inset 0 0 15px rgba(168, 85, 247, 0.1);
                }
                
                /* 深色主题特定样式 */
                .dark .feature-card::before {
                  content: '';
                  position: absolute;
                  inset: -1px;
                  padding: 1px;
                  background: linear-gradient(
                    135deg,
                    rgba(168, 85, 247, 0.5),
                    rgba(236, 72, 153, 0.5)
                  );
                  -webkit-mask: 
                    linear-gradient(#fff 0 0) content-box, 
                    linear-gradient(#fff 0 0);
                  -webkit-mask-composite: xor;
                  mask-composite: exclude;
                  pointer-events: none;
                  border-radius: 1rem;
                }
                
                /* 浅色主题特定样式 */
                :not(.dark) .feature-card::before {
                  content: '';
                  position: absolute;
                  inset: -1px;
                  padding: 1px;
                  background: linear-gradient(
                    135deg,
                    rgba(168, 85, 247, 0.3),
                    rgba(236, 72, 153, 0.3)
                  );
                  -webkit-mask: 
                    linear-gradient(#fff 0 0) content-box, 
                    linear-gradient(#fff 0 0);
                  -webkit-mask-composite: xor;
                  mask-composite: exclude;
                  pointer-events: none;
                  border-radius: 1rem;
                }
                
                .dark .feature-card::after {
                  content: '';
                  position: absolute;
                  inset: 0;
                  background: 
                    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, 0.15), transparent 70%),
                    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, 0.15), transparent 70%);
                  pointer-events: none;
                  border-radius: 1rem;
                }
                
                :not(.dark) .feature-card::after {
                  content: '';
                  position: absolute;
                  inset: 0;
                  background: 
                    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, 0.05), transparent 50%),
                    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, 0.05), transparent 50%);
                  pointer-events: none;
                  border-radius: 1rem;
                }
              `}
            </style>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-10 md:gap-10 lg:gap-12">
              {/* 每日运势 - 使用HoroscopeFeatureCard组件 */}
              <HoroscopeFeatureCard
                title={t('horoscope.daily.title', 'Free Daily Horoscope: Today\'s Horoscope for Your Star Sign')}
                description={t('horoscope.daily.description', 'Get your free daily horoscope updated every 24 hours. Discover today\'s horoscope for Aries, Leo, Virgo, and all zodiac signs with actionable guidance for love, career, and finances.')}
                icon={
                  <svg className={`w-10 h-10 ${isDark ? 'text-amber-500' : 'text-amber-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="5"></circle>
                    <line x1="12" y1="1" x2="12" y2="3"></line>
                    <line x1="12" y1="21" x2="12" y2="23"></line>
                    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                    <line x1="1" y1="12" x2="3" y2="12"></line>
                    <line x1="21" y1="12" x2="23" y2="12"></line>
                    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                  </svg>
                }
                onClick={() => navigate(`/horoscope/${selectedSign}-daily-horoscope`)}
              />
              
              {/* 每周运势 - 使用HoroscopeFeatureCard组件 */}
              <HoroscopeFeatureCard
                title={t('horoscope.weekly.title', 'Weekly Horoscope Forecast: This Week\'s Astrology Reading')}
                description={t('horoscope.weekly.description', 'Plan your week with our weekly horoscope forecast. We track key planetary shifts to help you navigate important moments with confidence using weekly astrology predictions.')}
                icon={
                  <svg className={`w-10 h-10 ${isDark ? 'text-amber-500' : 'text-amber-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                }
                onClick={() => navigate(`/horoscope/${selectedSign}-weekly-horoscope`)}
              />
              
              {/* 每月运势 - 使用HoroscopeFeatureCard组件 */}
              <HoroscopeFeatureCard
                title={t('horoscope.monthly.title', 'Monthly Horoscope: Complete Monthly Astrology Guide')}
                description={t('horoscope.monthly.description', 'Comprehensive monthly horoscope analyzing major astrological trends. Align your actions with cosmic energies for optimal personal growth through detailed monthly astrology insights.')}
                icon={
                  <svg className={`w-10 h-10 ${isDark ? 'text-amber-500' : 'text-amber-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                  </svg>
                }
                onClick={() => navigate(`/horoscope/${selectedSign}-monthly-horoscope`)}
              />
              
              {/* 年度运势 - 使用HoroscopeFeatureCard组件 */}
              <HoroscopeFeatureCard
                title={t('horoscope.yearly.title', '2025 Yearly Horoscope: Annual Astrology Forecast')}
                description={t('horoscope.yearly.description', 'Your 2025 yearly horoscope based on major planetary orbits and eclipses. Essential astrology guidance for long-term planning and purposeful living throughout the year.')}
                icon={
                  <div className={`text-center ${isDark ? 'text-amber-500' : 'text-amber-600'} font-bold text-lg`}>
                    365
                  </div>
                }
                onClick={() => navigate(`/horoscope/${selectedSign}-yearly-horoscope`)}
              />
              
              {/* 爱情运势 - 使用HoroscopeFeatureCard组件 */}
              <HoroscopeFeatureCard
                title={t('horoscope.compatibility.title', 'Zodiac Compatibility: Free Astrology Reading for Love')}
                description={t('horoscope.compatibility.description', 'Discover true zodiac compatibility through complete birth chart analysis. Reveal your relationship\'s strengths, challenges, and deeper purpose with our free astrology reading.')}
                icon={
                  <svg className={`w-10 h-10 ${isDark ? 'text-amber-500' : 'text-amber-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                  </svg>
                }
                onClick={() => navigate(`/horoscope/${selectedSign}-love-horoscope`)}
                isFullWidth={true}
              />
            </div>
          </div>

          {/* The Celestial Compass Difference 板块 */}
          <div className="mt-36 max-w-6xl mx-auto px-4 sm:px-6">
            <div className="text-center mb-16">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                isDark ? 'text-white' : 'text-purple-800'
              }`}>
                {t('horoscope.difference.title', 'The Celestial Compass Difference')}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
              <div className="flex items-center justify-center">
                <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t('horoscope.difference.tag1', 'Accurate Astrology Reading')}</span>
                <span className="mx-2 text-purple-500">✦</span>
                <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t('horoscope.difference.tag2', 'Personalized Horoscope')}</span>
                <span className="mx-2 text-purple-500">✦</span>
                <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t('horoscope.difference.tag3', 'Professional Astrology')}</span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-10 md:gap-12 lg:gap-12">
              {/* Block 1: Complete Birth Chart Analysis */}
              <div className="relative backdrop-blur-xl p-8 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div className="flex justify-center mb-6">
                  {/* Astrological chart icon */}
                  <div className={`w-20 h-20 rounded-full flex items-center justify-center ${isDark ? 'bg-indigo-900/50' : 'bg-indigo-100'}`}>
                    <svg className={`w-12 h-12 ${isDark ? 'text-indigo-300' : 'text-indigo-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                  </div>
                </div>
                <h3 className={`text-xl font-semibold text-center mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t('horoscope.difference.block1.title', 'Complete Birth Chart Analysis, Not Just Sun Signs')}
                </h3>
                <p className="body-text dark:text-gray-300 text-gray-600">
                  {t('horoscope.difference.block1.content', "Generic daily horoscopes only look at your sun sign—one piece of a vast cosmic puzzle. We provide accurate astrology readings using your complete birth chart, including your Moon sign (your emotional world) and your Ascendant (your interface with life). We analyze the critical angles and planetary aspects in today's horoscope and how they interact with your unique zodiac compatibility. This is the difference between a map of the world and a real-time GPS route to your front door.")}
                </p>
              </div>
              
              {/* Block 2: AI-Enhanced Astrology Insights */}
              <div className="relative backdrop-blur-xl p-8 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div className="flex justify-center mb-6">
                  {/* AI + Astrology icon */}
                  <div className={`w-20 h-20 rounded-full flex items-center justify-center ${isDark ? 'bg-purple-900/50' : 'bg-purple-100'}`}>
                    <svg className={`w-12 h-12 ${isDark ? 'text-purple-300' : 'text-purple-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="16 18 22 12 16 6"></polyline>
                      <polyline points="8 6 2 12 8 18"></polyline>
                    </svg>
                  </div>
                </div>
                <h3 className={`text-xl font-semibold text-center mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t('horoscope.difference.block2.title', 'AI-Enhanced Astrology Insights')}
                </h3>
                <p className="body-text dark:text-gray-300 text-gray-600">
                  {t('horoscope.difference.block2.content', "Our proprietary AI, trained on millennia of astrological texts and cross-referenced with real-time planetary data from NASA's Jet Propulsion Laboratory, identifies subtle patterns and complex transit connections that human analysis can miss. It's the ancient art of astrology reading, amplified by the processing power of 21st-century technology, delivering personalized horoscope insights with unparalleled speed and accuracy.")}
                </p>
              </div>
              
              {/* Block 3: Professional Astrologers Review Every Reading */}
              <div className="relative backdrop-blur-xl p-8 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div className="flex justify-center mb-6">
                  {/* Expert astrologer icon */}
                  <div className={`w-20 h-20 rounded-full flex items-center justify-center ${isDark ? 'bg-pink-900/50' : 'bg-pink-100'}`}>
                    <svg className={`w-12 h-12 ${isDark ? 'text-pink-300' : 'text-pink-600'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M17 11l-5-5-5 5"></path>
                      <path d="M17 18l-5-5-5 5"></path>
                    </svg>
                  </div>
                </div>
                <h3 className={`text-xl font-semibold text-center mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t('horoscope.difference.block3.title', 'Professional Astrologers Review Every Reading')}
                </h3>
                <p className="body-text dark:text-gray-300 text-gray-600">
                  {t('horoscope.difference.block3.content', "Technology provides the data, but wisdom provides the meaning. Our AI-generated astrology readings are reviewed, refined, and interpreted by a team of real, professional astrologers. We combine the precision of computational power with the nuance, empathy, and inspiring wisdom that only a human expert can provide. You get astrology guidance that is not only accurate but also deeply resonant and actionable.")}
                </p>
              </div>
            </div>
          </div>

          {/* Your Horoscope is Just the Beginning 板块 */}
          <div className="mt-36 max-w-6xl mx-auto px-4 sm:px-6">
            <div className="text-center mb-16">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                isDark ? 'text-white' : 'text-purple-800'
              }`}>
                {t('horoscope.beginning.title', 'Your Free Horoscope is Just the Beginning')}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
            </div>
            
            <div className="flex flex-col md:flex-row gap-12 md:gap-16 items-center">
              {/* 左侧图片 */}
              <div className="md:w-1/2">
                <div className="rounded-xl overflow-hidden">
                  <CdnLazyImage 
                    src="/images-optimized/daily-fortune/step3-select-tarot.webp" 
                    alt={t('horoscope.beginning.image_alt', 'Multi-dimensional Astrology and Tarot Reading')} 
                    className="w-full h-auto object-cover"
                  />
                </div>
              </div>
              
              {/* 右侧文案 */}
              <div className="md:w-1/2">
                <h3 className={`text-xl font-semibold mb-6 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {t('horoscope.beginning.subtitle', 'Free Tarot Reading & Astrology: The Ultimate Cosmic Partnership')}
                </h3>
                <p className="body-text dark:text-gray-300 text-gray-600 mb-6">
                  {t('horoscope.beginning.description', 'Free tarot card readings and zodiac energies unite for powerful insights. Our AI merges ancient tarot symbolism with precise astrology for personalized guidance across all life dimensions.')}
                </p>
                <ul className="space-y-8">
                  <li className="flex items-start">
                    <div className="mr-3 mt-1">
                      <div className="w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900/60 flex items-center justify-center">
                        <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M12 2v10l4.24 4.24"></path>
                          <circle cx="12" cy="12" r="10"></circle>
                        </svg>
                      </div>
                    </div>
                    <div>
                      <HoverableText 
                        as="h4" 
                        onClick={() => navigate('/daily-fortune')}
                        className="font-medium dark:text-purple-300 text-purple-700 text-lg mb-2"
                      >
                        {t('horoscope.beginning.daily.title', 'Daily Horoscope & Free Tarot Reading')}
                      </HoverableText>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('horoscope.beginning.daily.description', 'Precise daily guidance blending free tarot wisdom with your zodiac\'s planetary influences for actionable insights and today\'s horoscope predictions.')}
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-3 mt-1">
                      <div className="w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900/60 flex items-center justify-center">
                        <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                        </svg>
                      </div>
                    </div>
                    <div>
                      <HoverableText 
                        as="h4" 
                        onClick={() => navigate('/home')}
                        className="font-medium dark:text-purple-300 text-purple-700 text-lg mb-2"
                      >
                        {t('home.tarot_types.ai_tarot.title', 'AI塔罗牌占卜')}
                      </HoverableText>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('home.tarot_types.ai_tarot.description', '结合人工智能与传统塔罗智慧，我们的AI塔罗占卜为您提供个性化、深度的牌阵解读，帮助您洞察生活难题和未来走向。通过高级算法分析牌面关系，提供比传统占卜更全面的视角。')}
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-3 mt-1">
                      <div className="w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900/60 flex items-center justify-center">
                        <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"></circle>
                          <line x1="12" y1="8" x2="12" y2="16"></line>
                          <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>
                      </div>
                    </div>
                    <div>
                      <HoverableText 
                        as="h4" 
                        onClick={() => navigate('/yes-no-tarot')}
                        className="font-medium dark:text-purple-300 text-purple-700 text-lg mb-2"
                      >
                        {t('home.tarot_types.yes_no_tarot.title', '是否塔罗占卜')}
                      </HoverableText>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('home.tarot_types.yes_no_tarot.description', '面对人生关键决策时，我们的是非塔罗能提供清晰指引。只需问一个具体问题，抽一张牌，立即获得宇宙的回应。每张牌都有明确的是与否指向，并辅以深入解释，帮您在人生十字路口找到方向。')}
                      </p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* 添加探索区域 */}
          <div className="spotlight-section py-24 md:py-32 mt-28">
            <div className="max-w-3xl mx-auto px-2 sm:px-4">
              <SpotlightCard
                className="custom-spotlight-card"
                spotlightColor="rgba(0, 229, 255, 0.2)"
              >
                <div className="p-6 sm:p-10 text-center">
                  <h3
                    className="text-2xl md:text-3xl font-semibold mb-4"
                    style={{
                      background: theme === 'light' 
                        ? "none" 
                        : "linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)",
                      WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                      WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                      color: theme === 'light' ? "#000" : "inherit"
                    }}
                  >
                    {t("home.explore_section.title", "探索塔罗牌阅读")}
                  </h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                  } text-lg md:text-xl mb-6 px-1`}>
                    {t("home.explore_section.description", "开始您的塔罗之旅，获取专属于您的塔罗牌阅读")}
                  </p>
                  <div className="flex justify-center">
                    <motion.button
                      onClick={handleStartReading}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-6 py-3 rounded-full"
                      style={{
                        background:
                          theme === 'light'
                            ? "linear-gradient(135deg, rgba(124, 58, 237, 0.7), rgba(168, 85, 247, 0.7))"
                            : "linear-gradient(135deg, rgba(124, 58, 237, 0.8), rgba(168, 85, 247, 0.8))",
                        boxShadow: theme === 'light' 
                          ? "0 0 20px rgba(168, 85, 247, 0.4)"
                          : "0 0 20px rgba(168, 85, 247, 0.5)",
                        color: 'white',
                      }}
                    >
                      {t("home.explore_section.button", "开始阅读")}
                    </motion.button>
                  </div>
                </div>
              </SpotlightCard>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Horoscope; 