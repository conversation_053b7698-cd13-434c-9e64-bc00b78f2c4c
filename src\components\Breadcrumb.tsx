import React from 'react';
// import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import LanguageLink from './LanguageLink';
import HoverableText from './horoscope/HoverableText';

export interface BreadcrumbItem {
  label: string;
  path: string;
  isActive?: boolean;
}

export interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, className = '' }) => {
  // const { t } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <nav aria-label="Breadcrumb" className={`flex ${className}`}>
      <ol className="flex items-center text-base">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <span 
                className={`mx-3 font-bold ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
                aria-hidden="true"
              >
                &gt;
              </span>
            )}
            {item.isActive ? (
              <span 
                className={`font-bold ${isDark ? 'text-purple-300' : 'text-purple-700'}`}
                aria-current="page"
              >
                {item.label}
              </span>
            ) : (
              <LanguageLink to={item.path}>
                <HoverableText as="span">
                  {item.label}
                </HoverableText>
              </LanguageLink>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb; 