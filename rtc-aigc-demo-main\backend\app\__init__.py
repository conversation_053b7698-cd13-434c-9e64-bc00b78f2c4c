from flask import Flask
from flask_cors import CORS
from flask_socketio import SocketIO
import asyncio
import os

# 导入日志模块
from .logger import app_logger

# 初始化SocketIO对象
socketio = SocketIO(cors_allowed_origins="*")

def create_app(config=None):
    app = Flask(__name__)
    
    # 允许跨域请求
    CORS(app)
    
    # 应用默认配置
    app.config.from_mapping(
        SECRET_KEY='dev',
        DEBUG=True,
    )
    
    # 应用自定义配置
    if config:
        app.config.update(config)
    
    # 注册蓝图
    from .api import api_bp
    app.register_blueprint(api_bp)
    
    # 注册错误处理器
    from .error_handler import register_error_handlers
    register_error_handlers(app)
    
    # 初始化SocketIO
    socketio.init_app(app, async_mode='eventlet')
    
    # 导入和注册Socket.IO处理程序
    from . import socket_handlers
    
    # 初始化会话管理器
    @app.before_first_request
    def init_session_manager():
        from .session_manager import SessionManager
        try:
            # 获取会话管理器实例
            session_manager = SessionManager.get_instance()
            # 启动清理任务
            asyncio.run(session_manager.start_cleanup_task())
            app_logger.info("会话管理器已初始化")
        except Exception as e:
            app_logger.error(f"初始化会话管理器失败: {str(e)}")
    
    app_logger.info(f"应用创建完成，运行模式: {'调试' if app.debug else '生产'}")
    
    return app 