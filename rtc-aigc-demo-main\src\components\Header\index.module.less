/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

.header {
  height: 48px;
  background: white;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;

  :global {
    .arco-popover-content-top {
      padding: 0px;
    }
  }
}

.header-logo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 24px;

  :global {
    img {
      height: 24px;
    }
    .arco-popover-content {
      padding: 0;
    }
  }
}

.menu-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 8px;
  justify-content: space-between;
}

.header-logo-text {
  background: linear-gradient(90deg, #004FFF 38.86%, #9865FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: 16px;
}

.header-right {
  z-index: 2;
  color: #fff;
  display: flex;
  align-items: center;
  :global {
    span {
      height: 24px;
    }
  }
}

.header-setting-btn {
  background-color: transparent;
  border: none;
  margin-right: 24px;
  color: #000000;
  font-size: 16px;
  cursor: pointer;
}

.header-pop {
  :global {
    .ant-popover-arrow {
      left: 16px;
      .ant-popover-arrow-content {
        &:before {
          background-color: white;
        }
      }
    }
    .ant-popover-content {
      margin-left: 12px;
    }
    .ant-popover-inner {
      margin-right: 12px;
    }
    .ant-popover-inner-content {
      padding: 0;
      background-color: white;
      position: relative;
      width: 100px;
      height: 100px;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: space-between;
      padding-bottom: 7px;
      padding-top: 7px;
      cursor: pointer;
      color: black;

      div {
        font-size: 13px;
        font-weight: 400;
        line-height: 20px;
        &:hover {
          color: #1664ff;
        }
      }
    }
  }
}

.divider {
  margin-top: 2px;
  margin-bottom: 2px;
  min-width: 70%;
  width: 70%;
}

.header-right-text {
  color: #000000;
  margin-right: 24px;
  cursor: pointer;
}
