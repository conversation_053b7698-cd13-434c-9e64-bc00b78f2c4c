const AlipaySdk = require('alipay-sdk').default;
const AlipayFormData = require('alipay-sdk/lib/form').default;
const crypto = require('crypto');
require('dotenv').config();
const { getConnection } = require('../../services/database');

class AlipayService {
  constructor() {
    // 确保公钥格式正确
    let alipayPublicKey = process.env.ALIPAY_PUBLIC_KEY || '';
    // console.log('原始公钥:', alipayPublicKey);
    
    try {
      // 1. 移除所有换行和头尾标记
      alipayPublicKey = alipayPublicKey
        .replace(/-----BEGIN PUBLIC KEY-----|-----END PUBLIC KEY-----|\n|\r/g, '')
        .trim();
      
      // console.log('清理后的公钥:', alipayPublicKey);
      
      // 2. 按64字符分组
      const chunks = alipayPublicKey.match(/.{1,64}/g) || [];
      // console.log('分组后的公钥:', chunks);
      
      // 3. 重新组装PEM格式
      alipayPublicKey = [
        '-----BEGIN PUBLIC KEY-----',
        ...chunks,
        '-----END PUBLIC KEY-----'
      ].join('\n');
      
      // console.log('最终格式化的公钥:\n', alipayPublicKey);
      
    //   // 4. 验证公钥格式是否正确
    //   try {
    //     crypto.createPublicKey(alipayPublicKey);
    //     console.log('公钥格式验证成功');
    //   } catch (error) {
    //     console.error('公钥格式无效:', error.message);
    //     throw error;
    //   }
    } catch (error) {
      // console.error('公钥格式化失败:', error);
      throw error;
    }
    
    this.alipaySdk = new AlipaySdk({
      appId: process.env.ALIPAY_APP_ID,
      privateKey: process.env.ALIPAY_PRIVATE_KEY.replace(/\\n/g, '\n'),
      alipayPublicKey: alipayPublicKey,
      gateway: 'https://openapi.alipay.com/gateway.do',
      charset: 'utf-8',
      version: '1.0',
      signType: 'RSA2',
      camelcase: false // 设置为 false，保持原始的下划线格式
    });
    
    // 测试模式标志
    this.isTestMode = process.env.NODE_ENV !== 'production';
  }

  async createPageOrder(orderData) {
    try {
      console.log('=== 开始创建支付宝订单 ===');
      console.log('订单数据:', orderData);
      const { amount, productName, orderId } = orderData;

      // 构造支付参数
      const bizContent = {
        out_trade_no: orderId,
        total_amount: amount.toFixed(2),
        subject: productName,
        product_code: 'FAST_INSTANT_TRADE_PAY'
      };
      console.log('构造支付参数:', bizContent);

      // 使用 SDK 生成支付链接
      const formData = new AlipayFormData();
      formData.setMethod('get');
      formData.addField('bizContent', bizContent);
      formData.addField('returnUrl', 'https://tarotqa.com/membership');
      formData.addField('notifyUrl', 'https://tarotqa.com/api/payment/alipay/notify');

      // 生成支付链接
      const result = await this.alipaySdk.exec(
        'alipay.trade.page.pay',
        {},
        { formData: formData }
      );

      console.log('支付宝返回结果:', result);

      return {
        orderId: orderId,
        payUrl: result,
        paymentMethod: 'alipay'
      };
    } catch (error) {
      console.error('创建支付宝订单失败:', error);
      throw error;
    }
  }

  /**
   * 创建移动端支付宝支付订单
   * @param {object} orderData 订单数据
   * @returns {Promise<object>} 支付结果
   */
  async createWapOrder(orderData) {
    try {
      console.log('=== 开始创建支付宝移动端订单 ===');
      console.log('订单数据:', orderData);
      const { amount, productName, orderId } = orderData;

      // 构造支付参数
      const bizContent = {
        out_trade_no: orderId,
        total_amount: amount.toFixed(2),
        subject: productName,
        product_code: 'QUICK_WAP_WAY' // 移动端支付产品码
      };
      console.log('构造支付参数:', bizContent);

      // 使用 SDK 生成支付链接
      const formData = new AlipayFormData();
      formData.setMethod('get');
      formData.addField('bizContent', bizContent);
      formData.addField('returnUrl', 'https://tarotqa.com/membership');
      formData.addField('notifyUrl', 'https://tarotqa.com/api/payment/alipay/notify');

      // 生成支付链接
      const result = await this.alipaySdk.exec(
        'alipay.trade.wap.pay',
        {},
        { formData: formData }
      );

      console.log('支付宝移动端返回结果:', result);

      return {
        orderId: orderId,
        payUrl: result,
        paymentMethod: 'alipay'
      };
    } catch (error) {
      console.error('创建支付宝移动端订单失败:', error);
      throw error;
    }
  }

  /**
   * 检查订单是否已处理
   * @param {object} order 订单信息
   * @returns {boolean} 是否已处理
   */
  isOrderProcessed(order) {
    return order && (order.status === 'success' || order.status === 'closed');
  }

  /**
   * 更新会员状态
   * @param {object} pool 数据库连接池
   * @param {string} userId 用户ID
   * @param {string} productId 商品ID
   * @param {number} amount 支付金额
   * @param {string} logPrefix 日志前缀
   */
  async updateMembershipStatus(pool, userId, productId, amount, logPrefix) {
    try {
      // 先查询用户当前的会员状态和订单信息
      const [userRows] = await pool.query(
        'SELECT vip_status, vip_type, vip_end_date FROM users WHERE id = ?',
        [userId]
      );
      
      // 获取产品名称
      const [productRows] = await pool.query(
        'SELECT product_name FROM payment_orders WHERE user_id = ? AND product_id = ? ORDER BY created_at DESC LIMIT 1',
        [userId, productId]
      );
      
      const productName = productRows.length > 0 ? productRows[0].product_name : '';
      
      const currentUser = userRows[0];

      // 处理按次付费
      if (productId.includes('reads') || productId === 'pay_per_use') {
        await this.handlePayPerUse(pool, userId, amount, logPrefix, productName);
        return;
      }

      // 处理会员订阅
      await this.handleMembershipSubscription(pool, userId, productId, currentUser, logPrefix);
    } catch (error) {
      console.error(`${logPrefix} 更新会员状态失败:`, error);
      throw error;
    }
  }

  /**
   * 处理按次付费
   */
  async handlePayPerUse(pool, userId, amount, logPrefix, productName) {
    console.log(`${logPrefix} 检测到按次付费订单`);
    
    // 从商品名称中解析购买次数
    const readCount = parseInt(productName?.split('-').pop()) || 1;
    console.log(`${logPrefix} 获取购买次数:`, {
      productName,
      readCount
    });
    
    if (readCount <= 0) return;

    // 查询当前剩余次数
    const [userRows] = await pool.query(
      'SELECT remaining_reads, has_internal_privilege, has_used_discount FROM users WHERE id = ?',
      [userId]
    );
    const currentReads = userRows[0]?.remaining_reads || 0;

    console.log(`${logPrefix} 更新前用户信息:`, {
      userId,
      currentReads,
      readCount,
      newTotal: currentReads + readCount
    });

    // 更新剩余次数
    await pool.query(
      `UPDATE users 
       SET remaining_reads = remaining_reads + ?,
           vip_status = 'none',
           vip_type = 'none',
           vip_start_date = NULL,
           vip_end_date = NULL
       WHERE id = ?`,
      [readCount, userId]
    );

    // 验证更新
    const [verifyRows] = await pool.query(
      'SELECT remaining_reads FROM users WHERE id = ?',
      [userId]
    );
    console.log(`${logPrefix} 更新后用户信息:`, {
      userId,
      newReads: verifyRows[0]?.remaining_reads
    });
    
    // 检查用户是否有内部折扣权限且未使用过
    const hasInternalPrivilege = userRows[0]?.has_internal_privilege === 1;
    const hasUsedDiscount = userRows[0]?.has_used_discount === 1;
    
    if (hasInternalPrivilege && !hasUsedDiscount) {
      console.log(`${logPrefix} 用户有内部折扣权限且未使用过，标记订单和用户`);
      
      // 查询最近的订单ID
      const [orderRows] = await pool.query(
        `SELECT order_id FROM payment_orders 
         WHERE user_id = ? AND status = 'success' 
         ORDER BY created_at DESC LIMIT 1`,
        [userId]
      );
      
      if (orderRows.length > 0) {
        const orderId = orderRows[0].order_id;
        
        // 更新订单的discount_applied字段
        await pool.query(
          `UPDATE payment_orders SET discount_applied = 1 WHERE order_id = ?`,
          [orderId]
        );
        
        // 更新用户的has_used_discount字段
        await pool.query(
          `UPDATE users SET has_used_discount = 1 WHERE id = ?`,
          [userId]
        );
        
        console.log(`${logPrefix} 内部折扣标记完成，订单ID: ${orderId}`);
      }
    }
  }

  /**
   * 处理会员订阅
   */
  async handleMembershipSubscription(pool, userId, productId, currentUser, logPrefix) {
    const vipType = productId.includes('year') ? 'yearly' : 'monthly';
    const membershipDays = vipType === 'yearly' ? 365 : 30;
    
    // 查询用户的内部折扣信息
    const [userRows] = await pool.query(
      'SELECT has_internal_privilege, has_used_discount FROM users WHERE id = ?',
      [userId]
    );

    const newEndDate = this.calculateNewEndDate(currentUser, vipType, membershipDays);
    const formattedEndDate = newEndDate.toISOString().slice(0, 19).replace('T', ' ');

    // 更新会员状态
    await pool.query(
      `UPDATE users 
       SET vip_status = 'active',
           vip_type = ?,
           vip_start_date = CURRENT_TIMESTAMP,
           vip_end_date = ?
       WHERE id = ?`,
      [vipType, formattedEndDate, userId]
    );

    // 验证更新
    const [verifyRows] = await pool.query(
      'SELECT vip_status, vip_type, vip_end_date FROM users WHERE id = ?',
      [userId]
    );
    console.log(`${logPrefix}: 会员已激活，到期时间 ${verifyRows[0]?.vip_end_date}`);
    
    // 检查用户是否有内部折扣权限且未使用过
    const hasInternalPrivilege = userRows[0]?.has_internal_privilege === 1;
    const hasUsedDiscount = userRows[0]?.has_used_discount === 1;
    
    if (hasInternalPrivilege && !hasUsedDiscount) {
      console.log(`${logPrefix} 用户有内部折扣权限且未使用过，标记订单和用户`);
      
      // 查询最近的订单ID
      const [orderRows] = await pool.query(
        `SELECT order_id FROM payment_orders 
         WHERE user_id = ? AND status = 'success' 
         ORDER BY created_at DESC LIMIT 1`,
        [userId]
      );
      
      if (orderRows.length > 0) {
        const orderId = orderRows[0].order_id;
        
        // 更新订单的discount_applied字段
        await pool.query(
          `UPDATE payment_orders SET discount_applied = 1 WHERE order_id = ?`,
          [orderId]
        );
        
        // 更新用户的has_used_discount字段
        await pool.query(
          `UPDATE users SET has_used_discount = 1 WHERE id = ?`,
          [userId]
        );
        
        console.log(`${logPrefix} 内部折扣标记完成，订单ID: ${orderId}`);
      }
    }
  }

  /**
   * 计算新的到期日期
   */
  calculateNewEndDate(currentUser, targetVipType, membershipDays) {
    let newEndDate;
    
    if (currentUser && currentUser.vip_status === 'active' && currentUser.vip_end_date) {
      const currentEndDate = new Date(currentUser.vip_end_date);
      
      if (currentUser.vip_type === 'monthly' && targetVipType === 'yearly') {
        // 月度会员升级到年度会员
        newEndDate = new Date(currentEndDate);
        newEndDate.setFullYear(newEndDate.getFullYear() + 1);
      } else {
        // 普通续费
        newEndDate = new Date(currentEndDate);
        newEndDate.setDate(newEndDate.getDate() + membershipDays);
      }
    } else {
      // 新会员
      newEndDate = new Date();
      newEndDate.setDate(newEndDate.getDate() + membershipDays);
    }
    
    return newEndDate;
  }

  async queryOrder(outTradeNo) {
    try {
      console.log(`[支付宝查询] ${outTradeNo} 开始查询`);
      
      // 先查询数据库中的订单状态
      const pool = await getConnection();
      const [orders] = await pool.query(
        'SELECT status, trade_status, user_id, product_id, product_name, amount FROM payment_orders WHERE order_id = ?',
        [outTradeNo]
      );
      
      console.log(`[支付宝查询] ${outTradeNo} 数据库订单状态:`, {
        hasOrder: orders.length > 0,
        orderStatus: orders[0]?.status,
        tradeStatus: orders[0]?.trade_status
      });

      // 检查订单是否已处理
      if (orders.length > 0 && this.isOrderProcessed(orders[0])) {
        console.log(`[支付宝查询] ${outTradeNo} 订单已处理完成，无需查询支付宝`);
        return {
          success: true,
          tradeState: orders[0].trade_status,
          trade_status: orders[0].trade_status
        };
      }
      
      // 查询支付宝API
      console.log(`[支付宝查询] ${outTradeNo} 开始查询支付宝API`);
      const result = await this.alipaySdk.exec('alipay.trade.query', {
        bizContent: {
          out_trade_no: outTradeNo
        }
      });

      console.log(`[支付宝查询] ${outTradeNo} 支付宝API返回:`, {
        code: result.code,
        msg: result.msg,
        subCode: result.sub_code,
        subMsg: result.sub_msg,
        tradeStatus: result.trade_status
      });

      // 检查API响应
      if (!result.code || result.code === '10000') {
        console.log(`[支付宝查询] ${outTradeNo} 查询成功:`, {
          trade_status: result.trade_status,
          total_amount: result.total_amount,
          payment_time: result.send_pay_date
        });

        // 如果订单支付成功，更新数据库
        if (result.trade_status === 'TRADE_SUCCESS' || result.trade_status === 'TRADE_FINISHED') {
          console.log(`[支付宝查询] ${outTradeNo} 订单已支付成功，检查用户状态更新`);
          
          if (orders.length > 0) {
            const { user_id, product_id, amount } = orders[0];
            await this.updateMembershipStatus(pool, user_id, product_id, amount, `[支付宝查询] ${outTradeNo}`);
          }

          const updateData = {
            trade_no: result.trade_no,
            trade_status: result.trade_status,
            trade_state_desc: '支付宝支付成功',
            buyer_logon_id: result.buyer_logon_id,
            buyer_user_id: result.buyer_user_id,
            status: 'success',
            success_time: result.send_pay_date,
            trade_time: result.send_pay_date,
            currency: 'CNY',
            payer_currency: 'CNY'
          };

          const sql = `
            UPDATE payment_orders 
            SET ? 
            WHERE order_id = ? AND status = 'pending'
          `;

          const [updateResult] = await pool.query(sql, [updateData, outTradeNo]);
          if (updateResult.affectedRows > 0) {
            console.log(`[支付宝查询] ${outTradeNo} 订单状态已更新为支付成功`);
          }
        }

        return {
          success: true,
          tradeState: result.trade_status,
          trade_status: result.trade_status,
          total_amount: result.total_amount,
          payment_time: result.send_pay_date
        };
      } else {
        console.error(`[支付宝查询] ${outTradeNo} 查询失败:`, result);
        return {
          success: false,
          message: result.msg || result.subMsg || '查询订单失败'
        };
      }
    } catch (error) {
      console.error(`[支付宝查询] ${outTradeNo} 查询异常:`, error);
      return {
        success: false,
        message: error.message || '查询订单发生异常'
      };
    }
  }

  async closeOrder(orderId) {
    try {
      console.log('=== 开始关闭支付宝订单 ===');
      console.log('订单号:', orderId);
      
      const result = await this.alipaySdk.exec('alipay.trade.close', {
        bizContent: {
          out_trade_no: orderId
        }
      });

      console.log('支付宝关闭订单结果:', result);

      // 更新数据库中的订单状态的函数
      const updateOrderStatus = async (status, desc) => {
        try {
          const pool = await getConnection();
          const updateSql = `
            UPDATE payment_orders 
            SET status = ?,
                trade_status = ?,
                trade_state_desc = ?,
                updated_at = NOW()
            WHERE order_id = ? AND status = 'pending'
          `;
          
          const [updateResult] = await pool.query(updateSql, [status, status === 'closed' ? 'TRADE_CLOSED' : 'TRADE_NOT_EXIST', desc, orderId]);
          
          if (updateResult.affectedRows > 0) {
            console.log(`订单 ${orderId} 状态已更新为 ${status}`);
          } else {
            console.log(`订单 ${orderId} 状态更新失败，可能订单不存在或已处理`);
          }
        } catch (dbError) {
          console.error('更新订单状态失败:', dbError);
        }
      };

      if (result.code === '10000') {
        console.log('订单关闭成功');
        await updateOrderStatus('closed', '订单已关闭');
        return { success: true };
      }
      
      // 处理交易不存在的情况
      if (result.code === '40004' && result.sub_code === 'ACQ.TRADE_NOT_EXIST') {
        console.log('订单不存在，将更新本地订单状态');
        await updateOrderStatus('closed', '交易不存在，订单已关闭');
        return { success: true };
      }

      console.log('订单关闭失败:', result.sub_msg || result.msg);
      return { 
        success: false, 
        error: result.sub_msg || result.msg
      };
    } catch (error) {
      console.error('关闭支付宝订单发生错误:', {
        orderId,
        errorMessage: error.message,
        errorStack: error.stack
      });
      throw error;
    }
  }

  buildSignStr(params) {
    try {
      // 去除 sign 和 sign_type
      const signParams = { ...params };
      delete signParams.sign;
      delete signParams.sign_type;

      // URL decode
      const decodedParams = {};
      for (const key of Object.keys(signParams)) {
        if (typeof signParams[key] === 'string') {
          try {
            decodedParams[key] = decodeURIComponent(signParams[key]);
          } catch (error) {
            decodedParams[key] = signParams[key];
          }
        } else {
          decodedParams[key] = signParams[key];
        }
      }

      // 字典序排序并组装待签名字符串
      const keys = Object.keys(decodedParams).sort();
      const signStr = keys.map(key => `${key}=${decodedParams[key]}`).join('&');

      return signStr;
    } catch (error) {
      console.error('构建待签名字符串时发生错误:', error);
      throw error;
    }
  }

  async verifyCallback(params) {
    try {
      const { out_trade_no, charset, sign_type } = params;

      // 基本参数验证
      if (charset !== 'utf-8') {
        console.error(`[支付宝验签] ${out_trade_no}: 字符集不匹配 ${charset}`);
        return false;
      }

      if (sign_type !== 'RSA2') {
        console.error(`[支付宝验签] ${out_trade_no}: 签名类型不匹配 ${sign_type}`);
        return false;
      }

      // 测试模式跳过验证
      if (this.isTestMode && out_trade_no?.startsWith('test_')) {
        console.log(`[支付宝验签] ${out_trade_no}: 测试模式跳过验证`);
        return true;
      }

      // 生成待签名字符串并验证
      const signStr = this.buildSignStr(params);
      const signature = Buffer.from(params.sign, 'base64');

      // 使用 Node.js 原生方式验证
      try {
        const verify = crypto.createVerify('RSA-SHA256');
        verify.update(signStr, 'utf8');
        const verifyResult = verify.verify(this.alipaySdk.config.alipayPublicKey, signature);

        if (verifyResult) {
          return true;
        }
      } catch (error) {
        console.error(`[支付宝验签] ${out_trade_no}: 原生验签失败`, error.message);
      }

      // SDK 验证作为备选
      try {
        const signVerified = await this.alipaySdk.checkNotifySign(params);
        if (signVerified) {
          return true;
        }
      } catch (error) {
        console.error(`[支付宝验签] ${out_trade_no}: SDK验签失败`, error.message);
      }

      console.error(`[支付宝验签] ${out_trade_no}: 所有验签方式都失败`);
      return false;
    } catch (error) {
      console.error('验证支付宝回调签名失败:', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  async saveOrderToDatabase(notifyData) {
    const {
      out_trade_no,
      trade_no,
      trade_status,
      total_amount,
      buyer_logon_id,
      buyer_user_id,
      gmt_create,
      gmt_payment,
      notify_id,
      notify_time
    } = notifyData;

    try {
      const pool = await getConnection();
      
      // 查询订单信息
      const [orderRows] = await pool.query(
        'SELECT user_id, product_id, product_name, amount, status FROM payment_orders WHERE order_id = ?',
        [out_trade_no]
      );

      console.log(`[支付宝通知] ${out_trade_no} 订单查询结果:`, {
        hasOrder: orderRows.length > 0,
        tradeStatus: trade_status,
        orderInfo: orderRows[0]
      });

      // 检查订单是否已处理
      if (orderRows.length > 0 && this.isOrderProcessed(orderRows[0])) {
        console.log(`[支付宝通知] ${out_trade_no}: 订单已处理，跳过`);
        return { success: true, message: '订单已处理' };
      }

      // 检查订单是否存在
      if (orderRows.length === 0) {
        console.error(`[支付宝通知] ${out_trade_no}: 订单不存在`);
        return { success: false, message: '订单不存在' };
      }

      // 处理支付成功的订单
      if (trade_status === 'TRADE_SUCCESS') {
        const { user_id, product_id, amount } = orderRows[0];
        await this.updateMembershipStatus(pool, user_id, product_id, amount, `[支付宝通知] ${out_trade_no}`);
      }

      // 更新订单状态
      const updateData = {
        trade_no,
        trade_status,
        trade_state_desc: '支付宝支付成功',
        buyer_logon_id,
        buyer_user_id,
        gmt_create: new Date(gmt_create),
        gmt_payment: new Date(gmt_payment),
        notify_id,
        notify_time: new Date(notify_time),
        status: trade_status === 'TRADE_SUCCESS' ? 'success' : 'pending',
        success_time: new Date(gmt_payment),
        trade_time: new Date(gmt_payment),
        currency: 'CNY',
        payer_currency: 'CNY'
      };

      const [result] = await pool.query(
        'UPDATE payment_orders SET ? WHERE order_id = ? AND status IN (?, ?)',
        [updateData, out_trade_no, 'pending', 'failed']
      );

      if (result.affectedRows === 0) {
        console.warn(`[支付宝通知] ${out_trade_no}: 订单更新失败，可能已处理`);
        // 查询当前状态用于调试
        const [currentOrderRows] = await pool.query(
          'SELECT status, trade_status FROM payment_orders WHERE order_id = ?',
          [out_trade_no]
        );
        console.log(`[支付宝通知] ${out_trade_no}: 当前状态=${currentOrderRows[0]?.status}`);
      }

      return result;
    } catch (error) {
      console.error(`[支付宝订单] ${out_trade_no} 保存失败:`, error);
      throw error;
    }
  }

  async handlePaymentNotification(params) {
    const { out_trade_no, trade_status, total_amount } = params;

    try {
      // 验证通知签名
      const verifyResult = await this.verifyCallback(params);
      if (!verifyResult) {
        console.error(`[支付宝通知] ${out_trade_no}: 签名验证失败`);
        return { success: false, message: '签名验证失败' };
      }

      // 验证交易状态
      const validTradeStatus = ['TRADE_SUCCESS', 'TRADE_FINISHED'].includes(trade_status);
      if (!validTradeStatus) {
        console.warn(`[支付宝通知] ${out_trade_no}: 交易状态无效 ${trade_status}`);
        return { success: false, message: '交易状态无效' };
      }

      // 验证金额格式
      if (!/^\d+\.\d{2}$/.test(total_amount)) {
        console.error(`[支付宝通知] ${out_trade_no}: 金额格式错误 ${total_amount}`);
        return { success: false, message: '金额格式错误' };
      }

      // 保存订单到数据库
      await this.saveOrderToDatabase(params);

      return { success: true, message: '成功' };
    } catch (error) {
      console.error(`[支付宝通知] ${out_trade_no}: 处理异常`, error.message);
      throw error;
    }
  }

  /**
   * 确认交易状态
   * @param {string} outTradeNo - 商户订单号
   * @param {object} notifyParams - 通知参数
   * @returns {Promise<boolean>} 是否确认成功
   */
  async confirmTradeStatus(outTradeNo, notifyParams) {
    const logPrefix = `[支付宝订单] ${outTradeNo}`;
    
    try {
      // 1. 检查通知状态
      const notifySuccess = ['TRADE_SUCCESS', 'TRADE_FINISHED'].includes(notifyParams.trade_status);
      console.log(`${logPrefix} 通知状态: ${notifyParams.trade_status} (${notifySuccess ? '成功' : '未成功'})`);
      
      if (!notifySuccess) {
        return false;
      }

      // 2. 查询订单状态
      const queryResult = await this.queryOrder(outTradeNo);
      console.log(`${logPrefix} 查询结果:`, {
        trade_status: queryResult.trade_status,
        total_amount: queryResult.total_amount,
        success: queryResult.success
      });

      // 3. 验证订单金额
      if (queryResult.total_amount !== notifyParams.total_amount) {
        console.error(`${logPrefix} 订单金额不匹配:`, {
          notify: notifyParams.total_amount,
          query: queryResult.total_amount
        });
        return false;
      }

      // 4. 确认最终状态
      const finalSuccess = queryResult.success && notifySuccess;
      
      if (finalSuccess) {
        console.log(`${logPrefix} 交易成功确认`, {
          notify_time: notifyParams.notify_time,
          payment_time: notifyParams.gmt_payment,
          amount: notifyParams.total_amount
        });
      } else {
        console.warn(`${logPrefix} 交易状态不一致`, {
          notify_status: notifyParams.trade_status,
          query_status: queryResult.trade_status
        });
      }

      return finalSuccess;
    } catch (error) {
      console.error(`${logPrefix} 确认交易状态异常:`, error);
      return false;
    }
  }
}

module.exports = new AlipayService(); 