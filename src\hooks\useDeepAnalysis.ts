import { useCallback } from 'react';
import { generateDeepAnalysis } from '../services/deepseekService';

// 定义Message接口
interface Message {
  type: 'user' | 'reader';
  content: string;
  className?: string;
  isParagraph?: boolean;
  id?: string;
}

// 辅助函数：移除段落标识文本的辅助函数，用于清除如prologue:, answer:, analysis1:等前缀
const removeSectionPrefix = (text: string): string => {
  // 匹配常见的段落前缀，如prologue:, answer:, analysis1:, summary:, advice1:等
  const prefixRegex = /^(prologue|answer|analysis\d*|summary|advice\d*|record):\s*/i;
  return text.replace(prefixRegex, '').trim();
};

interface UseDeepAnalysisProps {
  user: any;
  navigate: any;
  hasUsedDeepAnalysis: boolean;
  isGeneratingDeepAnalysis: boolean;
  t: any; // i18n translate function
  setShowVipPrompt: (show: boolean) => void;
  setIsGeneratingDeepAnalysis: (isGenerating: boolean) => void;
  setProgressBarCompleted: (completed: boolean) => void;
  setReceivingStreamContent: (receiving: boolean) => void;
  setMessages: (updater: React.SetStateAction<Message[]>) => void;
  setShouldAutoScroll: (shouldScroll: boolean) => void;
  messagesContainerRef: React.RefObject<HTMLDivElement>;
  setDeepAnalysisLoading: (loading: boolean) => void;
  startDeepAnalysisProgress: () => void;
  markDeepAnalysisReceived: () => void;
  completeDeepAnalysisProgress: (callback?: () => void) => void;
  progressBarCompleted: boolean;
  receivingStreamContent: boolean;
  deepAnalysisLoading: boolean;
  getFontClass: () => string;
  setShowDeepAnalysis: (show: boolean) => void;
  setHasUsedDeepAnalysis: (hasUsed: boolean) => void;
}

export const useDeepAnalysis = (props: UseDeepAnalysisProps) => {
  const {
    user,
    navigate,
    hasUsedDeepAnalysis,
    isGeneratingDeepAnalysis,
    t,
    setShowVipPrompt,
    setIsGeneratingDeepAnalysis,
    setProgressBarCompleted,
    setReceivingStreamContent,
    setMessages,
    setShouldAutoScroll,
    messagesContainerRef,
    setDeepAnalysisLoading,
    startDeepAnalysisProgress,
    markDeepAnalysisReceived,
    completeDeepAnalysisProgress,
    progressBarCompleted,
    receivingStreamContent,
    deepAnalysisLoading,
    getFontClass,
    setShowDeepAnalysis,
    setHasUsedDeepAnalysis
  } = props;

  return useCallback(async () => {
    if (!user) {
      navigate('/login');
      return;
    }

    // 非VIP用户且已使用过深度解析，直接显示VIP提示
    if (user.vipStatus !== 'active' && hasUsedDeepAnalysis) {
      setShowVipPrompt(true);
      return;
    }

    if (isGeneratingDeepAnalysis) return;

    try {
      // 重置状态
      setIsGeneratingDeepAnalysis(true);
      setProgressBarCompleted(false);
      setReceivingStreamContent(false);

      setMessages(prev => [...prev, {
        type: 'user' as const,
        content: t('reading.deep_analysis.request')
      }]);
      // 设置自动滚动到用户提问位置
      setShouldAutoScroll(true);

      // 对于非VIP用户显示特殊提示
      if (user.vipStatus !== 'active') {
        // 删除等待消息
        /*
        setMessages(prev => [...prev, {
          type: 'reader' as const,
          content: t('reading.deep_analysis.waiting'),
          className: getFontClass()
        }]);
        */
      } else {
        // 删除等待消息
        /*
        setMessages(prev => [...prev, {
          type: 'reader' as const,
          content: t('reading.deep_analysis.waiting'),
          className: getFontClass()
        }]);
        */
      }
      // 确保会自动滚动到底部显示等待消息
      setShouldAutoScroll(true);

      // 在添加消息后延迟一点时间执行滚动，确保DOM已更新
      setTimeout(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
      }, 100);

      setDeepAnalysisLoading(true);
      startDeepAnalysisProgress();

      // 获取初步解读的 analysis 内容
      const sessionId = localStorage.getItem('sessionId');
      const initialAnalysis = sessionId ? localStorage.getItem(`${sessionId}_initial_analysis`) : null;

      // 创建一个 abort 控制器，用于中断请求
      const abortController = new AbortController();
      
      // 追踪已接收的段落，防止重复
      let receivedParagraphs: string[] = [];

      // 初始化消息，移除loading消息，但保留所有基础解读内容
      setMessages(prev => {
        // 移除所有加载中的消息
        const messagesWithoutLoading = prev.filter(msg => 
          !msg.className?.includes('loading-message')
        );
        
        // 返回所有非loading的消息，保留所有基础解读段落
        return messagesWithoutLoading;
      });

      // 创建一个流式处理回调
      const handleStreamContent = (content: string, isParagraphComplete?: boolean) => {
        // 检查进度条是否已达到100%，如果没有，先等待进度条完成
        if (!progressBarCompleted) {
          // 标记API已接收到响应
          markDeepAnalysisReceived();
          
          // 快速完成进度条
          completeDeepAnalysisProgress(() => {
            setProgressBarCompleted(true);
            
            // 现在可以开始处理内容了
            processStreamContent(content, isParagraphComplete);
          });
          return;
        }
        
        // 如果进度条已完成，直接处理内容
        processStreamContent(content, isParagraphComplete);
      };
      
      // 实际处理流式内容的函数
      const processStreamContent = (content: string, isParagraphComplete?: boolean) => {
        // 标记开始接收流式内容，此时应该显示loading-message动画，而不是圆形进度条
        if (!receivingStreamContent) {
          setReceivingStreamContent(true);
          
          // 初次接收到内容时，添加loading-message占位符
          setMessages(prev => {
            // 检查是否已经有loading-message
            const hasLoadingMessage = prev.some(msg => msg.className?.includes('loading-message'));
            if (!hasLoadingMessage) {
              return [...prev, {
                type: 'reader',
                content: '',
                className: 'font-sans loading-message'
              }];
            }
            return prev;
          });
        }
        
        // 如果是完整段落，直接显示（这是由服务端确定的完整段落）
        if (isParagraphComplete) {
          // 仅当该段落不在已接收列表中才处理
          if (!receivedParagraphs.includes(content)) {
            // console.log('处理新的完整段落:', content.substring(0, 20) + '...');
            receivedParagraphs.push(content);
            
            // 移除段落标识文本
            const cleanedContent = removeSectionPrefix(content);
            
            // 更新消息显示，添加带读者头像的新段落
            setMessages(prev => {
              // 移除加载中的消息
              const messagesWithoutLoading = prev.filter(msg => !msg.className?.includes('loading-message'));
              
              // 返回更新后的消息数组：原有消息 + 新段落 + 加载动画
              return [...messagesWithoutLoading, {
                type: 'reader',
                content: cleanedContent,
                className: 'font-sans',
                isParagraph: true  // 使用isParagraph标记，确保段落使用带头像的样式
              }, {
                type: 'reader',
                content: '',
                className: 'font-sans loading-message'
              }];
            });
            
            // 深度解析时不自动滚动
            // setShouldAutoScroll(true);
          } else {
            // console.log('跳过已处理的段落:', content.substring(0, 20) + '...');
            
            // 即使跳过这个段落，也要确保加载动画继续显示
            setMessages(prev => {
              // 检查是否已存在加载动画
              const hasLoadingMessage = prev.some(msg => msg.className?.includes('loading-message'));
              if (!hasLoadingMessage) {
                return [...prev, {
                  type: 'reader',
                  content: '',
                  className: 'font-sans loading-message'
                }];
              }
              return prev;
            });
          }
        } else {
          // 非完整段落内容不直接显示在此处处理
          
          // 确保加载动画一直显示
          setMessages(prev => {
            // 检查是否已存在加载动画
            const hasLoadingMessage = prev.some(msg => msg.className?.includes('loading-message'));
            if (!hasLoadingMessage) {
              return [...prev, {
                type: 'reader',
                content: '',
                className: 'font-sans loading-message'
              }];
            }
            return prev;
          });
        }
      };

      try {
        // 使用流式API获取深度解析
        const analysis = await generateDeepAnalysis(
          initialAnalysis, 
          abortController.signal,
          handleStreamContent // 传入流处理回调
        );

        if (analysis === 'REQUEST_CANCELED') {
          setIsGeneratingDeepAnalysis(false);
          setDeepAnalysisLoading(false);
          setReceivingStreamContent(false);
          setProgressBarCompleted(false);
          // 确保移除所有加载动画
          setMessages(prev => prev.filter(msg => !msg.className?.includes('loading-message')));
          return;
        }

        // 流式API完成
        completeDeepAnalysisProgress(() => {
          // 在进度完成的回调中确保所有状态都被重置
          setDeepAnalysisLoading(false);
          setReceivingStreamContent(false);
          setProgressBarCompleted(false);
          setIsGeneratingDeepAnalysis(false);
          // 确保移除所有加载动画
          setMessages(prev => prev.filter(msg => !msg.className?.includes('loading-message')));
        });
        
        try {
          // 检查是否是错误响应
          const parsedResponse = JSON.parse(analysis);
          if (parsedResponse.error && parsedResponse.errorType === 'NO_FREE_ANALYSIS') {
            // 用户已使用过免费深度解析，显示VIP提示
            setShowVipPrompt(true);
            
            // 显示错误消息
            setMessages(prev => {
              // 移除加载中的消息
              const messagesWithoutLoading = prev.filter(msg => !msg.className?.includes('loading-message'));
              
              return [...messagesWithoutLoading, {
                type: 'reader' as const,
                content: parsedResponse.message,
                className: 'font-sans japanese'
              }];
            });
            
            return;
          }

          // 设置已完成深度解析的状态
          setShowDeepAnalysis(true);
        } catch (parseError) {
          // 可能已经通过流式处理显示了内容，只需在控制台记录错误
          // console.error('Error parsing analysis response:', parseError);
          
          // 如果没有接收到任何内容，显示错误消息
          if (receivedParagraphs.length === 0) {
            setMessages(prev => {
              // 移除加载中的消息
              const messagesWithoutLoading = prev.filter(msg => !msg.className?.includes('loading-message'));
              
              return [...messagesWithoutLoading, {
                type: 'reader' as const,
                content: t('reading.error.deep_analysis_failed'),
                className: 'font-sans japanese'
              }];
            });
          }
        }
      } catch (streamError: any) {
        // console.error('Stream request error:', streamError);
        
        // 显示错误消息
        setMessages(prev => {
          // 移除加载中的消息
          const messagesWithoutLoading = prev.filter(msg => !msg.className?.includes('loading-message'));
          
          return [...messagesWithoutLoading, {
            type: 'reader' as const,
            content: t('reading.error.deep_analysis_failed') + (streamError?.message ? `: ${streamError.message}` : ''),
            className: 'font-sans japanese'
          }];
        });
        
        // 中断请求
        abortController.abort();
      }
    } catch (error) {
      // console.error('Error generating deep analysis:', error);
      setMessages(prev => {
        // 移除加载中的消息
        const messagesWithoutLoading = prev.filter(msg => !msg.className?.includes('loading-message'));
        
        return [...messagesWithoutLoading, {
          type: 'reader' as const,
          content: t('reading.error.deep_analysis_failed'),
          className: 'font-sans japanese'
        }];
      });
    } finally {
      completeDeepAnalysisProgress(() => {
        setDeepAnalysisLoading(false);
        setReceivingStreamContent(false); 
        setProgressBarCompleted(false);
        setIsGeneratingDeepAnalysis(false);
      
        // 确保再次移除所有加载动画
        setMessages(prev => prev.filter(msg => !msg.className?.includes('loading-message')));
        
        // 在每次生成完成后，保存会话状态
        const sessionId = localStorage.getItem('sessionId');
        if (sessionId) {
          localStorage.setItem(`${sessionId}_analysisShown`, 'true');
        }
      });
    }
  }, [
    user,
    navigate,
    hasUsedDeepAnalysis,
    isGeneratingDeepAnalysis,
    t,
    setShowVipPrompt,
    setIsGeneratingDeepAnalysis,
    setProgressBarCompleted,
    setReceivingStreamContent,
    setMessages,
    setShouldAutoScroll,
    messagesContainerRef,
    setDeepAnalysisLoading,
    startDeepAnalysisProgress,
    markDeepAnalysisReceived,
    completeDeepAnalysisProgress,
    progressBarCompleted,
    receivingStreamContent,
    deepAnalysisLoading,
    getFontClass,
    setShowDeepAnalysis,
    setHasUsedDeepAnalysis
  ]);
}; 