import React, { useContext, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useTarotSpeech } from './useTarotSpeech';
import { audioManager } from './audioManager';

// 创建一个全局播放状态上下文
export const PlayingContext = React.createContext<{
  playingId: string | null;
  setPlayingId: (id: string | null) => void;
}>({
  playingId: null,
  setPlayingId: () => {},
});

// 导出Provider组件供外部使用
export const PlayingProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [playingId, setPlayingId] = React.useState<string | null>(null);
  
  return (
    <PlayingContext.Provider value={{ playingId, setPlayingId }}>
      {children}
    </PlayingContext.Provider>
  );
};

// 组件属性类型定义
interface TarotSpeechProps {
  text: string;
  sessionId?: string;
  readerId?: string;
  messageId?: string;
  className?: string;
  disabled?: boolean;
  cacheKey?: string;
  blockType?: string;
  paragraphRef?: React.RefObject<HTMLDivElement>; // 段落引用
  user?: any; // 添加用户信息
  setShowVipPrompt?: (show: boolean) => void; // 添加显示VIP提示的函数
  language?: string; // 添加语言参数
}

/**
 * 语音朗读组件，支持单段落朗读
 */
const TarotSpeech: React.FC<TarotSpeechProps> = ({ 
  text, 
  sessionId,
  readerId,
  messageId,
  className = '',
  disabled = false,
  cacheKey,
  blockType = 'base',
  paragraphRef, // 段落引用
  user,
  setShowVipPrompt,
  language // 语言参数
}) => {
  const { t, i18n } = useTranslation();
  const { playingId, setPlayingId } = useContext(PlayingContext);
  
  // 确保blockType始终为base，不使用basic
  const normalizedBlockType = blockType === 'basic' ? 'base' : blockType;
  
  // 生成唯一的段落标识
  const paragraphUniqueId = React.useMemo(() => {
    return `${sessionId ? sessionId + '_' : ''}${messageId || '未知'}_${normalizedBlockType}`;
  }, [sessionId, messageId, normalizedBlockType]);
  
  // 检查当前段落是否正在播放
  const isThisParagraphPlaying = playingId === paragraphUniqueId;
  
  // 从当前界面语言获取语言代码
  const currentLanguage = React.useMemo(() => {
    if (language) return language;
    
    // 从i18n.language获取语言代码
    if (i18n.language === 'zh-CN' || i18n.language === 'zh-TW') {
      return 'zh';
    } else if (i18n.language === 'en') {
      return 'en';
    } else if (i18n.language === 'ja') {
      return 'ja';
    }
    return 'zh'; // 默认中文
  }, [i18n.language, language]);
  
  // 使用自定义hook处理语音朗读逻辑
  const {
    isPlaying,
    isLoading,
    error,
    togglePlay,
    audioElement,
    setIsPlaying
  } = useTarotSpeech({
    text,
    sessionId,
    readerId,
    messageId,
    cacheKey,
    blockType: normalizedBlockType, // 使用标准化的板块类型
    language: currentLanguage // 传递当前语言
  });

  // 创建组件唯一ID，用于音频管理
  const componentId = React.useMemo(() => `tarot-speech-${messageId || Math.random().toString(36).substring(2, 9)}`, [messageId]);
  
  // 当播放状态改变时，更新全局播放状态
  useEffect(() => {
    if (isPlaying) {
      setPlayingId(paragraphUniqueId);
    } else if (isThisParagraphPlaying) {
      setPlayingId(null);
    }
  }, [isPlaying, paragraphUniqueId, setPlayingId, isThisParagraphPlaying]);
  
  // 当段落引用存在时，添加高亮效果
  useEffect(() => {
    if (paragraphRef?.current) {
      if (isThisParagraphPlaying) {
        // 添加高亮边框
        paragraphRef.current.style.transition = 'box-shadow 0.3s ease, border 0.3s ease';
        paragraphRef.current.style.boxShadow = '0 0 0 2px rgba(139, 92, 246, 0.5)';
        paragraphRef.current.style.border = '1px solid rgba(139, 92, 246, 0.8)';
        
        // 确保段落垂直居中显示在视图中
        paragraphRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
      } else {
        // 移除高亮边框
        paragraphRef.current.style.boxShadow = '';
        paragraphRef.current.style.border = '';
      }
    }
  }, [isThisParagraphPlaying, paragraphRef]);
  
  // 处理播放/暂停切换
  const handleTogglePlay = useCallback(() => {
    // 检查VIP状态 - 对于非basic占卜师，只有VIP用户可以使用语音功能
    if (readerId && readerId !== 'basic' && user) {
      // 检查VIP状态是否为active
      if (user.vipStatus !== 'active') {
        // 非VIP用户点击非basic占卜师的语音，显示VIP提示
        if (setShowVipPrompt) {
          setShowVipPrompt(true);
        }
        return;
      }
      
      // 检查VIP是否过期
      if (user.vipEndDate) {
        const now = new Date();
        const vipEndDate = new Date(user.vipEndDate);
        
        if (now > vipEndDate) {
          // VIP已过期，显示VIP提示
          if (setShowVipPrompt) {
            setShowVipPrompt(true);
          }
          return;
        }
      }
    }
    
    togglePlay();
  }, [togglePlay, readerId, user, setShowVipPrompt]);
  
  // 注册音频元素到管理器，同时设置回调
  React.useEffect(() => {
    if (audioElement) {
      audioManager.registerAudio(audioElement, componentId, () => {
        // 当被其他音频暂停时，更新播放状态
        setIsPlaying(false);
      });
      
      // 添加播放开始事件处理
      const onPlayHandler = () => {
        // 播放开始事件
        // 确保播放状态更新到全局上下文，触发高亮和滚动
        setIsPlaying(true);
        setPlayingId(paragraphUniqueId);
      };
      
      // 添加播放结束事件处理
      const onEndedHandler = () => {
        // 播放结束事件
        setIsPlaying(false);
        if (isThisParagraphPlaying) {
          setPlayingId(null);
        }
      };
      
      audioElement.addEventListener('play', onPlayHandler);
      audioElement.addEventListener('ended', onEndedHandler);
      
      return () => {
        audioElement.removeEventListener('play', onPlayHandler);
        audioElement.removeEventListener('ended', onEndedHandler);
      };
    }
  }, [audioElement, componentId, sessionId, messageId, normalizedBlockType, setIsPlaying, setPlayingId, paragraphUniqueId, isThisParagraphPlaying]);
  
  // 如果没有文本或者被禁用，则不显示组件
  if (!text || disabled) return null;
  
  // 渲染播放按钮
  return (
    <>
      {/* 移除语言限制，在所有语言环境下显示播放按钮 */}
      <div className={`relative inline-flex items-center align-text-bottom ${className}`}>
        <motion.button
          whileTap={{ scale: 0.95 }}
          onClick={handleTogglePlay}
          disabled={isLoading}
          className={`inline-flex items-center justify-center cursor-pointer align-[-0.1em]`}
          title={isPlaying ? t('speech.pause', '暂停朗读') : t('speech.play', '朗读内容')}
        >
          {isLoading ? (
            // 加载中图标
            <svg className="w-[1em] h-[1em] text-purple-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : isPlaying ? (
            // 暂停图标
            <svg xmlns="http://www.w3.org/2000/svg" className="w-[1em] h-[1em] text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ) : (
            // 播放图标（带声音波纹）
            <svg xmlns="http://www.w3.org/2000/svg" className="w-[1em] h-[1em] text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
            </svg>
          )}
        </motion.button>
      </div>
      
      {/* 错误提示 */}
      {error && (
        <div className={`text-xs text-red-500`}>
          {error}
        </div>
      )}
    </>
  );
};

export default TarotSpeech;