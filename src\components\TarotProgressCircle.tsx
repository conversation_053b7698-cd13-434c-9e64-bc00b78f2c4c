import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';

interface Props {
  progress: number;
  isGenerating: boolean;
}

const TarotProgressCircle: React.FC<Props> = ({ progress, isGenerating }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  // 定义7个阶段的阈值和对应的消息
  const stages = [
    { threshold: 0, message: 'reading.shuffle.interpretation_progress.awakening' },
    { threshold: 15, message: 'reading.shuffle.interpretation_progress.understanding' },
    { threshold: 30, message: 'reading.shuffle.interpretation_progress.sensing' },
    { threshold: 45, message: 'reading.shuffle.interpretation_progress.interpreting' },
    { threshold: 60, message: 'reading.shuffle.interpretation_progress.analyzing' },
    { threshold: 75, message: 'reading.shuffle.interpretation_progress.integrating' },
    { threshold: 90, message: 'reading.shuffle.interpretation_progress.generating' }
  ];

  // 获取当前阶段的消息
  const getCurrentStageMessage = () => {
    const currentStage = [...stages]
      .reverse()
      .find(stage => progress >= stage.threshold);
    return currentStage ? t(currentStage.message) : '';
  };

  if (!isGenerating) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center"
    >
      {/* Circular Progress */}
      <div className="relative w-44 h-44">
        {/* Background Circle */}
        <div className={`absolute inset-0 rounded-full ${theme === 'light' ? 'bg-gray-200/80' : 'bg-gray-900/80'}`} />
        
        {/* Progress Circle */}
        <svg className="absolute inset-0 w-full h-full -rotate-90">
          <circle
            cx="88"
            cy="88"
            r="76"
            fill="none"
            stroke={theme === 'light' ? "#E5E7EB" : "#2A2A2A"}
            strokeWidth="10"
          />
          <circle
            cx="88"
            cy="88"
            r="76"
            fill="none"
            stroke={theme === 'light' ? "#8B5CF6" : "#A855F7"}
            strokeWidth="10"
            strokeLinecap="round"
            strokeDasharray={`${2 * Math.PI * 76}`}
            strokeDashoffset={2 * Math.PI * 76 * (1 - progress / 100)}
            className="transition-all duration-300 ease-out"
          />
        </svg>

        {/* Percentage Text */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className={`text-5xl font-bold ${theme === 'light' ? 'text-gray-800' : 'text-white'} font-['Barlow']`}>
            {Math.round(progress)}%
          </span>
        </div>
      </div>

      {/* Stage Message */}
      <div className="mt-6 flex flex-col items-center">
        <span className={`text-2xl font-semibold tracking-wide ${theme === 'light' ? 'text-gray-700' : 'text-gray-200'} font-['Inter']`}>
          {getCurrentStageMessage()}
        </span>
      </div>
    </motion.div>
  );
};

export default TarotProgressCircle; 