/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = sessions   */
/******************************************/
CREATE TABLE `sessions` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `question` text NOT NULL,
  `reader_id` varchar(36) DEFAULT NULL,
  `reader_name` varchar(255) DEFAULT NULL,
  `reader_type` varchar(50) DEFAULT NULL,
  `spread_id` varchar(36) DEFAULT NULL,
  `spread_name` varchar(255) DEFAULT NULL,
  `spread_card_count` int DEFAULT NULL,
  `selected_cards` json DEFAULT NULL,
  `selected_positions` json DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'pending',
  `reading_result` json DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `deep_analysis` text,
  `advice` text,
  `input_tokens` int DEFAULT NULL,
  `output_tokens` int DEFAULT NULL,
  `llm_model` varchar(50) DEFAULT NULL,
  `response_time` int DEFAULT NULL,
  `prompt_cache_hit_tokens` int DEFAULT NULL,
  `prompt_cache_miss_tokens` int DEFAULT NULL,
  `summary` text,
  `dialog_history` json DEFAULT NULL COMMENT '追问信息',
  `fortune_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '运势预测结果',
  `Ethical_input_token` int DEFAULT NULL,
  `Ethical_output_token` int DEFAULT NULL,
  `Ethical_category` varchar(100) DEFAULT NULL,
  `Ethical_reason` text,
  `Ethical_confidence` float DEFAULT NULL COMMENT '伦理检查置信度',
  `followup_ethical_categories` json DEFAULT NULL COMMENT '追问伦理检查的分类记录，JSON数组',
  `followup_ethical_reasons` json DEFAULT NULL COMMENT '追问伦理检查的原因记录，JSON数组',
  `Similarity_input_token` int DEFAULT NULL,
  `Similarity_output_token` int DEFAULT NULL,
  `Similarity_is_repeated` tinyint(1) DEFAULT NULL,
  `Similarity_matched_question` text,
  `spread_recommendation_input_tokens` int DEFAULT NULL COMMENT 'AI推荐牌阵输入token总数',
  `spread_recommendation_output_tokens` int DEFAULT NULL COMMENT 'AI推荐牌阵输出token总数',
  `spread_recommendation_time` int DEFAULT NULL COMMENT 'AI推荐牌阵处理时间(秒)',
  `spread_recommendation_category` varchar(50) DEFAULT NULL COMMENT 'AI推荐的牌阵类别ID',
  `spread_recommendation_spread_id` varchar(36) DEFAULT NULL COMMENT 'AI推荐的具体牌阵ID',
  `newline_type` varchar(20) DEFAULT NULL COMMENT '解读结果使用的换行符类型：single或double',
  `paragraph_detection_status` tinyint(1) DEFAULT NULL COMMENT '分段检测是否成功，基于关键词检查',
  `ethical_status` varchar(50) DEFAULT NULL COMMENT '安全检测状态值，包括ethical_intervention、ethical_intervention_follow、potential_ethical_issue、potential_ethical_issue_follow',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3
;
