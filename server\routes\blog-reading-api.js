const express = require('express');
const router = express.Router();
const OpenAI = require('openai');
const { getConnection } = require('../services/database');

// 创建OpenAI实例用于调用Qwen API
const qwenAPI = new OpenAI({
  apiKey: process.env.QWEN_API_KEY || "YOUR_API_KEY_HERE",
  baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
  // 关闭API调试日志
  logLevel: 'off',
  // 设置debug为false强制关闭日志
  debug: false,
  // 设置默认API请求配置
  defaultQuery: { debug: false },
  defaultHeaders: { 'Debug-Logs': 'false' }
});

// 初始化数据库表结构
const initDatabase = async () => {
  try {
    const pool = await getConnection();
    
    // 检查并创建blog_readings表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS blog_readings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        fingerprint VARCHAR(255) NOT NULL,
        blog_id VARCHAR(255) NOT NULL,
        question TEXT,
        prompt_content TEXT,
        response_content TEXT,
        spread_type VARCHAR(100) DEFAULT NULL COMMENT '牌阵类型',
        selected_cards TEXT COMMENT '抽出的卡牌，JSON格式',
        status VARCHAR(50) DEFAULT 'completed' COMMENT '解读状态',
        input_tokens INT,
        output_tokens INT,
        created_at DATETIME
      )
    `);
    
  } catch (error) {
    console.error('初始化数据库表结构失败:', error);
  }
};

// 应用启动时初始化数据库
initDatabase();

// 《折腰》主要角色描述
const foldingWaistCharacters = `
《折腰》主要角色介绍：
1. 小乔：聪慧机敏，心思缜密，在家族危难之际挺身而出，拥有过人胆识和判断力。表面看似柔弱，实则坚韧不拔，能够在困境中找到解决方法。她聪明伶俐，擅长察言观色，在危机中保持冷静。
2. 魏劭：战场上杀伐果断，深爱子民，有情有义的君主。他威严中带着温情，刚毅中存有柔软，待人真诚却也有自己的原则和底线。他心怀天下，肩负使命，对待在意的人会全力以赴地守护。
3. 苏娥皇：成熟稳重，有着丰富的政治智慧，处事圆滑得体。她拥有强大的内心和判断力，能够在复杂的宫廷斗争中保持冷静，为人处世老练圆融，善于协调各方关系。
4. 魏俨：风流倜傥，潇洒不羁，有着敏锐的观察力和过人的智慧。表面看似玩世不恭，实则心怀家国，对兄长忠心耿耿。他机智多谋，能言善辩，在危急时刻展现出非凡的才智。
5. 郑楚玉：心思细腻，温婉敦厚，善解人意。她性格温和，待人真诚，对感情专一执着，愿意为爱付出一切。她有着细腻的感知力，能够察觉他人的微妙情绪变化。
6. 大乔：温柔贤淑，沉稳大方，有着长姐的担当和包容。她深爱家人，为人处事考虑周全，懂得权衡利弊，在危难时刻表现出不亚于男子的勇气和决断力。
`;

// 针对博客页面的简化塔罗牌解读API - 非流式
router.post('/', async (req, res) => {
  try {
    const { 
      question, 
      selectedCards, 
      selectedSpread,
      sessionId,
      language = 'zh-CN',
      fingerprint // 添加指纹参数
    } = req.body;


    // 验证必要参数
    if (!selectedCards || !selectedSpread) {
      return res.status(400).json({ error: '缺少必要的参数' });
    }

    // 生成卡牌描述
    const cardsDescription = selectedCards
      .map((card, index) => {
        let position;
        
        // 针对是否牌阵的特殊处理
        if (selectedSpread.id === 'yes-no-spread') {
          // 使用牌阵的positions中的位置名称
          position = Array.isArray(selectedSpread.positions) 
            ? selectedSpread.positions[index] 
            : `位置${index + 1}`;
          
          // 添加是/否/中性的判断提示
          return `${position}：${card.name}${card.isReversed ? '（逆位）' : ''}（请判断此牌代表"是"、"否"还是"中性"回答）`;
        } else {
          // 其他牌阵正常处理
          position = Array.isArray(selectedSpread.positions) 
            ? selectedSpread.positions[index] 
            : `位置${index + 1}`;
          
          return `${position}：${card.name}${card.isReversed ? '（逆位）' : ''}`;
        }
      })
      .join('\n\n');

    // 获取当前年月日
    const now = new Date();
    const currentFullDate = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月${String(now.getDate()).padStart(2, '0')}日`;

    // 检查是否是《折腰》人物测试牌阵
    const isFoldingWaistSpread = selectedSpread.id === 'folding-waist-spread';

    // 选择适当的系统提示
    let systemPrompt;
    
    if (isFoldingWaistSpread) {
      systemPrompt = `
  角色设定：
  - 你是一位专业的塔罗师，精通塔罗牌与人物性格分析
  - 你的任务是通过塔罗牌阵解读分析问卜者与《折腰》剧中哪个角色最为相似
  - 整个解读过程语言通俗易懂、生动有趣，像朋友间的轻松交流
  - 称呼用户为"您"

  解读要求：
  - 开场白：简短友好地向问卜者问候
  - 正文解读：
    1. 分析每张抽出的塔罗牌含义及其在牌阵中的位置意义，每张牌解读50-100字
    2. 根据三张牌的组合解读问卜者的性格特点和潜质
    3. 与《折腰》剧中人物性格对照，找出最匹配的角色（必须从提供的6个角色中选择）
    4. 详细说明为什么问卜者与该角色相似，列举2-3点具体特质
  - 总结：给出一段100字左右的总结，强调问卜者与所匹配角色的共同点和独特魅力

  输出格式要求：
  - 纯文本输出，不要使用任何markdown语法（如**加粗**、*斜体*等）
  
  注意事项：
  - 解读要有趣味性和个人化
  - 保持积极乐观的基调，即使牌面有负面信息也要转为建设性意见
  - 确保将问卜者与剧中一个具体角色匹配，而不是多个角色`;
    } else {
      // 默认的系统提示
      systemPrompt = `
  角色设定：
  - 你是塔罗师Molly，性格温暖友善，富有同理心
  - 整个解读过程说话亲切自然，语言通俗、直白，像老友聊天
  - 称呼用户为"您"
  解读要求：
   - 开场语，介绍自己+问候问卜者
   - 若用户询问了"何时"、"什么时候"等时间预测问题，则根据牌面信息给出时间区间的预测，例如"未来x-y月"，否则无需给出时间预测
   - 解读卡牌时，首先分析用户问题和卡牌本身的含义关系，给出针对该问题的每张牌面解析；然后补充分析每张卡牌所处牌阵位置含义与用户问题的关联
   - 每张卡牌解读50-100字
   - 每个部分之间应该用自然的语言衔接
   - 问题答案需具体、明确
  输出格式要求：
  - 纯文本输出，不要使用任何markdown语法（如**加粗**、*斜体*等）`;
    }

    // 构建用户提示
    let userPrompt;
    
    if (isFoldingWaistSpread) {
      userPrompt = `
请使用${language}语言回答

今日日期：${currentFullDate}
问题：${question || '我与《折腰》中的哪个角色最相似？'}
塔罗牌阵：${selectedSpread.name}
牌阵描述：探索你与《折腰》中角色的契合度

${foldingWaistCharacters}

牌阵位置信息：
${cardsDescription}
`;
    } else {
      // 默认用户提示
      userPrompt = `
请使用${language}语言回答
请为问卜者答疑解惑，拨开迷雾，指引前路
今日日期：${currentFullDate}
问题：${question}
塔罗牌阵：${selectedSpread.name}
${selectedSpread.id === 'yes-no-spread' ? `注意：请根据以下规则，综合分析三张牌的情况，给出最终的是/否答案：
1. 三张牌均为"是"：表示肯定答案
2. 两张"是"牌 + 一张"否/中性"牌：表示可能性较大，但需要时间
3. 一张"是"牌 + 两张"否/中性"牌：表示可能性较小，需要付出更多努力
4. 三张牌均为"否"或"否"与"中性"混合：表示否定答案
请在解读中明确指出最终结论。` : ''}
牌阵位置信息：
${cardsDescription}
`;
    }

    console.log('正在发送请求到Qwen API，参数：', {
      系统提示: systemPrompt,
      用户提示: userPrompt
    });

    // 调用 Qwen API - 非流式
    const response = await qwenAPI.chat.completions.create({
      model: "qwen-turbo-latest",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ],
      stream: false,
      parameters: {
        response_language: language
      }
    });

    // 打印API响应结果
    console.log('Qwen API 响应结果:', JSON.stringify({
      choices: response.choices,
      usage: response.usage
    }, null, 2));

    let fullContent = "";
    let tokensUsage = {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    };

    if (response.usage) {
      tokensUsage = response.usage;
    }

    if (response.choices && response.choices.length > 0) {
      fullContent = response.choices[0].message.content;
    }

    // 如果提供了指纹，保存指纹数据
    if (fingerprint) {
      try {
        const pool = await getConnection();
        const blogId = req.headers.referer ? new URL(req.headers.referer).pathname.split('/').pop() : '';
        
        // 保存博客解读数据
        await pool.query(
          `INSERT INTO blog_readings 
           (fingerprint, blog_id, question, prompt_content, response_content, spread_type, selected_cards, status, input_tokens, output_tokens, created_at) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
          [
            fingerprint,
            blogId || req.body.blogId || '',
            question || '',
            JSON.stringify({
              systemPrompt,
              userPrompt
            }),
            fullContent,
            selectedSpread?.name || selectedSpread?.id || '',
            JSON.stringify(selectedCards || []),
            'completed',
            tokensUsage.prompt_tokens || 0,
            tokensUsage.completion_tokens || 0
          ]
        );
        console.log('博客解读数据已保存到数据库，指纹:', fingerprint.substring(0, 10) + '...');
      } catch (fingerprintError) {
        console.error('保存博客解读数据失败:', fingerprintError);
        // 即使保存数据失败，也继续返回解读结果
      }
    }

    // 返回完整内容
    return res.status(200).json({ content: fullContent });

  } catch (error) {
    console.error('API错误详情:', {
      error: error.message,
      response: error.response?.data,
      status: error.response?.status,
      headers: error.response?.headers,
      config: error.config
    });

    // 如果是 API key 相关的错误，返回更具体的错误信息
    if (error.response?.status === 401) {
      return res.status(500).json({ 
        error: 'API 认证失败，请检查 API key 配置。' 
      });
    }

    // 如果是请求超时
    if (error.code === 'ECONNABORTED') {
      return res.status(500).json({ 
        error: '请求超时，请稍后重试。' 
      });
    }

    return res.status(500).json({
      error: '生成解读时出现了问题，请稍后再试。'
    });
  }
});

// 获取特定会话的解读结果
router.get('/session/:sessionId/reading', async (req, res) => {
  try {
    const sessionId = req.params.sessionId;

    // 验证sessionId
    if (!sessionId) {
      return res.status(400).json({ error: '缺少会话ID' });
    }

    // 临时会话直接返回空结果
    if (sessionId.startsWith('temp-')) {
      console.log('尝试获取临时会话解读结果，跳过数据库查询:', sessionId);
      return res.status(404).json({ error: '找不到对应的会话' });
    }

    // 从数据库获取会话信息
    const pool = await getConnection();
    const [sessions] = await pool.query(
      'SELECT id, reading_result, status FROM sessions WHERE id = ?',
      [sessionId]
    );

    if (sessions.length === 0) {
      return res.status(404).json({ error: '找不到对应的会话' });
    }

    const session = sessions[0];

    // 如果会话状态不是已完成，则返回等待中状态
    if (session.status !== 'completed') {
      return res.status(202).json({ 
        status: 'processing',
        message: '解读正在生成中，请稍后再试'
      });
    }

    // 返回解读结果
    return res.status(200).json({
      status: 'completed',
      readingResult: session.reading_result
    });

  } catch (error) {
    console.error('获取会话解读结果失败:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router; 