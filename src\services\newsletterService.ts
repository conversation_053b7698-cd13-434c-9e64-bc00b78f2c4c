import axiosInstance from '../utils/axios';

/**
 * 订阅邮件通讯
 * @param email 用户邮箱
 * @param language 语言设置
 * @returns 请求结果
 */
export const subscribeNewsletter = async (email: string, language?: string): Promise<{ success: boolean; message: string }> => {
  try {
    // 获取当前语言设置
    const currentLanguage = language || localStorage.getItem('i18nextLng') || 'zh-CN';
    const response = await axiosInstance.post('/api/newsletter/subscribe', { 
      email,
      language: currentLanguage
    });
    return response.data;
  } catch (error) {
    // console.error('订阅邮件通讯失败:', error);
    throw error;
  }
}; 