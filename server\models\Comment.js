const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');

class Comment {
  /**
   * 创建评论
   * @param {Object} commentData - 评论数据
   * @param {string} commentData.sessionId - 会话ID
   * @param {string} commentData.userId - 用户ID
   * @param {number} commentData.rating - 评分（1-5）
   * @param {string} commentData.content - 评论内容
   * @param {string} [commentData.pageType] - 页面类型
   * @param {string} [commentData.language] - 语言
   * @param {Object} [commentData.metadata] - 额外元数据
   * @returns {Promise<Object>} 创建的评论信息
   */
  static async create(commentData) {
    const pool = await getConnection();
    const id = uuidv4();
    const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
    
    // 只对顶级评论（非回复）检查用户是否已经评论过
    if (!commentData.parentId) {
      // 检查用户是否已经对该会话评论过
      const [existingComments] = await pool.query(
        'SELECT id FROM comments WHERE session_id = ? AND user_id = ? AND parent_id IS NULL',
        [commentData.sessionId, commentData.userId]
      );
      
      if (existingComments.length > 0) {
        // 根据语言返回不同的错误信息
        const language = commentData.language || 'zh-CN';
        let errorMessage;
        switch (language) {
          case 'en':
            errorMessage = 'You have already commented on this content';
            break;
          case 'ja':
            errorMessage = 'このコンテンツにはすでにコメントしています';
            break;
          case 'zh-TW':
            errorMessage = '您已經對此內容評價過了';
            break;
          default:
            errorMessage = '您已经对此内容评价过了';
        }
        throw new Error(errorMessage);
      }
    }
    
    const [result] = await pool.query(
      `INSERT INTO comments (
        id, session_id, user_id, parent_id, reply_to_user_id, rating, content, page_type, language, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        commentData.sessionId,
        commentData.userId,
        commentData.parentId || null,
        commentData.replyToUserId || null,
        commentData.rating || null, // 回复可以没有评分
        commentData.content,
        commentData.pageType || 'yes-no-tarot',
        commentData.language || 'zh-CN',
        commentData.metadata ? JSON.stringify(commentData.metadata) : null,
        now,
        now
      ]
    );
    
    if (result.affectedRows === 0) {
      throw new Error('创建评论失败');
    }
    
    return {
      id,
      sessionId: commentData.sessionId,
      userId: commentData.userId,
      rating: commentData.rating,
      content: commentData.content,
      pageType: commentData.pageType,
      language: commentData.language,
      createdAt: now
    };
  }
  
  /**
   * 获取所有评论列表
   * @param {Object} options - 查询选项
   * @param {number} [options.limit=1000] - 限制数量
   * @param {number} [options.offset=0] - 偏移量
   * @returns {Promise<Array>} 评论列表
   */
  static async findAll(options = {}) {
    const pool = await getConnection();
    const { limit = 1000, offset = 0 } = options;

    const [comments] = await pool.query(
      `SELECT
        c.id,
        c.session_id,
        c.user_id,
        c.parent_id,
        c.reply_to_user_id,
        c.reply_count,
        c.like_count,
        c.rating,
        c.content,
        c.page_type,
        c.language,
        c.created_at,
        u.username,
        ru.username as reply_to_username
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      LEFT JOIN users ru ON c.reply_to_user_id = ru.id
      ORDER BY c.created_at DESC
      LIMIT ? OFFSET ?`,
      [limit, offset]
    );

    return comments.map(comment => ({
      id: comment.id,
      sessionId: comment.session_id,
      userId: comment.user_id,
      parentId: comment.parent_id,
      replyToUserId: comment.reply_to_user_id,
      replyToUsername: comment.reply_to_username,
      replyCount: comment.reply_count,
      likeCount: comment.like_count,
      username: comment.username || '匿名用户',
      rating: comment.rating,
      content: comment.content,
      pageType: comment.page_type,
      language: comment.language,
      createdAt: comment.created_at
    }));
  }

  /**
   * 根据会话ID获取评论列表
   * @param {string} sessionId - 会话ID
   * @param {Object} options - 查询选项
   * @param {number} [options.limit=1000] - 限制数量
   * @param {number} [options.offset=0] - 偏移量
   * @returns {Promise<Array>} 评论列表
   */
  static async findBySessionId(sessionId, options = {}) {
    const pool = await getConnection();
    const { limit = 1000, offset = 0 } = options;
    
    const [comments] = await pool.query(
      `SELECT 
        c.id,
        c.session_id,
        c.user_id,
        c.rating,
        c.content,
        c.page_type,
        c.language,
        c.created_at,
        u.username
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.session_id = ?
      ORDER BY c.created_at DESC
      LIMIT ? OFFSET ?`,
      [sessionId, limit, offset]
    );
    
    return comments.map(comment => ({
      id: comment.id,
      sessionId: comment.session_id,
      userId: comment.user_id,
      username: comment.username || '匿名用户',
      rating: comment.rating,
      content: comment.content,
      pageType: comment.page_type,
      language: comment.language,
      createdAt: comment.created_at
    }));
  }
  
  /**
   * 获取评论统计信息
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Object>} 统计信息
   */
  static async getStats(sessionId) {
    const pool = await getConnection();
    
    const [stats] = await pool.query(
      `SELECT 
        COUNT(*) as total_comments,
        AVG(rating) as average_rating,
        COUNT(CASE WHEN rating = 5 THEN 1 END) as five_star,
        COUNT(CASE WHEN rating = 4 THEN 1 END) as four_star,
        COUNT(CASE WHEN rating = 3 THEN 1 END) as three_star,
        COUNT(CASE WHEN rating = 2 THEN 1 END) as two_star,
        COUNT(CASE WHEN rating = 1 THEN 1 END) as one_star
      FROM comments 
      WHERE session_id = ?`,
      [sessionId]
    );
    
    const result = stats[0];
    return {
      totalComments: result.total_comments,
      averageRating: result.average_rating ? parseFloat(result.average_rating).toFixed(1) : 0,
      ratingDistribution: {
        5: result.five_star,
        4: result.four_star,
        3: result.three_star,
        2: result.two_star,
        1: result.one_star
      }
    };
  }
  
  /**
   * 删除评论
   * @param {string} id - 评论ID
   * @param {string} userId - 用户ID（只能删除自己的评论）
   * @returns {Promise<boolean>} 删除是否成功
   */
  static async delete(id, userId) {
    const pool = await getConnection();
    
    const [result] = await pool.query(
      'DELETE FROM comments WHERE id = ? AND user_id = ?',
      [id, userId]
    );
    
    return result.affectedRows > 0;
  }
  
  /**
   * 获取评论的回复列表
   * @param {string} parentId - 父评论ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 回复列表
   */
  static async getReplies(parentId, options = {}) {
    const pool = await getConnection();
    const { limit = 100, offset = 0 } = options;

    const [replies] = await pool.query(
      `SELECT
        c.id,
        c.session_id,
        c.user_id,
        c.parent_id,
        c.reply_to_user_id,
        c.content,
        c.created_at,
        u.username,
        ru.username as reply_to_username
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      LEFT JOIN users ru ON c.reply_to_user_id = ru.id
      WHERE c.parent_id = ?
      ORDER BY c.created_at ASC
      LIMIT ? OFFSET ?`,
      [parentId, limit, offset]
    );

    return replies.map(reply => ({
      id: reply.id,
      sessionId: reply.session_id,
      userId: reply.user_id,
      parentId: reply.parent_id,
      replyToUserId: reply.reply_to_user_id,
      replyToUsername: reply.reply_to_username,
      username: reply.username || '匿名用户',
      content: reply.content,
      createdAt: reply.created_at
    }));
  }

  /**
   * 更新回复数量
   * @param {string} parentId - 父评论ID
   */
  static async updateReplyCount(parentId) {
    const pool = await getConnection();

    // 先查询回复数量
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as reply_count 
       FROM comments 
       WHERE parent_id = ?`,
      [parentId]
    );
    
    const replyCount = countResult[0].reply_count;
    
    // 然后更新父评论的回复数量
    await pool.query(
      `UPDATE comments
       SET reply_count = ?
       WHERE id = ?`,
      [replyCount, parentId]
    );
  }

  /**
   * 根据ID查找评论
   * @param {string} id - 评论ID
   * @returns {Promise<Object|null>} 评论对象或null
   */
  static async findById(id) {
    const pool = await getConnection();
    
    const [comments] = await pool.query(
      `SELECT 
        c.id,
        c.session_id,
        c.user_id,
        c.parent_id,
        c.reply_to_user_id,
        c.rating,
        c.content,
        c.page_type,
        c.language,
        c.created_at,
        u.username
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.id = ?
      LIMIT 1`,
      [id]
    );
    
    if (comments.length === 0) {
      return null;
    }
    
    const comment = comments[0];
    return {
      id: comment.id,
      sessionId: comment.session_id,
      userId: comment.user_id,
      parentId: comment.parent_id,
      replyToUserId: comment.reply_to_user_id,
      username: comment.username || '匿名用户',
      rating: comment.rating,
      content: comment.content,
      pageType: comment.page_type,
      language: comment.language,
      createdAt: comment.created_at
    };
  }

  /**
   * 获取用户的评论历史
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @param {number} [options.limit=100] - 限制数量
   * @param {number} [options.offset=0] - 偏移量
   * @returns {Promise<Array>} 评论列表
   */
  static async findByUserId(userId, options = {}) {
    const pool = await getConnection();
    const { limit = 100, offset = 0 } = options;
    
    const [comments] = await pool.query(
      `SELECT 
        id,
        session_id,
        rating,
        content,
        page_type,
        language,
        created_at
      FROM comments
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?`,
      [userId, limit, offset]
    );
    
    return comments.map(comment => ({
      id: comment.id,
      sessionId: comment.session_id,
      rating: comment.rating,
      content: comment.content,
      pageType: comment.page_type,
      language: comment.language,
      createdAt: comment.created_at
    }));
  }
}

module.exports = Comment;
