const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');

class ReadingFeedback {
  /**
   * 创建塔罗解读反馈
   * @param {Object} feedbackData - 反馈数据
   * @param {string} feedbackData.sessionId - 会话ID
   * @param {string} [feedbackData.userId] - 用户ID（可选，未登录用户为null）
   * @param {Object} [feedbackData.feedbackTypes] - 反馈类型标志
   * @param {number} feedbackData.rating - 评分（1-5）
   * @param {string} feedbackData.content - 反馈内容
   * @param {Object} [feedbackData.metadata] - 额外元数据
   * @returns {Promise<Object>} 创建的反馈信息
   */
  static async create(feedbackData) {
    // 首先检查是否已存在该会话的反馈记录
    const existingFeedback = await this.findBySessionIdAndUserId(feedbackData.sessionId, feedbackData.userId);
    
    if (existingFeedback && existingFeedback.length > 0) {
      // 如果已存在记录，则更新而不是创建新记录
      
      // 构建更新数据
      const updateData = {};
      
      // 根据反馈类型设置不同字段
      if (feedbackData.feedbackTypes?.basicReading) {
        updateData.basicReadingRating = feedbackData.rating;
        updateData.basicReadingContent = feedbackData.content;
      }
      
      if (feedbackData.feedbackTypes?.deepAnalysis) {
        updateData.deepAnalysisRating = feedbackData.rating;
        updateData.deepAnalysisContent = feedbackData.content;
      }
      
      if (feedbackData.feedbackTypes?.followup) {
        updateData.followupRating = feedbackData.rating;
        updateData.followupContent = feedbackData.content;
      }
      
      // 更新元数据
      if (feedbackData.metadata) {
        updateData.metadata = feedbackData.metadata;
      }
      
      // 更新记录
      await this.update(existingFeedback[0].id, updateData);
      
      // 返回更新后的记录ID
      return { 
        id: existingFeedback[0].id, 
        ...feedbackData, 
        created_at: existingFeedback[0].created_at, 
        updated_at: new Date() 
      };
    }
    
    // 如果不存在记录，则创建新记录
    const pool = await getConnection();
    const id = uuidv4();
    const now = new Date();

    // 判断反馈类型
    const isFeedbackTypeBasicReading = feedbackData.feedbackTypes?.basicReading;
    const isFeedbackTypeDeepAnalysis = feedbackData.feedbackTypes?.deepAnalysis;
    const isFeedbackTypeFollowup = feedbackData.feedbackTypes?.followup;

    // 构建插入数据
    const insertData = {
      id,
      user_id: feedbackData.userId || null,
      session_id: feedbackData.sessionId,
      
      // 根据反馈类型填充对应字段
      basic_reading_rating: isFeedbackTypeBasicReading ? feedbackData.rating : null,
      basic_reading_content: isFeedbackTypeBasicReading ? feedbackData.content : null,
      
      deep_analysis_rating: isFeedbackTypeDeepAnalysis ? feedbackData.rating : null,
      deep_analysis_content: isFeedbackTypeDeepAnalysis ? feedbackData.content : null,
      
      followup_rating: isFeedbackTypeFollowup ? feedbackData.rating : null,
      followup_content: isFeedbackTypeFollowup ? feedbackData.content : null,
      
      metadata: feedbackData.metadata ? JSON.stringify(feedbackData.metadata) : null,
      created_at: now,
      updated_at: now
    };

    // 构建SQL查询
    const fields = Object.keys(insertData).join(', ');
    const placeholders = Object.keys(insertData).map(() => '?').join(', ');
    const values = Object.values(insertData);

    const [result] = await pool.query(
      `INSERT INTO reading_feedback (${fields}) VALUES (${placeholders})`,
      values
    );

    return { id, ...feedbackData, created_at: now, updated_at: now };
  }

  /**
   * 获取会话的所有反馈
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Array>} 反馈列表
   */
  static async findBySessionId(sessionId) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      `SELECT * FROM reading_feedback WHERE session_id = ? ORDER BY created_at DESC`,
      [sessionId]
    );

    return rows.map(feedback => {
      if (feedback.metadata) {
        try {
          // 只有当metadata是字符串时才尝试解析
          if (typeof feedback.metadata === 'string') {
            feedback.metadata = JSON.parse(feedback.metadata);
          }
          // 如果已经是对象，不需要解析
        } catch (error) {
          console.error('Error parsing feedback metadata:', error);
          feedback.metadata = {};
        }
      }
      return feedback;
    });
  }

  /**
   * 获取用户的所有反馈
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @param {number} [options.limit=20] - 每页数量
   * @param {number} [options.offset=0] - 偏移量
   * @returns {Promise<Object>} 反馈列表和总数
   */
  static async findByUserId(userId, options = {}) {
    const pool = await getConnection();
    const limit = options.limit || 20;
    const offset = options.offset || 0;
    
    // 查询总记录数
    const [countRows] = await pool.query(
      `SELECT COUNT(*) as total FROM reading_feedback WHERE user_id = ?`,
      [userId]
    );
    
    const total = countRows[0].total;
    
    // 查询数据
    const [dataRows] = await pool.query(
      `SELECT * FROM reading_feedback 
       WHERE user_id = ? 
       ORDER BY created_at DESC LIMIT ? OFFSET ?`,
      [userId, limit, offset]
    );
    
    // 处理metadata字段
    const feedbacks = dataRows.map(feedback => {
      if (feedback.metadata) {
        try {
          // 只有当metadata是字符串时才尝试解析
          if (typeof feedback.metadata === 'string') {
            feedback.metadata = JSON.parse(feedback.metadata);
          }
          // 如果已经是对象，不需要解析
        } catch (error) {
          console.error('Error parsing feedback metadata:', error);
          feedback.metadata = {};
        }
      }
      return feedback;
    });
    
    return {
      data: feedbacks,
      total,
      limit,
      offset
    };
  }

  /**
   * 获取统计数据
   * @param {Object} options - 查询选项
   * @param {Date} [options.startDate] - 开始日期
   * @param {Date} [options.endDate] - 结束日期
   * @returns {Promise<Object>} 统计数据
   */
  static async getStats(options = {}) {
    const pool = await getConnection();
    
    let whereClause = '';
    let queryParams = [];
    
    if (options.startDate && options.endDate) {
      whereClause = 'WHERE created_at BETWEEN ? AND ?';
      queryParams = [options.startDate, options.endDate];
    }
    
    // 获取各类型评分的平均值
    const [avgRatings] = await pool.query(
      `SELECT 
        AVG(basic_reading_rating) as avg_basic_reading,
        AVG(deep_analysis_rating) as avg_deep_analysis,
        AVG(followup_rating) as avg_followup
       FROM reading_feedback ${whereClause}`,
      queryParams
    );
    
    // 获取各类型评分的数量
    const [countRatings] = await pool.query(
      `SELECT 
        COUNT(basic_reading_rating) as count_basic_reading,
        COUNT(deep_analysis_rating) as count_deep_analysis,
        COUNT(followup_rating) as count_followup,
        COUNT(*) as total
       FROM reading_feedback ${whereClause}`,
      queryParams
    );
    
    return {
      averageRatings: {
        basicReading: avgRatings[0].avg_basic_reading || 0,
        deepAnalysis: avgRatings[0].avg_deep_analysis || 0,
        followup: avgRatings[0].avg_followup || 0
      },
      counts: {
        basicReading: countRatings[0].count_basic_reading || 0,
        deepAnalysis: countRatings[0].count_deep_analysis || 0,
        followup: countRatings[0].count_followup || 0,
        total: countRatings[0].total || 0
      }
    };
  }

  /**
   * 更新现有反馈
   * @param {string} id - 反馈ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<boolean>} 更新结果
   */
  static async update(id, updateData) {
    const pool = await getConnection();
    const now = new Date();
    
    // 构建更新数据
    let updateFields = [];
    let updateValues = [];
    
    if (updateData.basicReadingRating !== undefined) {
      updateFields.push('basic_reading_rating = ?');
      updateValues.push(updateData.basicReadingRating);
    }
    
    if (updateData.basicReadingContent !== undefined) {
      updateFields.push('basic_reading_content = ?');
      updateValues.push(updateData.basicReadingContent);
    }
    
    if (updateData.deepAnalysisRating !== undefined) {
      updateFields.push('deep_analysis_rating = ?');
      updateValues.push(updateData.deepAnalysisRating);
    }
    
    if (updateData.deepAnalysisContent !== undefined) {
      updateFields.push('deep_analysis_content = ?');
      updateValues.push(updateData.deepAnalysisContent);
    }
    
    if (updateData.followupRating !== undefined) {
      updateFields.push('followup_rating = ?');
      updateValues.push(updateData.followupRating);
    }
    
    if (updateData.followupContent !== undefined) {
      updateFields.push('followup_content = ?');
      updateValues.push(updateData.followupContent);
    }
    
    if (updateData.metadata !== undefined) {
      updateFields.push('metadata = ?');
      updateValues.push(JSON.stringify(updateData.metadata));
    }
    
    // 添加更新时间
    updateFields.push('updated_at = ?');
    updateValues.push(now);
    
    // 如果没有要更新的字段，直接返回
    if (updateFields.length === 0) {
      return false;
    }
    
    const [result] = await pool.query(
      `UPDATE reading_feedback SET ${updateFields.join(', ')} WHERE id = ?`,
      [...updateValues, id]
    );
    
    return result.affectedRows > 0;
  }

  /**
   * 根据会话ID和用户ID查找反馈
   * @param {string} sessionId - 会话ID
   * @param {string|null} userId - 用户ID
   * @returns {Promise<Array>} 反馈列表
   */
  static async findBySessionIdAndUserId(sessionId, userId) {
    const pool = await getConnection();
    let query = `SELECT * FROM reading_feedback WHERE session_id = ?`;
    let params = [sessionId];
    
    // 如果提供了userId，则加入查询条件
    if (userId !== undefined) {
      query += ` AND user_id = ?`;
      params.push(userId);
    }
    
    query += ` ORDER BY created_at DESC`;
    
    const [rows] = await pool.query(query, params);

    return rows.map(feedback => {
      if (feedback.metadata) {
        try {
          // 只有当metadata是字符串时才尝试解析
          if (typeof feedback.metadata === 'string') {
            feedback.metadata = JSON.parse(feedback.metadata);
          }
          // 如果已经是对象，不需要解析
        } catch (error) {
          console.error('Error parsing feedback metadata:', error);
          feedback.metadata = {};
        }
      }
      return feedback;
    });
  }
}

module.exports = ReadingFeedback; 