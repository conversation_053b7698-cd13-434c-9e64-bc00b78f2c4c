import React, { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { CalendarIcon, CursorArrowRaysIcon } from '@heroicons/react/24/outline';

interface BirthdayInputProps {
  showBirthdayInput: boolean;
  toggleBirthdayInput: () => void;
  birthYear: string;
  birthMonth: string;
  birthDay: string;
  birthDate: string;
  zodiacSign: string;
  setBirthYear: (year: string) => void;
  setBirthMonth: (month: string) => void;
  setBirthDay: (day: string) => void;
  setBirthDate: (date: string) => void;
  setZodiacSign: (sign: string) => void;
  isMobile: boolean;
  isIOSDevice: boolean;
  getFontClass: () => string;
}

const BirthdayInput: React.FC<BirthdayInputProps> = ({
  showBirthdayInput,
  toggleBirthdayInput,
  birthYear,
  birthMonth,
  birthDay,
  birthDate,
  zodiacSign,
  setBirthYear,
  setBirthMonth,
  setBirthDay,
  setBirthDate,
  setZodiacSign,
  isMobile,
  isIOSDevice,
  getFontClass
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const birthdayInputRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // 判断是否已输入生日信息
  const hasBirthdayInfo = birthYear || birthMonth || birthDay || birthDate || zodiacSign;

  // 添加点击外部区域关闭生日输入框的功能
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showBirthdayInput &&
        birthdayInputRef.current && 
        buttonRef.current &&
        !birthdayInputRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        toggleBirthdayInput();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showBirthdayInput, toggleBirthdayInput]);

  // 生成年份选项
  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = currentYear - 100; year <= currentYear; year++) {
      years.push(year);
    }
    return years.reverse();
  };
  
  // 生成月份选项
  const generateMonthOptions = () => {
    const months = [];
    for (let month = 1; month <= 12; month++) {
      months.push(month);
    }
    return months;
  };
  
  // 生成日期选项
  const generateDayOptions = () => {
    if (!birthMonth) return Array.from({ length: 31 }, (_, i) => i + 1);
    
    const month = parseInt(birthMonth);
    const year = birthYear ? parseInt(birthYear) : new Date().getFullYear();
    
    // 判断是否为闰年
    const isLeapYear = (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
    
    // 根据月份返回对应的天数
    switch (month) {
      case 2: // 二月
        return Array.from({ length: isLeapYear ? 29 : 28 }, (_, i) => i + 1);
      case 4: case 6: case 9: case 11: // 小月
        return Array.from({ length: 30 }, (_, i) => i + 1);
      default: // 大月
        return Array.from({ length: 31 }, (_, i) => i + 1);
    }
  };

  return (
    <div className="mt-4 flex flex-col items-center">
      <button 
        ref={buttonRef}
        onClick={toggleBirthdayInput}
        className={`flex items-center px-4 py-2 rounded-full text-sm font-medium transition-colors ${getFontClass()}
          ${theme === 'light' 
            ? 'bg-purple-100 text-purple-700 hover:bg-purple-200' 
            : 'bg-purple-900/50 text-purple-100 hover:bg-purple-800/60'}`}
      >
        {hasBirthdayInfo ? (
          <CalendarIcon className="w-4 h-4 mr-2" />
        ) : (
          <CursorArrowRaysIcon className="w-5 h-5 mr-2" />
        )}
        {showBirthdayInput 
          ? t('daily.birthday.hide', '隐藏生日信息') 
          : zodiacSign 
            ? `${t('daily.birthday.your_sign', '您的星座')}: ${zodiacSign}` 
            : t('daily.birthday.add', '添加生日信息')}
      </button>
      
      {showBirthdayInput && (
        <motion.div 
          ref={birthdayInputRef}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
          className={`mt-4 p-4 rounded-lg backdrop-blur-md relative ${
            theme === 'light' ? 'bg-white/80 shadow-lg' : 'bg-gray-900/70 border border-purple-500/30'
          }`}
        >
          {/* 重置按钮 */}
          <button
            onClick={() => {
              setBirthYear('');
              setBirthMonth('');
              setBirthDay('');
              setBirthDate('');
              setZodiacSign('');
              localStorage.removeItem('userBirthYear');
              localStorage.removeItem('userBirthMonth');
              localStorage.removeItem('userBirthDay');
              localStorage.removeItem('userZodiacSign');
            }}
            className={`absolute top-2 right-2 p-1.5 rounded-full transition-colors ${
              theme === 'light' 
                ? 'bg-gray-100 hover:bg-gray-200 text-gray-500' 
                : 'bg-gray-800 hover:bg-gray-700 text-gray-400'
            }`}
            aria-label={t('daily.birthday.reset', '重置生日信息')}
            title={t('daily.birthday.reset', '重置生日信息')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
          
          <div className="text-center mb-3">
            <h3 className={`text-lg font-medium ${theme === 'light' ? 'text-purple-800' : 'text-purple-200'} ${getFontClass()}`}>
              {t('daily.birthday.title', '输入您的生日')}
            </h3>
            <p className={`text-xs ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'} ${getFontClass()}`}>
              {t('daily.birthday.description', '生日信息将用于提供更个性化的塔罗解读')}
            </p>
          </div>
          
          {/* 移动端和PC端不同的日期选择器 */}
          {isMobile ? (
            // 移动端原生日期选择器
            <div className="w-full">
              <div className="relative">
                <input
                  type="date"
                  value={birthDate}
                  onChange={(e) => setBirthDate(e.target.value)}
                  className={`w-full py-2 px-3 rounded text-base ${getFontClass()} 
                    ${theme === 'light' 
                      ? 'bg-white border border-gray-300 text-gray-800' 
                      : 'bg-gray-800 border border-gray-600 text-white'}
                    ${isIOSDevice ? 'appearance-none' : ''}`}
                  style={{
                    // 针对iOS设备的特殊样式
                    ...(isIOSDevice ? {
                      backgroundImage: 'none',
                      height: '44px'  // iOS推荐的触摸目标高度
                    } : {})
                  }}
                  placeholder={t('daily.birthday.date', '生日日期')}
                  max={new Date().toISOString().split('T')[0]}  // 限制最大日期为今天
                />
              </div>
            </div>
          ) : (
            // 优化的PC端年月日选择器 - iOS/GitHub风格
            <div className="flex flex-wrap gap-3 justify-center">
              {/* 年份选择 */}
              <div className="w-28">
                <div className="relative">
                  <select
                    value={birthYear}
                    onChange={(e) => setBirthYear(e.target.value)}
                    className={`w-full py-2 pl-3 pr-8 rounded-md text-sm appearance-none ${getFontClass()} 
                      ${theme === 'light' 
                        ? 'bg-white border border-gray-200 text-gray-800 hover:border-purple-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200' 
                        : 'bg-gray-800 border border-gray-700 text-white hover:border-purple-600 focus:border-purple-500 focus:ring-2 focus:ring-purple-900'}
                      transition-all duration-200 focus:outline-none`}
                  >
                    <option value="">{t('daily.birthday.year', '年')}</option>
                    {generateYearOptions().map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                    <svg className="w-4 h-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                    </svg>
                  </div>
                </div>
              </div>
              
              {/* 月份选择 */}
              <div className="w-24">
                <div className="relative">
                  <select
                    value={birthMonth}
                    onChange={(e) => setBirthMonth(e.target.value)}
                    className={`w-full py-2 pl-3 pr-8 rounded-md text-sm appearance-none ${getFontClass()} 
                      ${theme === 'light' 
                        ? 'bg-white border border-gray-200 text-gray-800 hover:border-purple-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200' 
                        : 'bg-gray-800 border border-gray-700 text-white hover:border-purple-600 focus:border-purple-500 focus:ring-2 focus:ring-purple-900'}
                      transition-all duration-200 focus:outline-none`}
                  >
                    <option value="">{t('daily.birthday.month', '月')}</option>
                    {generateMonthOptions().map(month => (
                      <option key={month} value={month}>{month}</option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                    <svg className="w-4 h-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                    </svg>
                  </div>
                </div>
              </div>
              
              {/* 日期选择 */}
              <div className="w-24">
                <div className="relative">
                  <select
                    value={birthDay}
                    onChange={(e) => setBirthDay(e.target.value)}
                    className={`w-full py-2 pl-3 pr-8 rounded-md text-sm appearance-none ${getFontClass()} 
                      ${theme === 'light' 
                        ? 'bg-white border border-gray-200 text-gray-800 hover:border-purple-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200' 
                        : 'bg-gray-800 border border-gray-700 text-white hover:border-purple-600 focus:border-purple-500 focus:ring-2 focus:ring-purple-900'}
                      transition-all duration-200 focus:outline-none`}
                  >
                    <option value="">{t('daily.birthday.day', '日')}</option>
                    {generateDayOptions().map(day => (
                      <option key={day} value={day}>{day}</option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                    <svg className="w-4 h-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {zodiacSign && (
            <div className="mt-3 text-center">
              <span className={`inline-block px-3 py-1 rounded-full text-sm ${getFontClass()} 
                ${theme === 'light' 
                  ? 'bg-purple-100 text-purple-700' 
                  : 'bg-purple-900/60 text-purple-100'}`}
              >
                {t('daily.birthday.your_sign', '您的星座')}: {zodiacSign}
              </span>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default BirthdayInput; 