import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import CdnLazyImage from '../CdnLazyImage';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';
import HoverableText from './HoverableText';

interface HoroscopePromoBlockProps {
  className?: string;
  title?: string;
  imageSrc?: string;
  imageAlt?: string;
}

const HoroscopePromoBlock: React.FC<HoroscopePromoBlockProps> = ({
  className = '',
  title,
  imageSrc = '/images-optimized/horoscope/horoscope-promo.webp',
  imageAlt = 'Horoscope Astrology Reading',
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { navigate } = useLanguageNavigate();
  const isDark = theme === 'dark';

  // 使用i18n翻译键获取默认标题
  const defaultTitle = t('horoscope.promo.title');
  
  const displayTitle = title || defaultTitle;
  
  // 处理点击导航
  const handleDailyClick = () => navigate('/horoscope/daily-horoscope');
  const handleYearlyClick = () => navigate('/horoscope/yearly-horoscope');
  const handleLoveClick = () => navigate('/horoscope/love-horoscope');

  return (
    <div className={`py-12 md:py-16 ${className}`}>
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        {/* 标题区域 */}
        <div className="text-center mb-10">
          <h2 className={`text-2xl sm:text-3xl font-bold mb-3 ${isDark ? 'text-white' : 'text-purple-800'}`}>
            {displayTitle}
          </h2>
          <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-4"></div>
        </div>

        {/* 主内容区域 */}
        <div className="relative">
          {/* 内容区域 */}
          <div className="flex flex-col md:flex-row gap-8 md:gap-12 items-center">
            {/* 左侧图片 */}
            <div className="md:w-2/5">
              <div className="rounded-xl overflow-hidden aspect-square">
                <CdnLazyImage 
                  src={imageSrc}
                  alt={imageAlt || t('horoscope.promo.imageAlt', 'Horoscope Astrology Reading')}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* 右侧链接和介绍 */}
            <div className="md:w-3/5 flex flex-col">
              <div className="space-y-6">
                {/* 每日运势 */}
                <div className="p-3">
                  <HoverableText 
                    as="h3" 
                    onClick={handleDailyClick}
                    className="block font-medium text-lg mb-1"
                  >
                    {t('horoscope.daily.title')}
                  </HoverableText>
                  <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    {t('horoscope.daily.description')}
                  </p>
                </div>

                {/* 年度运势 */}
                <div className="p-3">
                  <HoverableText 
                    as="h3" 
                    onClick={handleYearlyClick}
                    className="block font-medium text-lg mb-1"
                  >
                    {t('horoscope.yearly.title')}
                  </HoverableText>
                  <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    {t('horoscope.yearly.description')}
                  </p>
                </div>

                {/* 爱情运势 */}
                <div className="p-3">
                  <HoverableText 
                    as="h3" 
                    onClick={handleLoveClick}
                    className="block font-medium text-lg mb-1"
                  >
                    {t('horoscope.compatibility.title')}
                  </HoverableText>
                  <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    {t('horoscope.compatibility.description')}
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          {/* 查看全部按钮 - 底部中央位置 */}
          <div className="mt-10 text-center">
            <button
              onClick={() => navigate('/horoscope')}
              className={`px-6 py-2.5 rounded-full text-white ${
                isDark 
                  ? 'bg-gradient-to-r from-purple-700 to-indigo-600 hover:from-purple-600 hover:to-indigo-500'
                  : 'bg-gradient-to-r from-purple-600 to-indigo-500 hover:from-purple-500 hover:to-indigo-400'
              }`}
            >
              {t('horoscope.promo.viewAll')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HoroscopePromoBlock; 