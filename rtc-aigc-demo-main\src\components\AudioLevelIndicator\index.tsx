/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import RtcClient from '@/lib/RtcClient';
import { RootState } from '@/store';
import styles from './index.module.less';

/**
 * 音频电平指示器组件
 * 用于直观显示麦克风是否工作及输入电平
 */
function AudioLevelIndicator() {
  const [audioLevel, setAudioLevel] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const isAudioPublished = useSelector((state: RootState) => state.room.localUser.publishAudio);

  useEffect(() => {
    // 设置音频电平回调
    RtcClient.setAudioLevelCallback((level) => {
      setAudioLevel(level);
      setIsActive(level > 5); // 如果电平大于5，认为是有声音的
    });

    // 组件卸载时移除回调
    return () => {
      RtcClient.setAudioLevelCallback(null);
    };
  }, []);

  // 根据电平计算指示器的样式
  const getLevelClass = () => {
    if (!isAudioPublished) return styles.disabled;
    if (!isActive) return styles.inactive;
    if (audioLevel < 20) return styles.low;
    if (audioLevel < 50) return styles.medium;
    return styles.high;
  };

  return (
    <div className={`${styles.container} ${!isAudioPublished ? styles.micDisabled : ''}`}>
      <div className={styles.label}>
        {isAudioPublished 
          ? `麦克风状态: ${isActive ? '活跃' : '静默'}`
          : `麦克风已关闭${isActive ? ' (仍在捕获声音!)' : ''}`
        }
      </div>
      <div className={styles.indicator}>
        <div 
          className={`${styles.level} ${getLevelClass()}`} 
          style={{ width: `${audioLevel}%` }}
        />
      </div>
    </div>
  );
}

export default AudioLevelIndicator; 