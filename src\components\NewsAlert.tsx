import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';

interface NewsAlertProps {
  message: string;
  className?: string;
}

const NewsAlert: React.FC<NewsAlertProps> = ({ message, className = '' }) => {
  const [isVisible, setIsVisible] = useState(true);
  const { t } = useTranslation();
  
  // 检查本地存储，看用户是否已经关闭了此消息
  useEffect(() => {
    const isNewsClosed = localStorage.getItem('newsAlert_closed');
    if (isNewsClosed) {
      setIsVisible(false);
    }
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    // 在本地存储中记录用户已关闭新闻
    localStorage.setItem('newsAlert_closed', 'true');
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className={`fixed left-0 right-0 top-14 z-40 bg-gradient-to-r from-indigo-500/90 via-purple-500/90 to-pink-500/90 dark:from-indigo-900/95 dark:via-purple-900/95 dark:to-pink-900/95 backdrop-blur-sm shadow-md border-b border-purple-300/30 dark:border-purple-800/30 ${className}`}
        >
          {/* 移动端布局 - 只在小屏幕上显示 */}
          <div className="sm:hidden w-full px-3 py-2.5 relative">
            <div className="flex items-start">
              <div className="flex-1 pr-8">
                <p className="text-sm text-white dark:text-purple-50 font-medium leading-relaxed break-words">
                  {message}
                </p>
              </div>
              <button
                onClick={handleClose}
                className="absolute top-2.5 right-3 p-1 rounded-full hover:bg-white/20 transition-colors"
                aria-label={t('common.close', '关闭')}
              >
                <svg className="w-4 h-4 text-white dark:text-purple-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          
          {/* 桌面端布局 - 保持原样 */}
          <div className="hidden sm:flex w-full px-4 py-2.5 items-center justify-center">
            <div className="flex items-center max-w-5xl">
              <p className="text-sm sm:text-base text-white dark:text-purple-50 font-medium text-center">
                {message}
              </p>
            </div>
          </div>
          
          {/* PC端关闭按钮 */}
          <button
            onClick={handleClose}
            className="absolute hidden sm:block top-1/2 -translate-y-1/2 right-3 sm:right-6 md:right-8 p-1.5 rounded-full hover:bg-white/20 dark:hover:bg-purple-800/50 transition-colors"
            aria-label={t('common.close', '关闭')}
          >
            <svg className="w-5 h-5 text-white dark:text-purple-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default NewsAlert; 