const cron = require('node-cron');
const { HOROSCOPE_TYPES, ZODIAC_SIGNS, getHoroscope } = require('../services/horoscopeService');
const { getAllHoroscopesFromFile } = require('../services/horoscopeFileService');
const EmailService = require('../services/emailService');
const { formatDateForDb } = require('../services/horoscopeService');

/**
 * 检查星座运势生成结果，如果有失败的情况则发送提醒邮件
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<boolean>} 是否全部成功
 */
async function checkHoroscopeGeneration(type, date, language) {
  try {
    // 格式化日期为YYYY-MM-DD格式
    const formattedDate = formatDateForDb(date);
    console.log(`检查 ${language} ${type} ${formattedDate} 运势生成结果...`);
    
    // 从本地文件获取所有星座的运势数据
    const horoscopes = await getAllHoroscopesFromFile(type, date, language);
    
    // 检查是否所有星座都有数据
    const missingZodiacs = [];
    for (const sign of ZODIAC_SIGNS) {
      if (!horoscopes || !horoscopes[sign]) {
        missingZodiacs.push(sign);
      }
    }
    
    // 如果有缺失的星座数据，发送提醒邮件
    if (missingZodiacs.length > 0) {
      console.warn(`${language} ${type} ${formattedDate} 运势生成不完整，缺少以下星座: ${missingZodiacs.join(', ')}`);
      
      // 获取管理员邮箱，使用ADMIN_EMAILS环境变量
      const adminEmails = process.env.ADMIN_EMAILS || '<EMAIL>';
      
      // 构建邮件内容
      const emailSubject = `星座运势生成失败提醒 - ${language} ${type} ${formattedDate}`;
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #d9534f; margin-top: 0;">星座运势生成失败提醒</h2>
          <p>系统检测到部分星座运势生成失败，详情如下：</p>
          
          <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
            <p><strong>运势类型:</strong> ${type}</p>
            <p><strong>语言:</strong> ${language}</p>
            <p><strong>目标日期:</strong> ${formattedDate}</p>
            <p><strong>缺失星座:</strong> ${missingZodiacs.join(', ')}</p>
          </div>
          
          <p>请检查API调用日志和本地文件，确认失败原因并手动重新生成。</p>
          
          <hr style="margin: 20px 0; border: 0; border-top: 1px solid #eee;">
          <p style="color: #777; font-size: 12px;">此邮件由系统自动发送，请勿直接回复。</p>
        </div>
      `;
      
      // 发送提醒邮件
      try {
        await EmailService.sendMailWithRetry({
          to: adminEmails,
          subject: emailSubject,
          html: emailHtml
        });
        console.log(`已发送提醒邮件至 ${adminEmails}`);
      } catch (emailError) {
        console.error('发送提醒邮件失败:', emailError);
      }
      
      return false;
    }
    
    console.log(`${language} ${type} ${formattedDate} 所有星座运势生成成功!`);
    return true;
  } catch (error) {
    // 格式化日期为YYYY-MM-DD格式
    const formattedDate = formatDateForDb(date);
    console.error(`检查 ${language} ${type} ${formattedDate} 运势生成结果失败:`, error);
    return false;
  }
}

/**
 * 定时生成星座运势数据的任务
 * 所有数据直接保存到本地文件中
 */
function scheduleHoroscopeTasks() {
  // 每天12:15生成四种语言的每日运势（昨天、今天、明天和后天）
  cron.schedule('32 12 * * *', async () => {
    try {
      console.log('开始在12:15生成四种语言的每日运势（昨天、今天、明天和后天）...');
      
      // 创建昨天、今天和明天的日期对象
      // 获取当前服务器日期（不使用UTC时间）
      const serverDate = new Date();
      const serverYear = serverDate.getFullYear();
      const serverMonth = serverDate.getMonth();
      const serverDay = serverDate.getDate();
      
      // 基于服务器当前日期创建UTC日期对象
      const todayUTC = new Date(Date.UTC(serverYear, serverMonth, serverDay));
      const yesterday = new Date(Date.UTC(serverYear, serverMonth, serverDay - 1));
      const tomorrow = new Date(Date.UTC(serverYear, serverMonth, serverDay + 1));
      const dayAfterTomorrow = new Date(Date.UTC(serverYear, serverMonth, serverDay + 2));
      
      // 格式化日期为YYYY-MM-DD格式
      const yesterdayFormatted = formatDateForDb(yesterday);
      const todayFormatted = formatDateForDb(todayUTC);
      const tomorrowFormatted = formatDateForDb(tomorrow);
      const dayAfterTomorrowFormatted = formatDateForDb(dayAfterTomorrow);
      
      console.log(`将生成日期: ${yesterdayFormatted}(昨天), ${todayFormatted}(今天), ${tomorrowFormatted}(明天), ${dayAfterTomorrowFormatted}(后天)`);
      
      // 按顺序生成四种语言的每日运势，每种语言之间添加延迟
      const languages = ['zh-CN', 'en', 'ja', 'zh-TW'];
      const delay = 3000; // 3秒延迟
      
      for (let i = 0; i < languages.length; i++) {
        const lang = languages[i];
        
        // 生成昨天的运势
        console.log(`开始生成 ${lang} ${yesterdayFormatted}的每日运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.DAILY, yesterday, lang);
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // 生成今天的运势
        console.log(`开始生成 ${lang} ${todayFormatted}的每日运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.DAILY, todayUTC, lang);
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // 生成明天的运势
        console.log(`开始生成 ${lang} ${tomorrowFormatted}的每日运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.DAILY, tomorrow, lang);
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // 生成后天的运势
        console.log(`开始生成 ${lang} ${dayAfterTomorrowFormatted}的每日运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.DAILY, dayAfterTomorrow, lang);
        
        // 添加延迟，除了最后一种语言
        if (i < languages.length - 1) {
          console.log(`等待 ${delay}毫秒后继续下一种语言...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
      
      console.log(`所有语言的每日运势（${yesterdayFormatted}、${todayFormatted}、${tomorrowFormatted}、${dayAfterTomorrowFormatted}）生成完成！`);
    } catch (error) {
      console.error('在12:15生成四种语言的每日运势失败:', error);
      
      // 发送错误提醒邮件
      try {
        const adminEmails = process.env.ADMIN_EMAILS || '<EMAIL>';
        await EmailService.sendMailWithRetry({
          to: adminEmails,
          subject: '12:15星座运势批量生成任务失败',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <h2 style="color: #d9534f; margin-top: 0;">星座运势批量生成任务失败</h2>
              <p>12:15的四语言每日运势生成任务执行失败，错误详情：</p>
              
              <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px; overflow-x: auto;">
                <pre style="margin: 0;">${error.message}\n\n${error.stack}</pre>
              </div>
              
              <p>请检查服务器日志获取更多信息。</p>
              
              <hr style="margin: 20px 0; border: 0; border-top: 1px solid #eee;">
              <p style="color: #777; font-size: 12px;">此邮件由系统自动发送，请勿直接回复。</p>
            </div>
          `
        });
      } catch (emailError) {
        console.error('发送错误提醒邮件失败:', emailError);
      }
    }
  });

  // 每周日晚上20点生成上周、本周、下周和下下周的每周运势（四种语言）
  cron.schedule('0 20 * * 0', async () => {
    try {
      console.log('开始生成四种语言的每周运势（上周、本周、下周和下下周）...');
      
      // 创建上周一、本周一、下周一和下下周一的日期对象（使用UTC时间）
      const today = new Date();
      // 计算本周一（0是周日，1是周一，所以需要减去today.getUTCDay()并加1）
      const thisMonday = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate() - today.getUTCDay() + 1));
      const lastMonday = new Date(Date.UTC(thisMonday.getUTCFullYear(), thisMonday.getUTCMonth(), thisMonday.getUTCDate() - 7));
      const nextMonday = new Date(Date.UTC(thisMonday.getUTCFullYear(), thisMonday.getUTCMonth(), thisMonday.getUTCDate() + 7));
      const nextNextMonday = new Date(Date.UTC(thisMonday.getUTCFullYear(), thisMonday.getUTCMonth(), thisMonday.getUTCDate() + 14));
      
      // 格式化日期为YYYY-MM-DD格式
      const lastMondayFormatted = formatDateForDb(lastMonday);
      const thisMondayFormatted = formatDateForDb(thisMonday);
      const nextMondayFormatted = formatDateForDb(nextMonday);
      const nextNextMondayFormatted = formatDateForDb(nextNextMonday);
      
      // 按顺序生成四种语言的每周运势，每种语言之间添加延迟
      const languages = ['zh-CN', 'en', 'ja', 'zh-TW'];
      const delay = 3000; // 3秒延迟
      
      for (let i = 0; i < languages.length; i++) {
        const lang = languages[i];
        
        // 生成上周的运势
        console.log(`开始生成 ${lang} ${lastMondayFormatted}的每周运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.WEEKLY, lastMonday, lang);
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // 生成本周的运势
        console.log(`开始生成 ${lang} ${thisMondayFormatted}的每周运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.WEEKLY, thisMonday, lang);
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // 生成下周的运势
        console.log(`开始生成 ${lang} ${nextMondayFormatted}的每周运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.WEEKLY, nextMonday, lang);
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // 生成下下周的运势
        console.log(`开始生成 ${lang} ${nextNextMondayFormatted}的每周运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.WEEKLY, nextNextMonday, lang);
        
        // 添加延迟，除了最后一种语言
        if (i < languages.length - 1) {
          console.log(`等待 ${delay}毫秒后继续下一种语言...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
      
      console.log(`所有语言的每周运势（${lastMondayFormatted}、${thisMondayFormatted}、${nextMondayFormatted}、${nextNextMondayFormatted}）生成完成！`);
    } catch (error) {
      console.error('生成四种语言的每周运势失败:', error);
      
      // 发送错误提醒邮件
      try {
        const adminEmails = process.env.ADMIN_EMAILS || '<EMAIL>';
        await EmailService.sendMailWithRetry({
          to: adminEmails,
          subject: '每周星座运势批量生成任务失败',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <h2 style="color: #d9534f; margin-top: 0;">星座运势批量生成任务失败</h2>
              <p>每周运势生成任务执行失败，错误详情：</p>
              
              <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px; overflow-x: auto;">
                <pre style="margin: 0;">${error.message}\n\n${error.stack}</pre>
              </div>
              
              <p>请检查服务器日志获取更多信息。</p>
              
              <hr style="margin: 20px 0; border: 0; border-top: 1px solid #eee;">
              <p style="color: #777; font-size: 12px;">此邮件由系统自动发送，请勿直接回复。</p>
            </div>
          `
        });
      } catch (emailError) {
        console.error('发送错误提醒邮件失败:', emailError);
      }
    }
  });

  // 每月最后一天晚上20点生成上月、本月、下月和下下月的每月运势（四种语言）
  cron.schedule('0 20 28-31 * *', async () => {
    try {
      // 检查是否是月末最后一天（使用UTC时间）
      const today = new Date();
      const tomorrow = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate() + 1));
      
      // 如果明天的月份不同于今天，说明今天是月末
      if (tomorrow.getUTCMonth() !== today.getUTCMonth()) {
        console.log('开始生成四种语言的每月运势（上月、本月、下月和下下月）...');
        
        // 创建上月、本月、下月和下下月的日期对象（使用UTC时间）
        const thisMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), 1));
        const lastMonth = new Date(Date.UTC(thisMonth.getUTCFullYear(), thisMonth.getUTCMonth() - 1, 1));
        const nextMonth = new Date(Date.UTC(tomorrow.getUTCFullYear(), tomorrow.getUTCMonth(), 1));
        const nextNextMonth = new Date(Date.UTC(nextMonth.getUTCFullYear(), nextMonth.getUTCMonth() + 1, 1));
        
        // 格式化日期为YYYY-MM格式
        const lastMonthFormatted = `${lastMonth.getUTCFullYear()}-${String(lastMonth.getUTCMonth() + 1).padStart(2, '0')}`;
        const thisMonthFormatted = `${thisMonth.getUTCFullYear()}-${String(thisMonth.getUTCMonth() + 1).padStart(2, '0')}`;
        const nextMonthFormatted = `${nextMonth.getUTCFullYear()}-${String(nextMonth.getUTCMonth() + 1).padStart(2, '0')}`;
        const nextNextMonthFormatted = `${nextNextMonth.getUTCFullYear()}-${String(nextNextMonth.getUTCMonth() + 1).padStart(2, '0')}`;
        
        // 按顺序生成四种语言的每月运势，每种语言之间添加延迟
        const languages = ['zh-CN', 'en', 'ja', 'zh-TW'];
        const delay = 3000; // 3秒延迟
        
        for (let i = 0; i < languages.length; i++) {
          const lang = languages[i];
          
          // 生成上月的运势
          console.log(`开始生成 ${lang} ${lastMonthFormatted}月的每月运势...`);
          await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.MONTHLY, lastMonth, lang);
          
          // 添加延迟
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // 生成本月的运势
          console.log(`开始生成 ${lang} ${thisMonthFormatted}月的每月运势...`);
          await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.MONTHLY, thisMonth, lang);
          
          // 添加延迟
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // 生成下月的运势
          console.log(`开始生成 ${lang} ${nextMonthFormatted}月的每月运势...`);
          await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.MONTHLY, nextMonth, lang);
          
          // 添加延迟
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // 生成下下月的运势
          console.log(`开始生成 ${lang} ${nextNextMonthFormatted}月的每月运势...`);
          await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.MONTHLY, nextNextMonth, lang);
          
          // 添加延迟，除了最后一种语言
          if (i < languages.length - 1) {
            console.log(`等待 ${delay}毫秒后继续下一种语言...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
        
        console.log(`所有语言的每月运势（${lastMonthFormatted}月、${thisMonthFormatted}月、${nextMonthFormatted}月、${nextNextMonthFormatted}月）生成完成！`);
      }
    } catch (error) {
      console.error('生成四种语言的每月运势失败:', error);
      
      // 发送错误提醒邮件
      try {
        const adminEmails = process.env.ADMIN_EMAILS || '<EMAIL>';
        await EmailService.sendMailWithRetry({
          to: adminEmails,
          subject: '每月星座运势批量生成任务失败',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <h2 style="color: #d9534f; margin-top: 0;">星座运势批量生成任务失败</h2>
              <p>每月运势生成任务执行失败，错误详情：</p>
              
              <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px; overflow-x: auto;">
                <pre style="margin: 0;">${error.message}\n\n${error.stack}</pre>
              </div>
              
              <p>请检查服务器日志获取更多信息。</p>
              
              <hr style="margin: 20px 0; border: 0; border-top: 1px solid #eee;">
              <p style="color: #777; font-size: 12px;">此邮件由系统自动发送，请勿直接回复。</p>
            </div>
          `
        });
      } catch (emailError) {
        console.error('发送错误提醒邮件失败:', emailError);
      }
    }
  });

  // 每年12月31日晚上20点生成去年、今年、明年和后年的年度运势（四种语言）
  cron.schedule('0 20 31 12 *', async () => {
    try {
      console.log('开始生成四种语言的年度运势（去年、今年、明年和后年）...');
      
      // 创建去年、今年、明年和后年的日期对象（使用UTC时间）
      const today = new Date();
      const lastYear = new Date(Date.UTC(today.getUTCFullYear() - 1, 0, 1));
      const thisYear = new Date(Date.UTC(today.getUTCFullYear(), 0, 1));
      const nextYear = new Date(Date.UTC(today.getUTCFullYear() + 1, 0, 1));
      const nextNextYear = new Date(Date.UTC(today.getUTCFullYear() + 2, 0, 1));
      
      // 格式化年份
      const lastYearFormatted = lastYear.getUTCFullYear().toString();
      const thisYearFormatted = thisYear.getUTCFullYear().toString();
      const nextYearFormatted = nextYear.getUTCFullYear().toString();
      const nextNextYearFormatted = nextNextYear.getUTCFullYear().toString();
      
      // 按顺序生成四种语言的年度运势，每种语言之间添加延迟
      const languages = ['zh-CN', 'en', 'ja', 'zh-TW'];
      const delay = 3000; // 3秒延迟
      
      for (let i = 0; i < languages.length; i++) {
        const lang = languages[i];
        
        // 生成去年的运势
        console.log(`开始生成 ${lang} ${lastYearFormatted}年的年度运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.YEARLY, lastYear, lang);
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // 生成今年的运势
        console.log(`开始生成 ${lang} ${thisYearFormatted}年的年度运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.YEARLY, thisYear, lang);
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // 生成明年的运势
        console.log(`开始生成 ${lang} ${nextYearFormatted}年的年度运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.YEARLY, nextYear, lang);
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // 生成后年的运势
        console.log(`开始生成 ${lang} ${nextNextYearFormatted}年的年度运势...`);
        await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.YEARLY, nextNextYear, lang);
        
        // 添加延迟，除了最后一种语言
        if (i < languages.length - 1) {
          console.log(`等待 ${delay}毫秒后继续下一种语言...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
      
      console.log(`所有语言的年度运势（${lastYearFormatted}年、${thisYearFormatted}年、${nextYearFormatted}年、${nextNextYearFormatted}年）生成完成！`);
    } catch (error) {
      console.error('生成四种语言的年度运势失败:', error);
      
      // 发送错误提醒邮件
      try {
        const adminEmails = process.env.ADMIN_EMAILS || '<EMAIL>';
        await EmailService.sendMailWithRetry({
          to: adminEmails,
          subject: '年度星座运势批量生成任务失败',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <h2 style="color: #d9534f; margin-top: 0;">星座运势批量生成任务失败</h2>
              <p>年度运势生成任务执行失败，错误详情：</p>
              
              <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px; overflow-x: auto;">
                <pre style="margin: 0;">${error.message}\n\n${error.stack}</pre>
              </div>
              
              <p>请检查服务器日志获取更多信息。</p>
              
              <hr style="margin: 20px 0; border: 0; border-top: 1px solid #eee;">
              <p style="color: #777; font-size: 12px;">此邮件由系统自动发送，请勿直接回复。</p>
            </div>
          `
        });
      } catch (emailError) {
        console.error('发送错误提醒邮件失败:', emailError);
      }
    }
  });

  // 每月最后一天晚上21点生成上月、本月、下月和下下月的爱情运势（四种语言）
  cron.schedule('0 21 28-31 * *', async () => {
    try {
      // 检查是否是月末最后一天（使用UTC时间）
      const today = new Date();
      const tomorrow = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate() + 1));
      
      // 如果明天的月份不同于今天，说明今天是月末
      if (tomorrow.getUTCMonth() !== today.getUTCMonth()) {
        console.log('开始生成四种语言的爱情运势（上月、本月、下月和下下月）...');
        
        // 创建上月、本月、下月和下下月的日期对象（使用UTC时间）
        const thisMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), 1));
        const lastMonth = new Date(Date.UTC(thisMonth.getUTCFullYear(), thisMonth.getUTCMonth() - 1, 1));
        const nextMonth = new Date(Date.UTC(tomorrow.getUTCFullYear(), tomorrow.getUTCMonth(), 1));
        const nextNextMonth = new Date(Date.UTC(nextMonth.getUTCFullYear(), nextMonth.getUTCMonth() + 1, 1));
        
        // 格式化日期为YYYY-MM格式
        const lastMonthFormatted = `${lastMonth.getUTCFullYear()}-${String(lastMonth.getUTCMonth() + 1).padStart(2, '0')}`;
        const thisMonthFormatted = `${thisMonth.getUTCFullYear()}-${String(thisMonth.getUTCMonth() + 1).padStart(2, '0')}`;
        const nextMonthFormatted = `${nextMonth.getUTCFullYear()}-${String(nextMonth.getUTCMonth() + 1).padStart(2, '0')}`;
        const nextNextMonthFormatted = `${nextNextMonth.getUTCFullYear()}-${String(nextNextMonth.getUTCMonth() + 1).padStart(2, '0')}`;
        
        // 按顺序生成四种语言的爱情运势，每种语言之间添加延迟
        const languages = ['zh-CN', 'en', 'ja', 'zh-TW'];
        const delay = 3000; // 3秒延迟
        
        for (let i = 0; i < languages.length; i++) {
          const lang = languages[i];
          
          // 生成上月的运势
          console.log(`开始生成 ${lang} ${lastMonthFormatted}月的爱情运势...`);
          await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.LOVE, lastMonth, lang);
          
          // 添加延迟
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // 生成本月的运势
          console.log(`开始生成 ${lang} ${thisMonthFormatted}月的爱情运势...`);
          await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.LOVE, thisMonth, lang);
          
          // 添加延迟
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // 生成下月的运势
          console.log(`开始生成 ${lang} ${nextMonthFormatted}月的爱情运势...`);
          await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.LOVE, nextMonth, lang);
          
          // 添加延迟
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // 生成下下月的运势
          console.log(`开始生成 ${lang} ${nextNextMonthFormatted}月的爱情运势...`);
          await generateHoroscopesForAllSigns(HOROSCOPE_TYPES.LOVE, nextNextMonth, lang);
          
          // 添加延迟，除了最后一种语言
          if (i < languages.length - 1) {
            console.log(`等待 ${delay}毫秒后继续下一种语言...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
        
        console.log(`所有语言的爱情运势（${lastMonthFormatted}月、${thisMonthFormatted}月、${nextMonthFormatted}月、${nextNextMonthFormatted}月）生成完成！`);
      }
    } catch (error) {
      console.error('生成四种语言的爱情运势失败:', error);
      
      // 发送错误提醒邮件
      try {
        const adminEmails = process.env.ADMIN_EMAILS || '<EMAIL>';
        await EmailService.sendMailWithRetry({
          to: adminEmails,
          subject: '爱情星座运势批量生成任务失败',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <h2 style="color: #d9534f; margin-top: 0;">星座运势批量生成任务失败</h2>
              <p>爱情运势生成任务执行失败，错误详情：</p>
              
              <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px; overflow-x: auto;">
                <pre style="margin: 0;">${error.message}\n\n${error.stack}</pre>
              </div>
              
              <p>请检查服务器日志获取更多信息。</p>
              
              <hr style="margin: 20px 0; border: 0; border-top: 1px solid #eee;">
              <p style="color: #777; font-size: 12px;">此邮件由系统自动发送，请勿直接回复。</p>
            </div>
          `
        });
      } catch (emailError) {
        console.error('发送错误提醒邮件失败:', emailError);
      }
    }
  });
}

/**
 * 为指定类型生成运势
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<boolean>} 是否成功
 */
async function generateHoroscopesForAllSigns(type, date, language) {
  try {
    // 只需调用一次getHoroscope，它会生成并保存所有星座的运势
    // 使用第一个星座作为参数，但实际上会生成所有星座的数据
    await getHoroscope(type, ZODIAC_SIGNS[0], date, language);
    
    // 添加检查逻辑，验证所有星座的数据是否都已成功生成
    await checkHoroscopeGeneration(type, date, language);
    
    return true;
  } catch (error) {
    console.error(`生成 ${language} ${type} 运势失败:`, error);
    throw error;
  }
}

module.exports = {
  scheduleHoroscopeTasks
}; 